/* eslint-disable import/no-extraneous-dependencies,no-console */

const fs = require('fs');
const path = require('path');

const mime = require('mime');
const MSS = require('mss-sdk');

const { getBucketName } = require('./util');

const config = {
    bucketName: getBucketName(), // cdn的bucketName，cdn为每一个bucketName，生成一个单独的文件夹
    distDir: path.resolve(__dirname, '../build'), // 要上传的目录
    exclude: /\.html/, // 匹配的成功的文件不上传
};

// 美团云 CDN 实例
const s3 = new MSS.S3({
    accessKeyId: 'aa19bf25ac004c418b2b6f9690dd737a',
    secretAccessKey: '5ff9801dc6aa4901b1d36f2cf235b607',
    endpoint: 's3plus.vip.sankuai.com',
});

function uploadFile(filePath) {
    if (config.exclude.test(filePath)) {
        return;
    }
    const file = fs.createReadStream(filePath);
    const key = path.relative(config.distDir, filePath);

    const params = {
        Bucket: config.bucketName,
        Key: key,
        ContentType: mime.getType(key),
        Body: file,
    };

    s3.putObject(params).on('error', (err) => {
        console.error(`上传${key}失败`);
        console.error(err.stack);
    }).on('success', () => {
        console.log(`上传${key}成功`);
    }).send();
}

function uploadDir(dirPath) {
    fs.readdir(dirPath, (readErr, files) => {
        if (readErr) {
            if (readErr.code !== 'ENOENT') { // ENOENT 为没有找到目录和文件
                console.error(`读取目录${dirPath}失败`);
                console.error(readErr.stack);
            }
            return;
        }

        files.forEach((fileName) => {
            const filePath = path.join(dirPath, fileName);
            fs.stat(filePath, (statErr, stats) => {
                if (statErr) {
                    console.error(statErr.stack);
                    return;
                }
                if (stats.isFile()) {
                    uploadFile(filePath);
                } else {
                    uploadDir(filePath);
                }
            });
        });
    });
}

function main() {
    const params = { Bucket: config.bucketName };
    // 寻找是否存在Bucket， 如果不存在则创建一个，然后上传
    s3.headBucket(params, (err1) => {
        if (err1) {
            console.error(`寻找bucket：${config.bucketName} 失败`);
            console.error(err1.stack);
            params.ACL = 'public-read';
            s3.createBucket(params, (err2) => {
                if (err2) {
                    console.error(`尝试创建bucket：${config.bucketName} 失败`);
                    console.error(err2.stack);
                } else {
                    console.log(`尝试创建bucket：${config.bucketName} 成功`);
                    uploadDir(config.distDir);
                }
            });
        } else {
            uploadDir(config.distDir);
        }
    });
}

main();
