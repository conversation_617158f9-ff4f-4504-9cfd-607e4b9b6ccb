/**
 * 打包灵犀 & Owl Combo
 * require Node.js 8.x
 */

/* eslint-disable import/no-extraneous-dependencies, no-console */
const crypto = require('crypto');
const axios = require('axios');
const MSS = require('mss-sdk');

const s3 = new MSS.S3({
    accessKeyId: 'aa19bf25ac004c418b2b6f9690dd737a',
    secretAccessKey: '5ff9801dc6aa4901b1d36f2cf235b607',
    endpoint: 's3plus.vip.sankuai.com',
});

const analyticsUrl = 'https://lx.meituan.net/lx.js';
const owlUrl = 'https://www.dpfile.com/app/owl/static/owl_1.8.11.js';
const bucketName = 'waimai-fem-alchemist-combo';

function pad(num) {
    if (num < 10) {
        return `0${num}`;
    }
    return `${num}`;
}

function timeStr() {
    const now = new Date();
    const YYYY = `${now.getFullYear()}`;
    const MM = pad(now.getMonth() + 1);
    const DD = pad(now.getDate());
    const hh = pad(now.getHours());
    const mm = pad(now.getMinutes());
    const ss = pad(now.getSeconds());
    return `${YYYY}-${MM}-${DD} ${hh}:${mm}:${ss}`;
}

function fetch(url) {
    return axios.get(url, { responseType: 'text' }).then(res => res.data);
}

function putObject(data) {
    return new Promise((resolve, reject) => {
        s3.putObject(data, (err, res) => {
            if (err) {
                reject(err);
            } else {
                resolve(res);
            }
        });
    });
}

async function main() {
    const time = timeStr();
    const [analytics, owl] = await Promise.all([
        fetch(analyticsUrl),
        fetch(owlUrl),
    ]);
    const content = `/* ${analyticsUrl} @ ${time} */\n${analytics}\n/* ${owlUrl} */\n${owl}`;
    const hash = crypto.createHash('sha256');
    hash.update(content);
    const digest = hash.digest('hex');
    const fileName = `combo-${digest.slice(0, 12)}.js`;
    const res = await putObject({
        Bucket: bucketName,
        Key: fileName,
        ContentType: 'application/javascript',
        Body: content,
    });
    console.log(`文件: ${fileName} 上传成功!`);
    console.log(res);
}

main();

