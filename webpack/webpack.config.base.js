const path = require('path');
const PhoenixCDN = require('@wmfe/phoenix');
const { DefinePlugin } = require('webpack');

const MiniCssExtractPlugin = require('mini-css-extract-plugin');
const HtmlWebpackPlugin = require('html-webpack-plugin');
const VueLoaderPlugin = require('vue-loader/lib/plugin');
const RaptorSourcemapUploadPlugin = require("@bfe/raptor-sourcemap-upload-plugin")

const isDev = process.env.NODE_ENV === 'development';


const {
    NODE_ENV,
    DEPLOY_ENV,
} = process.env;
const devMode = NODE_ENV !== 'production';

module.exports = {
    mode: 'none',
    entry: {
        // vendor: [
        //     // vue 全家桶
        //     'vue', 'vuex', 'vue-router',
        //     // 常用库
        //     'axios', 'dayjs',
        //     // 组件库
        //     '@waimai/kangarooui-vue',
        //     '@waimai/kangarooui-vue/dist/css/kangarooui-vue.css',
        // ],
        main: './src/pages/main/index.js',
        invoice: './src/pages/invoice/index.js',
    },
    output: {
        path: path.resolve(__dirname, '../build'),
        publicPath: '/',
        filename: 'js/[name].js',
    },
    module: {
        rules: [{
            test: /\.vue$/,
            loader: 'vue-loader',
        }, {
            test: /\.js$/,
            use: 'babel-loader',
            exclude: /node_modules/,
        }, {
            test: /\.css$/,
            oneOf: [{
                resourceQuery: /module/,
                use: [
                    devMode
                        ? 'vue-style-loader'
                        : MiniCssExtractPlugin.loader,
                    {
                        loader: 'css-loader',
                        options: {
                            modules: true,
                            camelCase: true,
                            // localIdentName: '[path][name]---[local]---[hash:base64:5]',
                        },
                    },
                    'postcss-loader',
                ],
            }, {
                use: [
                    devMode
                        ? 'vue-style-loader'
                        : MiniCssExtractPlugin.loader,
                    'css-loader',
                    'postcss-loader',
                ],
            }],
        }, {
            test: /\.scss$/,
            oneOf: [{
                resourceQuery: /module/,
                use: [
                    devMode
                        ? 'vue-style-loader'
                        : MiniCssExtractPlugin.loader,
                    {
                        loader: 'css-loader',
                        options: {
                            modules: true,
                            camelCase: true,
                            // localIdentName: '[path][name]---[local]---[hash:base64:5]',
                        },
                    },
                    'postcss-loader',
                    'sass-loader',
                ],
            }, {
                use: [
                    devMode
                        ? 'vue-style-loader'
                        : MiniCssExtractPlugin.loader,
                    'css-loader',
                    'postcss-loader',
                    'sass-loader',
                ],
            }],
        }, {
            test: /\.(png|jpe?g|gif|svg)$/,
            // svg 字体文件不应放到 img 目录
            exclude: /fonts?/,
            use: [{
                loader: 'file-loader',
                options: {
                    name: '/img/[name]_[hash].[ext]',
                },
            }],
        }, {
            test: /\.(eot|svg|ttf|woff|woff2)(#\S*)?$/,
            use: [{
                loader: 'file-loader',
                options: {
                    name: '[name]_[hash].[ext]',
                    outputPath: isDev ? 'fonts/' : '/fonts/',
                },
            }],
        }],
    },
    resolve: {
        extensions: ['.js', '.json', '.vue'],
        alias: {
            vue$: 'vue/dist/vue.runtime.esm.js',
            $src: path.resolve(__dirname, '../src'),
            $lib: path.resolve(__dirname, '../src/lib'),
            $components: path.resolve(__dirname, '../src/components'),
            $config: path.resolve(__dirname, '../src/config'),
            $invoice: path.resolve(__dirname, '../src/pages/invoice'),
            $utils: path.resolve(__dirname, '../src/utils'),
        },
    },
  plugins: [
        new RaptorSourcemapUploadPlugin({
            project: 'com.sankuai.wmfinance.merchant.invoicepc',
            env: DEPLOY_ENV,
        }),
        new VueLoaderPlugin(),

        new DefinePlugin({
            'process.env': {
                NODE_ENV: JSON.stringify(NODE_ENV),
                DEPLOY_ENV: JSON.stringify(DEPLOY_ENV),
            },
        }),

        new MiniCssExtractPlugin({
            filename: devMode ? 'css/[name].css' : 'css/[name]-[hash].css',
            chunkFilename: devMode ? 'css/[name].chunk.[id].css' : 'css/[name].chunk.[id]-[hash].css',
        }),

        // html
        new HtmlWebpackPlugin({
            filename: 'billReconciliation.html',
            template: path.resolve(__dirname, '../src/pages/main/index.html'),
            inject: 'body',
            chunks: ['main'],
            // options
            NODE_ENV,
            DEPLOY_ENV,
        }),

        new HtmlWebpackPlugin({
            filename: 'invoice.html',
            template: path.resolve(__dirname, '../src/pages/invoice/index.html'),
            inject: 'body',
            chunks: ['invoice'],

            // options
            NODE_ENV,
            DEPLOY_ENV,
        }),
        new PhoenixCDN({
          // 开启静态资源CDN
          resCDN: true,
          // 兜底图展示
          imageCDN: {
              fallbackImg: '//p0.meituan.net/travelcube/0013581daa76b28fb711a36a458457fb4185.jpg',
          },
          phoenixConfig: {
              phoenixKey: "wm_store_cdn_s3,wm_store_cdn_img,wm_store_cdn_s3plus,wm_store_cdn_dpfile,wm_store_cdn_p1,wm_store_cdn_p0,wm_store_base_cdn",
              appKey: "com.sankuai.wmfinance.merchant.invoicepc"
          },
          timeout: 2000,
          // 参考 https://km.sankuai.com/page/*********
          cdnList: [
              // 静态资源容灾
              {
                  match: ['s3.meituan.net', 's3plus.meituan.net'],
                  replace: [
                    {
                        host: 's3plus.meituan.net',
                        try_count: 1,
                    },
                    {
                        host: 's3.meituan.net',
                        try_count: 1,
                    },
                  ],
              },
              // 图片容灾 
              {
                  match: ['p0.meituan.net', 'p1.meituan.net', 'img.meituan.net'],
                  replace: [
                    {
                        host: 'p1.meituan.net',
                        try_count: 1,
                    },
                    {
                        host: 'img.meituan.net',
                        try_count: 1,
                    },
                    {
                        host: 'p0.meituan.net',
                        try_count: 1,
                    },
                 ],
              },
          ],
      }),
    ],
    // SplitChunksPlugin config
    optimization: {
        splitChunks: {
            // include all types of chunks
            chunks: 'all',
        },
    },
    // stats output
    stats: {
        source: false,
    },
    // Some libraries import Node modules but don't use them in the browser.
    // Tell Webpack to provide empty mocks for them so importing them works.
    node: {
        Buffer: false,
        // axios/lib/defaults use `process`
        process: false,
        // vue use setImmediate
        setImmediate: false,
    },
};
