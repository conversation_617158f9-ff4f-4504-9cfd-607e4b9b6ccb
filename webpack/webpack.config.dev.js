
const webpack = require('webpack');
const merge = require('webpack-merge');
const FriendlyErrorsPlugin = require('friendly-errors-webpack-plugin');
const proxy = require('bee-proxy')

const baseConfig = require('./webpack.config.base');

module.exports = merge(baseConfig, {
    mode: 'development',
    plugins: [
        // dev hot reload
        new webpack.HotModuleReplacementPlugin(),

        // https://www.npmjs.com/package/friendly-errors-webpack-plugin
        new FriendlyErrorsPlugin(),

        // webpack v1 NoErrorsPlugin
        new webpack.NoEmitOnErrorsPlugin(),
    ],
    devServer: {
        // need webpack.HotModuleReplacementPlugin
        hot: true,

        overlay: {
            warnings: true,
            errors: true,
        },
        quiet: false,
        index: '/billReconciliation.html#/account-flow?wmPoiId=511150',
        noInfo: false,

        host: '0.0.0.0',
        disableHostCheck: true,
        inline: true,

        port: 99,
        watchOptions: {
            poll: 1000,//监测修改的时间(ms)
            aggregateTimeout: 500,//防止重复按键，500毫秒内算按一次
            ignored: /node_modules/,//不监测
        },

        proxy: {},
        // 主要是下面的配置
        before(app) {
            app.use(proxy);
        },
    },
    devtool: 'cheap-source-map',
    performance: {
        hints: false,
    },
});
