const pkg = require('../package.json');

const { DEPLOY_ENV, IS_GRAY, AWP_DEPLOY_ENV, PUBLIC_URL } = process.env;

function getBucketName() {
    if (DEPLOY_ENV !== 'online' && DEPLOY_ENV !== 'stage') {
        throw new Error('只有 online 和 stage 需要上传 CDN');
    }
    return `${pkg.name}-${DEPLOY_ENV}`;
}

function getPublicPath() {
    if (DEPLOY_ENV === 'online' || DEPLOY_ENV === 'stage') {
        if (AWP_DEPLOY_ENV) { // Talos 环境特有变量
            return `${PUBLIC_URL}`;
        } else {
            return `//s3plus.meituan.net/v1/mss_c4375b35f5cb4e678b5b55a48c40cf9d/${getBucketName()}/`;
        }
    }
    if (DEPLOY_ENV === 'test') {
      if (AWP_DEPLOY_ENV) {
        // 测试环境下，将静态资源部署到s3上
        return '//msstest-corp.sankuai.com/v1/mss_kSA9pkozf0iWTcuco+XPnw==/hfe-assets-st/wm/waimai_mfe_alchemist_pc/';
      } else {
        return `/finance/static/${IS_GRAY === 'true' ? 'gray_' : ''}html_pc/`;
      }
    }
    if (DEPLOY_ENV === 'dev') {
      return `/finance/static/${IS_GRAY === 'true' ? 'gray_' : ''}html_pc/`;
    }
    return '/';
}

module.exports = {
    getBucketName,
    getPublicPath,
};