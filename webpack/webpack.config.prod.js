const webpack = require('webpack');
const merge = require('webpack-merge');
const CopyWebpackPlugin = require('copy-webpack-plugin');
const OptimizeCssAssetsPlugin = require('optimize-css-assets-webpack-plugin');

const baseConfig = require('./webpack.config.base');
const { getPublicPath } = require('./util');

module.exports = merge(baseConfig, {
    mode: 'production',
    output: {
        publicPath: process.env.PUBLIC_PATH,
        filename: 'js/[name]-[chunkhash:12].js',
        chunkFilename: 'js/[name].chunk.[id]-[chunkhash:12].js',
    },
    plugins: [
        // hashed `module.id`
        new webpack.HashedModuleIdsPlugin(),

        // minify css
        new OptimizeCssAssetsPlugin(),

        // 拷贝跳板页
        new CopyWebpackPlugin([
            { from: 'static/*', to: '', flatten: true },
        ]),
    ],
    devtool: 'source-map',
    performance: {
        hints: 'warning',
    },
});
