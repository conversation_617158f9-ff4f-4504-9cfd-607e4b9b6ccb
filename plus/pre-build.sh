#!/bin/bash

#把nvm加入路径
source /home/<USER>/.bashrc

#切换node版本
nvm install v8.10.0
nvm use v8.10.0

#配置node-sass, 不配置的话，默认会去github上下载，速度会非常慢
export SASS_BINARY_SITE=http://npm.sankuai.com/mirrors/node-sass/

# 发布前分支校验
if [[ $PLUS_TEMPLATE_ENV == "prod" || $PLUS_TEMPLATE_ENV == "production" ]]; then
    if [[ $GIT_REFERENCE == release/* || $GIT_REFERENCE == release-* || $GIT_REFERENCE == r-* ]]; then
	    echo -e "\n分支检测成功！"
    else
        echo -e "\n请使用release-*或者release/*分支或者rTag进行发布，当前分支为：$GIT_REFERENCE"
	    exit 1;
    fi
else
    echo -e "\n 环境：$PLUS_TEMPLATE_ENV 不进行发布校验"
fi

#安装依赖
echo -e "\033[32m安装依赖-开始 `date +%Y-%m-%d_%H:%M:%S`\033[0m"
npm install --registry=http://r.npm.sankuai.com
echo -e "\033[32m安装依赖-结束 `date +%Y-%m-%d_%H:%M:%S`\033[0m"

#构建
echo -e "\033[32m构建-开始 `date +%Y-%m-%d_%H:%M:%S`\033[0m"
npm run build
echo -e "\033[32m构建-结束 `date +%Y-%m-%d_%H:%M:%S`\033[0m"

#上传cdn
if [ $DEPLOY_ENV == 'online' -o $DEPLOY_ENV == 'stage' ]; then
    echo -e "\033[32m上传cdn-开始 `date +%Y-%m-%d_%H:%M:%S`\033[0m"
    node webpack/upload-cdn.js
    echo -e "\033[32m安装依赖-结束 `date +%Y-%m-%d_%H:%M:%S`\033[0m"
fi
