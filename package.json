{"name": "wa<PERSON><PERSON>-mfe-alchemist-pc", "version": "0.0.2", "description": "", "main": "index.js", "scripts": {"start": "npm run dev -- --env", "dev": "NODE_ENV=development webpack-dev-server --config webpack/webpack.config.dev.js --color", "build": "rm -rf build && NODE_ENV=production webpack --config webpack/webpack.config.prod.js --color --progress", "profile": "rm -rf build && NODE_ENV=production webpack --config webpack/webpack.config.prod.js --color --progress --profile --json > stats.json", "lint:fix": "eslint --ext .js,.vue --ignore-path .gitignore --fix src"}, "repository": {"type": "git", "url": "ssh://*******************/wm/waimai_mfe_alchemist_pc.git"}, "author": "孙海阔<<EMAIL>>", "license": "XJBG", "devDependencies": {"@babel/core": "^7.0.1", "@babel/plugin-syntax-jsx": "^7.0.0", "@babel/preset-env": "^7.0.0", "@commitlint/cli": "^8.2.0", "@mfe/precommit-eslint": "1.0.9", "@types/query-string": "^6.3.0", "@utiljs/eslint-plugin-utiljs": "latest", "@wmfe/eslint-config-mt": "^0.2.0", "autoprefixer": "^9.1.5", "babel-eslint": "9.0.0", "babel-helper-vue-jsx-merge-props": "^2.0.3", "babel-loader": "^8.0.2", "babel-plugin-transform-vue-jsx": "^4.0.1", "bee-proxy": "0.0.8", "copy-webpack-plugin": "^4.5.2", "css-loader": "^1.0.0", "eslint": "5.5.0", "eslint-config-airbnb-base": "13.1.0", "eslint-plugin-html": "4.0.5", "eslint-plugin-import": "2.14.0", "file-loader": "^2.0.0", "friendly-errors-webpack-plugin": "^1.7.0", "html-webpack-plugin": "^4.0.0-beta.2", "husky": "^3.1.0", "mime": "^2.3.1", "mini-css-extract-plugin": "^0.4.2", "mss-sdk": "^2.0.0", "node-sass": "^4.14.1", "optimize-css-assets-webpack-plugin": "^5.0.1", "postcss-loader": "^3.0.0", "sass-loader": "^7.1.0", "style-loader": "^0.23.0", "validate-git-branch": "0.0.6", "vue-loader": "^15.4.2", "vue-template-compiler": "^2.5.17", "webpack": "^4.19.0", "webpack-cli": "^3.1.0", "webpack-dev-server": "^3.1.8", "webpack-merge": "^4.1.4"}, "dependencies": {"@babel/polyfill": "^7.0.0", "@bfe/raptor-sourcemap-upload-plugin": "^1.1.8", "@dp/owl": "^1.9.5", "@mtfe/pay-cashier-sdk-npm": "^1.0.9", "@roo-design/roo-vue": "^3.2.13", "@roo/roo-vue": "^1.2.0", "@ss/mtd-vue": "^0.4.16", "@utiljs/clone": "^0.2.5", "@utiljs/cookie": "^0.1.6", "@utiljs/param": "^0.6.11", "@wmfe/phoenix": "^1.0.0", "@wmfe/svelte-components": "^1.3.2", "axios": "^0.18.0", "dayjs": "^1.7.5", "eslint-plugin-vue": "^7.3.0", "express": "^4.18.2", "image-preview-vue": "^1.2.11", "moment": "^2.29.4", "svelte-adapter": "^0.4.0", "vue": "^2.5.17", "vue-router": "^3.0.1", "vuex": "^3.0.1"}, "config": {"validate-git-branch": {"pattern": "^(feat|fix|perf|refact)\\/20\\d{2}(10|11|12|(0\\d))(((0|1|2)\\d)|(30|31))-\\S+$", "ignorePattern": "(ignore|qa)", "helpMessage": "推送的分支名称格式不正确,正确的格式是 功能(feat或fix或perf或refact)+jira+描述,如 refact/20180512-readme-optimize", "justWarnOnfail": true}}, "browserslist": ">= 0%"}