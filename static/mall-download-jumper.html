<html>
    <head>
        <title>sku page jumper</title>
        <script>
             // 获取当前 URL 的查询参数
             const currentSearch = window.location.search;
            
            // 解析查询参数
            const urlParams = new URLSearchParams(currentSearch);
            
            // 添加 sku 参数
            urlParams.set('sku', '2');
            
            // 构建目标 URL 基础部分（不包含查询参数）
            const baseUrl = '/finance/static/html_pc/billReconciliation.html#/downloads/downloadBill';
            
            // 将所有参数拼接到目标 URL
            const targetUrl = baseUrl + '?' + urlParams.toString();
            
            // 重定向到目标 URL
            window.location = targetUrl;
        </script>
    </head>
    <body>
    </body>
</html>
