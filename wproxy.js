/* eslint-disable indent */
module.exports = {
  test: [
    {
      pattern: /^\/download/,
      responder: 'http://moneyepai.m.waimai.test.sankuai.com/',
    },
    {
      pattern: /^\/finance\/pc\/api/,
      responder: 'http://moneyepai.m.waimai.test.sankuai.com/',
    },
    {
      pattern: /^\/finance\/v3\/h5\/api/,
      responder: 'http://moneyepai.m.waimai.test.sankuai.com/',
    },
    {
      pattern: /^\/finance\/pc\/api\/poiSettleBill\/expectedIncome/,
      responder: 'http://yapi.sankuai.com/mock/2487',
    },
    {
      pattern: /^\/finance\/invoice/,
      responder: 'http://moneyepai.m.waimai.test.sankuai.com/',
    },
  ],
  invoicetest: [
    {
      pattern: /^\/download/,
      responder: 'http://invoice.waimai.test.sankuai.com/',
    },
    {
      pattern: /^\/finance\/pc\/api/,
      responder: 'http://invoice.waimai.test.sankuai.com/',
    },
    {
      pattern: /^\/finance\/v3\/h5\/api/,
      responder: 'http://invoice.waimai.test.sankuai.com/',
    },
    {
      pattern: /^\/finance\/pc\/api\/poiSettleBill\/expectedIncome/,
      responder: 'http://yapi.sankuai.com/mock/2487',
    },
    {
      pattern: /^\/finance\/invoice/,
      responder: 'http://invoice.waimai.test.sankuai.com/',
    },
  ],
  cargo: [
    {
      pattern: /^\/download/,
      responder: 'http://yapi.sankuai.com/mock/2487',
    },
    {
      pattern: /^\/finance/,
      responder: 'http://e.platform.proxy.b.waimai.test.sankuai.com',
    },
  ],
  mock: [
    {
      pattern: /^\/download/,
      responder: 'http://yapi.sankuai.com/mock/2487',
    },
    {
      pattern: /^\/finance\/pc\/api/,
      responder: 'http://yapi.sankuai.com/mock/2487',
    },
    {
      pattern: /^\/finance\/v3\/h5\/api/,
      responder: 'http://yapi.sankuai.com/mock/2487',
    },
    {
      pattern: /^\/finance\/invoice/,
      responder: 'http://yapi.sankuai.com/mock/2487',
    },
    {
      pattern: /^\/gw/,
      responder: 'http://yapi.sankuai.com/mock/2487',
    },
    // {
    //   pattern: /^\/finance\/v4\/h5\/api/,
    //   responder: 'http://yapi.sankuai.com/mock/2994',

    // },
    // {
    //   pattern: /^\/finance/,
    //   responder: 'http://yapi.sankuai.com/mock/2994',
    // },
  ],
  dev: [
    {
      pattern: /^\/download/,
      responder: 'http://yapi.sankuai.com/mock/2487',
    },
    {
      pattern: /^\/finance/,
      responder: 'http://waimai-vaamh-sl-moneyepai.m.waimai.test.sankuai.com/',
    },
  ],
  prod: [
    {
      pattern: /^\/test/,
      responder: '/mock',
    },
  ],
  globalRules: [
    {
      pattern: /^\/test/,
      responder: '/mock',
    },
  ],
};
