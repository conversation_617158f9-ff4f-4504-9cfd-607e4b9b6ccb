{"name": "wa<PERSON><PERSON>-mfe-alchemist-pc", "version": "0.0.2", "lockfileVersion": 1, "requires": true, "dependencies": {"@babel/code-frame": {"version": "7.0.0", "resolved": "http://r.npm.sankuai.com/@babel/code-frame/download/@babel/code-frame-7.0.0.tgz", "integrity": "sha1-BuKrGb21NThVWaq7W6WXKUgoAPg=", "dev": true, "requires": {"@babel/highlight": "^7.0.0"}}, "@babel/core": {"version": "7.0.1", "resolved": "https://registry.npmjs.org/@babel/core/-/core-7.0.1.tgz", "integrity": "sha512-7Yy2vRB6KYbhWeIrrwJmKv9UwDxokmlo43wi6AV84oNs4Gi71NTNGh3YxY/hK3+CxuSc6wcKSl25F2tQOhm1GQ==", "dev": true, "requires": {"@babel/code-frame": "^7.0.0", "@babel/generator": "^7.0.0", "@babel/helpers": "^7.0.0", "@babel/parser": "^7.0.0", "@babel/template": "^7.0.0", "@babel/traverse": "^7.0.0", "@babel/types": "^7.0.0", "convert-source-map": "^1.1.0", "debug": "^3.1.0", "json5": "^0.5.0", "lodash": "^4.17.10", "resolve": "^1.3.2", "semver": "^5.4.1", "source-map": "^0.5.0"}}, "@babel/generator": {"version": "7.0.0", "resolved": "http://r.npm.sankuai.com/@babel/generator/download/@babel/generator-7.0.0.tgz", "integrity": "sha1-Hv1Yv/qVHchGRJ5YzjodfwLTk6o=", "dev": true, "requires": {"@babel/types": "^7.0.0", "jsesc": "^2.5.1", "lodash": "^4.17.10", "source-map": "^0.5.0", "trim-right": "^1.0.1"}}, "@babel/helper-annotate-as-pure": {"version": "7.0.0", "resolved": "https://registry.npmjs.org/@babel/helper-annotate-as-pure/-/helper-annotate-as-pure-7.0.0.tgz", "integrity": "sha512-3UYcJUj9kvSLbLbUIfQTqzcy5VX7GRZ/CCDrnOaZorFFM01aXp1+GJwuFGV4NDDoAS+mOUyHcO6UD/RfqOks3Q==", "dev": true, "requires": {"@babel/types": "^7.0.0"}}, "@babel/helper-builder-binary-assignment-operator-visitor": {"version": "7.0.0", "resolved": "https://registry.npmjs.org/@babel/helper-builder-binary-assignment-operator-visitor/-/helper-builder-binary-assignment-operator-visitor-7.0.0.tgz", "integrity": "sha512-9HdU8lrAc4FUZOy+y2w//kUhynSpkGIRYDzJW1oKJx7+v8m6UEAbAd2tSvxirsq2kJTXJZZS6Eo8FnUDUH0ZWw==", "dev": true, "requires": {"@babel/helper-explode-assignable-expression": "^7.0.0", "@babel/types": "^7.0.0"}}, "@babel/helper-call-delegate": {"version": "7.0.0", "resolved": "https://registry.npmjs.org/@babel/helper-call-delegate/-/helper-call-delegate-7.0.0.tgz", "integrity": "sha512-HdYG6vr4KgXHK0q1QRZ8guoYCF5rZjIdPlhcVY+j4EBK/FDR+cXRM5/6lQr3NIWDc7dO1KfgjG5rfH6lM89VBw==", "dev": true, "requires": {"@babel/helper-hoist-variables": "^7.0.0", "@babel/traverse": "^7.0.0", "@babel/types": "^7.0.0"}}, "@babel/helper-define-map": {"version": "7.0.0", "resolved": "https://registry.npmjs.org/@babel/helper-define-map/-/helper-define-map-7.0.0.tgz", "integrity": "sha512-acbCxYS9XufWxsBiclmXMK1CFz7en/XSYvHFcbb3Jb8BqjFEBrA46WlIsoSQTRG/eYN60HciUnzdyQxOZhrHfw==", "dev": true, "requires": {"@babel/helper-function-name": "^7.0.0", "@babel/types": "^7.0.0", "lodash": "^4.17.10"}}, "@babel/helper-explode-assignable-expression": {"version": "7.0.0", "resolved": "https://registry.npmjs.org/@babel/helper-explode-assignable-expression/-/helper-explode-assignable-expression-7.0.0.tgz", "integrity": "sha512-5gLPwdDnYf8GfPsjS+UmZUtYE1jaXTFm1P+ymGobqvXbA0q3ANgpH60+C6zDrRAWXYbQXYvzzQC/r0gJVNNltQ==", "dev": true, "requires": {"@babel/traverse": "^7.0.0", "@babel/types": "^7.0.0"}}, "@babel/helper-function-name": {"version": "7.0.0", "resolved": "http://r.npm.sankuai.com/@babel/helper-function-name/download/@babel/helper-function-name-7.0.0.tgz", "integrity": "sha1-pozI0EQgzMZj3SWPnMQbgmHvotQ=", "dev": true, "requires": {"@babel/helper-get-function-arity": "^7.0.0", "@babel/template": "^7.0.0", "@babel/types": "^7.0.0"}}, "@babel/helper-get-function-arity": {"version": "7.0.0", "resolved": "http://r.npm.sankuai.com/@babel/helper-get-function-arity/download/@babel/helper-get-function-arity-7.0.0.tgz", "integrity": "sha1-g1ctQyDipGVyY3NBE8QoaLZOScM=", "dev": true, "requires": {"@babel/types": "^7.0.0"}}, "@babel/helper-hoist-variables": {"version": "7.0.0", "resolved": "https://registry.npmjs.org/@babel/helper-hoist-variables/-/helper-hoist-variables-7.0.0.tgz", "integrity": "sha512-Ggv5sldXUeSKsuzLkddtyhyHe2YantsxWKNi7A+7LeD12ExRDWTRk29JCXpaHPAbMaIPZSil7n+lq78WY2VY7w==", "dev": true, "requires": {"@babel/types": "^7.0.0"}}, "@babel/helper-member-expression-to-functions": {"version": "7.0.0", "resolved": "https://registry.npmjs.org/@babel/helper-member-expression-to-functions/-/helper-member-expression-to-functions-7.0.0.tgz", "integrity": "sha512-avo+lm/QmZlv27Zsi0xEor2fKcqWG56D5ae9dzklpIaY7cQMK5N8VSpaNVPPagiqmy7LrEjK1IWdGMOqPu5csg==", "dev": true, "requires": {"@babel/types": "^7.0.0"}}, "@babel/helper-module-imports": {"version": "7.0.0", "resolved": "https://registry.npmjs.org/@babel/helper-module-imports/-/helper-module-imports-7.0.0.tgz", "integrity": "sha512-aP/hlLq01DWNEiDg4Jn23i+CXxW/owM4WpDLFUbpjxe4NS3BhLVZQ5i7E0ZrxuQ/vwekIeciyamgB1UIYxxM6A==", "dev": true, "requires": {"@babel/types": "^7.0.0"}}, "@babel/helper-module-transforms": {"version": "7.0.0", "resolved": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.0.0.tgz", "integrity": "sha512-QdwmTTlPmT7TZcf30dnqm8pem+o48tVt991xXogE5CQCwqSpWKuzH2E9v8VWeccQ66a6/CmrLZ+bwp66JYeM5A==", "dev": true, "requires": {"@babel/helper-module-imports": "^7.0.0", "@babel/helper-simple-access": "^7.0.0", "@babel/helper-split-export-declaration": "^7.0.0", "@babel/template": "^7.0.0", "@babel/types": "^7.0.0", "lodash": "^4.17.10"}}, "@babel/helper-optimise-call-expression": {"version": "7.0.0", "resolved": "https://registry.npmjs.org/@babel/helper-optimise-call-expression/-/helper-optimise-call-expression-7.0.0.tgz", "integrity": "sha512-u8nd9NQePYNQV8iPWu/pLLYBqZBa4ZaY1YWRFMuxrid94wKI1QNt67NEZ7GAe5Kc/0LLScbim05xZFWkAdrj9g==", "dev": true, "requires": {"@babel/types": "^7.0.0"}}, "@babel/helper-plugin-utils": {"version": "7.0.0", "resolved": "https://registry.npmjs.org/@babel/helper-plugin-utils/-/helper-plugin-utils-7.0.0.tgz", "integrity": "sha512-CYAOUCARwExnEixLdB6sDm2dIJ/YgEAKDM1MOeMeZu9Ld/bDgVo8aiWrXwcY7OBh+1Ea2uUcVRcxKk0GJvW7QA==", "dev": true}, "@babel/helper-regex": {"version": "7.0.0", "resolved": "https://registry.npmjs.org/@babel/helper-regex/-/helper-regex-7.0.0.tgz", "integrity": "sha512-TR0/N0NDCcUIUEbqV6dCO+LptmmSQFQ7q70lfcEB4URsjD0E1HzicrwUH+ap6BAQ2jhCX9Q4UqZy4wilujWlkg==", "dev": true, "requires": {"lodash": "^4.17.10"}}, "@babel/helper-remap-async-to-generator": {"version": "7.0.0", "resolved": "https://registry.npmjs.org/@babel/helper-remap-async-to-generator/-/helper-remap-async-to-generator-7.0.0.tgz", "integrity": "sha512-3o4sYLOsK6m0A7t1P0saTanBPmk5MAlxVnp9773Of4L8PMVLukU7loZix5KoJgflxSo2c2ETTzseptc0rQEp7A==", "dev": true, "requires": {"@babel/helper-annotate-as-pure": "^7.0.0", "@babel/helper-wrap-function": "^7.0.0", "@babel/template": "^7.0.0", "@babel/traverse": "^7.0.0", "@babel/types": "^7.0.0"}}, "@babel/helper-replace-supers": {"version": "7.0.0", "resolved": "https://registry.npmjs.org/@babel/helper-replace-supers/-/helper-replace-supers-7.0.0.tgz", "integrity": "sha512-fsSv7VogxzMSmGch6DwhKHGsciVXo7hbfhBgH9ZrgJMXKMjO7ASQTUfbVL7MU1uCfviyqjucazGK7TWPT9weuQ==", "dev": true, "requires": {"@babel/helper-member-expression-to-functions": "^7.0.0", "@babel/helper-optimise-call-expression": "^7.0.0", "@babel/traverse": "^7.0.0", "@babel/types": "^7.0.0"}}, "@babel/helper-simple-access": {"version": "7.0.0", "resolved": "https://registry.npmjs.org/@babel/helper-simple-access/-/helper-simple-access-7.0.0.tgz", "integrity": "sha512-CNeuX52jbQSq4j1n+R+21xrjbTjsnXa9n1aERbgHRD/p9h4Udkxr1n24yPMQmnTETHdnQDvkVSYWFw/ETAymYg==", "dev": true, "requires": {"@babel/template": "^7.0.0", "@babel/types": "^7.0.0"}}, "@babel/helper-split-export-declaration": {"version": "7.0.0", "resolved": "http://r.npm.sankuai.com/@babel/helper-split-export-declaration/download/@babel/helper-split-export-declaration-7.0.0.tgz", "integrity": "sha1-Oq4oXAMRwqsJXZl7jJqUytVH2BM=", "dev": true, "requires": {"@babel/types": "^7.0.0"}}, "@babel/helper-wrap-function": {"version": "7.0.0", "resolved": "https://registry.npmjs.org/@babel/helper-wrap-function/-/helper-wrap-function-7.0.0.tgz", "integrity": "sha512-kjprWPDNVPZ/9pyLRXcZBvfjnFwqokmXTPTaC4AV8Ns7WRl7ewSxrB19AWZzQsC/WSPQLOw1ciR8uPYkAM1znA==", "dev": true, "requires": {"@babel/helper-function-name": "^7.0.0", "@babel/template": "^7.0.0", "@babel/traverse": "^7.0.0", "@babel/types": "^7.0.0"}}, "@babel/helpers": {"version": "7.0.0", "resolved": "https://registry.npmjs.org/@babel/helpers/-/helpers-7.0.0.tgz", "integrity": "sha512-jbvgR8iLZPnyk6m/UqdXYsSxbVtRi7Pd3CzB4OPwPBnmhNG1DWjiiy777NTuoyIcniszK51R40L5pgfXAfHDtw==", "dev": true, "requires": {"@babel/template": "^7.0.0", "@babel/traverse": "^7.0.0", "@babel/types": "^7.0.0"}}, "@babel/highlight": {"version": "7.0.0", "resolved": "http://r.npm.sankuai.com/@babel/highlight/download/@babel/highlight-7.0.0.tgz", "integrity": "sha1-9xDDjI1Fjm3ZogGvtjf8t4HOmeQ=", "dev": true, "requires": {"chalk": "^2.0.0", "esutils": "^2.0.2", "js-tokens": "^4.0.0"}}, "@babel/parser": {"version": "7.0.0", "resolved": "http://r.npm.sankuai.com/@babel/parser/download/@babel/parser-7.0.0.tgz", "integrity": "sha1-aXZVGDOU+s/7BjQ33fUsAndph3U=", "dev": true}, "@babel/plugin-proposal-async-generator-functions": {"version": "7.0.0", "resolved": "https://registry.npmjs.org/@babel/plugin-proposal-async-generator-functions/-/plugin-proposal-async-generator-functions-7.0.0.tgz", "integrity": "sha512-QsXmmjLrFADCcDQAfdQn7tfBRLjpTzRWaDpKpW4ZXW1fahPG4SvjcF1xfvVnXGC662RSExYXL+6DAqbtgqMXeA==", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/helper-remap-async-to-generator": "^7.0.0", "@babel/plugin-syntax-async-generators": "^7.0.0"}}, "@babel/plugin-proposal-json-strings": {"version": "7.0.0", "resolved": "https://registry.npmjs.org/@babel/plugin-proposal-json-strings/-/plugin-proposal-json-strings-7.0.0.tgz", "integrity": "sha512-kfVdUkIAGJIVmHmtS/40i/fg/AGnw/rsZBCaapY5yjeO5RA9m165Xbw9KMOu2nqXP5dTFjEjHdfNdoVcHv133Q==", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/plugin-syntax-json-strings": "^7.0.0"}}, "@babel/plugin-proposal-object-rest-spread": {"version": "7.0.0", "resolved": "https://registry.npmjs.org/@babel/plugin-proposal-object-rest-spread/-/plugin-proposal-object-rest-spread-7.0.0.tgz", "integrity": "sha512-14fhfoPcNu7itSen7Py1iGN0gEm87hX/B+8nZPqkdmANyyYWYMY2pjA3r8WXbWVKMzfnSNS0xY8GVS0IjXi/iw==", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/plugin-syntax-object-rest-spread": "^7.0.0"}}, "@babel/plugin-proposal-optional-catch-binding": {"version": "7.0.0", "resolved": "https://registry.npmjs.org/@babel/plugin-proposal-optional-catch-binding/-/plugin-proposal-optional-catch-binding-7.0.0.tgz", "integrity": "sha512-JPqAvLG1s13B/AuoBjdBYvn38RqW6n1TzrQO839/sIpqLpbnXKacsAgpZHzLD83Sm8SDXMkkrAvEnJ25+0yIpw==", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/plugin-syntax-optional-catch-binding": "^7.0.0"}}, "@babel/plugin-proposal-unicode-property-regex": {"version": "7.0.0", "resolved": "https://registry.npmjs.org/@babel/plugin-proposal-unicode-property-regex/-/plugin-proposal-unicode-property-regex-7.0.0.tgz", "integrity": "sha512-tM3icA6GhC3ch2SkmSxv7J/hCWKISzwycub6eGsDrFDgukD4dZ/I+x81XgW0YslS6mzNuQ1Cbzh5osjIMgepPQ==", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/helper-regex": "^7.0.0", "regexpu-core": "^4.2.0"}}, "@babel/plugin-syntax-async-generators": {"version": "7.0.0", "resolved": "https://registry.npmjs.org/@babel/plugin-syntax-async-generators/-/plugin-syntax-async-generators-7.0.0.tgz", "integrity": "sha512-im7ged00ddGKAjcZgewXmp1vxSZQQywuQXe2B1A7kajjZmDeY/ekMPmWr9zJgveSaQH0k7BcGrojQhcK06l0zA==", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.0.0"}}, "@babel/plugin-syntax-json-strings": {"version": "7.0.0", "resolved": "https://registry.npmjs.org/@babel/plugin-syntax-json-strings/-/plugin-syntax-json-strings-7.0.0.tgz", "integrity": "sha512-UlSfNydC+XLj4bw7ijpldc1uZ/HB84vw+U6BTuqMdIEmz/LDe63w/GHtpQMdXWdqQZFeAI9PjnHe/vDhwirhKA==", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.0.0"}}, "@babel/plugin-syntax-jsx": {"version": "7.0.0", "resolved": "https://registry.npmjs.org/@babel/plugin-syntax-jsx/-/plugin-syntax-jsx-7.0.0.tgz", "integrity": "sha512-PdmL2AoPsCLWxhIr3kG2+F9v4WH06Q3z+NoGVpQgnUNGcagXHq5sB3OXxkSahKq9TLdNMN/AJzFYSOo8UKDMHg==", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.0.0"}}, "@babel/plugin-syntax-object-rest-spread": {"version": "7.0.0", "resolved": "https://registry.npmjs.org/@babel/plugin-syntax-object-rest-spread/-/plugin-syntax-object-rest-spread-7.0.0.tgz", "integrity": "sha512-5A0n4p6bIiVe5OvQPxBnesezsgFJdHhSs3uFSvaPdMqtsovajLZ+G2vZyvNe10EzJBWWo3AcHGKhAFUxqwp2dw==", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.0.0"}}, "@babel/plugin-syntax-optional-catch-binding": {"version": "7.0.0", "resolved": "https://registry.npmjs.org/@babel/plugin-syntax-optional-catch-binding/-/plugin-syntax-optional-catch-binding-7.0.0.tgz", "integrity": "sha512-Wc+HVvwjcq5qBg1w5RG9o9RVzmCaAg/Vp0erHCKpAYV8La6I94o4GQAmFYNmkzoMO6gzoOSulpKeSSz6mPEoZw==", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.0.0"}}, "@babel/plugin-transform-arrow-functions": {"version": "7.0.0", "resolved": "https://registry.npmjs.org/@babel/plugin-transform-arrow-functions/-/plugin-transform-arrow-functions-7.0.0.tgz", "integrity": "sha512-2EZDBl1WIO/q4DIkIp4s86sdp4ZifL51MoIviLY/gG/mLSuOIEg7J8o6mhbxOTvUJkaN50n+8u41FVsr5KLy/w==", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.0.0"}}, "@babel/plugin-transform-async-to-generator": {"version": "7.0.0", "resolved": "https://registry.npmjs.org/@babel/plugin-transform-async-to-generator/-/plugin-transform-async-to-generator-7.0.0.tgz", "integrity": "sha512-CiWNhSMZzj1n3uEKUUS/oL+a7Xi8hnPQB6GpC1WfL/ZYvxBLDBn14sHMo5EyOaeArccSonyk5jFIKMRRbrHOnQ==", "dev": true, "requires": {"@babel/helper-module-imports": "^7.0.0", "@babel/helper-plugin-utils": "^7.0.0", "@babel/helper-remap-async-to-generator": "^7.0.0"}}, "@babel/plugin-transform-block-scoped-functions": {"version": "7.0.0", "resolved": "https://registry.npmjs.org/@babel/plugin-transform-block-scoped-functions/-/plugin-transform-block-scoped-functions-7.0.0.tgz", "integrity": "sha512-AOBiyUp7vYTqz2Jibe1UaAWL0Hl9JUXEgjFvvvcSc9MVDItv46ViXFw2F7SVt1B5k+KWjl44eeXOAk3UDEaJjQ==", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.0.0"}}, "@babel/plugin-transform-block-scoping": {"version": "7.0.0", "resolved": "https://registry.npmjs.org/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-7.0.0.tgz", "integrity": "sha512-GWEMCrmHQcYWISilUrk9GDqH4enf3UmhOEbNbNrlNAX1ssH3MsS1xLOS6rdjRVPgA7XXVPn87tRkdTEoA/dxEg==", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.0.0", "lodash": "^4.17.10"}}, "@babel/plugin-transform-classes": {"version": "7.0.0", "resolved": "https://registry.npmjs.org/@babel/plugin-transform-classes/-/plugin-transform-classes-7.0.0.tgz", "integrity": "sha512-8LBm7XsHQiNISEmb+ejBiHi1pUihwUf+lrIwyVsXVbQ1vLqgkvhgayK5JnW3WXvQD2rmM0qxFAIyDE5vtMem2A==", "dev": true, "requires": {"@babel/helper-annotate-as-pure": "^7.0.0", "@babel/helper-define-map": "^7.0.0", "@babel/helper-function-name": "^7.0.0", "@babel/helper-optimise-call-expression": "^7.0.0", "@babel/helper-plugin-utils": "^7.0.0", "@babel/helper-replace-supers": "^7.0.0", "@babel/helper-split-export-declaration": "^7.0.0", "globals": "^11.1.0"}}, "@babel/plugin-transform-computed-properties": {"version": "7.0.0", "resolved": "https://registry.npmjs.org/@babel/plugin-transform-computed-properties/-/plugin-transform-computed-properties-7.0.0.tgz", "integrity": "sha512-ubouZdChNAv4AAWAgU7QKbB93NU5sHwInEWfp+/OzJKA02E6Woh9RVoX4sZrbRwtybky/d7baTUqwFx+HgbvMA==", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.0.0"}}, "@babel/plugin-transform-destructuring": {"version": "7.0.0", "resolved": "https://registry.npmjs.org/@babel/plugin-transform-destructuring/-/plugin-transform-destructuring-7.0.0.tgz", "integrity": "sha512-Fr2GtF8YJSXGTyFPakPFB4ODaEKGU04bPsAllAIabwoXdFrPxL0LVXQX5dQWoxOjjgozarJcC9eWGsj0fD6Zsg==", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.0.0"}}, "@babel/plugin-transform-dotall-regex": {"version": "7.0.0", "resolved": "https://registry.npmjs.org/@babel/plugin-transform-dotall-regex/-/plugin-transform-dotall-regex-7.0.0.tgz", "integrity": "sha512-00THs8eJxOJUFVx1w8i1MBF4XH4PsAjKjQ1eqN/uCH3YKwP21GCKfrn6YZFZswbOk9+0cw1zGQPHVc1KBlSxig==", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/helper-regex": "^7.0.0", "regexpu-core": "^4.1.3"}}, "@babel/plugin-transform-duplicate-keys": {"version": "7.0.0", "resolved": "https://registry.npmjs.org/@babel/plugin-transform-duplicate-keys/-/plugin-transform-duplicate-keys-7.0.0.tgz", "integrity": "sha512-w2vfPkMqRkdxx+C71ATLJG30PpwtTpW7DDdLqYt2acXU7YjztzeWW2Jk1T6hKqCLYCcEA5UQM/+xTAm+QCSnuQ==", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.0.0"}}, "@babel/plugin-transform-exponentiation-operator": {"version": "7.0.0", "resolved": "https://registry.npmjs.org/@babel/plugin-transform-exponentiation-operator/-/plugin-transform-exponentiation-operator-7.0.0.tgz", "integrity": "sha512-Ig74elCuFQ0mvHkWUq5qDCNI3qHWlop5w4TcDxdtJiOk8Egqe2uxDRY9XnXGSlmWClClmnixcoYumyvbAuj4dA==", "dev": true, "requires": {"@babel/helper-builder-binary-assignment-operator-visitor": "^7.0.0", "@babel/helper-plugin-utils": "^7.0.0"}}, "@babel/plugin-transform-for-of": {"version": "7.0.0", "resolved": "https://registry.npmjs.org/@babel/plugin-transform-for-of/-/plugin-transform-for-of-7.0.0.tgz", "integrity": "sha512-TlxKecN20X2tt2UEr2LNE6aqA0oPeMT1Y3cgz8k4Dn1j5ObT8M3nl9aA37LLklx0PBZKETC9ZAf9n/6SujTuXA==", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.0.0"}}, "@babel/plugin-transform-function-name": {"version": "7.0.0", "resolved": "https://registry.npmjs.org/@babel/plugin-transform-function-name/-/plugin-transform-function-name-7.0.0.tgz", "integrity": "sha512-mR7JN9vkwsAIot74pSwzn/2Gq4nn2wN0HKtQyJLc1ghAarsymdBMTfh+Q/aeR2N3heXs3URQscTLrKe3yUU7Yw==", "dev": true, "requires": {"@babel/helper-function-name": "^7.0.0", "@babel/helper-plugin-utils": "^7.0.0"}}, "@babel/plugin-transform-literals": {"version": "7.0.0", "resolved": "https://registry.npmjs.org/@babel/plugin-transform-literals/-/plugin-transform-literals-7.0.0.tgz", "integrity": "sha512-1NTDBWkeNXgpUcyoVFxbr9hS57EpZYXpje92zv0SUzjdu3enaRwF/l3cmyRnXLtIdyJASyiS6PtybK+CgKf7jA==", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.0.0"}}, "@babel/plugin-transform-modules-amd": {"version": "7.0.0", "resolved": "https://registry.npmjs.org/@babel/plugin-transform-modules-amd/-/plugin-transform-modules-amd-7.0.0.tgz", "integrity": "sha512-CtSVpT/0tty/4405qczoIHm41YfFbPChplsmfBwsi3RTq/M9cHgVb3ixI5bqqgdKkqWwSX2sXqejvMKLuTVU+Q==", "dev": true, "requires": {"@babel/helper-module-transforms": "^7.0.0", "@babel/helper-plugin-utils": "^7.0.0"}}, "@babel/plugin-transform-modules-commonjs": {"version": "7.0.0", "resolved": "https://registry.npmjs.org/@babel/plugin-transform-modules-commonjs/-/plugin-transform-modules-commonjs-7.0.0.tgz", "integrity": "sha512-BIcQLgPFCxi7YygtNpz5xj+7HxhOprbCGZKeLW6Kxsn1eHS6sJZMw4MfmqFZagl/v6IVa0AJoMHdDXLVrpd3Aw==", "dev": true, "requires": {"@babel/helper-module-transforms": "^7.0.0", "@babel/helper-plugin-utils": "^7.0.0", "@babel/helper-simple-access": "^7.0.0"}}, "@babel/plugin-transform-modules-systemjs": {"version": "7.0.0", "resolved": "https://registry.npmjs.org/@babel/plugin-transform-modules-systemjs/-/plugin-transform-modules-systemjs-7.0.0.tgz", "integrity": "sha512-8EDKMAsitLkiF/D4Zhe9CHEE2XLh4bfLbb9/Zf3FgXYQOZyZYyg7EAel/aT2A7bHv62jwHf09q2KU/oEexr83g==", "dev": true, "requires": {"@babel/helper-hoist-variables": "^7.0.0", "@babel/helper-plugin-utils": "^7.0.0"}}, "@babel/plugin-transform-modules-umd": {"version": "7.0.0", "resolved": "https://registry.npmjs.org/@babel/plugin-transform-modules-umd/-/plugin-transform-modules-umd-7.0.0.tgz", "integrity": "sha512-EMyKpzgugxef+R1diXDwqw/Hmt5ls8VxfI8Gq5Lo8Qp3oKIepkYG4L/mvE2dmZSRalgL9sguoPKbnQ1m96hVFw==", "dev": true, "requires": {"@babel/helper-module-transforms": "^7.0.0", "@babel/helper-plugin-utils": "^7.0.0"}}, "@babel/plugin-transform-new-target": {"version": "7.0.0", "resolved": "https://registry.npmjs.org/@babel/plugin-transform-new-target/-/plugin-transform-new-target-7.0.0.tgz", "integrity": "sha512-yin069FYjah+LbqfGeTfzIBODex/e++Yfa0rH0fpfam9uTbuEeEOx5GLGr210ggOV77mVRNoeqSYqeuaqSzVSw==", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.0.0"}}, "@babel/plugin-transform-object-super": {"version": "7.0.0", "resolved": "https://registry.npmjs.org/@babel/plugin-transform-object-super/-/plugin-transform-object-super-7.0.0.tgz", "integrity": "sha512-BfAiF1l18Xr1shy1NyyQgLiHDvh/S7APiEM5+0wxTsQ+e3fgXO+NA47u4PvppzH0meJS21y0gZHcjnvUAJj8tQ==", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/helper-replace-supers": "^7.0.0"}}, "@babel/plugin-transform-parameters": {"version": "7.0.0", "resolved": "https://registry.npmjs.org/@babel/plugin-transform-parameters/-/plugin-transform-parameters-7.0.0.tgz", "integrity": "sha512-eWngvRBWx0gScot0xa340JzrkA+8HGAk1OaCHDfXAjkrTFkp73Lcf+78s7AStSdRML5nzx5aXpnjN1MfrjkBoA==", "dev": true, "requires": {"@babel/helper-call-delegate": "^7.0.0", "@babel/helper-get-function-arity": "^7.0.0", "@babel/helper-plugin-utils": "^7.0.0"}}, "@babel/plugin-transform-regenerator": {"version": "7.0.0", "resolved": "https://registry.npmjs.org/@babel/plugin-transform-regenerator/-/plugin-transform-regenerator-7.0.0.tgz", "integrity": "sha512-sj2qzsEx8KDVv1QuJc/dEfilkg3RRPvPYx/VnKLtItVQRWt1Wqf5eVCOLZm29CiGFfYYsA3VPjfizTCV0S0Dlw==", "dev": true, "requires": {"regenerator-transform": "^0.13.3"}}, "@babel/plugin-transform-shorthand-properties": {"version": "7.0.0", "resolved": "https://registry.npmjs.org/@babel/plugin-transform-shorthand-properties/-/plugin-transform-shorthand-properties-7.0.0.tgz", "integrity": "sha512-g/99LI4vm5iOf5r1Gdxq5Xmu91zvjhEG5+yZDJW268AZELAu4J1EiFLnkSG3yuUsZyOipVOVUKoGPYwfsTymhw==", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.0.0"}}, "@babel/plugin-transform-spread": {"version": "7.0.0", "resolved": "https://registry.npmjs.org/@babel/plugin-transform-spread/-/plugin-transform-spread-7.0.0.tgz", "integrity": "sha512-L702YFy2EvirrR4shTj0g2xQp7aNwZoWNCkNu2mcoU0uyzMl0XRwDSwzB/xp6DSUFiBmEXuyAyEN16LsgVqGGQ==", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.0.0"}}, "@babel/plugin-transform-sticky-regex": {"version": "7.0.0", "resolved": "https://registry.npmjs.org/@babel/plugin-transform-sticky-regex/-/plugin-transform-sticky-regex-7.0.0.tgz", "integrity": "sha512-LFUToxiyS/WD+XEWpkx/XJBrUXKewSZpzX68s+yEOtIbdnsRjpryDw9U06gYc6klYEij/+KQVRnD3nz3AoKmjw==", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/helper-regex": "^7.0.0"}}, "@babel/plugin-transform-template-literals": {"version": "7.0.0", "resolved": "https://registry.npmjs.org/@babel/plugin-transform-template-literals/-/plugin-transform-template-literals-7.0.0.tgz", "integrity": "sha512-vA6rkTCabRZu7Nbl9DfLZE1imj4tzdWcg5vtdQGvj+OH9itNNB6hxuRMHuIY8SGnEt1T9g5foqs9LnrHzsqEFg==", "dev": true, "requires": {"@babel/helper-annotate-as-pure": "^7.0.0", "@babel/helper-plugin-utils": "^7.0.0"}}, "@babel/plugin-transform-typeof-symbol": {"version": "7.0.0", "resolved": "https://registry.npmjs.org/@babel/plugin-transform-typeof-symbol/-/plugin-transform-typeof-symbol-7.0.0.tgz", "integrity": "sha512-1r1X5DO78WnaAIvs5uC48t41LLckxsYklJrZjNKcevyz83sF2l4RHbw29qrCPr/6ksFsdfRpT/ZgxNWHXRnffg==", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.0.0"}}, "@babel/plugin-transform-unicode-regex": {"version": "7.0.0", "resolved": "https://registry.npmjs.org/@babel/plugin-transform-unicode-regex/-/plugin-transform-unicode-regex-7.0.0.tgz", "integrity": "sha512-uJBrJhBOEa3D033P95nPHu3nbFwFE9ZgXsfEitzoIXIwqAZWk7uXcg06yFKXz9FSxBH5ucgU/cYdX0IV8ldHKw==", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/helper-regex": "^7.0.0", "regexpu-core": "^4.1.3"}}, "@babel/polyfill": {"version": "7.0.0", "resolved": "https://registry.npmjs.org/@babel/polyfill/-/polyfill-7.0.0.tgz", "integrity": "sha512-dnrMRkyyr74CRelJwvgnnSUDh2ge2NCTyHVwpOdvRMHtJUyxLtMAfhBN3s64pY41zdw0kgiLPh6S20eb1NcX6Q==", "requires": {"core-js": "^2.5.7", "regenerator-runtime": "^0.11.1"}}, "@babel/preset-env": {"version": "7.0.0", "resolved": "https://registry.npmjs.org/@babel/preset-env/-/preset-env-7.0.0.tgz", "integrity": "sha512-Fnx1wWaWv2w2rl+VHxA9si//Da40941IQ29fKiRejVR7oN1FxSEL8+SyAX/2oKIye2gPvY/GBbJVEKQ/oi43zQ==", "dev": true, "requires": {"@babel/helper-module-imports": "^7.0.0", "@babel/helper-plugin-utils": "^7.0.0", "@babel/plugin-proposal-async-generator-functions": "^7.0.0", "@babel/plugin-proposal-json-strings": "^7.0.0", "@babel/plugin-proposal-object-rest-spread": "^7.0.0", "@babel/plugin-proposal-optional-catch-binding": "^7.0.0", "@babel/plugin-proposal-unicode-property-regex": "^7.0.0", "@babel/plugin-syntax-async-generators": "^7.0.0", "@babel/plugin-syntax-object-rest-spread": "^7.0.0", "@babel/plugin-syntax-optional-catch-binding": "^7.0.0", "@babel/plugin-transform-arrow-functions": "^7.0.0", "@babel/plugin-transform-async-to-generator": "^7.0.0", "@babel/plugin-transform-block-scoped-functions": "^7.0.0", "@babel/plugin-transform-block-scoping": "^7.0.0", "@babel/plugin-transform-classes": "^7.0.0", "@babel/plugin-transform-computed-properties": "^7.0.0", "@babel/plugin-transform-destructuring": "^7.0.0", "@babel/plugin-transform-dotall-regex": "^7.0.0", "@babel/plugin-transform-duplicate-keys": "^7.0.0", "@babel/plugin-transform-exponentiation-operator": "^7.0.0", "@babel/plugin-transform-for-of": "^7.0.0", "@babel/plugin-transform-function-name": "^7.0.0", "@babel/plugin-transform-literals": "^7.0.0", "@babel/plugin-transform-modules-amd": "^7.0.0", "@babel/plugin-transform-modules-commonjs": "^7.0.0", "@babel/plugin-transform-modules-systemjs": "^7.0.0", "@babel/plugin-transform-modules-umd": "^7.0.0", "@babel/plugin-transform-new-target": "^7.0.0", "@babel/plugin-transform-object-super": "^7.0.0", "@babel/plugin-transform-parameters": "^7.0.0", "@babel/plugin-transform-regenerator": "^7.0.0", "@babel/plugin-transform-shorthand-properties": "^7.0.0", "@babel/plugin-transform-spread": "^7.0.0", "@babel/plugin-transform-sticky-regex": "^7.0.0", "@babel/plugin-transform-template-literals": "^7.0.0", "@babel/plugin-transform-typeof-symbol": "^7.0.0", "@babel/plugin-transform-unicode-regex": "^7.0.0", "browserslist": "^4.1.0", "invariant": "^2.2.2", "js-levenshtein": "^1.1.3", "semver": "^5.3.0"}}, "@babel/template": {"version": "7.0.0", "resolved": "http://r.npm.sankuai.com/@babel/template/download/@babel/template-7.0.0.tgz", "integrity": "sha1-wryYcEBZWcianIFDdqLsskeDjIA=", "dev": true, "requires": {"@babel/code-frame": "^7.0.0", "@babel/parser": "^7.0.0", "@babel/types": "^7.0.0"}}, "@babel/traverse": {"version": "7.0.0", "resolved": "http://r.npm.sankuai.com/@babel/traverse/download/@babel/traverse-7.0.0.tgz", "integrity": "sha1-sf6bZWf986tULPrW87MfhU15mmE=", "dev": true, "requires": {"@babel/code-frame": "^7.0.0", "@babel/generator": "^7.0.0", "@babel/helper-function-name": "^7.0.0", "@babel/helper-split-export-declaration": "^7.0.0", "@babel/parser": "^7.0.0", "@babel/types": "^7.0.0", "debug": "^3.1.0", "globals": "^11.1.0", "lodash": "^4.17.10"}}, "@babel/types": {"version": "7.0.0", "resolved": "http://r.npm.sankuai.com/@babel/types/download/@babel/types-7.0.0.tgz", "integrity": "sha1-bhkXk9PIVNGcZ0mYnjvFXw6WIRg=", "dev": true, "requires": {"esutils": "^2.0.2", "lodash": "^4.17.10", "to-fast-properties": "^2.0.0"}}, "@bfe/raptor-sourcemap-upload-plugin": {"version": "1.1.8", "resolved": "http://r.npm.sankuai.com/@bfe/raptor-sourcemap-upload-plugin/download/@bfe/raptor-sourcemap-upload-plugin-1.1.8.tgz", "integrity": "sha1-gQ8oRxwsixBRB0D7cig1ofX8GYM=", "requires": {"fs": "0.0.1-security", "request": "^2.88.2"}}, "@commitlint/cli": {"version": "8.2.0", "resolved": "http://r.npm.sankuai.com/@commitlint/cli/download/@commitlint/cli-8.2.0.tgz", "integrity": "sha1-+/mWngTiFi2YXqpkT9rWzoB6rbY=", "dev": true, "requires": {"@commitlint/format": "^8.2.0", "@commitlint/lint": "^8.2.0", "@commitlint/load": "^8.2.0", "@commitlint/read": "^8.2.0", "babel-polyfill": "6.26.0", "chalk": "2.4.2", "get-stdin": "7.0.0", "lodash": "4.17.14", "meow": "5.0.0", "resolve-from": "5.0.0", "resolve-global": "1.0.0"}, "dependencies": {"camelcase-keys": {"version": "4.2.0", "resolved": "http://r.npm.sankuai.com/camelcase-keys/download/camelcase-keys-4.2.0.tgz", "integrity": "sha1-oqpfsa9oh1glnDLBQUJteJI7m3c=", "dev": true, "requires": {"camelcase": "^4.1.0", "map-obj": "^2.0.0", "quick-lru": "^1.0.0"}}, "chalk": {"version": "2.4.2", "resolved": "http://r.npm.sankuai.com/chalk/download/chalk-2.4.2.tgz", "integrity": "sha1-zUJUFnelQzPPVBpJEIwUMrRMlCQ=", "dev": true, "requires": {"ansi-styles": "^3.2.1", "escape-string-regexp": "^1.0.5", "supports-color": "^5.3.0"}}, "find-up": {"version": "2.1.0", "resolved": "http://r.npm.sankuai.com/find-up/download/find-up-2.1.0.tgz", "integrity": "sha1-RdG35QbHF93UgndaK3eSCjwMV6c=", "dev": true, "requires": {"locate-path": "^2.0.0"}}, "get-stdin": {"version": "7.0.0", "resolved": "http://r.npm.sankuai.com/get-stdin/download/get-stdin-7.0.0.tgz", "integrity": "sha1-jV3pjxUXGhJcXlFmQ8em0OqKlvY=", "dev": true}, "indent-string": {"version": "3.2.0", "resolved": "http://r.npm.sankuai.com/indent-string/download/indent-string-3.2.0.tgz", "integrity": "sha1-Sl/W0nzDMvN+VBmlBNu4NxBckok=", "dev": true}, "load-json-file": {"version": "4.0.0", "resolved": "http://r.npm.sankuai.com/load-json-file/download/load-json-file-4.0.0.tgz", "integrity": "sha1-L19Fq5HjMhYjT9U62rZo607AmTs=", "dev": true, "requires": {"graceful-fs": "^4.1.2", "parse-json": "^4.0.0", "pify": "^3.0.0", "strip-bom": "^3.0.0"}}, "lodash": {"version": "4.17.14", "resolved": "http://r.npm.sankuai.com/lodash/download/lodash-4.17.14.tgz", "integrity": "sha1-nOSHrmbJYlT+ILWZ8htoFgKAeLo=", "dev": true}, "map-obj": {"version": "2.0.0", "resolved": "http://r.npm.sankuai.com/map-obj/download/map-obj-2.0.0.tgz", "integrity": "sha1-plzSkIepJZi4eRJXpSPgISIqwfk=", "dev": true}, "meow": {"version": "5.0.0", "resolved": "http://r.npm.sankuai.com/meow/download/meow-5.0.0.tgz", "integrity": "sha1-38c9Y6mvxxSl43F2DrXIi5EHiqQ=", "dev": true, "requires": {"camelcase-keys": "^4.0.0", "decamelize-keys": "^1.0.0", "loud-rejection": "^1.0.0", "minimist-options": "^3.0.1", "normalize-package-data": "^2.3.4", "read-pkg-up": "^3.0.0", "redent": "^2.0.0", "trim-newlines": "^2.0.0", "yargs-parser": "^10.0.0"}}, "parse-json": {"version": "4.0.0", "resolved": "http://r.npm.sankuai.com/parse-json/download/parse-json-4.0.0.tgz", "integrity": "sha1-vjX1Qlvh9/bHRxhPmKeIy5lHfuA=", "dev": true, "requires": {"error-ex": "^1.3.1", "json-parse-better-errors": "^1.0.1"}}, "path-type": {"version": "3.0.0", "resolved": "http://r.npm.sankuai.com/path-type/download/path-type-3.0.0.tgz", "integrity": "sha1-zvMdyOCho7sNEFwM2Xzzv0f0428=", "dev": true, "requires": {"pify": "^3.0.0"}}, "pify": {"version": "3.0.0", "resolved": "http://r.npm.sankuai.com/pify/download/pify-3.0.0.tgz", "integrity": "sha1-5aSs0sEB/fPZpNB/DbxNtJ3SgXY=", "dev": true}, "read-pkg": {"version": "3.0.0", "resolved": "http://r.npm.sankuai.com/read-pkg/download/read-pkg-3.0.0.tgz", "integrity": "sha1-nLxoaXj+5l0WwA4rGcI3/Pbjg4k=", "dev": true, "requires": {"load-json-file": "^4.0.0", "normalize-package-data": "^2.3.2", "path-type": "^3.0.0"}}, "read-pkg-up": {"version": "3.0.0", "resolved": "http://r.npm.sankuai.com/read-pkg-up/download/read-pkg-up-3.0.0.tgz", "integrity": "sha1-PtSWaF26D4/hGNBpHcUfSh/5bwc=", "dev": true, "requires": {"find-up": "^2.0.0", "read-pkg": "^3.0.0"}}, "redent": {"version": "2.0.0", "resolved": "http://r.npm.sankuai.com/redent/download/redent-2.0.0.tgz", "integrity": "sha1-wbIAe0LVfrE4kHmzyDM2OdXhzKo=", "dev": true, "requires": {"indent-string": "^3.0.0", "strip-indent": "^2.0.0"}}, "resolve-from": {"version": "5.0.0", "resolved": "http://r.npm.sankuai.com/resolve-from/download/resolve-from-5.0.0.tgz", "integrity": "sha1-w1IlhD3493bfIcV1V7wIfp39/Gk=", "dev": true}, "strip-indent": {"version": "2.0.0", "resolved": "http://r.npm.sankuai.com/strip-indent/download/strip-indent-2.0.0.tgz", "integrity": "sha1-XvjbKV0B5u1sv3qrlpmNeCJSe2g=", "dev": true}, "trim-newlines": {"version": "2.0.0", "resolved": "http://r.npm.sankuai.com/trim-newlines/download/trim-newlines-2.0.0.tgz", "integrity": "sha1-tAPQuRvlDDMd/EuC7s6yLD3hbSA=", "dev": true}}}, "@commitlint/ensure": {"version": "8.2.0", "resolved": "http://r.npm.sankuai.com/@commitlint/ensure/download/@commitlint/ensure-8.2.0.tgz", "integrity": "sha1-+tDIHD070Jql+8vMSDrh85vIr48=", "dev": true, "requires": {"lodash": "4.17.14"}, "dependencies": {"lodash": {"version": "4.17.14", "resolved": "http://r.npm.sankuai.com/lodash/download/lodash-4.17.14.tgz", "integrity": "sha1-nOSHrmbJYlT+ILWZ8htoFgKAeLo=", "dev": true}}}, "@commitlint/execute-rule": {"version": "8.2.0", "resolved": "http://r.npm.sankuai.com/@commitlint/execute-rule/download/@commitlint/execute-rule-8.2.0.tgz", "integrity": "sha1-rvs3ROImE2YK3vt+vMyqYL0k540=", "dev": true}, "@commitlint/format": {"version": "8.2.0", "resolved": "http://r.npm.sankuai.com/@commitlint/format/download/@commitlint/format-8.2.0.tgz", "integrity": "sha1-CiRH+trHwEIc6KjX4n36IXLHN9Q=", "dev": true, "requires": {"chalk": "^2.0.1"}}, "@commitlint/is-ignored": {"version": "8.2.0", "resolved": "http://r.npm.sankuai.com/@commitlint/is-ignored/download/@commitlint/is-ignored-8.2.0.tgz", "integrity": "sha1-tkCasov1qA8l4U2hfaORatsjCok=", "dev": true, "requires": {"@types/semver": "^6.0.1", "semver": "6.2.0"}, "dependencies": {"semver": {"version": "6.2.0", "resolved": "http://r.npm.sankuai.com/semver/download/semver-6.2.0.tgz", "integrity": "sha1-TYE9lZCq+KkZJpPWyFuTRN5ZAds=", "dev": true}}}, "@commitlint/lint": {"version": "8.2.0", "resolved": "http://r.npm.sankuai.com/@commitlint/lint/download/@commitlint/lint-8.2.0.tgz", "integrity": "sha1-qtxgY3nzVQ64d/FtT1sQNjnL+So=", "dev": true, "requires": {"@commitlint/is-ignored": "^8.2.0", "@commitlint/parse": "^8.2.0", "@commitlint/rules": "^8.2.0", "babel-runtime": "^6.23.0", "lodash": "4.17.14"}, "dependencies": {"lodash": {"version": "4.17.14", "resolved": "http://r.npm.sankuai.com/lodash/download/lodash-4.17.14.tgz", "integrity": "sha1-nOSHrmbJYlT+ILWZ8htoFgKAeLo=", "dev": true}}}, "@commitlint/load": {"version": "8.2.0", "resolved": "http://r.npm.sankuai.com/@commitlint/load/download/@commitlint/load-8.2.0.tgz", "integrity": "sha1-nKU6DHleT2PXlrTUInnoVlSa3Ro=", "dev": true, "requires": {"@commitlint/execute-rule": "^8.2.0", "@commitlint/resolve-extends": "^8.2.0", "babel-runtime": "^6.23.0", "chalk": "2.4.2", "cosmiconfig": "^5.2.0", "lodash": "4.17.14", "resolve-from": "^5.0.0"}, "dependencies": {"caller-path": {"version": "2.0.0", "resolved": "http://r.npm.sankuai.com/caller-path/download/caller-path-2.0.0.tgz", "integrity": "sha1-Ro+DBE42mrIBD6xfBs7uFbsssfQ=", "dev": true, "requires": {"caller-callsite": "^2.0.0"}}, "chalk": {"version": "2.4.2", "resolved": "http://r.npm.sankuai.com/chalk/download/chalk-2.4.2.tgz", "integrity": "sha1-zUJUFnelQzPPVBpJEIwUMrRMlCQ=", "dev": true, "requires": {"ansi-styles": "^3.2.1", "escape-string-regexp": "^1.0.5", "supports-color": "^5.3.0"}}, "cosmiconfig": {"version": "5.2.1", "resolved": "http://r.npm.sankuai.com/cosmiconfig/download/cosmiconfig-5.2.1.tgz", "integrity": "sha1-BA9yaAnFked6F8CjYmykW08Wixo=", "dev": true, "requires": {"import-fresh": "^2.0.0", "is-directory": "^0.3.1", "js-yaml": "^3.13.1", "parse-json": "^4.0.0"}}, "import-fresh": {"version": "2.0.0", "resolved": "http://r.npm.sankuai.com/import-fresh/download/import-fresh-2.0.0.tgz", "integrity": "sha1-2BNVwVYS04bGH53dOSLUMEgipUY=", "dev": true, "requires": {"caller-path": "^2.0.0", "resolve-from": "^3.0.0"}, "dependencies": {"resolve-from": {"version": "3.0.0", "resolved": "http://r.npm.sankuai.com/resolve-from/download/resolve-from-3.0.0.tgz", "integrity": "sha1-six699nWiBvItuZTM17rywoYh0g=", "dev": true}}}, "js-yaml": {"version": "3.13.1", "resolved": "http://r.npm.sankuai.com/js-yaml/download/js-yaml-3.13.1.tgz", "integrity": "sha1-r/FRswv9+o5J4F2iLnQV6d+jeEc=", "dev": true, "requires": {"argparse": "^1.0.7", "esprima": "^4.0.0"}}, "lodash": {"version": "4.17.14", "resolved": "http://r.npm.sankuai.com/lodash/download/lodash-4.17.14.tgz", "integrity": "sha1-nOSHrmbJYlT+ILWZ8htoFgKAeLo=", "dev": true}, "parse-json": {"version": "4.0.0", "resolved": "http://r.npm.sankuai.com/parse-json/download/parse-json-4.0.0.tgz", "integrity": "sha1-vjX1Qlvh9/bHRxhPmKeIy5lHfuA=", "dev": true, "requires": {"error-ex": "^1.3.1", "json-parse-better-errors": "^1.0.1"}}, "resolve-from": {"version": "5.0.0", "resolved": "http://r.npm.sankuai.com/resolve-from/download/resolve-from-5.0.0.tgz", "integrity": "sha1-w1IlhD3493bfIcV1V7wIfp39/Gk=", "dev": true}}}, "@commitlint/message": {"version": "8.2.0", "resolved": "http://r.npm.sankuai.com/@commitlint/message/download/@commitlint/message-8.2.0.tgz", "integrity": "sha1-vcA4gYP2vGAGx+fhl6chaDARkHo=", "dev": true}, "@commitlint/parse": {"version": "8.2.0", "resolved": "http://r.npm.sankuai.com/@commitlint/parse/download/@commitlint/parse-8.2.0.tgz", "integrity": "sha1-3oATfonuWi0wKWVsmzPpDIjG9Ww=", "dev": true, "requires": {"conventional-changelog-angular": "^1.3.3", "conventional-commits-parser": "^2.1.0", "lodash": "^4.17.11"}}, "@commitlint/read": {"version": "8.2.0", "resolved": "http://r.npm.sankuai.com/@commitlint/read/download/@commitlint/read-8.2.0.tgz", "integrity": "sha1-VMZUlyPVMsdENO4NdOBFkDLckVk=", "dev": true, "requires": {"@commitlint/top-level": "^8.2.0", "@marionebl/sander": "^0.6.0", "babel-runtime": "^6.23.0", "git-raw-commits": "^1.3.0"}}, "@commitlint/resolve-extends": {"version": "8.2.0", "resolved": "http://r.npm.sankuai.com/@commitlint/resolve-extends/download/@commitlint/resolve-extends-8.2.0.tgz", "integrity": "sha1-t/LwxxwQ8kuYoZntEdLBTP16MY8=", "dev": true, "requires": {"@types/node": "^12.0.2", "import-fresh": "^3.0.0", "lodash": "4.17.14", "resolve-from": "^5.0.0", "resolve-global": "^1.0.0"}, "dependencies": {"lodash": {"version": "4.17.14", "resolved": "http://r.npm.sankuai.com/lodash/download/lodash-4.17.14.tgz", "integrity": "sha1-nOSHrmbJYlT+ILWZ8htoFgKAeLo=", "dev": true}, "resolve-from": {"version": "5.0.0", "resolved": "http://r.npm.sankuai.com/resolve-from/download/resolve-from-5.0.0.tgz", "integrity": "sha1-w1IlhD3493bfIcV1V7wIfp39/Gk=", "dev": true}}}, "@commitlint/rules": {"version": "8.2.0", "resolved": "http://r.npm.sankuai.com/@commitlint/rules/download/@commitlint/rules-8.2.0.tgz", "integrity": "sha1-TNajI8oaPz0zrm3HI/jIjz3N40c=", "dev": true, "requires": {"@commitlint/ensure": "^8.2.0", "@commitlint/message": "^8.2.0", "@commitlint/to-lines": "^8.2.0", "babel-runtime": "^6.23.0"}}, "@commitlint/to-lines": {"version": "8.2.0", "resolved": "http://r.npm.sankuai.com/@commitlint/to-lines/download/@commitlint/to-lines-8.2.0.tgz", "integrity": "sha1-3dtZFqRX4aeeQ3EVqbjqx7+a1So=", "dev": true}, "@commitlint/top-level": {"version": "8.2.0", "resolved": "http://r.npm.sankuai.com/@commitlint/top-level/download/@commitlint/top-level-8.2.0.tgz", "integrity": "sha1-IG58vFTb6UlBkGd/iH3WCUP+1bA=", "dev": true, "requires": {"find-up": "^4.0.0"}, "dependencies": {"find-up": {"version": "4.1.0", "resolved": "http://r.npm.sankuai.com/find-up/download/find-up-4.1.0.tgz", "integrity": "sha1-l6/n1s3AvFkoWEt8jXsW6KmqXRk=", "dev": true, "requires": {"locate-path": "^5.0.0", "path-exists": "^4.0.0"}}, "locate-path": {"version": "5.0.0", "resolved": "http://r.npm.sankuai.com/locate-path/download/locate-path-5.0.0.tgz", "integrity": "sha1-Gvujlq/WdqbUJQTQpno6frn2KqA=", "dev": true, "requires": {"p-locate": "^4.1.0"}}, "p-limit": {"version": "2.2.1", "resolved": "http://r.npm.sankuai.com/p-limit/download/p-limit-2.2.1.tgz", "integrity": "sha1-qgeniMwxUck5tRMfY1cPDdIAlTc=", "dev": true, "requires": {"p-try": "^2.0.0"}}, "p-locate": {"version": "4.1.0", "resolved": "http://r.npm.sankuai.com/p-locate/download/p-locate-4.1.0.tgz", "integrity": "sha1-o0KLtwiLOmApL2aRkni3wpetTwc=", "dev": true, "requires": {"p-limit": "^2.2.0"}}, "p-try": {"version": "2.2.0", "resolved": "http://r.npm.sankuai.com/p-try/download/p-try-2.2.0.tgz", "integrity": "sha1-yyhoVA4xPWHeWPr741zpAE1VQOY=", "dev": true}, "path-exists": {"version": "4.0.0", "resolved": "http://r.npm.sankuai.com/path-exists/download/path-exists-4.0.0.tgz", "integrity": "sha1-UTvb4tO5XXdi6METfvoZXGxhtbM=", "dev": true}}}, "@dp/logger-container": {"version": "1.2.0", "resolved": "http://r.npm.sankuai.com/@dp/logger-container/download/@dp/logger-container-1.2.0.tgz", "integrity": "sha1-AuoLwhN37/N6UHL/tRTccMB88m8="}, "@dp/node-kms": {"version": "2.0.13", "resolved": "http://r.npm.sankuai.com/@dp/node-kms/download/@dp/node-kms-2.0.13.tgz", "integrity": "sha1-aOGzsEXrSiDJj0aCHIBICVl+Qx0=", "requires": {"@dp/server-env": "^1.0.3", "@dp/simple-util": "^1.1.1", "bindings": "^1.5.0", "co-request": "^1.0.0", "ip": "^1.1.9"}, "dependencies": {"ip": {"version": "1.1.9", "resolved": "http://r.npm.sankuai.com/ip/download/ip-1.1.9.tgz", "integrity": "sha1-jfvMmadU0H9CUxC4aplUaxFR45Y="}}}, "@dp/owl": {"version": "1.9.5", "resolved": "http://r.npm.sankuai.com/@dp/owl/download/@dp/owl-1.9.5.tgz", "integrity": "sha1-Gv4ivJEUI4fSwnyEnwS8nC23+2Q=", "requires": {"eslint-plugin-html": "^3.2.2", "eslint-plugin-hybrid": "^0.0.1", "int64-convert": "^0.1.2", "uuid": "^3.3.2"}, "dependencies": {"eslint-plugin-html": {"version": "3.2.2", "resolved": "http://r.npm.sankuai.com/eslint-plugin-html/download/eslint-plugin-html-3.2.2.tgz", "integrity": "sha1-73CTYh06k94yBv0fkvNH6poaTfo=", "requires": {"htmlparser2": "^3.8.2", "semver": "^5.4.1"}}}}, "@dp/server-env": {"version": "1.0.3", "resolved": "http://r.npm.sankuai.com/@dp/server-env/download/@dp/server-env-1.0.3.tgz", "integrity": "sha1-BFoJRaBdRvRS2X0GBDVBbAgzJzE=", "requires": {"@dp/logger-container": "^1.1.0", "properties-parser": "^0.3.1"}}, "@dp/simple-util": {"version": "1.1.1", "resolved": "http://r.npm.sankuai.com/@dp/simple-util/download/@dp/simple-util-1.1.1.tgz", "integrity": "sha1-lyx6QVEa7L2mekrEiTstPr89WEE="}, "@marionebl/sander": {"version": "0.6.1", "resolved": "http://r.npm.sankuai.com/@marionebl/sander/download/@marionebl/sander-0.6.1.tgz", "integrity": "sha1-GViWWHTyS8Ub5Ih1/rUNZC/EH3s=", "dev": true, "requires": {"graceful-fs": "^4.1.3", "mkdirp": "^0.5.1", "rimraf": "^2.5.2"}}, "@mfe/precommit-eslint": {"version": "1.0.9", "resolved": "http://r.npm.sankuai.com/@mfe/precommit-eslint/download/@mfe/precommit-eslint-1.0.9.tgz", "integrity": "sha1-uaPtuiq5PsZyL7tFi6+MdrJy7to=", "dev": true}, "@mtfe/ba": {"version": "2.1.5", "resolved": "http://r.npm.sankuai.com/@mtfe/ba/download/@mtfe/ba-2.1.5.tgz", "integrity": "sha1-EZtx9Mlqf6QAxUz20N3O/jbbIRA="}, "@mtfe/daxiang": {"version": "2.3.2", "resolved": "http://r.npm.sankuai.com/@mtfe/daxiang/download/@mtfe/daxiang-2.3.2.tgz", "integrity": "sha1-DOD/noetCmOYJl2zUKo00vAYNEI=", "requires": {"@mtfe/ba": "^2.1.5", "@mtfe/http": "^0.0.3", "form-data": "^2.3.2"}}, "@mtfe/http": {"version": "0.0.3", "resolved": "http://r.npm.sankuai.com/@mtfe/http/download/@mtfe/http-0.0.3.tgz", "integrity": "sha1-djtcmyAvQ14IZA/uT9YdK7/hzsY=", "requires": {"@mtfe/ba": "*", "xttp": "*"}}, "@mtfe/pay-cashier-sdk-npm": {"version": "1.0.9", "resolved": "http://r.npm.sankuai.com/@mtfe/pay-cashier-sdk-npm/download/@mtfe/pay-cashier-sdk-npm-1.0.9.tgz", "integrity": "sha1-C3BNe6+v6S75qUWIjPnjkkBjCLg=", "requires": {"@mx/util": "^2.1.2", "urijs": "^1.19.9"}}, "@mx/util": {"version": "2.1.16", "resolved": "http://r.npm.sankuai.com/@mx/util/download/@mx/util-2.1.16.tgz", "integrity": "sha1-Uqua0AG43pnkdY6ZUaWDSkOI8bA=", "requires": {"tslib": "^1.9.3", "whatwg-fetch": "^2.0.3"}, "dependencies": {"whatwg-fetch": {"version": "2.0.4", "resolved": "http://r.npm.sankuai.com/whatwg-fetch/download/whatwg-fetch-2.0.4.tgz", "integrity": "sha1-3eal3zFfnTmZGqF2IYU9cguFVm8="}}}, "@rollup/plugin-node-resolve": {"version": "9.0.0", "resolved": "http://r.npm.sankuai.com/@rollup/plugin-node-resolve/download/@rollup/plugin-node-resolve-9.0.0.tgz", "integrity": "sha1-Ob0ANM6RJrOcFplpX0QLS30rYuY=", "requires": {"@rollup/pluginutils": "^3.1.0", "@types/resolve": "1.17.1", "builtin-modules": "^3.1.0", "deepmerge": "^4.2.2", "is-module": "^1.0.0", "resolve": "^1.17.0"}, "dependencies": {"builtin-modules": {"version": "3.3.0", "resolved": "http://r.npm.sankuai.com/builtin-modules/download/builtin-modules-3.3.0.tgz", "integrity": "sha1-yuYoEriYAellYzbkYiPgMDhr57Y="}, "path-parse": {"version": "1.0.7", "resolved": "http://r.npm.sankuai.com/path-parse/download/path-parse-1.0.7.tgz", "integrity": "sha1-+8EUtgykKzDZ2vWFjkvWi77bZzU="}, "resolve": {"version": "1.22.8", "resolved": "http://r.npm.sankuai.com/resolve/download/resolve-1.22.8.tgz", "integrity": "sha1-tsh6nyqgbfq1Lj1wrIzeMh+lpI0=", "requires": {"is-core-module": "^2.13.0", "path-parse": "^1.0.7", "supports-preserve-symlinks-flag": "^1.0.0"}}}}, "@rollup/pluginutils": {"version": "3.1.0", "resolved": "http://r.npm.sankuai.com/@rollup/pluginutils/download/@rollup/pluginutils-3.1.0.tgz", "integrity": "sha1-cGtFJO5tyLEDs8mVUz5a1oDAK5s=", "requires": {"@types/estree": "0.0.39", "estree-walker": "^1.0.1", "picomatch": "^2.2.2"}}, "@roo-design/roo-vue": {"version": "3.2.13", "resolved": "http://r.npm.sankuai.com/@roo-design/roo-vue/download/@roo-design/roo-vue-3.2.13.tgz", "integrity": "sha1-LAK1hm99sSTKzi8AHRvWyqkxGA8=", "requires": {"@dp/node-kms": "^2.0.9", "@mtfe/daxiang": "^2.2.0", "async-validator": "^1.11.3", "changelog-parser": "^2.8.0", "countup": "^1.8.2", "deepmerge": "^4.0.0", "element-resize-detector": "^1.1.15", "highlight.js": "^9.13.1", "inquirer": "^7.0.0", "js-calendar": "^1.2.3", "lodash.throttle": "^4.1.1", "moment": "^2.22.2", "popper.js": "^1.15.0", "qr.js": "0.0.0", "tinycolor2": "^1.4.1", "v-click-outside-x": "^4.0.9", "vue-router": "^3.0.1", "vue2-editor": "^2.6.6"}, "dependencies": {"ansi-escapes": {"version": "4.3.2", "resolved": "http://r.npm.sankuai.com/ansi-escapes/download/ansi-escapes-4.3.2.tgz", "integrity": "sha1-ayKR0dt9mLZSHV8e+kLQ86n+tl4=", "requires": {"type-fest": "^0.21.3"}}, "ansi-regex": {"version": "5.0.1", "resolved": "http://r.npm.sankuai.com/ansi-regex/download/ansi-regex-5.0.1.tgz", "integrity": "sha1-CCyyyJyf6GWaMRpTvWpNxTAdswQ="}, "ansi-styles": {"version": "4.3.0", "resolved": "http://r.npm.sankuai.com/ansi-styles/download/ansi-styles-4.3.0.tgz", "integrity": "sha1-7dgDYornHATIWuegkG7a00tkiTc=", "requires": {"color-convert": "^2.0.1"}}, "async-validator": {"version": "1.12.2", "resolved": "http://r.npm.sankuai.com/async-validator/download/async-validator-1.12.2.tgz", "integrity": "sha1-vq5nHnF00pOLe0tp0vt+cit/1yw="}, "chalk": {"version": "4.1.2", "resolved": "http://r.npm.sankuai.com/chalk/download/chalk-4.1.2.tgz", "integrity": "sha1-qsTit3NKdAhnrrFr8CqtVWoeegE=", "requires": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}}, "cli-cursor": {"version": "3.1.0", "resolved": "http://r.npm.sankuai.com/cli-cursor/download/cli-cursor-3.1.0.tgz", "integrity": "sha1-JkMFp65JDR0Dvwybp8kl0XU68wc=", "requires": {"restore-cursor": "^3.1.0"}}, "cli-width": {"version": "3.0.0", "resolved": "http://r.npm.sankuai.com/cli-width/download/cli-width-3.0.0.tgz", "integrity": "sha1-ovSEN6LKqaIkNueUvwceyeYc7fY="}, "color-convert": {"version": "2.0.1", "resolved": "http://r.npm.sankuai.com/color-convert/download/color-convert-2.0.1.tgz", "integrity": "sha1-ctOmjVmMm9s68q0ehPIdiWq9TeM=", "requires": {"color-name": "~1.1.4"}}, "color-name": {"version": "1.1.4", "resolved": "http://r.npm.sankuai.com/color-name/download/color-name-1.1.4.tgz", "integrity": "sha1-wqCah6y95pVD3m9j+jmVyCbFNqI="}, "emoji-regex": {"version": "8.0.0", "resolved": "http://r.npm.sankuai.com/emoji-regex/download/emoji-regex-8.0.0.tgz", "integrity": "sha1-6Bj9ac5cz8tARZT4QpY79TFkzDc="}, "figures": {"version": "3.2.0", "resolved": "http://r.npm.sankuai.com/figures/download/figures-3.2.0.tgz", "integrity": "sha1-YlwYvSk8YE3EqN2y/r8MiDQXRq8=", "requires": {"escape-string-regexp": "^1.0.5"}}, "has-flag": {"version": "4.0.0", "resolved": "http://r.npm.sankuai.com/has-flag/download/has-flag-4.0.0.tgz", "integrity": "sha1-lEdx/ZyByBJlxNaUGGDaBrtZR5s="}, "inquirer": {"version": "7.3.3", "resolved": "http://r.npm.sankuai.com/inquirer/download/inquirer-7.3.3.tgz", "integrity": "sha1-BNF2sq8Er8FXqD/XwQDpjuCq0AM=", "requires": {"ansi-escapes": "^4.2.1", "chalk": "^4.1.0", "cli-cursor": "^3.1.0", "cli-width": "^3.0.0", "external-editor": "^3.0.3", "figures": "^3.0.0", "lodash": "^4.17.19", "mute-stream": "0.0.8", "run-async": "^2.4.0", "rxjs": "^6.6.0", "string-width": "^4.1.0", "strip-ansi": "^6.0.0", "through": "^2.3.6"}}, "is-fullwidth-code-point": {"version": "3.0.0", "resolved": "http://r.npm.sankuai.com/is-fullwidth-code-point/download/is-fullwidth-code-point-3.0.0.tgz", "integrity": "sha1-8Rb4Bk/pCz94RKOJl8C3UFEmnx0="}, "lodash": {"version": "4.17.21", "resolved": "http://r.npm.sankuai.com/lodash/download/lodash-4.17.21.tgz", "integrity": "sha1-Z5WRxWTDv/quhFTPCz3zcMPWkRw="}, "mimic-fn": {"version": "2.1.0", "resolved": "http://r.npm.sankuai.com/mimic-fn/download/mimic-fn-2.1.0.tgz", "integrity": "sha1-ftLCzMyvhNP/y3pptXcR/CCDQBs="}, "mute-stream": {"version": "0.0.8", "resolved": "http://r.npm.sankuai.com/mute-stream/download/mute-stream-0.0.8.tgz", "integrity": "sha1-FjDEKyJR/4HiooPelqVJfqkuXg0="}, "onetime": {"version": "5.1.2", "resolved": "http://r.npm.sankuai.com/onetime/download/onetime-5.1.2.tgz", "integrity": "sha1-0Oluu1awdHbfHdnEgG5SN5hcpF4=", "requires": {"mimic-fn": "^2.1.0"}}, "restore-cursor": {"version": "3.1.0", "resolved": "http://r.npm.sankuai.com/restore-cursor/download/restore-cursor-3.1.0.tgz", "integrity": "sha1-OfZ8VLOnpYzqUjbZXPADQjljH34=", "requires": {"onetime": "^5.1.0", "signal-exit": "^3.0.2"}}, "run-async": {"version": "2.4.1", "resolved": "http://r.npm.sankuai.com/run-async/download/run-async-2.4.1.tgz", "integrity": "sha1-hEDsz5nqPnC9QJ1JqriOEMGJpFU="}, "rxjs": {"version": "6.6.7", "resolved": "http://r.npm.sankuai.com/rxjs/download/rxjs-6.6.7.tgz", "integrity": "sha1-kKwBisq/SRv2UEQjXVhjxNq4BMk=", "requires": {"tslib": "^1.9.0"}}, "string-width": {"version": "4.2.3", "resolved": "http://r.npm.sankuai.com/string-width/download/string-width-4.2.3.tgz", "integrity": "sha1-JpxxF9J7Ba0uU2gwqOyJXvnG0BA=", "requires": {"emoji-regex": "^8.0.0", "is-fullwidth-code-point": "^3.0.0", "strip-ansi": "^6.0.1"}}, "strip-ansi": {"version": "6.0.1", "resolved": "http://r.npm.sankuai.com/strip-ansi/download/strip-ansi-6.0.1.tgz", "integrity": "sha1-nibGPTD1NEPpSJSVshBdN7Z6hdk=", "requires": {"ansi-regex": "^5.0.1"}}, "supports-color": {"version": "7.2.0", "resolved": "http://r.npm.sankuai.com/supports-color/download/supports-color-7.2.0.tgz", "integrity": "sha1-G33NyzK4E4gBs+R4umpRyqiWSNo=", "requires": {"has-flag": "^4.0.0"}}, "type-fest": {"version": "0.21.3", "resolved": "http://r.npm.sankuai.com/type-fest/download/type-fest-0.21.3.tgz", "integrity": "sha1-0mCiSwGYQ24TP6JqUkptZfo7Ljc="}}}, "@roo/roo-vue": {"version": "1.2.0", "resolved": "http://r.npm.sankuai.com/@roo/roo-vue/download/@roo/roo-vue-1.2.0.tgz", "integrity": "sha1-p7waB1ioHbYvhd3ikFcuDCYq7ds=", "requires": {"async-validator": "1.6.8"}}, "@ss/lx-report": {"version": "0.0.3", "resolved": "http://r.npm.sankuai.com/@ss/lx-report/download/@ss/lx-report-0.0.3.tgz", "integrity": "sha1-uKXj589Xz8EO4dpDPbL123Xk8r0="}, "@ss/mtd-vue": {"version": "0.4.16", "resolved": "http://r.npm.sankuai.com/@ss/mtd-vue/download/@ss/mtd-vue-0.4.16.tgz", "integrity": "sha1-/xpr3cfhxZoMVmGowbYA4Gq956o=", "requires": {"@ss/lx-report": "^0.0.3-beta.5", "async-validator": "^1.12.2", "babel-helper-vue-jsx-merge-props": "^2.0.3", "babel-runtime": "^6.26.0", "dayjs": "^1.10.7", "deepmerge": "^2.2.1", "normalize-wheel": "^1.0.1", "popper.js": "^1.16.0", "resize-observer-polyfill": "^1.5.1", "throttle-debounce": "^1.0.1"}, "dependencies": {"async-validator": {"version": "1.12.2", "resolved": "http://r.npm.sankuai.com/async-validator/download/async-validator-1.12.2.tgz", "integrity": "sha1-vq5nHnF00pOLe0tp0vt+cit/1yw="}, "dayjs": {"version": "1.11.13", "resolved": "http://r.npm.sankuai.com/dayjs/download/dayjs-1.11.13.tgz", "integrity": "sha1-kkMLATkFXD67YBUKoT6GCktaNmw="}, "deepmerge": {"version": "2.2.1", "resolved": "http://r.npm.sankuai.com/deepmerge/download/deepmerge-2.2.1.tgz", "integrity": "sha1-XT/yKgHAD2RUBaL7wX0HeKGAEXA="}}}, "@types/estree": {"version": "0.0.39", "resolved": "http://r.npm.sankuai.com/@types/estree/download/@types/estree-0.0.39.tgz", "integrity": "sha1-4Xfmme4bjCLSMXTKqnQiZEOJUJ8="}, "@types/node": {"version": "12.12.14", "resolved": "http://r.npm.sankuai.com/@types/node/download/@types/node-12.12.14.tgz", "integrity": "sha1-HB1uPHXbpGbgMmlI1W6L1yoZA9I="}, "@types/normalize-package-data": {"version": "2.4.0", "resolved": "http://r.npm.sankuai.com/@types/normalize-package-data/download/@types/normalize-package-data-2.4.0.tgz", "integrity": "sha1-5IbQ2XOW15vu3QpuM/RTT/a0lz4=", "dev": true}, "@types/query-string": {"version": "6.3.0", "resolved": "https://registry.npmjs.org/@types/query-string/-/query-string-6.3.0.tgz", "integrity": "sha512-yuIv/WRffRzL7cBW+sla4HwBZrEXRNf1MKQ5SklPEadth+BKbDxiVG8A3iISN5B3yC4EeSCzMZP8llHTcUhOzQ==", "dev": true, "requires": {"query-string": "*"}}, "@types/resolve": {"version": "1.17.1", "resolved": "http://r.npm.sankuai.com/@types/resolve/download/@types/resolve-1.17.1.tgz", "integrity": "sha1-Ov1q2JZ8d+Q3bFmKgt3Vj0bsRdY=", "requires": {"@types/node": "*"}}, "@types/semver": {"version": "6.2.0", "resolved": "http://r.npm.sankuai.com/@types/semver/download/@types/semver-6.2.0.tgz", "integrity": "sha1-1ojVdEANlsWwEUlocFNm9DGDHho=", "dev": true}, "@utiljs/clone": {"version": "0.2.5", "resolved": "http://r.npm.sankuai.com/@utiljs/clone/download/@utiljs/clone-0.2.5.tgz", "integrity": "sha1-2LXspG3z4kOE9709T918kHQ/ww0=", "requires": {"@utiljs/type": "^0.4.0"}}, "@utiljs/console": {"version": "0.1.5", "resolved": "http://r.npm.sankuai.com/@utiljs/console/download/@utiljs/console-0.1.5.tgz", "integrity": "sha1-7KNyAQTW/IFMT3Hc9cK2rMbOSVY="}, "@utiljs/cookie": {"version": "0.1.6", "resolved": "http://r.npm.sankuai.com/@utiljs/cookie/download/@utiljs/cookie-0.1.6.tgz", "integrity": "sha1-y9CLSVGW3/bApk2vckWQyo3fO/U="}, "@utiljs/eslint-plugin-utiljs": {"version": "0.3.2", "resolved": "http://r.npm.sankuai.com/@utiljs/eslint-plugin-utiljs/download/@utiljs/eslint-plugin-utiljs-0.3.2.tgz", "integrity": "sha1-8ZwP8oVtIgvvwYhIl1IpKmesCgY=", "dev": true, "requires": {"requireindex": "~1.1.0"}}, "@utiljs/extend": {"version": "0.1.9", "resolved": "http://r.npm.sankuai.com/@utiljs/extend/download/@utiljs/extend-0.1.9.tgz", "integrity": "sha1-b1mA5noquNiV3FlQOKe7jas5M3o=", "requires": {"@utiljs/is": "0.11.10"}}, "@utiljs/is": {"version": "0.11.10", "resolved": "http://r.npm.sankuai.com/@utiljs/is/download/@utiljs/is-0.11.10.tgz", "integrity": "sha1-OybUJruQeMa/2OrPmrp81tbhcW4=", "requires": {"@utiljs/string": "0.6.6", "@utiljs/type": "0.5.5"}, "dependencies": {"@utiljs/type": {"version": "0.5.5", "resolved": "http://r.npm.sankuai.com/@utiljs/type/download/@utiljs/type-0.5.5.tgz", "integrity": "sha1-W8eZEzqFEY//v/rxxrXRSwyi6Mw="}}}, "@utiljs/param": {"version": "0.6.11", "resolved": "http://r.npm.sankuai.com/@utiljs/param/download/@utiljs/param-0.6.11.tgz", "integrity": "sha1-o+uDpNl8lJcv7fdaob5H2CdUbdA=", "requires": {"@utiljs/extend": "0.1.9", "@utiljs/type": "0.5.5"}, "dependencies": {"@utiljs/type": {"version": "0.5.5", "resolved": "http://r.npm.sankuai.com/@utiljs/type/download/@utiljs/type-0.5.5.tgz", "integrity": "sha1-W8eZEzqFEY//v/rxxrXRSwyi6Mw="}}}, "@utiljs/string": {"version": "0.6.6", "resolved": "http://r.npm.sankuai.com/@utiljs/string/download/@utiljs/string-0.6.6.tgz", "integrity": "sha1-YoVx7FwKwSux6jUmWG+e7XdMm38=", "requires": {"@utiljs/console": "0.1.5", "@utiljs/type": "0.5.4"}, "dependencies": {"@utiljs/type": {"version": "0.5.4", "resolved": "http://r.npm.sankuai.com/@utiljs/type/download/@utiljs/type-0.5.4.tgz", "integrity": "sha1-ljgZeJK11DoYV7vBxlB8DdANGMY="}}}, "@utiljs/type": {"version": "0.4.4", "resolved": "http://r.npm.sankuai.com/@utiljs/type/download/@utiljs/type-0.4.4.tgz", "integrity": "sha1-s2n5lnxIVRCtspOaP4tvZj6l8PY="}, "@vue/compiler-sfc": {"version": "2.7.16", "resolved": "http://r.npm.sankuai.com/@vue/compiler-sfc/download/@vue/compiler-sfc-2.7.16.tgz", "integrity": "sha1-/4FxGg+snGhoPYuwC2P4V9533IM=", "requires": {"@babel/parser": "^7.23.5", "postcss": "^8.4.14", "prettier": "^1.18.2 || ^2.0.0", "source-map": "^0.6.1"}, "dependencies": {"@babel/parser": {"version": "7.24.0", "resolved": "http://r.npm.sankuai.com/@babel/parser/download/@babel/parser-7.24.0.tgz", "integrity": "sha1-JqPR/0kDHFOpfQO2BDdfAodGqaw="}, "postcss": {"version": "8.4.35", "resolved": "http://r.npm.sankuai.com/postcss/download/postcss-8.4.35.tgz", "integrity": "sha1-YJl3dWic4JAR7fCDpUnOpEqr4vc=", "requires": {"nanoid": "^3.3.7", "picocolors": "^1.0.0", "source-map-js": "^1.0.2"}}, "prettier": {"version": "2.8.8", "resolved": "http://r.npm.sankuai.com/prettier/download/prettier-2.8.8.tgz", "integrity": "sha1-6MXX6YpDBf/j3i4fxKyhpxwosdo=", "optional": true}, "source-map": {"version": "0.6.1", "resolved": "http://r.npm.sankuai.com/source-map/download/source-map-0.6.1.tgz", "integrity": "sha1-dHIq8y6WFOnCh6jQu95IteLxomM="}}}, "@vue/component-compiler-utils": {"version": "2.2.0", "resolved": "https://registry.npmjs.org/@vue/component-compiler-utils/-/component-compiler-utils-2.2.0.tgz", "integrity": "sha512-pS4zlcdD7BvedyB+IfiTfrbi6C977UMIfulSk8r6uL0BU46ZE2+fUj/zbSNSfVxeaj9ElmnSni5OMwF9np+b+w==", "dev": true, "requires": {"consolidate": "^0.15.1", "hash-sum": "^1.0.2", "lru-cache": "^4.1.2", "merge-source-map": "^1.1.0", "postcss": "^6.0.20", "postcss-selector-parser": "^3.1.1", "prettier": "1.13.7", "source-map": "^0.5.6", "vue-template-es2015-compiler": "^1.6.0"}}, "@webassemblyjs/ast": {"version": "1.7.6", "resolved": "https://registry.npmjs.org/@webassemblyjs/ast/-/ast-1.7.6.tgz", "integrity": "sha512-8nkZS48EVsMUU0v6F1LCIOw4RYWLm2plMtbhFTjNgeXmsTNLuU3xTRtnljt9BFQB+iPbLRobkNrCWftWnNC7wQ==", "dev": true, "requires": {"@webassemblyjs/helper-module-context": "1.7.6", "@webassemblyjs/helper-wasm-bytecode": "1.7.6", "@webassemblyjs/wast-parser": "1.7.6", "mamacro": "^0.0.3"}}, "@webassemblyjs/floating-point-hex-parser": {"version": "1.7.6", "resolved": "https://registry.npmjs.org/@webassemblyjs/floating-point-hex-parser/-/floating-point-hex-parser-1.7.6.tgz", "integrity": "sha512-VBOZvaOyBSkPZdIt5VBMg3vPWxouuM13dPXGWI1cBh3oFLNcFJ8s9YA7S9l4mPI7+Q950QqOmqj06oa83hNWBA==", "dev": true}, "@webassemblyjs/helper-api-error": {"version": "1.7.6", "resolved": "https://registry.npmjs.org/@webassemblyjs/helper-api-error/-/helper-api-error-1.7.6.tgz", "integrity": "sha512-SCzhcQWHXfrfMSKcj8zHg1/kL9kb3aa5TN4plc/EREOs5Xop0ci5bdVBApbk2yfVi8aL+Ly4Qpp3/TRAUInjrg==", "dev": true}, "@webassemblyjs/helper-buffer": {"version": "1.7.6", "resolved": "https://registry.npmjs.org/@webassemblyjs/helper-buffer/-/helper-buffer-1.7.6.tgz", "integrity": "sha512-1/gW5NaGsEOZ02fjnFiU8/OEEXU1uVbv2um0pQ9YVL3IHSkyk6xOwokzyqqO1qDZQUAllb+V8irtClPWntbVqw==", "dev": true}, "@webassemblyjs/helper-code-frame": {"version": "1.7.6", "resolved": "https://registry.npmjs.org/@webassemblyjs/helper-code-frame/-/helper-code-frame-1.7.6.tgz", "integrity": "sha512-+suMJOkSn9+vEvDvgyWyrJo5vJsWSDXZmJAjtoUq4zS4eqHyXImpktvHOZwXp1XQjO5H+YQwsBgqTQEc0J/5zg==", "dev": true, "requires": {"@webassemblyjs/wast-printer": "1.7.6"}}, "@webassemblyjs/helper-fsm": {"version": "1.7.6", "resolved": "https://registry.npmjs.org/@webassemblyjs/helper-fsm/-/helper-fsm-1.7.6.tgz", "integrity": "sha512-HCS6KN3wgxUihGBW7WFzEC/o8Eyvk0d56uazusnxXthDPnkWiMv+kGi9xXswL2cvfYfeK5yiM17z2K5BVlwypw==", "dev": true}, "@webassemblyjs/helper-module-context": {"version": "1.7.6", "resolved": "https://registry.npmjs.org/@webassemblyjs/helper-module-context/-/helper-module-context-1.7.6.tgz", "integrity": "sha512-e8/6GbY7OjLM+6OsN7f2krC2qYVNaSr0B0oe4lWdmq5sL++8dYDD1TFbD1TdAdWMRTYNr/Qq7ovXWzia2EbSjw==", "dev": true, "requires": {"mamacro": "^0.0.3"}}, "@webassemblyjs/helper-wasm-bytecode": {"version": "1.7.6", "resolved": "https://registry.npmjs.org/@webassemblyjs/helper-wasm-bytecode/-/helper-wasm-bytecode-1.7.6.tgz", "integrity": "sha512-PzYFCb7RjjSdAOljyvLWVqd6adAOabJW+8yRT+NWhXuf1nNZWH+igFZCUK9k7Cx7CsBbzIfXjJc7u56zZgFj9Q==", "dev": true}, "@webassemblyjs/helper-wasm-section": {"version": "1.7.6", "resolved": "https://registry.npmjs.org/@webassemblyjs/helper-wasm-section/-/helper-wasm-section-1.7.6.tgz", "integrity": "sha512-3GS628ppDPSuwcYlQ7cDCGr4W2n9c4hLzvnRKeuz+lGsJSmc/ADVoYpm1ts2vlB1tGHkjtQMni+yu8mHoMlKlA==", "dev": true, "requires": {"@webassemblyjs/ast": "1.7.6", "@webassemblyjs/helper-buffer": "1.7.6", "@webassemblyjs/helper-wasm-bytecode": "1.7.6", "@webassemblyjs/wasm-gen": "1.7.6"}}, "@webassemblyjs/ieee754": {"version": "1.7.6", "resolved": "https://registry.npmjs.org/@webassemblyjs/ieee754/-/ieee754-1.7.6.tgz", "integrity": "sha512-V4cIp0ruyw+hawUHwQLn6o2mFEw4t50tk530oKsYXQhEzKR+xNGDxs/SFFuyTO7X3NzEu4usA3w5jzhl2RYyzQ==", "dev": true, "requires": {"@xtuc/ieee754": "^1.2.0"}}, "@webassemblyjs/leb128": {"version": "1.7.6", "resolved": "https://registry.npmjs.org/@webassemblyjs/leb128/-/leb128-1.7.6.tgz", "integrity": "sha512-ojdlG8WpM394lBow4ncTGJoIVZ4aAtNOWHhfAM7m7zprmkVcKK+2kK5YJ9Bmj6/ketTtOn7wGSHCtMt+LzqgYQ==", "dev": true, "requires": {"@xtuc/long": "4.2.1"}}, "@webassemblyjs/utf8": {"version": "1.7.6", "resolved": "https://registry.npmjs.org/@webassemblyjs/utf8/-/utf8-1.7.6.tgz", "integrity": "sha512-oId+tLxQ+AeDC34ELRYNSqJRaScB0TClUU6KQfpB8rNT6oelYlz8axsPhf6yPTg7PBJ/Z5WcXmUYiHEWgbbHJw==", "dev": true}, "@webassemblyjs/wasm-edit": {"version": "1.7.6", "resolved": "https://registry.npmjs.org/@webassemblyjs/wasm-edit/-/wasm-edit-1.7.6.tgz", "integrity": "sha512-pTNjLO3o41v/Vz9VFLl+I3YLImpCSpodFW77pNoH4agn5I6GgSxXHXtvWDTvYJFty0jSeXZWLEmbaSIRUDlekg==", "dev": true, "requires": {"@webassemblyjs/ast": "1.7.6", "@webassemblyjs/helper-buffer": "1.7.6", "@webassemblyjs/helper-wasm-bytecode": "1.7.6", "@webassemblyjs/helper-wasm-section": "1.7.6", "@webassemblyjs/wasm-gen": "1.7.6", "@webassemblyjs/wasm-opt": "1.7.6", "@webassemblyjs/wasm-parser": "1.7.6", "@webassemblyjs/wast-printer": "1.7.6"}}, "@webassemblyjs/wasm-gen": {"version": "1.7.6", "resolved": "https://registry.npmjs.org/@webassemblyjs/wasm-gen/-/wasm-gen-1.7.6.tgz", "integrity": "sha512-mQvFJVumtmRKEUXMohwn8nSrtjJJl6oXwF3FotC5t6e2hlKMh8sIaW03Sck2MDzw9xPogZD7tdP5kjPlbH9EcQ==", "dev": true, "requires": {"@webassemblyjs/ast": "1.7.6", "@webassemblyjs/helper-wasm-bytecode": "1.7.6", "@webassemblyjs/ieee754": "1.7.6", "@webassemblyjs/leb128": "1.7.6", "@webassemblyjs/utf8": "1.7.6"}}, "@webassemblyjs/wasm-opt": {"version": "1.7.6", "resolved": "https://registry.npmjs.org/@webassemblyjs/wasm-opt/-/wasm-opt-1.7.6.tgz", "integrity": "sha512-go44K90fSIsDwRgtHhX14VtbdDPdK2sZQtZqUcMRvTojdozj5tLI0VVJAzLCfz51NOkFXezPeVTAYFqrZ6rI8Q==", "dev": true, "requires": {"@webassemblyjs/ast": "1.7.6", "@webassemblyjs/helper-buffer": "1.7.6", "@webassemblyjs/wasm-gen": "1.7.6", "@webassemblyjs/wasm-parser": "1.7.6"}}, "@webassemblyjs/wasm-parser": {"version": "1.7.6", "resolved": "https://registry.npmjs.org/@webassemblyjs/wasm-parser/-/wasm-parser-1.7.6.tgz", "integrity": "sha512-t1T6TfwNY85pDA/HWPA8kB9xA4sp9ajlRg5W7EKikqrynTyFo+/qDzIpvdkOkOGjlS6d4n4SX59SPuIayR22Yg==", "dev": true, "requires": {"@webassemblyjs/ast": "1.7.6", "@webassemblyjs/helper-api-error": "1.7.6", "@webassemblyjs/helper-wasm-bytecode": "1.7.6", "@webassemblyjs/ieee754": "1.7.6", "@webassemblyjs/leb128": "1.7.6", "@webassemblyjs/utf8": "1.7.6"}}, "@webassemblyjs/wast-parser": {"version": "1.7.6", "resolved": "https://registry.npmjs.org/@webassemblyjs/wast-parser/-/wast-parser-1.7.6.tgz", "integrity": "sha512-1MaWTErN0ziOsNUlLdvwS+NS1QWuI/kgJaAGAMHX8+fMJFgOJDmN/xsG4h/A1Gtf/tz5VyXQciaqHZqp2q0vfg==", "dev": true, "requires": {"@webassemblyjs/ast": "1.7.6", "@webassemblyjs/floating-point-hex-parser": "1.7.6", "@webassemblyjs/helper-api-error": "1.7.6", "@webassemblyjs/helper-code-frame": "1.7.6", "@webassemblyjs/helper-fsm": "1.7.6", "@xtuc/long": "4.2.1", "mamacro": "^0.0.3"}}, "@webassemblyjs/wast-printer": {"version": "1.7.6", "resolved": "https://registry.npmjs.org/@webassemblyjs/wast-printer/-/wast-printer-1.7.6.tgz", "integrity": "sha512-vHdHSK1tOetvDcl1IV1OdDeGNe/NDDQ+KzuZHMtqTVP1xO/tZ/IKNpj5BaGk1OYFdsDWQqb31PIwdEyPntOWRQ==", "dev": true, "requires": {"@webassemblyjs/ast": "1.7.6", "@webassemblyjs/wast-parser": "1.7.6", "@xtuc/long": "4.2.1"}}, "@wmfe/eslint-config-mt": {"version": "0.2.1", "resolved": "http://r.npm.sankuai.com/@wmfe/eslint-config-mt/download/@wmfe/eslint-config-mt-0.2.1.tgz", "integrity": "sha1-eqDt/Mj1dEQtMI6Ukqlc0ad4tPM=", "dev": true}, "@wmfe/phoenix": {"version": "1.0.0", "resolved": "http://r.npm.sankuai.com/@wmfe/phoenix/download/@wmfe/phoenix-1.0.0.tgz", "integrity": "sha1-5psPnEDHBMz4DTYhiWav67cS+ts="}, "@wmfe/svelte-components": {"version": "1.3.2", "resolved": "http://r.npm.sankuai.com/@wmfe/svelte-components/download/@wmfe/svelte-components-1.3.2.tgz", "integrity": "sha1-3jLRxtRMbt9d0Z+6X9i5D3wTm70=", "requires": {"@rollup/plugin-node-resolve": "^9.0.0", "axios": "^0.20.0", "whatwg-fetch": "^3.0.0"}, "dependencies": {"axios": {"version": "0.20.0", "resolved": "http://r.npm.sankuai.com/axios/download/axios-0.20.0.tgz", "integrity": "sha1-BXujDwSIRpSZOozQf6OUz/EcUL0=", "requires": {"follow-redirects": "^1.10.0"}}, "follow-redirects": {"version": "1.15.9", "resolved": "http://r.npm.sankuai.com/follow-redirects/download/follow-redirects-1.15.9.tgz", "integrity": "sha1-pgT6EORDv5jKlCKNnuvMLoosjuE="}}}, "@xtuc/ieee754": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/@xtuc/ieee754/-/ieee754-1.2.0.tgz", "integrity": "sha512-DX8nKgqcGwsc0eJSqYt5lwP4DH5FlHnmuWWBRy7X0NcaGR0ZtuyeESgMwTYVEtxmsNGY+qit4QYT/MIYTOTPeA==", "dev": true}, "@xtuc/long": {"version": "4.2.1", "resolved": "https://registry.npmjs.org/@xtuc/long/-/long-4.2.1.tgz", "integrity": "sha512-FZdkNBDqBRHKQ2MEbSC17xnPFOhZxeJ2YGSfr2BKf3sujG49Qe3bB+rGCwQfIaA7WHnGeGkSijX4FuBCdrzW/g==", "dev": true}, "JSONStream": {"version": "1.3.5", "resolved": "http://r.npm.sankuai.com/JSONStream/download/JSONStream-1.3.5.tgz", "integrity": "sha1-MgjB8I06TZkmGrZPkjArwV4RHKA=", "dev": true, "requires": {"jsonparse": "^1.2.0", "through": ">=2.2.7 <3"}}, "abbrev": {"version": "1.1.1", "resolved": "https://registry.npm.taobao.org/abbrev/download/abbrev-1.1.1.tgz", "integrity": "sha1-+PLIh60Qv2f2NPAFtph/7TF5qsg=", "dev": true}, "accepts": {"version": "1.3.5", "resolved": "https://registry.npmjs.org/accepts/-/accepts-1.3.5.tgz", "integrity": "sha1-63d99gEXI6OxTopywIBcjoZ0a9I=", "dev": true, "requires": {"mime-types": "~2.1.18", "negotiator": "0.6.1"}}, "acorn": {"version": "5.7.3", "resolved": "http://r.npm.sankuai.com/acorn/download/acorn-5.7.3.tgz", "integrity": "sha1-Z6ojG/iBKXS4UjWpZ3Hra9B+onk=", "dev": true}, "acorn-dynamic-import": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/acorn-dynamic-import/-/acorn-dynamic-import-3.0.0.tgz", "integrity": "sha512-zVWV8Z8lislJoOKKqdNMOB+s6+XV5WERty8MnKBeFgwA+19XJjJHs2RP5dzM57FftIs+jQnRToLiWazKr6sSWg==", "dev": true, "requires": {"acorn": "^5.0.0"}}, "acorn-jsx": {"version": "4.1.1", "resolved": "http://r.npm.sankuai.com/acorn-jsx/download/acorn-jsx-4.1.1.tgz", "integrity": "sha1-6OQeSOov4MiWdAYQq2pP/YrdIl4=", "dev": true, "requires": {"acorn": "^5.0.3"}}, "ajv": {"version": "6.5.3", "resolved": "http://r.npm.sankuai.com/ajv/download/ajv-6.5.3.tgz", "integrity": "sha1-caVp0Yns9PTzISJP7LFm8HHdkPk=", "dev": true, "requires": {"fast-deep-equal": "^2.0.1", "fast-json-stable-stringify": "^2.0.0", "json-schema-traverse": "^0.4.1", "uri-js": "^4.2.2"}}, "ajv-errors": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/ajv-errors/-/ajv-errors-1.0.0.tgz", "integrity": "sha1-7PAh+hCP0X37Xms4Py3SM+Mf/Fk=", "dev": true}, "ajv-keywords": {"version": "3.2.0", "resolved": "http://r.npm.sankuai.com/ajv-keywords/download/ajv-keywords-3.2.0.tgz", "integrity": "sha1-6GuBnGAs+IIa1jdBNpjx3sAhhHo=", "dev": true}, "alphanum-sort": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/alphanum-sort/-/alphanum-sort-1.0.2.tgz", "integrity": "sha1-l6ERlkmyEa0zaR2fn0hqjsn74KM=", "dev": true}, "amdefine": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/amdefine/-/amdefine-1.0.1.tgz", "integrity": "sha1-SlKCrBZHKek2Gbz9OtFR+BfOkfU=", "dev": true}, "ansi-colors": {"version": "3.0.5", "resolved": "https://registry.npmjs.org/ansi-colors/-/ansi-colors-3.0.5.tgz", "integrity": "sha512-VVjWpkfaphxUBFarydrQ3n26zX5nIK7hcbT3/ielrvwDDyBBjuh2vuSw1P9zkPq0cfqvdw7lkYHnu+OLSfIBsg==", "dev": true}, "ansi-escapes": {"version": "3.1.0", "resolved": "http://r.npm.sankuai.com/ansi-escapes/download/ansi-escapes-3.1.0.tgz", "integrity": "sha1-9zIHu4EgfXX9bIPxJa8m7qN4yjA=", "dev": true}, "ansi-html": {"version": "0.0.7", "resolved": "https://registry.npmjs.org/ansi-html/-/ansi-html-0.0.7.tgz", "integrity": "sha1-gTWEAhliqenm/QOflA0S9WynhZ4=", "dev": true}, "ansi-regex": {"version": "3.0.0", "resolved": "http://r.npm.sankuai.com/ansi-regex/download/ansi-regex-3.0.0.tgz", "integrity": "sha1-7QMXwyIGT3lGbAKWa922Bas32Zg=", "dev": true}, "ansi-styles": {"version": "3.2.1", "resolved": "http://r.npm.sankuai.com/ansi-styles/download/ansi-styles-3.2.1.tgz", "integrity": "sha1-QfuyAkPlCxK+DwS43tvwdSDOhB0=", "dev": true, "requires": {"color-convert": "^1.9.0"}}, "anymatch": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/anymatch/-/anymatch-2.0.0.tgz", "integrity": "sha512-5teOsQWABXHHBFP9y3skS5P3d/WfWXpv3FUpy+LorMrNYaT9pI4oLMQX7jzQ2KklNpGpWHzdCXTDT2Y3XGlZBw==", "dev": true, "requires": {"micromatch": "^3.1.4", "normalize-path": "^2.1.1"}}, "aproba": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/aproba/-/aproba-1.2.0.tgz", "integrity": "sha512-Y9J6ZjXtoYh8RnXVCMOU/ttDmk1aBjunq9vO0ta5x85WDQiQfUF9sIPBITdbiiIVcBo03Hi3jMxigBtsddlXRw==", "dev": true}, "are-we-there-yet": {"version": "1.1.5", "resolved": "https://registry.npm.taobao.org/are-we-there-yet/download/are-we-there-yet-1.1.5.tgz", "integrity": "sha1-SzXClE8GKov82mZBB2A1D+nd/CE=", "dev": true, "requires": {"delegates": "^1.0.0", "readable-stream": "^2.0.6"}}, "argparse": {"version": "1.0.10", "resolved": "http://r.npm.sankuai.com/argparse/download/argparse-1.0.10.tgz", "integrity": "sha1-vNZ5HqWuCXJeF+WtmIE0zUCz2RE=", "dev": true, "requires": {"sprintf-js": "~1.0.2"}}, "arr-diff": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/arr-diff/-/arr-diff-4.0.0.tgz", "integrity": "sha1-1kYQdP6/7HHn4VI1dhoyml3HxSA=", "dev": true}, "arr-flatten": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/arr-flatten/-/arr-flatten-1.1.0.tgz", "integrity": "sha512-L3hKV5R/p5o81R7O02IGnwpDmkp6E982XhtbuwSe3O4qOtMMMtodicASA1Cny2U+aCXcNpml+m4dPsvsJ3jatg==", "dev": true}, "arr-union": {"version": "3.1.0", "resolved": "https://registry.npmjs.org/arr-union/-/arr-union-3.1.0.tgz", "integrity": "sha1-45sJrqne+Gao8gbiiK9jkZuuOcQ=", "dev": true}, "array-find-index": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/array-find-index/-/array-find-index-1.0.2.tgz", "integrity": "sha1-3wEKoSh+Fku9pvlyOwqWoexBh6E=", "dev": true}, "array-flatten": {"version": "2.1.1", "resolved": "https://registry.npmjs.org/array-flatten/-/array-flatten-2.1.1.tgz", "integrity": "sha1-Qmu52oQJDBg42BLIFQryCoMx4pY=", "dev": true}, "array-ify": {"version": "1.0.0", "resolved": "http://r.npm.sankuai.com/array-ify/download/array-ify-1.0.0.tgz", "integrity": "sha1-nlKHYrSpBmrRY6aWKjZEGOlibs4=", "dev": true}, "array-union": {"version": "1.0.2", "resolved": "http://r.npm.sankuai.com/array-union/download/array-union-1.0.2.tgz", "integrity": "sha1-mjRBDk9OPaI96jdb5b5w8kd47Dk=", "dev": true, "requires": {"array-uniq": "^1.0.1"}}, "array-uniq": {"version": "1.0.3", "resolved": "http://r.npm.sankuai.com/array-uniq/download/array-uniq-1.0.3.tgz", "integrity": "sha1-r2rId6Jcx/dOBYiUdThY39sk/bY=", "dev": true}, "array-unique": {"version": "0.3.2", "resolved": "https://registry.npmjs.org/array-unique/-/array-unique-0.3.2.tgz", "integrity": "sha1-qJS3XUvE9s1nnvMkSp/Y9Gri1Cg=", "dev": true}, "arrify": {"version": "1.0.1", "resolved": "http://r.npm.sankuai.com/arrify/download/arrify-1.0.1.tgz", "integrity": "sha1-iYUI2iIm84DfkEcoRWhJwVAaSw0=", "dev": true}, "asn1": {"version": "0.2.4", "resolved": "https://registry.npm.taobao.org/asn1/download/asn1-0.2.4.tgz", "integrity": "sha1-jSR136tVO7M+d7VOWeiAu4ziMTY=", "requires": {"safer-buffer": "~2.1.0"}}, "asn1.js": {"version": "4.10.1", "resolved": "https://registry.npmjs.org/asn1.js/-/asn1.js-4.10.1.tgz", "integrity": "sha512-p32cOF5q0Zqs9uBiONKYLm6BClCoBCM5O9JfeUSlnQLBTxYdTK+pW+nXflm8UkKd2UYlEbYz5qEi0JuZR9ckSw==", "dev": true, "requires": {"bn.js": "^4.0.0", "inherits": "^2.0.1", "minimalistic-assert": "^1.0.0"}}, "assert": {"version": "1.4.1", "resolved": "https://registry.npmjs.org/assert/-/assert-1.4.1.tgz", "integrity": "sha1-mZEtWRg2tab1s0XA8H7vwI/GXZE=", "dev": true, "requires": {"util": "0.10.3"}, "dependencies": {"inherits": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/inherits/-/inherits-2.0.1.tgz", "integrity": "sha1-sX0I0ya0Qj5Wjv9xn5GwscvfafE=", "dev": true}, "util": {"version": "0.10.3", "resolved": "https://registry.npmjs.org/util/-/util-0.10.3.tgz", "integrity": "sha1-evsa/lCAUkZInj23/g7TeTNqwPk=", "dev": true, "requires": {"inherits": "2.0.1"}}}}, "assert-plus": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/assert-plus/-/assert-plus-1.0.0.tgz", "integrity": "sha1-8S4PPF13sLHN2RRpQuTpbB5N1SU="}, "assign-symbols": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/assign-symbols/-/assign-symbols-1.0.0.tgz", "integrity": "sha1-WWZ/QfrdTyDMvCu5a41Pf3jsA2c=", "dev": true}, "async": {"version": "1.5.2", "resolved": "https://registry.npmjs.org/async/-/async-1.5.2.tgz", "integrity": "sha1-7GphrlZIDAw8skHJVhjiCJL5Zyo=", "dev": true}, "async-each": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/async-each/-/async-each-1.0.1.tgz", "integrity": "sha1-GdOGodntxufByF04iu28xW0zYC0=", "dev": true}, "async-foreach": {"version": "0.1.3", "resolved": "https://registry.npmjs.org/async-foreach/-/async-foreach-0.1.3.tgz", "integrity": "sha1-NhIfhFwFeBct5Bmpfb6x0W7DRUI=", "dev": true}, "async-validator": {"version": "1.6.8", "resolved": "http://r.npm.sankuai.com/async-validator/download/async-validator-1.6.8.tgz", "integrity": "sha1-+6qpACtBBm/fO6IdikyosRea02s="}, "asynckit": {"version": "0.4.0", "resolved": "https://registry.npmjs.org/asynckit/-/asynckit-0.4.0.tgz", "integrity": "sha1-x57Zf380y48robyXkLzDZkdLS3k="}, "atob": {"version": "2.1.2", "resolved": "https://registry.npmjs.org/atob/-/atob-2.1.2.tgz", "integrity": "sha512-Wm6ukoaOGJi/73p/cl2GvLjTI5JM1k/O14isD73YML8StrH/7/lRFgmg8nICZgD3bZZvjwCGxtMOD3wWNAu8cg==", "dev": true}, "autoprefixer": {"version": "9.1.5", "resolved": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-9.1.5.tgz", "integrity": "sha512-kk4Zb6RUc58ld7gdosERHMF3DzIYJc2fp5sX46qEsGXQQy5bXsu8qyLjoxuY1NuQ/cJuCYnx99BfjwnRggrYIw==", "dev": true, "requires": {"browserslist": "^4.1.0", "caniuse-lite": "^1.0.30000884", "normalize-range": "^0.1.2", "num2fraction": "^1.2.2", "postcss": "^7.0.2", "postcss-value-parser": "^3.2.3"}, "dependencies": {"postcss": {"version": "7.0.2", "resolved": "https://registry.npmjs.org/postcss/-/postcss-7.0.2.tgz", "integrity": "sha512-fmaUY5370keLUTx+CnwRxtGiuFTcNBLQBqr1oE3WZ/euIYmGAo0OAgOhVJ3ByDnVmOR3PK+0V9VebzfjRIUcqw==", "dev": true, "requires": {"chalk": "^2.4.1", "source-map": "^0.6.1", "supports-color": "^5.4.0"}}, "source-map": {"version": "0.6.1", "resolved": "https://registry.npmjs.org/source-map/-/source-map-0.6.1.tgz", "integrity": "sha512-UjgapumWlbMhkBgzT7Ykc5YXUT46F0iKu8SGXq0bcwP5dz/h0Plj6enJqjz1Zbq2l5WaqYnrVbwWOWMyF3F47g==", "dev": true}}}, "aws-sign2": {"version": "0.7.0", "resolved": "https://registry.npmjs.org/aws-sign2/-/aws-sign2-0.7.0.tgz", "integrity": "sha1-tG6JCTSpWR8tL2+G1+ap8bP+dqg="}, "aws4": {"version": "1.10.1", "resolved": "https://registry.npm.taobao.org/aws4/download/aws4-1.10.1.tgz", "integrity": "sha1-4eguTz6Zniz9YbFhKA0WoRH4ZCg="}, "axios": {"version": "0.18.0", "resolved": "http://registry.npmjs.org/axios/-/axios-0.18.0.tgz", "integrity": "sha1-MtU+SFHv3AoRmTts0AB4nXDAUQI=", "requires": {"follow-redirects": "^1.3.0", "is-buffer": "^1.1.5"}}, "babel-code-frame": {"version": "6.26.0", "resolved": "https://registry.npmjs.org/babel-code-frame/-/babel-code-frame-6.26.0.tgz", "integrity": "sha1-Y/1D99weO7fONZR9uP42mj9Yx0s=", "dev": true, "requires": {"chalk": "^1.1.3", "esutils": "^2.0.2", "js-tokens": "^3.0.2"}, "dependencies": {"ansi-regex": {"version": "2.1.1", "resolved": "https://registry.npmjs.org/ansi-regex/-/ansi-regex-2.1.1.tgz", "integrity": "sha1-w7M6te42DYbg5ijwRorn7yfWVN8=", "dev": true}, "ansi-styles": {"version": "2.2.1", "resolved": "https://registry.npmjs.org/ansi-styles/-/ansi-styles-2.2.1.tgz", "integrity": "sha1-tDLdM1i2NM914eRmQ2gkBTPB3b4=", "dev": true}, "chalk": {"version": "1.1.3", "resolved": "http://registry.npmjs.org/chalk/-/chalk-1.1.3.tgz", "integrity": "sha1-qBFcVeSnAv5NFQq9OHKCKn4J/Jg=", "dev": true, "requires": {"ansi-styles": "^2.2.1", "escape-string-regexp": "^1.0.2", "has-ansi": "^2.0.0", "strip-ansi": "^3.0.0", "supports-color": "^2.0.0"}}, "js-tokens": {"version": "3.0.2", "resolved": "https://registry.npmjs.org/js-tokens/-/js-tokens-3.0.2.tgz", "integrity": "sha1-mGbfOVECEw449/mWvOtlRDIJwls=", "dev": true}, "strip-ansi": {"version": "3.0.1", "resolved": "https://registry.npmjs.org/strip-ansi/-/strip-ansi-3.0.1.tgz", "integrity": "sha1-ajhfuIU9lS1f8F0Oiq+UJ43GPc8=", "dev": true, "requires": {"ansi-regex": "^2.0.0"}}, "supports-color": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/supports-color/-/supports-color-2.0.0.tgz", "integrity": "sha1-U10EXOa2Nj+kARcIRimZXp3zJMc=", "dev": true}}}, "babel-eslint": {"version": "9.0.0", "resolved": "http://r.npm.sankuai.com/babel-eslint/download/babel-eslint-9.0.0.tgz", "integrity": "sha1-fZRF+B7Z9gr/OBFfg4lw358rYiA=", "dev": true, "requires": {"@babel/code-frame": "^7.0.0", "@babel/parser": "^7.0.0", "@babel/traverse": "^7.0.0", "@babel/types": "^7.0.0", "eslint-scope": "3.7.1", "eslint-visitor-keys": "^1.0.0"}, "dependencies": {"eslint-scope": {"version": "3.7.1", "resolved": "http://r.npm.sankuai.com/eslint-scope/download/eslint-scope-3.7.1.tgz", "integrity": "sha1-PWPD7f2gLgbgGkUq2IyqzHzctug=", "dev": true, "requires": {"esrecurse": "^4.1.0", "estraverse": "^4.1.1"}}}}, "babel-helper-vue-jsx-merge-props": {"version": "2.0.3", "resolved": "https://registry.npmjs.org/babel-helper-vue-jsx-merge-props/-/babel-helper-vue-jsx-merge-props-2.0.3.tgz", "integrity": "sha512-gsLiKK7Qrb7zYJNgiXKpXblxbV5ffSwR0f5whkPAaBAR4fhi6bwRZxX9wBlIc5M/v8CCkXUbXZL4N/nSE97cqg=="}, "babel-loader": {"version": "8.0.2", "resolved": "https://registry.npmjs.org/babel-loader/-/babel-loader-8.0.2.tgz", "integrity": "sha512-Law0PGtRV1JL8Y9Wpzc0d6EE0GD7LzXWCfaeWwboUMcBWNG6gvaWTK1/+BK7a4X5EmeJiGEuDDFxUsOa8RSWCw==", "dev": true, "requires": {"find-cache-dir": "^1.0.0", "loader-utils": "^1.0.2", "mkdirp": "^0.5.1", "util.promisify": "^1.0.0"}}, "babel-plugin-transform-vue-jsx": {"version": "4.0.1", "resolved": "https://registry.npmjs.org/babel-plugin-transform-vue-jsx/-/babel-plugin-transform-vue-jsx-4.0.1.tgz", "integrity": "sha512-wbOz7ITB5cloLSjKUU1hWn8zhR+Dwah/RZiTiJY/CQliCwhowmzu6m7NEF+y5EJX/blDzGjRtZvC10Vdb3Q7vw==", "dev": true, "requires": {"esutils": "^2.0.2"}}, "babel-polyfill": {"version": "6.26.0", "resolved": "http://r.npm.sankuai.com/babel-polyfill/download/babel-polyfill-6.26.0.tgz", "integrity": "sha1-N5k3q8Z9eJWXCtxiHyhM2WbPIVM=", "dev": true, "requires": {"babel-runtime": "^6.26.0", "core-js": "^2.5.0", "regenerator-runtime": "^0.10.5"}, "dependencies": {"regenerator-runtime": {"version": "0.10.5", "resolved": "http://r.npm.sankuai.com/regenerator-runtime/download/regenerator-runtime-0.10.5.tgz", "integrity": "sha1-M2w+/BIgrc7dosn6tntaeVWjNlg=", "dev": true}}}, "babel-runtime": {"version": "6.26.0", "resolved": "http://r.npm.sankuai.com/babel-runtime/download/babel-runtime-6.26.0.tgz", "integrity": "sha1-llxwWGaOgrVde/4E/yM3vItWR/4=", "requires": {"core-js": "^2.4.0", "regenerator-runtime": "^0.11.0"}}, "balanced-match": {"version": "1.0.0", "resolved": "http://r.npm.sankuai.com/balanced-match/download/balanced-match-1.0.0.tgz", "integrity": "sha1-ibTRmasr7kneFk6gK4nORi1xt2c=", "dev": true}, "base": {"version": "0.11.2", "resolved": "https://registry.npmjs.org/base/-/base-0.11.2.tgz", "integrity": "sha512-5T6P4xPgpp0YDFvSWwEZ4NoE3aM4QBQXDzmVbraCkFj8zHM+mba8SyqB5DbZWyR7mYHo6Y7BdQo3MoA4m0TeQg==", "dev": true, "requires": {"cache-base": "^1.0.1", "class-utils": "^0.3.5", "component-emitter": "^1.2.1", "define-property": "^1.0.0", "isobject": "^3.0.1", "mixin-deep": "^1.2.0", "pascalcase": "^0.1.1"}, "dependencies": {"define-property": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/define-property/-/define-property-1.0.0.tgz", "integrity": "sha1-dp66rz9KY6rTr56NMEybvnm/sOY=", "dev": true, "requires": {"is-descriptor": "^1.0.0"}}, "is-accessor-descriptor": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/is-accessor-descriptor/-/is-accessor-descriptor-1.0.0.tgz", "integrity": "sha512-m5hnHTkcVsPfqx3AKlyttIPb7J+XykHvJP2B9bZDjlhLIoEq4XoK64Vg7boZlVWYK6LUY94dYPEE7Lh0ZkZKcQ==", "dev": true, "requires": {"kind-of": "^6.0.0"}}, "is-data-descriptor": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/is-data-descriptor/-/is-data-descriptor-1.0.0.tgz", "integrity": "sha512-jbRXy1FmtAoCjQkVmIVYwuuqDFUbaOeDjmed1tOGPrsMhtJA4rD9tkgA0F1qJ3gRFRXcHYVkdeaP50Q5rE/jLQ==", "dev": true, "requires": {"kind-of": "^6.0.0"}}, "is-descriptor": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/is-descriptor/-/is-descriptor-1.0.2.tgz", "integrity": "sha512-2eis5WqQGV7peooDyLmNEPUrps9+SXX5c9pL3xEB+4e9HnGuDa7mB7kHxHw4CbqS9k1T2hOH3miL8n8WtiYVtg==", "dev": true, "requires": {"is-accessor-descriptor": "^1.0.0", "is-data-descriptor": "^1.0.0", "kind-of": "^6.0.2"}}}}, "base64-js": {"version": "1.3.0", "resolved": "https://registry.npmjs.org/base64-js/-/base64-js-1.3.0.tgz", "integrity": "sha512-ccav/yGvoa80BQDljCxsmmQ3Xvx60/UpBIij5QN21W3wBi/hhIC9OoO+KLpu9IJTS9j4DRVJ3aDDF9cMSoa2lw==", "dev": true}, "batch": {"version": "0.6.1", "resolved": "https://registry.npmjs.org/batch/-/batch-0.6.1.tgz", "integrity": "sha1-3DQxT05nkxgJP8dgJyUl+UvyXBY=", "dev": true}, "batch-processor": {"version": "1.0.0", "resolved": "http://r.npm.sankuai.com/batch-processor/download/batch-processor-1.0.0.tgz", "integrity": "sha1-dclcMrdI4IUNEMKxaPa9vpiRrOg="}, "bcrypt-pbkdf": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/bcrypt-pbkdf/-/bcrypt-pbkdf-1.0.2.tgz", "integrity": "sha1-pDAdOJtqQ/m2f/PKEaP2Y342Dp4=", "requires": {"tweetnacl": "^0.14.3"}}, "bee-proxy": {"version": "0.0.8", "resolved": "http://r.npm.sankuai.com/bee-proxy/download/bee-proxy-0.0.8.tgz", "integrity": "sha1-dCahAF+jFIMXozy7jBySSRatwlw=", "dev": true, "requires": {"chalk": "^2.4.1", "http-proxy": "^1.17.0", "require-uncached": "^1.0.3", "url": "^0.11.0", "valid-url": "^1.0.9"}}, "big.js": {"version": "3.2.0", "resolved": "https://registry.npmjs.org/big.js/-/big.js-3.2.0.tgz", "integrity": "sha512-+hN/Zh2D08Mx65pZ/4g5bsmNiZUuChDiQfTUQ7qJr4/kuopCr88xZsAXv6mBoZEsUI4OuGHlX59qE94K2mMW8Q==", "dev": true}, "binary-extensions": {"version": "1.11.0", "resolved": "https://registry.npmjs.org/binary-extensions/-/binary-extensions-1.11.0.tgz", "integrity": "sha1-RqoXUftqL5PuXmibsQh9SxTGwgU=", "dev": true}, "bindings": {"version": "1.5.0", "resolved": "http://r.npm.sankuai.com/bindings/download/bindings-1.5.0.tgz", "integrity": "sha1-EDU8npRTNLwFEabZCzj7x8nFBN8=", "requires": {"file-uri-to-path": "1.0.0"}}, "block-stream": {"version": "0.0.9", "resolved": "https://registry.npmjs.org/block-stream/-/block-stream-0.0.9.tgz", "integrity": "sha1-E+v+d4oDIFz+A3UUgeu0szAMEmo=", "dev": true, "requires": {"inherits": "~2.0.0"}}, "bluebird": {"version": "3.5.2", "resolved": "https://registry.npmjs.org/bluebird/-/bluebird-3.5.2.tgz", "integrity": "sha512-dhHTWMI7kMx5whMQntl7Vr9C6BvV10lFXDAasnqnrMYhXVCzzk6IO9Fo2L75jXHT07WrOngL1WDXOp+yYS91Yg==", "dev": true}, "bn.js": {"version": "4.11.8", "resolved": "https://registry.npmjs.org/bn.js/-/bn.js-4.11.8.tgz", "integrity": "sha512-ItfYfPLkWHUjckQCk8xC+LwxgK8NYcXywGigJgSwOP8Y2iyWT4f2vsZnoOXTTbo+o5yXmIUJ4gn5538SO5S3gA==", "dev": true}, "body-parser": {"version": "1.20.1", "resolved": "http://r.npm.sankuai.com/body-parser/download/body-parser-1.20.1.tgz", "integrity": "sha1-sYEqiRLBlc03Gj7l5m+qIzilxmg=", "requires": {"bytes": "3.1.2", "content-type": "~1.0.4", "debug": "2.6.9", "depd": "2.0.0", "destroy": "1.2.0", "http-errors": "2.0.0", "iconv-lite": "0.4.24", "on-finished": "2.4.1", "qs": "6.11.0", "raw-body": "2.5.1", "type-is": "~1.6.18", "unpipe": "1.0.0"}, "dependencies": {"bytes": {"version": "3.1.2", "resolved": "http://r.npm.sankuai.com/bytes/download/bytes-3.1.2.tgz", "integrity": "sha1-iwvuuYYFrfGxKPpDhkA8AJ4CIaU="}, "debug": {"version": "2.6.9", "resolved": "http://r.npm.sankuai.com/debug/download/debug-2.6.9.tgz", "integrity": "sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8=", "requires": {"ms": "2.0.0"}}, "depd": {"version": "2.0.0", "resolved": "http://r.npm.sankuai.com/depd/download/depd-2.0.0.tgz", "integrity": "sha1-tpYWPMdXVg0JzyLMj60Vcbeedt8="}, "http-errors": {"version": "2.0.0", "resolved": "http://r.npm.sankuai.com/http-errors/download/http-errors-2.0.0.tgz", "integrity": "sha1-t3dKFIbvc892Z6ya4IWMASxXudM=", "requires": {"depd": "2.0.0", "inherits": "2.0.4", "setprototypeof": "1.2.0", "statuses": "2.0.1", "toidentifier": "1.0.1"}}, "inherits": {"version": "2.0.4", "resolved": "http://r.npm.sankuai.com/inherits/download/inherits-2.0.4.tgz", "integrity": "sha1-D6LGT5MpF8NDOg3tVTY6rjdBa3w="}, "ms": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/ms/-/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g="}, "setprototypeof": {"version": "1.2.0", "resolved": "http://r.npm.sankuai.com/setprototypeof/download/setprototypeof-1.2.0.tgz", "integrity": "sha1-ZsmiSnP5/CjL5msJ/tPTPcrxtCQ="}, "statuses": {"version": "2.0.1", "resolved": "http://r.npm.sankuai.com/statuses/download/statuses-2.0.1.tgz", "integrity": "sha1-VcsADM8dSHKL0jxoWgY5mM8aG2M="}}}, "bonjour": {"version": "3.5.0", "resolved": "https://registry.npmjs.org/bonjour/-/bonjour-3.5.0.tgz", "integrity": "sha1-jokKGD2O6aI5OzhExpGkK897yfU=", "dev": true, "requires": {"array-flatten": "^2.1.0", "deep-equal": "^1.0.1", "dns-equal": "^1.0.0", "dns-txt": "^2.0.2", "multicast-dns": "^6.0.1", "multicast-dns-service-types": "^1.1.0"}}, "boolbase": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/boolbase/-/boolbase-1.0.0.tgz", "integrity": "sha1-aN/1++YMUes3cl6p4+0xDcwed24=", "dev": true}, "brace-expansion": {"version": "1.1.11", "resolved": "http://r.npm.sankuai.com/brace-expansion/download/brace-expansion-1.1.11.tgz", "integrity": "sha1-PH/L9SnYcibz0vUrlm/1Jx60Qd0=", "dev": true, "requires": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}}, "braces": {"version": "2.3.2", "resolved": "https://registry.npmjs.org/braces/-/braces-2.3.2.tgz", "integrity": "sha512-aNdbnj9P8PjdXU4ybaWLK2IF3jc/EoDYbC7AazW6to3TRsfXxscC9UXOB5iDiEQrkyIbWp2SLQda4+QAa7nc3w==", "dev": true, "requires": {"arr-flatten": "^1.1.0", "array-unique": "^0.3.2", "extend-shallow": "^2.0.1", "fill-range": "^4.0.0", "isobject": "^3.0.1", "repeat-element": "^1.1.2", "snapdragon": "^0.8.1", "snapdragon-node": "^2.0.1", "split-string": "^3.0.2", "to-regex": "^3.0.1"}, "dependencies": {"extend-shallow": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/extend-shallow/-/extend-shallow-2.0.1.tgz", "integrity": "sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8=", "dev": true, "requires": {"is-extendable": "^0.1.0"}}}}, "brorand": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/brorand/-/brorand-1.1.0.tgz", "integrity": "sha1-EsJe/kCkXjwyPrhnWgoM5XsiNx8=", "dev": true}, "browserify-aes": {"version": "1.2.0", "resolved": "http://registry.npmjs.org/browserify-aes/-/browserify-aes-1.2.0.tgz", "integrity": "sha512-+7CHXqGuspUn/Sl5aO7Ea0xWGAtETPXNSAjHo48JfLdPWcMng33Xe4znFvQweqc/uzk5zSOI3H52CYnjCfb5hA==", "dev": true, "requires": {"buffer-xor": "^1.0.3", "cipher-base": "^1.0.0", "create-hash": "^1.1.0", "evp_bytestokey": "^1.0.3", "inherits": "^2.0.1", "safe-buffer": "^5.0.1"}}, "browserify-cipher": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/browserify-cipher/-/browserify-cipher-1.0.1.tgz", "integrity": "sha512-sPhkz0ARKbf4rRQt2hTpAHqn47X3llLkUGn+xEJzLjwY8LRs2p0v7ljvI5EyoRO/mexrNunNECisZs+gw2zz1w==", "dev": true, "requires": {"browserify-aes": "^1.0.4", "browserify-des": "^1.0.0", "evp_bytestokey": "^1.0.0"}}, "browserify-des": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/browserify-des/-/browserify-des-1.0.2.tgz", "integrity": "sha512-BioO1xf3hFwz4kc6iBhI3ieDFompMhrMlnDFC4/0/vd5MokpuAc3R+LYbwTA9A5Yc9pq9UYPqffKpW2ObuwX5A==", "dev": true, "requires": {"cipher-base": "^1.0.1", "des.js": "^1.0.0", "inherits": "^2.0.1", "safe-buffer": "^5.1.2"}}, "browserify-rsa": {"version": "4.0.1", "resolved": "http://registry.npmjs.org/browserify-rsa/-/browserify-rsa-4.0.1.tgz", "integrity": "sha1-IeCr+vbyApzy+vsTNWenAdQTVSQ=", "dev": true, "requires": {"bn.js": "^4.1.0", "randombytes": "^2.0.1"}}, "browserify-sign": {"version": "4.0.4", "resolved": "https://registry.npmjs.org/browserify-sign/-/browserify-sign-4.0.4.tgz", "integrity": "sha1-qk62jl17ZYuqa/alfmMMvXqT0pg=", "dev": true, "requires": {"bn.js": "^4.1.1", "browserify-rsa": "^4.0.0", "create-hash": "^1.1.0", "create-hmac": "^1.1.2", "elliptic": "^6.0.0", "inherits": "^2.0.1", "parse-asn1": "^5.0.0"}}, "browserify-zlib": {"version": "0.2.0", "resolved": "https://registry.npmjs.org/browserify-zlib/-/browserify-zlib-0.2.0.tgz", "integrity": "sha512-Z942RysHXmJrhqk88FmKBVq/v5tqmSkDz7p54G/MGyjMnCFFnC79XWNbg+Vta8W6Wb2qtSZTSxIGkJrRpCFEiA==", "dev": true, "requires": {"pako": "~1.0.5"}}, "browserslist": {"version": "4.1.1", "resolved": "https://registry.npmjs.org/browserslist/-/browserslist-4.1.1.tgz", "integrity": "sha512-VBorw+tgpOtZ1BYhrVSVTzTt/3+vSE3eFUh0N2GCFK1HffceOaf32YS/bs6WiFhjDAblAFrx85jMy3BG9fBK2Q==", "dev": true, "requires": {"caniuse-lite": "^1.0.30000884", "electron-to-chromium": "^1.3.62", "node-releases": "^1.0.0-alpha.11"}}, "buffer": {"version": "4.9.1", "resolved": "http://registry.npmjs.org/buffer/-/buffer-4.9.1.tgz", "integrity": "sha1-bRu2AbB6TvztlwlBMgkwJ8lbwpg=", "dev": true, "requires": {"base64-js": "^1.0.2", "ieee754": "^1.1.4", "isarray": "^1.0.0"}}, "buffer-from": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/buffer-from/-/buffer-from-1.1.1.tgz", "integrity": "sha512-MQcXEUbCKtEo7bhqEs6560Hyd4XaovZlO/k9V3hjVUF/zwW7KBVdSK4gIt/bzwS9MbR5qob+F5jusZsb0YQK2A==", "dev": true}, "buffer-indexof": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/buffer-indexof/-/buffer-indexof-1.1.1.tgz", "integrity": "sha512-4/rOEg86jivtPTeOUUT61jJO1Ya1TrR/OkqCSZDyq84WJh3LuuiphBYJN+fm5xufIk4XAFcEwte/8WzC8If/1g==", "dev": true}, "buffer-xor": {"version": "1.0.3", "resolved": "https://registry.npmjs.org/buffer-xor/-/buffer-xor-1.0.3.tgz", "integrity": "sha1-JuYe0UIvtw3ULm42cp7VHYVf6Nk=", "dev": true}, "builtin-modules": {"version": "1.1.1", "resolved": "http://r.npm.sankuai.com/builtin-modules/download/builtin-modules-1.1.1.tgz", "integrity": "sha1-Jw8HbFpywC9bZaR9+Uxf46J4iS8=", "dev": true}, "builtin-status-codes": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/builtin-status-codes/-/builtin-status-codes-3.0.0.tgz", "integrity": "sha1-hZgoeOIbmOHGZCXgPQF0eI9Wnug=", "dev": true}, "bytes": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/bytes/-/bytes-3.0.0.tgz", "integrity": "sha1-0ygVQE1olpn4Wk6k+odV3ROpYEg=", "dev": true}, "cacache": {"version": "10.0.4", "resolved": "https://registry.npmjs.org/cacache/-/cacache-10.0.4.tgz", "integrity": "sha512-Dph0MzuH+rTQzGPNT9fAnrPmMmjKfST6trxJeK7NQuHRaVw24VzPRWTmg9MpcwOVQZO0E1FBICUlFeNaKPIfHA==", "dev": true, "requires": {"bluebird": "^3.5.1", "chownr": "^1.0.1", "glob": "^7.1.2", "graceful-fs": "^4.1.11", "lru-cache": "^4.1.1", "mississippi": "^2.0.0", "mkdirp": "^0.5.1", "move-concurrently": "^1.0.1", "promise-inflight": "^1.0.1", "rimraf": "^2.6.2", "ssri": "^5.2.4", "unique-filename": "^1.1.0", "y18n": "^4.0.0"}}, "cache-base": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/cache-base/-/cache-base-1.0.1.tgz", "integrity": "sha512-AKcdTnFSWATd5/GCPRxr2ChwIJ85CeyrEyjRHlKxQ56d4XJMGym0uAiKn0xbLOGOl3+yRpOTi484dVCEc5AUzQ==", "dev": true, "requires": {"collection-visit": "^1.0.0", "component-emitter": "^1.2.1", "get-value": "^2.0.6", "has-value": "^1.0.0", "isobject": "^3.0.1", "set-value": "^2.0.0", "to-object-path": "^0.3.0", "union-value": "^1.0.0", "unset-value": "^1.0.0"}}, "call-bind": {"version": "1.0.2", "resolved": "http://r.npm.sankuai.com/call-bind/download/call-bind-1.0.2.tgz", "integrity": "sha1-sdTonmiBGcPJqQOtMKuy9qkZvjw=", "requires": {"function-bind": "^1.1.1", "get-intrinsic": "^1.0.2"}}, "caller-callsite": {"version": "2.0.0", "resolved": "http://r.npm.sankuai.com/caller-callsite/download/caller-callsite-2.0.0.tgz", "integrity": "sha1-hH4PzgoiN1CpoCfFSzNzGtMVQTQ=", "dev": true, "requires": {"callsites": "^2.0.0"}, "dependencies": {"callsites": {"version": "2.0.0", "resolved": "http://r.npm.sankuai.com/callsites/download/callsites-2.0.0.tgz", "integrity": "sha1-BuuE8A7qQT2oav/vrL/7Ngk7PFA=", "dev": true}}}, "caller-path": {"version": "0.1.0", "resolved": "http://r.npm.sankuai.com/caller-path/download/caller-path-0.1.0.tgz", "integrity": "sha1-lAhe9jWB7NPaqSREqP6U6CV3dR8=", "dev": true, "requires": {"callsites": "^0.2.0"}}, "callsites": {"version": "0.2.0", "resolved": "http://r.npm.sankuai.com/callsites/download/callsites-0.2.0.tgz", "integrity": "sha1-r6uWJikQp/M8GaV3WCXGnzTjUMo=", "dev": true}, "camel-case": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/camel-case/-/camel-case-3.0.0.tgz", "integrity": "sha1-yjw2iKTpzzpM2nd9xNy8cTJJz3M=", "dev": true, "requires": {"no-case": "^2.2.0", "upper-case": "^1.1.1"}}, "camelcase": {"version": "4.1.0", "resolved": "https://registry.npmjs.org/camelcase/-/camelcase-4.1.0.tgz", "integrity": "sha1-1UVjW+HjPFQmScaRc+Xeas+uNN0=", "dev": true}, "camelcase-keys": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/camelcase-keys/-/camelcase-keys-2.1.0.tgz", "integrity": "sha1-MIvur/3ygRkFHvodkyITyRuPkuc=", "dev": true, "requires": {"camelcase": "^2.0.0", "map-obj": "^1.0.0"}, "dependencies": {"camelcase": {"version": "2.1.1", "resolved": "https://registry.npmjs.org/camelcase/-/camelcase-2.1.1.tgz", "integrity": "sha1-fB0W1nmhu+WcoCys7PsBHiAfWh8=", "dev": true}}}, "caniuse-api": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/caniuse-api/-/caniuse-api-3.0.0.tgz", "integrity": "sha512-bsTwuIg/BZZK/vreVTYYbSWoe2F+71P7K5QGEX+pT250DZbfU1MQ5prOKpPR+LL6uWKK3KMwMCAS74QB3Um1uw==", "dev": true, "requires": {"browserslist": "^4.0.0", "caniuse-lite": "^1.0.0", "lodash.memoize": "^4.1.2", "lodash.uniq": "^4.5.0"}}, "caniuse-lite": {"version": "1.0.30000885", "resolved": "https://registry.npmjs.org/caniuse-lite/-/caniuse-lite-1.0.30000885.tgz", "integrity": "sha512-cXKbYwpxBLd7qHyej16JazPoUacqoVuDhvR61U7Fr5vSxMUiodzcYa1rQYRYfZ5GexV03vGZHd722vNPLjPJGQ==", "dev": true}, "caseless": {"version": "0.12.0", "resolved": "https://registry.npmjs.org/caseless/-/caseless-0.12.0.tgz", "integrity": "sha1-G2gcIf+EAzyCZUMJBolCDRhxUdw="}, "chalk": {"version": "2.4.1", "resolved": "http://r.npm.sankuai.com/chalk/download/chalk-2.4.1.tgz", "integrity": "sha1-GMSasWoDe26wFSzIPjRxM4IVtm4=", "dev": true, "requires": {"ansi-styles": "^3.2.1", "escape-string-regexp": "^1.0.5", "supports-color": "^5.3.0"}}, "changelog-parser": {"version": "2.8.1", "resolved": "http://r.npm.sankuai.com/changelog-parser/download/changelog-parser-2.8.1.tgz", "integrity": "sha1-FCiZjCdeT3wKhVAm3GDGbN42u4c=", "requires": {"line-reader": "^0.2.4", "remove-markdown": "^0.2.2"}}, "chardet": {"version": "0.7.0", "resolved": "http://r.npm.sankuai.com/chardet/download/chardet-0.7.0.tgz", "integrity": "sha1-kAlISfCTfy7twkJdDSip5fDLrZ4="}, "chokidar": {"version": "2.0.4", "resolved": "https://registry.npmjs.org/chokidar/-/chokidar-2.0.4.tgz", "integrity": "sha512-z9n7yt9rOvIJrMhvDtDictKrkFHeihkNl6uWMmZlmL6tJtX9Cs+87oK+teBx+JIgzvbX3yZHT3eF8vpbDxHJXQ==", "dev": true, "requires": {"anymatch": "^2.0.0", "async-each": "^1.0.0", "braces": "^2.3.0", "fsevents": "^1.2.2", "glob-parent": "^3.1.0", "inherits": "^2.0.1", "is-binary-path": "^1.0.0", "is-glob": "^4.0.0", "lodash.debounce": "^4.0.8", "normalize-path": "^2.1.1", "path-is-absolute": "^1.0.0", "readdirp": "^2.0.0", "upath": "^1.0.5"}}, "chownr": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/chownr/-/chownr-1.0.1.tgz", "integrity": "sha1-4qdQQqlVGQi+vSW4Uj1fl2nXkYE=", "dev": true}, "chrome-trace-event": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/chrome-trace-event/-/chrome-trace-event-1.0.0.tgz", "integrity": "sha512-xDbVgyfDTT2piup/h8dK/y4QZfJRSa73bw1WZ8b4XM1o7fsFubUVGYcE+1ANtOzJJELGpYoG2961z0Z6OAld9A==", "dev": true, "requires": {"tslib": "^1.9.0"}}, "ci-info": {"version": "2.0.0", "resolved": "http://r.npm.sankuai.com/ci-info/download/ci-info-2.0.0.tgz", "integrity": "sha1-Z6npZL4xpR4V5QENWObxKDQAL0Y=", "dev": true}, "cipher-base": {"version": "1.0.4", "resolved": "https://registry.npmjs.org/cipher-base/-/cipher-base-1.0.4.tgz", "integrity": "sha512-Kkht5ye6ZGmwv40uUDZztayT2ThLQGfnj/T71N/XzeZeo3nf8foyW7zGTsPYkEya3m5f3cAypH+qe7YOrM1U2Q==", "dev": true, "requires": {"inherits": "^2.0.1", "safe-buffer": "^5.0.1"}}, "circular-json": {"version": "0.3.3", "resolved": "http://r.npm.sankuai.com/circular-json/download/circular-json-0.3.3.tgz", "integrity": "sha1-gVyZ6oT2gJUp0vRXkb34JxE1LWY=", "dev": true}, "class-utils": {"version": "0.3.6", "resolved": "https://registry.npmjs.org/class-utils/-/class-utils-0.3.6.tgz", "integrity": "sha512-qOhPa/Fj7s6TY8H8esGu5QNpMMQxz79h+urzrNYN6mn+9BnxlDGf5QZ+XeCDsxSjPqsSR56XOZOJmpeurnLMeg==", "dev": true, "requires": {"arr-union": "^3.1.0", "define-property": "^0.2.5", "isobject": "^3.0.0", "static-extend": "^0.1.1"}, "dependencies": {"define-property": {"version": "0.2.5", "resolved": "https://registry.npmjs.org/define-property/-/define-property-0.2.5.tgz", "integrity": "sha1-w1se+RjsPJkPmlvFe+BKrOxcgRY=", "dev": true, "requires": {"is-descriptor": "^0.1.0"}}}}, "clean-css": {"version": "4.2.1", "resolved": "https://registry.npmjs.org/clean-css/-/clean-css-4.2.1.tgz", "integrity": "sha512-4ZxI6dy4lrY6FHzfiy1aEOXgu4LIsW2MhwG0VBKdcoGoH/XLFgaHSdLTGr4O8Be6A8r3MOphEiI8Gc1n0ecf3g==", "dev": true, "requires": {"source-map": "~0.6.0"}, "dependencies": {"source-map": {"version": "0.6.1", "resolved": "https://registry.npmjs.org/source-map/-/source-map-0.6.1.tgz", "integrity": "sha512-UjgapumWlbMhkBgzT7Ykc5YXUT46F0iKu8SGXq0bcwP5dz/h0Plj6enJqjz1Zbq2l5WaqYnrVbwWOWMyF3F47g==", "dev": true}}}, "cli-cursor": {"version": "2.1.0", "resolved": "http://r.npm.sankuai.com/cli-cursor/download/cli-cursor-2.1.0.tgz", "integrity": "sha1-s12sN2R5+sw+lHR9QdDQ9SOP/LU=", "dev": true, "requires": {"restore-cursor": "^2.0.0"}}, "cli-width": {"version": "2.2.0", "resolved": "http://r.npm.sankuai.com/cli-width/download/cli-width-2.2.0.tgz", "integrity": "sha1-/xnt6Kml5XkyQUewwR8PvLq+1jk=", "dev": true}, "cliui": {"version": "4.1.0", "resolved": "https://registry.npmjs.org/cliui/-/cliui-4.1.0.tgz", "integrity": "sha512-4FG+RSG9DL7uEwRUZXZn3SS34DiDPfzP0VOiEwtUWlE+AR2EIg+hSyvrIgUUfhdgR/UkAeW2QHgeP+hWrXs7jQ==", "dev": true, "requires": {"string-width": "^2.1.1", "strip-ansi": "^4.0.0", "wrap-ansi": "^2.0.0"}}, "clone": {"version": "2.1.2", "resolved": "http://r.npm.sankuai.com/clone/download/clone-2.1.2.tgz", "integrity": "sha1-G39Ln1kfHo+DZwQBYANFoCiHQ18="}, "clone-deep": {"version": "2.0.2", "resolved": "https://registry.npmjs.org/clone-deep/-/clone-deep-2.0.2.tgz", "integrity": "sha512-SZegPTKjCgpQH63E+eN6mVEEPdQBOUzjyJm5Pora4lrwWRFS8I0QAxV/KD6vV/i0WuijHZWQC1fMsPEdxfdVCQ==", "dev": true, "requires": {"for-own": "^1.0.0", "is-plain-object": "^2.0.4", "kind-of": "^6.0.0", "shallow-clone": "^1.0.0"}}, "co-request": {"version": "1.0.0", "resolved": "http://r.npm.sankuai.com/co-request/download/co-request-1.0.0.tgz", "integrity": "sha1-jrX7ZWwu4eguNsTM/pN2hGQGsmA=", "requires": {"request": "*"}}, "coa": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/coa/-/coa-2.0.1.tgz", "integrity": "sha512-5wfTTO8E2/ja4jFSxePXlG5nRu5bBtL/r1HCIpJW/lzT6yDtKl0u0Z4o/Vpz32IpKmBn7HerheEZQgA9N2DarQ==", "dev": true, "requires": {"q": "^1.1.2"}}, "code-point-at": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/code-point-at/-/code-point-at-1.1.0.tgz", "integrity": "sha1-DQcLTQQ6W+ozovGkDi7bPZpMz3c=", "dev": true}, "collection-visit": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/collection-visit/-/collection-visit-1.0.0.tgz", "integrity": "sha1-S8A3PBZLwykbTTaMgpzxqApZ3KA=", "dev": true, "requires": {"map-visit": "^1.0.0", "object-visit": "^1.0.0"}}, "color": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/color/-/color-3.0.0.tgz", "integrity": "sha512-jCpd5+s0s0t7p3pHQKpnJ0TpQKKdleP71LWcA0aqiljpiuAkOSUFN/dyH8ZwF0hRmFlrIuRhufds1QyEP9EB+w==", "dev": true, "requires": {"color-convert": "^1.9.1", "color-string": "^1.5.2"}}, "color-convert": {"version": "1.9.3", "resolved": "http://r.npm.sankuai.com/color-convert/download/color-convert-1.9.3.tgz", "integrity": "sha1-u3GFBpDh8TZWfeYp0tVHHe2kweg=", "dev": true, "requires": {"color-name": "1.1.3"}}, "color-name": {"version": "1.1.3", "resolved": "http://r.npm.sankuai.com/color-name/download/color-name-1.1.3.tgz", "integrity": "sha1-p9BVi9icQveV3UIyj3QIMcpTvCU=", "dev": true}, "color-string": {"version": "1.5.3", "resolved": "https://registry.npmjs.org/color-string/-/color-string-1.5.3.tgz", "integrity": "sha512-dC2C5qeWoYkxki5UAXapdjqO672AM4vZuPGRQfO8b5HKuKGBbKWpITyDYN7TOFKvRW7kOgAn3746clDBMDJyQw==", "dev": true, "requires": {"color-name": "^1.0.0", "simple-swizzle": "^0.2.2"}}, "colors": {"version": "1.1.2", "resolved": "https://registry.npmjs.org/colors/-/colors-1.1.2.tgz", "integrity": "sha1-FopHAXVran9RoSzgyXv6KMCE7WM=", "dev": true}, "combined-stream": {"version": "1.0.8", "resolved": "https://registry.npm.taobao.org/combined-stream/download/combined-stream-1.0.8.tgz", "integrity": "sha1-w9RaizT9cwYxoRCoolIGgrMdWn8=", "requires": {"delayed-stream": "~1.0.0"}}, "commander": {"version": "2.13.0", "resolved": "https://registry.npmjs.org/commander/-/commander-2.13.0.tgz", "integrity": "sha512-MVuS359B+YzaWqjCL/c+22gfryv+mCBPHAv3zyVI2GN8EY6IRP8VwtasXn8jyyhvvq84R4ImN1OKRtcbIasjYA==", "dev": true}, "commondir": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/commondir/-/commondir-1.0.1.tgz", "integrity": "sha1-3dgA2gxmEnOTzKWVDqloo6rxJTs=", "dev": true}, "compare-func": {"version": "1.3.2", "resolved": "http://r.npm.sankuai.com/compare-func/download/compare-func-1.3.2.tgz", "integrity": "sha1-md0LpFfh+bxyKxLAjsM+6rMfpkg=", "dev": true, "requires": {"array-ify": "^1.0.0", "dot-prop": "^3.0.0"}, "dependencies": {"dot-prop": {"version": "3.0.0", "resolved": "http://r.npm.sankuai.com/dot-prop/download/dot-prop-3.0.0.tgz", "integrity": "sha1-G3CK8JSknJoOfbyteQq6U52sEXc=", "dev": true, "requires": {"is-obj": "^1.0.0"}}}}, "component-emitter": {"version": "1.2.1", "resolved": "https://registry.npmjs.org/component-emitter/-/component-emitter-1.2.1.tgz", "integrity": "sha1-E3kY1teCg/ffemt8WmPhQOaUJeY=", "dev": true}, "compressible": {"version": "2.0.14", "resolved": "https://registry.npmjs.org/compressible/-/compressible-2.0.14.tgz", "integrity": "sha1-MmxfUH+7BV9UEWeCuWmoG2einac=", "dev": true, "requires": {"mime-db": ">= 1.34.0 < 2"}}, "compression": {"version": "1.7.3", "resolved": "https://registry.npmjs.org/compression/-/compression-1.7.3.tgz", "integrity": "sha512-HSjyBG5N1Nnz7tF2+O7A9XUhyjru71/fwgNb7oIsEVHR0WShfs2tIS/EySLgiTe98aOK18YDlMXpzjCXY/n9mg==", "dev": true, "requires": {"accepts": "~1.3.5", "bytes": "3.0.0", "compressible": "~2.0.14", "debug": "2.6.9", "on-headers": "~1.0.1", "safe-buffer": "5.1.2", "vary": "~1.1.2"}, "dependencies": {"debug": {"version": "2.6.9", "resolved": "https://registry.npmjs.org/debug/-/debug-2.6.9.tgz", "integrity": "sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA==", "dev": true, "requires": {"ms": "2.0.0"}}, "ms": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/ms/-/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g=", "dev": true}}}, "concat-map": {"version": "0.0.1", "resolved": "http://r.npm.sankuai.com/concat-map/download/concat-map-0.0.1.tgz", "integrity": "sha1-2Klr13/Wjfd5OnMDajug1UBdR3s=", "dev": true}, "concat-stream": {"version": "1.6.2", "resolved": "https://registry.npmjs.org/concat-stream/-/concat-stream-1.6.2.tgz", "integrity": "sha512-27HBghJxjiZtIk3Ycvn/4kbJk/1uZuJFfuPEns6LaEvpvG1f0hTea8lilrouyo9mVc2GWdcEZ8OLoGmSADlrCw==", "dev": true, "requires": {"buffer-from": "^1.0.0", "inherits": "^2.0.3", "readable-stream": "^2.2.2", "typedarray": "^0.0.6"}}, "connect-history-api-fallback": {"version": "1.5.0", "resolved": "https://registry.npmjs.org/connect-history-api-fallback/-/connect-history-api-fallback-1.5.0.tgz", "integrity": "sha1-sGhzk0vF40T+9hGhlqb6rgruAVo=", "dev": true}, "console-browserify": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/console-browserify/-/console-browserify-1.1.0.tgz", "integrity": "sha1-8CQcRXMKn8YyOyBtvzjtx0HQuxA=", "dev": true, "requires": {"date-now": "^0.1.4"}}, "console-control-strings": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/console-control-strings/-/console-control-strings-1.1.0.tgz", "integrity": "sha1-PXz0Rk22RG6mRL9LOVB/mFEAjo4=", "dev": true}, "consolidate": {"version": "0.15.1", "resolved": "https://registry.npmjs.org/consolidate/-/consolidate-0.15.1.tgz", "integrity": "sha512-DW46nrsMJgy9kqAbPt5rKaCr7uFtpo4mSUvLHIUbJEjm0vo+aY5QLwBUq3FK4tRnJr/X0Psc0C4jf/h+HtXSMw==", "dev": true, "requires": {"bluebird": "^3.1.1"}}, "constants-browserify": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/constants-browserify/-/constants-browserify-1.0.0.tgz", "integrity": "sha1-wguW2MYXdIqvHBYCF2DNJ/y4y3U=", "dev": true}, "contains-path": {"version": "0.1.0", "resolved": "http://r.npm.sankuai.com/contains-path/download/contains-path-0.1.0.tgz", "integrity": "sha1-/ozxhP9mcLa67wGp1IYaXL7EEgo=", "dev": true}, "content-disposition": {"version": "0.5.4", "resolved": "http://r.npm.sankuai.com/content-disposition/download/content-disposition-0.5.4.tgz", "integrity": "sha1-i4K076yCUSoCuwsdzsnSxejrW/4=", "requires": {"safe-buffer": "5.2.1"}, "dependencies": {"safe-buffer": {"version": "5.2.1", "resolved": "http://r.npm.sankuai.com/safe-buffer/download/safe-buffer-5.2.1.tgz", "integrity": "sha1-Hq+fqb2x/dTsdfWPnNtOa3gn7sY="}}}, "content-type": {"version": "1.0.4", "resolved": "http://r.npm.sankuai.com/content-type/download/content-type-1.0.4.tgz", "integrity": "sha1-4TjMdeBAxyexlm/l5fjJruJW/js="}, "conventional-changelog-angular": {"version": "1.6.6", "resolved": "http://r.npm.sankuai.com/conventional-changelog-angular/download/conventional-changelog-angular-1.6.6.tgz", "integrity": "sha1-sn8rMVwW0KHyPrGBMJ0OakaY6g8=", "dev": true, "requires": {"compare-func": "^1.3.1", "q": "^1.5.1"}}, "conventional-commits-parser": {"version": "2.1.7", "resolved": "http://r.npm.sankuai.com/conventional-commits-parser/download/conventional-commits-parser-2.1.7.tgz", "integrity": "sha1-7KRe1hQNcrqXIu5BMmdNY55kTo4=", "dev": true, "requires": {"JSONStream": "^1.0.4", "is-text-path": "^1.0.0", "lodash": "^4.2.1", "meow": "^4.0.0", "split2": "^2.0.0", "through2": "^2.0.0", "trim-off-newlines": "^1.0.0"}, "dependencies": {"camelcase-keys": {"version": "4.2.0", "resolved": "http://r.npm.sankuai.com/camelcase-keys/download/camelcase-keys-4.2.0.tgz", "integrity": "sha1-oqpfsa9oh1glnDLBQUJteJI7m3c=", "dev": true, "requires": {"camelcase": "^4.1.0", "map-obj": "^2.0.0", "quick-lru": "^1.0.0"}}, "find-up": {"version": "2.1.0", "resolved": "http://r.npm.sankuai.com/find-up/download/find-up-2.1.0.tgz", "integrity": "sha1-RdG35QbHF93UgndaK3eSCjwMV6c=", "dev": true, "requires": {"locate-path": "^2.0.0"}}, "indent-string": {"version": "3.2.0", "resolved": "http://r.npm.sankuai.com/indent-string/download/indent-string-3.2.0.tgz", "integrity": "sha1-Sl/W0nzDMvN+VBmlBNu4NxBckok=", "dev": true}, "load-json-file": {"version": "4.0.0", "resolved": "http://r.npm.sankuai.com/load-json-file/download/load-json-file-4.0.0.tgz", "integrity": "sha1-L19Fq5HjMhYjT9U62rZo607AmTs=", "dev": true, "requires": {"graceful-fs": "^4.1.2", "parse-json": "^4.0.0", "pify": "^3.0.0", "strip-bom": "^3.0.0"}}, "map-obj": {"version": "2.0.0", "resolved": "http://r.npm.sankuai.com/map-obj/download/map-obj-2.0.0.tgz", "integrity": "sha1-plzSkIepJZi4eRJXpSPgISIqwfk=", "dev": true}, "meow": {"version": "4.0.1", "resolved": "http://r.npm.sankuai.com/meow/download/meow-4.0.1.tgz", "integrity": "sha1-1IWY9vSxRy81v2MXqVlFrONH+XU=", "dev": true, "requires": {"camelcase-keys": "^4.0.0", "decamelize-keys": "^1.0.0", "loud-rejection": "^1.0.0", "minimist": "^1.1.3", "minimist-options": "^3.0.1", "normalize-package-data": "^2.3.4", "read-pkg-up": "^3.0.0", "redent": "^2.0.0", "trim-newlines": "^2.0.0"}}, "minimist": {"version": "1.2.0", "resolved": "http://r.npm.sankuai.com/minimist/download/minimist-1.2.0.tgz", "integrity": "sha1-o1AIsg9BOD7sH7kU9M1d95omQoQ=", "dev": true}, "parse-json": {"version": "4.0.0", "resolved": "http://r.npm.sankuai.com/parse-json/download/parse-json-4.0.0.tgz", "integrity": "sha1-vjX1Qlvh9/bHRxhPmKeIy5lHfuA=", "dev": true, "requires": {"error-ex": "^1.3.1", "json-parse-better-errors": "^1.0.1"}}, "path-type": {"version": "3.0.0", "resolved": "http://r.npm.sankuai.com/path-type/download/path-type-3.0.0.tgz", "integrity": "sha1-zvMdyOCho7sNEFwM2Xzzv0f0428=", "dev": true, "requires": {"pify": "^3.0.0"}}, "pify": {"version": "3.0.0", "resolved": "http://r.npm.sankuai.com/pify/download/pify-3.0.0.tgz", "integrity": "sha1-5aSs0sEB/fPZpNB/DbxNtJ3SgXY=", "dev": true}, "read-pkg": {"version": "3.0.0", "resolved": "http://r.npm.sankuai.com/read-pkg/download/read-pkg-3.0.0.tgz", "integrity": "sha1-nLxoaXj+5l0WwA4rGcI3/Pbjg4k=", "dev": true, "requires": {"load-json-file": "^4.0.0", "normalize-package-data": "^2.3.2", "path-type": "^3.0.0"}}, "read-pkg-up": {"version": "3.0.0", "resolved": "http://r.npm.sankuai.com/read-pkg-up/download/read-pkg-up-3.0.0.tgz", "integrity": "sha1-PtSWaF26D4/hGNBpHcUfSh/5bwc=", "dev": true, "requires": {"find-up": "^2.0.0", "read-pkg": "^3.0.0"}}, "redent": {"version": "2.0.0", "resolved": "http://r.npm.sankuai.com/redent/download/redent-2.0.0.tgz", "integrity": "sha1-wbIAe0LVfrE4kHmzyDM2OdXhzKo=", "dev": true, "requires": {"indent-string": "^3.0.0", "strip-indent": "^2.0.0"}}, "strip-indent": {"version": "2.0.0", "resolved": "http://r.npm.sankuai.com/strip-indent/download/strip-indent-2.0.0.tgz", "integrity": "sha1-XvjbKV0B5u1sv3qrlpmNeCJSe2g=", "dev": true}, "trim-newlines": {"version": "2.0.0", "resolved": "http://r.npm.sankuai.com/trim-newlines/download/trim-newlines-2.0.0.tgz", "integrity": "sha1-tAPQuRvlDDMd/EuC7s6yLD3hbSA=", "dev": true}}}, "convert-source-map": {"version": "1.6.0", "resolved": "https://registry.npmjs.org/convert-source-map/-/convert-source-map-1.6.0.tgz", "integrity": "sha512-eFu7XigvxdZ1ETfbgPBohgyQ/Z++C0eEhTor0qRwBw9unw+L0/6V8wkSuGgzdThkiS5lSpdptOQPD8Ak40a+7A==", "dev": true, "requires": {"safe-buffer": "~5.1.1"}}, "cookie": {"version": "0.5.0", "resolved": "http://r.npm.sankuai.com/cookie/download/cookie-0.5.0.tgz", "integrity": "sha1-0fXXGt7GVYxY84mYfDZqpH6ZT4s="}, "cookie-signature": {"version": "1.0.6", "resolved": "https://registry.npmjs.org/cookie-signature/-/cookie-signature-1.0.6.tgz", "integrity": "sha1-4wOogrNCzD7oylE6eZmXNNqzriw="}, "copy-concurrently": {"version": "1.0.5", "resolved": "https://registry.npmjs.org/copy-concurrently/-/copy-concurrently-1.0.5.tgz", "integrity": "sha512-f2domd9fsVDFtaFcbaRZuYXwtdmnzqbADSwhSWYxYB/Q8zsdUUFMXVRwXGDMWmbEzAn1kdRrtI1T/KTFOL4X2A==", "dev": true, "requires": {"aproba": "^1.1.1", "fs-write-stream-atomic": "^1.0.8", "iferr": "^0.1.5", "mkdirp": "^0.5.1", "rimraf": "^2.5.4", "run-queue": "^1.0.0"}}, "copy-descriptor": {"version": "0.1.1", "resolved": "https://registry.npmjs.org/copy-descriptor/-/copy-descriptor-0.1.1.tgz", "integrity": "sha1-Z29us8OZl8LuGsOpJP1hJHSPV40=", "dev": true}, "copy-webpack-plugin": {"version": "4.5.2", "resolved": "https://registry.npmjs.org/copy-webpack-plugin/-/copy-webpack-plugin-4.5.2.tgz", "integrity": "sha512-zmC33E8FFSq3AbflTvqvPvBo621H36Afsxlui91d+QyZxPIuXghfnTsa1CuqiAaCPgJoSUWfTFbKJnadZpKEbQ==", "dev": true, "requires": {"cacache": "^10.0.4", "find-cache-dir": "^1.0.0", "globby": "^7.1.1", "is-glob": "^4.0.0", "loader-utils": "^1.1.0", "minimatch": "^3.0.4", "p-limit": "^1.0.0", "serialize-javascript": "^1.4.0"}, "dependencies": {"globby": {"version": "7.1.1", "resolved": "https://registry.npmjs.org/globby/-/globby-7.1.1.tgz", "integrity": "sha1-+yzP+UAfhgCUXfral0QMypcrhoA=", "dev": true, "requires": {"array-union": "^1.0.1", "dir-glob": "^2.0.0", "glob": "^7.1.2", "ignore": "^3.3.5", "pify": "^3.0.0", "slash": "^1.0.0"}}, "ignore": {"version": "3.3.10", "resolved": "https://registry.npmjs.org/ignore/-/ignore-3.3.10.tgz", "integrity": "sha512-Pgs951kaMm5GXP7MOvxERINe3gsaVjUWFm+UZPSq9xYriQAksyhg0csnS0KXSNRD5NmNdapXEpjxG49+AKh/ug==", "dev": true}, "pify": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/pify/-/pify-3.0.0.tgz", "integrity": "sha1-5aSs0sEB/fPZpNB/DbxNtJ3SgXY=", "dev": true}}}, "core-js": {"version": "2.5.7", "resolved": "https://registry.npmjs.org/core-js/-/core-js-2.5.7.tgz", "integrity": "sha512-RszJCAxg/PP6uzXVXL6BsxSXx/B05oJAQ2vkJRjyjrEcNVycaqOmNb5OTxZPE3xa5gwZduqza6L9JOCenh/Ecw=="}, "core-util-is": {"version": "1.0.2", "resolved": "http://r.npm.sankuai.com/core-util-is/download/core-util-is-1.0.2.tgz", "integrity": "sha1-tf1UIgqivFq1eqtxQMlAdUUDwac="}, "cosmiconfig": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/cosmiconfig/-/cosmiconfig-4.0.0.tgz", "integrity": "sha512-6e5vDdrXZD+t5v0L8CrurPeybg4Fmf+FCSYxXKYVAqLUtyCSbuyqE059d0kDthTNRzKVjL7QMgNpEUlsoYH3iQ==", "dev": true, "requires": {"is-directory": "^0.3.1", "js-yaml": "^3.9.0", "parse-json": "^4.0.0", "require-from-string": "^2.0.1"}, "dependencies": {"parse-json": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/parse-json/-/parse-json-4.0.0.tgz", "integrity": "sha1-vjX1Qlvh9/bHRxhPmKeIy5lHfuA=", "dev": true, "requires": {"error-ex": "^1.3.1", "json-parse-better-errors": "^1.0.1"}}}}, "countup": {"version": "1.8.2", "resolved": "http://r.npm.sankuai.com/countup/download/countup-1.8.2.tgz", "integrity": "sha1-AhzMam+WRUDGsn7WRoGvJ/tV8BA="}, "create-ecdh": {"version": "4.0.3", "resolved": "https://registry.npmjs.org/create-ecdh/-/create-ecdh-4.0.3.tgz", "integrity": "sha512-GbEHQPMOswGpKXM9kCWVrremUcBmjteUaQ01T9rkKCPDXfUHX0IoP9LpHYo2NPFampa4e+/pFDc3jQdxrxQLaw==", "dev": true, "requires": {"bn.js": "^4.1.0", "elliptic": "^6.0.0"}}, "create-hash": {"version": "1.2.0", "resolved": "http://registry.npmjs.org/create-hash/-/create-hash-1.2.0.tgz", "integrity": "sha512-z00bCGNHDG8mHAkP7CtT1qVu+bFQUPjYq/4Iv3C3kWjTFV10zIjfSoeqXo9Asws8gwSHDGj/hl2u4OGIjapeCg==", "dev": true, "requires": {"cipher-base": "^1.0.1", "inherits": "^2.0.1", "md5.js": "^1.3.4", "ripemd160": "^2.0.1", "sha.js": "^2.4.0"}}, "create-hmac": {"version": "1.1.7", "resolved": "http://registry.npmjs.org/create-hmac/-/create-hmac-1.1.7.tgz", "integrity": "sha512-MJG9liiZ+ogc4TzUwuvbER1JRdgvUFSB5+VR/g5h82fGaIRWMWddtKBHi7/sVhfjQZ6SehlyhvQYrcYkaUIpLg==", "dev": true, "requires": {"cipher-base": "^1.0.3", "create-hash": "^1.1.0", "inherits": "^2.0.1", "ripemd160": "^2.0.0", "safe-buffer": "^5.0.1", "sha.js": "^2.4.8"}}, "cross-spawn": {"version": "6.0.5", "resolved": "http://r.npm.sankuai.com/cross-spawn/download/cross-spawn-6.0.5.tgz", "integrity": "sha1-Sl7Hxk364iw6FBJNus3uhG2Ay8Q=", "dev": true, "requires": {"nice-try": "^1.0.4", "path-key": "^2.0.1", "semver": "^5.5.0", "shebang-command": "^1.2.0", "which": "^1.2.9"}}, "crypto-browserify": {"version": "3.12.0", "resolved": "https://registry.npmjs.org/crypto-browserify/-/crypto-browserify-3.12.0.tgz", "integrity": "sha512-fz4spIh+znjO2VjL+IdhEpRJ3YN6sMzITSBijk6FK2UvTqruSQW+/cCZTSNsMiZNvUeq0CqurF+dAbyiGOY6Wg==", "dev": true, "requires": {"browserify-cipher": "^1.0.0", "browserify-sign": "^4.0.0", "create-ecdh": "^4.0.0", "create-hash": "^1.1.0", "create-hmac": "^1.1.0", "diffie-hellman": "^5.0.0", "inherits": "^2.0.1", "pbkdf2": "^3.0.3", "public-encrypt": "^4.0.0", "randombytes": "^2.0.0", "randomfill": "^1.0.3"}}, "css-color-names": {"version": "0.0.4", "resolved": "https://registry.npmjs.org/css-color-names/-/css-color-names-0.0.4.tgz", "integrity": "sha1-gIrcLnnPhHOAabZGyyDsJ762KeA=", "dev": true}, "css-declaration-sorter": {"version": "3.0.1", "resolved": "https://registry.npmjs.org/css-declaration-sorter/-/css-declaration-sorter-3.0.1.tgz", "integrity": "sha512-jH4024SHZ3e0M7ann9VxpFpH3moplRXNz9ZBqvFMZqi09Yo5ARbs2wdPH8GqN9iRTlQynrbGbraNbBxBLei85Q==", "dev": true, "requires": {"postcss": "^6.0.0", "timsort": "^0.3.0"}}, "css-loader": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/css-loader/-/css-loader-1.0.0.tgz", "integrity": "sha512-tMXlTYf3mIMt3b0dDCOQFJiVvxbocJ5Ho577WiGPYPZcqVEO218L2iU22pDXzkTZCLDE+9AmGSUkWxeh/nZReA==", "dev": true, "requires": {"babel-code-frame": "^6.26.0", "css-selector-tokenizer": "^0.7.0", "icss-utils": "^2.1.0", "loader-utils": "^1.0.2", "lodash.camelcase": "^4.3.0", "postcss": "^6.0.23", "postcss-modules-extract-imports": "^1.2.0", "postcss-modules-local-by-default": "^1.2.0", "postcss-modules-scope": "^1.1.0", "postcss-modules-values": "^1.3.0", "postcss-value-parser": "^3.3.0", "source-list-map": "^2.0.0"}}, "css-select": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/css-select/-/css-select-1.2.0.tgz", "integrity": "sha1-KzoRBTnFNV8c2NMUYj6HCxIeyFg=", "dev": true, "requires": {"boolbase": "~1.0.0", "css-what": "2.1", "domutils": "1.5.1", "nth-check": "~1.0.1"}, "dependencies": {"domutils": {"version": "1.5.1", "resolved": "https://registry.npmjs.org/domutils/-/domutils-1.5.1.tgz", "integrity": "sha1-3NhIiib1Y9YQeeSMn3t+Mjc2gs8=", "dev": true, "requires": {"dom-serializer": "0", "domelementtype": "1"}}}}, "css-select-base-adapter": {"version": "0.1.0", "resolved": "https://registry.npmjs.org/css-select-base-adapter/-/css-select-base-adapter-0.1.0.tgz", "integrity": "sha1-AQKz0UYw34bD65+p9UVicBBs+ZA=", "dev": true}, "css-selector-tokenizer": {"version": "0.7.0", "resolved": "https://registry.npmjs.org/css-selector-tokenizer/-/css-selector-tokenizer-0.7.0.tgz", "integrity": "sha1-5piEdK6MlTR3v15+/s/OzNnPTIY=", "dev": true, "requires": {"cssesc": "^0.1.0", "fastparse": "^1.1.1", "regexpu-core": "^1.0.0"}, "dependencies": {"jsesc": {"version": "0.5.0", "resolved": "https://registry.npmjs.org/jsesc/-/jsesc-0.5.0.tgz", "integrity": "sha1-597mbjXW/Bb3EP6R1c9p9w8IkR0=", "dev": true}, "regexpu-core": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/regexpu-core/-/regexpu-core-1.0.0.tgz", "integrity": "sha1-hqdj9Y7k18L2sQLkdkBQ3n7ZDGs=", "dev": true, "requires": {"regenerate": "^1.2.1", "regjsgen": "^0.2.0", "regjsparser": "^0.1.4"}}, "regjsgen": {"version": "0.2.0", "resolved": "https://registry.npmjs.org/regjsgen/-/regjsgen-0.2.0.tgz", "integrity": "sha1-bAFq3qxVT3WCP+N6wFuS1aTtsfc=", "dev": true}, "regjsparser": {"version": "0.1.5", "resolved": "https://registry.npmjs.org/regjsparser/-/regjsparser-0.1.5.tgz", "integrity": "sha1-fuj4Tcb6eS0/0K4ijSS9lJ6tIFw=", "dev": true, "requires": {"jsesc": "~0.5.0"}}}}, "css-tree": {"version": "1.0.0-alpha25", "resolved": "https://registry.npmjs.org/css-tree/-/css-tree-1.0.0-alpha25.tgz", "integrity": "sha512-XC6xLW/JqIGirnZuUWHXCHRaAjje2b3OIB0Vj5RIJo6mIi/AdJo30quQl5LxUl0gkXDIrTrFGbMlcZjyFplz1A==", "dev": true, "requires": {"mdn-data": "^1.0.0", "source-map": "^0.5.3"}}, "css-unit-converter": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/css-unit-converter/-/css-unit-converter-1.1.1.tgz", "integrity": "sha1-2bkoGtz9jO2TW9urqDeGiX9k6ZY=", "dev": true}, "css-url-regex": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/css-url-regex/-/css-url-regex-1.1.0.tgz", "integrity": "sha1-g4NCMMyfdMRX3lnuvRVD/uuDt+w=", "dev": true}, "css-what": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/css-what/-/css-what-2.1.0.tgz", "integrity": "sha1-lGfQMsOM+u+58teVASUwYvh/ob0=", "dev": true}, "cssesc": {"version": "0.1.0", "resolved": "https://registry.npmjs.org/cssesc/-/cssesc-0.1.0.tgz", "integrity": "sha1-yBSQPkViM3GgR3tAEJqq++6t27Q=", "dev": true}, "cssnano": {"version": "4.1.0", "resolved": "https://registry.npmjs.org/cssnano/-/cssnano-4.1.0.tgz", "integrity": "sha512-7x24b/ghbrQv2QRgqMR12H3ZZ38xYCKJSXfg21YCtnIE177/NyvMkeiuQdWauIgMjySaTZ+cd5PN2qvfbsGeSw==", "dev": true, "requires": {"cosmiconfig": "^5.0.0", "cssnano-preset-default": "^4.0.0", "is-resolvable": "^1.0.0", "postcss": "^6.0.0"}, "dependencies": {"cosmiconfig": {"version": "5.0.6", "resolved": "https://registry.npmjs.org/cosmiconfig/-/cosmiconfig-5.0.6.tgz", "integrity": "sha512-6DWfizHriCrFWURP1/qyhsiFvYdlJzbCzmtFWh744+KyWsJo5+kPzUZZaMRSSItoYc0pxFX7gEO7ZC1/gN/7AQ==", "dev": true, "requires": {"is-directory": "^0.3.1", "js-yaml": "^3.9.0", "parse-json": "^4.0.0"}}, "parse-json": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/parse-json/-/parse-json-4.0.0.tgz", "integrity": "sha1-vjX1Qlvh9/bHRxhPmKeIy5lHfuA=", "dev": true, "requires": {"error-ex": "^1.3.1", "json-parse-better-errors": "^1.0.1"}}}}, "cssnano-preset-default": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/cssnano-preset-default/-/cssnano-preset-default-4.0.0.tgz", "integrity": "sha1-wzQoe099SfstFwqS+SFGVXiOO2s=", "dev": true, "requires": {"css-declaration-sorter": "^3.0.0", "cssnano-util-raw-cache": "^4.0.0", "postcss": "^6.0.0", "postcss-calc": "^6.0.0", "postcss-colormin": "^4.0.0", "postcss-convert-values": "^4.0.0", "postcss-discard-comments": "^4.0.0", "postcss-discard-duplicates": "^4.0.0", "postcss-discard-empty": "^4.0.0", "postcss-discard-overridden": "^4.0.0", "postcss-merge-longhand": "^4.0.0", "postcss-merge-rules": "^4.0.0", "postcss-minify-font-values": "^4.0.0", "postcss-minify-gradients": "^4.0.0", "postcss-minify-params": "^4.0.0", "postcss-minify-selectors": "^4.0.0", "postcss-normalize-charset": "^4.0.0", "postcss-normalize-display-values": "^4.0.0", "postcss-normalize-positions": "^4.0.0", "postcss-normalize-repeat-style": "^4.0.0", "postcss-normalize-string": "^4.0.0", "postcss-normalize-timing-functions": "^4.0.0", "postcss-normalize-unicode": "^4.0.0", "postcss-normalize-url": "^4.0.0", "postcss-normalize-whitespace": "^4.0.0", "postcss-ordered-values": "^4.0.0", "postcss-reduce-initial": "^4.0.0", "postcss-reduce-transforms": "^4.0.0", "postcss-svgo": "^4.0.0", "postcss-unique-selectors": "^4.0.0"}}, "cssnano-util-get-arguments": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/cssnano-util-get-arguments/-/cssnano-util-get-arguments-4.0.0.tgz", "integrity": "sha1-7ToIKZ8h11dBsg87gfGU7UnMFQ8=", "dev": true}, "cssnano-util-get-match": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/cssnano-util-get-match/-/cssnano-util-get-match-4.0.0.tgz", "integrity": "sha1-wOTKB/U4a7F+xeUiULT1lhNlFW0=", "dev": true}, "cssnano-util-raw-cache": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/cssnano-util-raw-cache/-/cssnano-util-raw-cache-4.0.0.tgz", "integrity": "sha1-vgooVuJfGF9feivMBiTii38Xmp8=", "dev": true, "requires": {"postcss": "^6.0.0"}}, "cssnano-util-same-parent": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/cssnano-util-same-parent/-/cssnano-util-same-parent-4.0.0.tgz", "integrity": "sha1-0qPeEDmqmLxOwlAB+gUDMMKhbaw=", "dev": true}, "csso": {"version": "3.5.1", "resolved": "https://registry.npmjs.org/csso/-/csso-3.5.1.tgz", "integrity": "sha512-vrqULLffYU1Q2tLdJvaCYbONStnfkfimRxXNaGjxMldI0C7JPBC4rB1RyjhfdZ4m1frm8pM9uRPKH3d2knZ8gg==", "dev": true, "requires": {"css-tree": "1.0.0-alpha.29"}, "dependencies": {"css-tree": {"version": "1.0.0-alpha.29", "resolved": "https://registry.npmjs.org/css-tree/-/css-tree-1.0.0-alpha.29.tgz", "integrity": "sha512-sRNb1XydwkW9IOci6iB2xmy8IGCj6r/fr+JWitvJ2JxQRPzN3T4AGGVWCMlVmVwM1gtgALJRmGIlWv5ppnGGkg==", "dev": true, "requires": {"mdn-data": "~1.1.0", "source-map": "^0.5.3"}}, "mdn-data": {"version": "1.1.4", "resolved": "https://registry.npmjs.org/mdn-data/-/mdn-data-1.1.4.tgz", "integrity": "sha512-FSYbp3lyKjyj3E7fMl6rYvUdX0FBXaluGqlFoYESWQlyUTq8R+wp0rkFxoYFqZlHCvsUXGjyJmLQSnXToYhOSA==", "dev": true}}}, "csstype": {"version": "3.1.3", "resolved": "http://r.npm.sankuai.com/csstype/download/csstype-3.1.3.tgz", "integrity": "sha1-2A/ylNEU+w5qxQD7+FtgE31+/4E="}, "currently-unhandled": {"version": "0.4.1", "resolved": "https://registry.npmjs.org/currently-unhandled/-/currently-unhandled-0.4.1.tgz", "integrity": "sha1-mI3zP+qxke95mmE2nddsF635V+o=", "dev": true, "requires": {"array-find-index": "^1.0.1"}}, "cyclist": {"version": "0.2.2", "resolved": "https://registry.npmjs.org/cyclist/-/cyclist-0.2.2.tgz", "integrity": "sha1-GzN5LhHpFKL9bW7WRHRkRE5fpkA=", "dev": true}, "dargs": {"version": "4.1.0", "resolved": "http://r.npm.sankuai.com/dargs/download/dargs-4.1.0.tgz", "integrity": "sha1-A6nbtLXC8Tm/FK5T8LiipqhvThc=", "dev": true, "requires": {"number-is-nan": "^1.0.0"}}, "dashdash": {"version": "1.14.1", "resolved": "https://registry.npmjs.org/dashdash/-/dashdash-1.14.1.tgz", "integrity": "sha1-hTz6D3y+L+1d4gMmuN1YEDX24vA=", "requires": {"assert-plus": "^1.0.0"}}, "date-now": {"version": "0.1.4", "resolved": "https://registry.npmjs.org/date-now/-/date-now-0.1.4.tgz", "integrity": "sha1-6vQ5/U1ISK105cx9vvIAZyueNFs=", "dev": true}, "dayjs": {"version": "1.7.5", "resolved": "https://registry.npmjs.org/dayjs/-/dayjs-1.7.5.tgz", "integrity": "sha512-OzkAcosqOgWgQF+dQTXO/iaSGa3hMs/sSkfzkxwWpZXqJEbaA0V6O1V+Ew2tGBlTz1r7Rb7opU3w8ympWb9d2Q=="}, "de-indent": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/de-indent/-/de-indent-1.0.2.tgz", "integrity": "sha1-sgOOhG3DO6pXlhKNCAS0VbjB4h0=", "dev": true}, "debug": {"version": "3.2.5", "resolved": "http://r.npm.sankuai.com/debug/download/debug-3.2.5.tgz", "integrity": "sha1-wkGPv9ein01PcP9M6mBNS2TEZAc=", "dev": true, "requires": {"ms": "^2.1.1"}}, "decamelize": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/decamelize/-/decamelize-2.0.0.tgz", "integrity": "sha512-Ikpp5scV3MSYxY39ymh45ZLEecsTdv/Xj2CaQfI8RLMuwi7XvjX9H/fhraiSuU+C5w5NTDu4ZU72xNiZnurBPg==", "dev": true, "requires": {"xregexp": "4.0.0"}}, "decamelize-keys": {"version": "1.1.0", "resolved": "http://r.npm.sankuai.com/decamelize-keys/download/decamelize-keys-1.1.0.tgz", "integrity": "sha1-0XGoeTMlKAfrPLYdwcFEXQeN8tk=", "dev": true, "requires": {"decamelize": "^1.1.0", "map-obj": "^1.0.0"}, "dependencies": {"decamelize": {"version": "1.2.0", "resolved": "http://r.npm.sankuai.com/decamelize/download/decamelize-1.2.0.tgz", "integrity": "sha1-9lNNFRSCabIDUue+4m9QH5oZEpA=", "dev": true}}}, "decode-uri-component": {"version": "0.2.0", "resolved": "https://registry.npmjs.org/decode-uri-component/-/decode-uri-component-0.2.0.tgz", "integrity": "sha1-6zkTMzRYd1y4TNGh+uBiEGu4dUU=", "dev": true}, "deep-equal": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/deep-equal/-/deep-equal-1.0.1.tgz", "integrity": "sha1-9dJgKStmDghO/0zbyfCK0yR0SLU="}, "deep-is": {"version": "0.1.3", "resolved": "http://r.npm.sankuai.com/deep-is/download/deep-is-0.1.3.tgz", "integrity": "sha1-s2nW+128E+7PUk+RsHD+7cNXzzQ=", "dev": true}, "deepmerge": {"version": "4.3.1", "resolved": "http://r.npm.sankuai.com/deepmerge/download/deepmerge-4.3.1.tgz", "integrity": "sha1-RLXyFHzTsA1LVhN2hZZvJv0l3Uo="}, "default-gateway": {"version": "2.7.2", "resolved": "https://registry.npmjs.org/default-gateway/-/default-gateway-2.7.2.tgz", "integrity": "sha512-lAc4i9QJR0YHSDFdzeBQKfZ1SRDG3hsJNEkrpcZa8QhBfidLAilT60BDEIVUUGqosFp425KOgB3uYqcnQrWafQ==", "dev": true, "requires": {"execa": "^0.10.0", "ip-regex": "^2.1.0"}}, "define-properties": {"version": "1.1.3", "resolved": "http://r.npm.sankuai.com/define-properties/download/define-properties-1.1.3.tgz", "integrity": "sha1-z4jabL7ib+bbcJT2HYcMvYTO6fE=", "dev": true, "requires": {"object-keys": "^1.0.12"}}, "define-property": {"version": "2.0.2", "resolved": "https://registry.npmjs.org/define-property/-/define-property-2.0.2.tgz", "integrity": "sha512-jwK2UV4cnPpbcG7+VRARKTZPUWowwXA8bzH5NP6ud0oeAxyYPuGZUAC7hMugpCdz4BeSZl2Dl9k66CHJ/46ZYQ==", "dev": true, "requires": {"is-descriptor": "^1.0.2", "isobject": "^3.0.1"}, "dependencies": {"is-accessor-descriptor": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/is-accessor-descriptor/-/is-accessor-descriptor-1.0.0.tgz", "integrity": "sha512-m5hnHTkcVsPfqx3AKlyttIPb7J+XykHvJP2B9bZDjlhLIoEq4XoK64Vg7boZlVWYK6LUY94dYPEE7Lh0ZkZKcQ==", "dev": true, "requires": {"kind-of": "^6.0.0"}}, "is-data-descriptor": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/is-data-descriptor/-/is-data-descriptor-1.0.0.tgz", "integrity": "sha512-jbRXy1FmtAoCjQkVmIVYwuuqDFUbaOeDjmed1tOGPrsMhtJA4rD9tkgA0F1qJ3gRFRXcHYVkdeaP50Q5rE/jLQ==", "dev": true, "requires": {"kind-of": "^6.0.0"}}, "is-descriptor": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/is-descriptor/-/is-descriptor-1.0.2.tgz", "integrity": "sha512-2eis5WqQGV7peooDyLmNEPUrps9+SXX5c9pL3xEB+4e9HnGuDa7mB7kHxHw4CbqS9k1T2hOH3miL8n8WtiYVtg==", "dev": true, "requires": {"is-accessor-descriptor": "^1.0.0", "is-data-descriptor": "^1.0.0", "kind-of": "^6.0.2"}}}}, "del": {"version": "2.2.2", "resolved": "http://r.npm.sankuai.com/del/download/del-2.2.2.tgz", "integrity": "sha1-wSyYHQZ4RshLyvhiz/kw2Qf/0ag=", "dev": true, "requires": {"globby": "^5.0.0", "is-path-cwd": "^1.0.0", "is-path-in-cwd": "^1.0.0", "object-assign": "^4.0.1", "pify": "^2.0.0", "pinkie-promise": "^2.0.0", "rimraf": "^2.2.8"}}, "delayed-stream": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/delayed-stream/-/delayed-stream-1.0.0.tgz", "integrity": "sha1-3zrhmayt+31ECqrgsp4icrJOxhk="}, "delegates": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/delegates/-/delegates-1.0.0.tgz", "integrity": "sha1-hMbhWbgZBP3KWaDvRM2HDTElD5o=", "dev": true}, "depd": {"version": "1.1.2", "resolved": "https://registry.npmjs.org/depd/-/depd-1.1.2.tgz", "integrity": "sha1-m81S4UwJd2PnSbJ0xDRu0uVgtak=", "dev": true}, "des.js": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/des.js/-/des.js-1.0.0.tgz", "integrity": "sha1-wHTS4qpqipoH29YfmhXCzYPsjsw=", "dev": true, "requires": {"inherits": "^2.0.1", "minimalistic-assert": "^1.0.0"}}, "destroy": {"version": "1.2.0", "resolved": "http://r.npm.sankuai.com/destroy/download/destroy-1.2.0.tgz", "integrity": "sha1-SANzVQmti+VSk0xn32FPlOZvoBU="}, "detect-node": {"version": "2.0.4", "resolved": "https://registry.npmjs.org/detect-node/-/detect-node-2.0.4.tgz", "integrity": "sha512-ZIzRpLJrOj7jjP2miAtgqIfmzbxa4ZOr5jJc601zklsfEx9oTzmmj2nVpIPRpNlRTIh8lc1kyViIY7BWSGNmKw==", "dev": true}, "diffie-hellman": {"version": "5.0.3", "resolved": "http://registry.npmjs.org/diffie-hellman/-/diffie-hellman-5.0.3.tgz", "integrity": "sha512-kqag/Nl+f3GwyK25fhUMYj81BUOrZ9IuJsjIcDE5icNM9FJHAVm3VcUDxdLPoQtTuUylWm6ZIknYJwwaPxsUzg==", "dev": true, "requires": {"bn.js": "^4.1.0", "miller-rabin": "^4.0.0", "randombytes": "^2.0.0"}}, "dir-glob": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/dir-glob/-/dir-glob-2.0.0.tgz", "integrity": "sha512-37qirFDz8cA5fimp9feo43fSuRo2gHwaIn6dXL8Ber1dGwUosDrGZeCCXq57WnIqE4aQ+u3eQZzsk1yOzhdwag==", "dev": true, "requires": {"arrify": "^1.0.1", "path-type": "^3.0.0"}, "dependencies": {"path-type": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/path-type/-/path-type-3.0.0.tgz", "integrity": "sha512-T2ZUsdZFHgA3u4e5PfPbjd7HDDpxPnQb5jN0SrDsjNSuVXHJqtwTnWqG0B1jZrgmJ/7lj1EmVIByWt1gxGkWvg==", "dev": true, "requires": {"pify": "^3.0.0"}}, "pify": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/pify/-/pify-3.0.0.tgz", "integrity": "sha1-5aSs0sEB/fPZpNB/DbxNtJ3SgXY=", "dev": true}}}, "dns-equal": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/dns-equal/-/dns-equal-1.0.0.tgz", "integrity": "sha1-s55/HabrCnW6nBcySzR1PEfgZU0=", "dev": true}, "dns-packet": {"version": "1.3.1", "resolved": "https://registry.npmjs.org/dns-packet/-/dns-packet-1.3.1.tgz", "integrity": "sha512-0UxfQkMhYAUaZI+xrNZOz/as5KgDU0M/fQ9b6SpkyLbk3GEswDi6PADJVaYJradtRVsRIlF1zLyOodbcTCDzUg==", "dev": true, "requires": {"ip": "^1.1.0", "safe-buffer": "^5.0.1"}}, "dns-txt": {"version": "2.0.2", "resolved": "https://registry.npmjs.org/dns-txt/-/dns-txt-2.0.2.tgz", "integrity": "sha1-uR2Ab10nGI5Ks+fRB9iBocxGQrY=", "dev": true, "requires": {"buffer-indexof": "^1.0.0"}}, "doctrine": {"version": "2.1.0", "resolved": "http://r.npm.sankuai.com/doctrine/download/doctrine-2.1.0.tgz", "integrity": "sha1-XNAfwQFiG0LEzX9dGmYkNxbT850=", "dev": true, "requires": {"esutils": "^2.0.2"}}, "dom-converter": {"version": "0.2.0", "resolved": "https://registry.npmjs.org/dom-converter/-/dom-converter-0.2.0.tgz", "integrity": "sha512-gd3ypIPfOMr9h5jIKq8E3sHOTCjeirnl0WK5ZdS1AW0Odt0b1PaWaHdJ4Qk4klv+YB9aJBS7mESXjFoDQPu6DA==", "dev": true, "requires": {"utila": "~0.4"}}, "dom-serializer": {"version": "0.1.0", "resolved": "http://r.npm.sankuai.com/dom-serializer/download/dom-serializer-0.1.0.tgz", "integrity": "sha1-BzxpdUbOB4DOI75KKOKT5AvDDII=", "requires": {"domelementtype": "~1.1.1", "entities": "~1.1.1"}, "dependencies": {"domelementtype": {"version": "1.1.3", "resolved": "http://r.npm.sankuai.com/domelementtype/download/domelementtype-1.1.3.tgz", "integrity": "sha1-vSh3PiZCiBrsUVRJJCmcXNgiGFs="}}}, "domain-browser": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/domain-browser/-/domain-browser-1.2.0.tgz", "integrity": "sha512-jnjyiM6eRyZl2H+W8Q/zLMA481hzi0eszAaBUzIVnmYVDBbnLxVNnfu1HgEBvCbL+71FrxMl3E6lpKH7Ge3OXA==", "dev": true}, "domelementtype": {"version": "1.3.0", "resolved": "http://r.npm.sankuai.com/domelementtype/download/domelementtype-1.3.0.tgz", "integrity": "sha1-sXrtguirWeUt2cGbF1bg/BhyBMI="}, "domhandler": {"version": "2.4.2", "resolved": "http://r.npm.sankuai.com/domhandler/download/domhandler-2.4.2.tgz", "integrity": "sha1-iAUJfpM9ZehVRvcm1g9euItE+AM=", "requires": {"domelementtype": "1"}}, "domutils": {"version": "1.7.0", "resolved": "http://r.npm.sankuai.com/domutils/download/domutils-1.7.0.tgz", "integrity": "sha1-Vuo0HoNOBuZ0ivehyyXaZ+qfjCo=", "requires": {"dom-serializer": "0", "domelementtype": "1"}}, "dot-prop": {"version": "4.2.0", "resolved": "https://registry.npmjs.org/dot-prop/-/dot-prop-4.2.0.tgz", "integrity": "sha512-tUMXrxlExSW6U2EXiiKGSBVdYgtV8qlHL+C10TsW4PURY/ic+eaysnSkwB4kA/mBlCyy/IKDJ+Lc3wbWeaXtuQ==", "dev": true, "requires": {"is-obj": "^1.0.0"}}, "duplexify": {"version": "3.6.0", "resolved": "https://registry.npmjs.org/duplexify/-/duplexify-3.6.0.tgz", "integrity": "sha512-fO3Di4tBKJpYTFHAxTU00BcfWMY9w24r/x21a6rZRbsD/ToUgGxsMbiGRmB7uVAXeGKXD9MwiLZa5E97EVgIRQ==", "dev": true, "requires": {"end-of-stream": "^1.0.0", "inherits": "^2.0.1", "readable-stream": "^2.0.0", "stream-shift": "^1.0.0"}}, "ecc-jsbn": {"version": "0.1.2", "resolved": "https://registry.npmjs.org/ecc-jsbn/-/ecc-jsbn-0.1.2.tgz", "integrity": "sha1-OoOpBOVDUyh4dMVkt1SThoSamMk=", "requires": {"jsbn": "~0.1.0", "safer-buffer": "^2.1.0"}}, "ee-first": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/ee-first/-/ee-first-1.1.1.tgz", "integrity": "sha1-WQxhFWsK4vTwJVcyoViyZrxWsh0="}, "electron-to-chromium": {"version": "1.3.67", "resolved": "https://registry.npmjs.org/electron-to-chromium/-/electron-to-chromium-1.3.67.tgz", "integrity": "sha512-h3zEBLdHvsKfaXv1SHAtykJyNtwYFEKkrWGSFyW1BzGgPQ4ykAzD5Hd8C5MZGTAEhkCKmtyIwYUrapsI0xfKww==", "dev": true}, "element-resize-detector": {"version": "1.2.4", "resolved": "http://r.npm.sankuai.com/element-resize-detector/download/element-resize-detector-1.2.4.tgz", "integrity": "sha1-PmxZgt13UItfp+bVwCFw4mMlybE=", "requires": {"batch-processor": "1.0.0"}}, "elliptic": {"version": "6.4.1", "resolved": "https://registry.npmjs.org/elliptic/-/elliptic-6.4.1.tgz", "integrity": "sha512-BsXLz5sqX8OHcsh7CqBMztyXARmGQ3LWPtGjJi6DiJHq5C/qvi9P3OqgswKSDftbu8+IoI/QDTAm2fFnQ9SZSQ==", "dev": true, "requires": {"bn.js": "^4.4.0", "brorand": "^1.0.1", "hash.js": "^1.0.0", "hmac-drbg": "^1.0.0", "inherits": "^2.0.1", "minimalistic-assert": "^1.0.0", "minimalistic-crypto-utils": "^1.0.0"}}, "emoji-regex": {"version": "7.0.3", "resolved": "https://registry.npm.taobao.org/emoji-regex/download/emoji-regex-7.0.3.tgz?cache=0&sync_timestamp=1603212263242&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Femoji-regex%2Fdownload%2Femoji-regex-7.0.3.tgz", "integrity": "sha1-kzoEBShgyF6DwSJHnEdIqOTHIVY=", "dev": true}, "emojis-list": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/emojis-list/-/emojis-list-2.1.0.tgz", "integrity": "sha1-TapNnbAPmBmIDHn6RXrlsJof04k=", "dev": true}, "encodeurl": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/encodeurl/-/encodeurl-1.0.2.tgz", "integrity": "sha1-rT/0yG7C0CkyL1oCw6mmBslbP1k="}, "end-of-stream": {"version": "1.4.1", "resolved": "https://registry.npmjs.org/end-of-stream/-/end-of-stream-1.4.1.tgz", "integrity": "sha512-1MkrZNvWTKCaigbn+W15elq2BB/L22nqrSY5DKlo3X6+vclJm8Bb5djXJBmEX6fS3+zCh/F4VBK5Z2KxJt4s2Q==", "dev": true, "requires": {"once": "^1.4.0"}}, "enhanced-resolve": {"version": "4.1.0", "resolved": "https://registry.npmjs.org/enhanced-resolve/-/enhanced-resolve-4.1.0.tgz", "integrity": "sha512-F/7vkyTtyc/llOIn8oWclcB25KdRaiPBpZYDgJHgh/UHtpgT2p2eldQgtQnLtUvfMKPKxbRaQM/hHkvLHt1Vng==", "dev": true, "requires": {"graceful-fs": "^4.1.2", "memory-fs": "^0.4.0", "tapable": "^1.0.0"}}, "entities": {"version": "1.1.1", "resolved": "http://r.npm.sankuai.com/entities/download/entities-1.1.1.tgz", "integrity": "sha1-blwtClYhtdra7O+AuQ7ftc13cvA="}, "errno": {"version": "0.1.7", "resolved": "https://registry.npmjs.org/errno/-/errno-0.1.7.tgz", "integrity": "sha512-MfrRBDWzIWifgq6tJj60gkAwtLNb6sQPlcFrSOflcP1aFmmruKQ2wRnze/8V6kgyz7H3FF8Npzv78mZ7XLLflg==", "dev": true, "requires": {"prr": "~1.0.1"}}, "error-ex": {"version": "1.3.2", "resolved": "http://r.npm.sankuai.com/error-ex/download/error-ex-1.3.2.tgz", "integrity": "sha1-tKxAZIEH/c3PriQvQovqihTU8b8=", "dev": true, "requires": {"is-arrayish": "^0.2.1"}}, "error-stack-parser": {"version": "2.0.2", "resolved": "https://registry.npmjs.org/error-stack-parser/-/error-stack-parser-2.0.2.tgz", "integrity": "sha512-E1fPutRDdIj/hohG0UpT5mayXNCxXP9d+snxFsPU9X0XgccOumKraa3juDMwTUyi7+Bu5+mCGagjg4IYeNbOdw==", "dev": true, "requires": {"stackframe": "^1.0.4"}}, "es-abstract": {"version": "1.12.0", "resolved": "http://r.npm.sankuai.com/es-abstract/download/es-abstract-1.12.0.tgz", "integrity": "sha1-nbvdJ8aFbwABQhyhh4LXhr+KYWU=", "dev": true, "requires": {"es-to-primitive": "^1.1.1", "function-bind": "^1.1.1", "has": "^1.0.1", "is-callable": "^1.1.3", "is-regex": "^1.0.4"}}, "es-to-primitive": {"version": "1.1.1", "resolved": "http://r.npm.sankuai.com/es-to-primitive/download/es-to-primitive-1.1.1.tgz", "integrity": "sha1-RTVSSKiJeQNLZ5Lhm7gfK3l13Q0=", "dev": true, "requires": {"is-callable": "^1.1.1", "is-date-object": "^1.0.1", "is-symbol": "^1.0.1"}}, "escape-html": {"version": "1.0.3", "resolved": "https://registry.npmjs.org/escape-html/-/escape-html-1.0.3.tgz", "integrity": "sha1-Aljq5NPQwJdN4cFpGI7wBR0dGYg="}, "escape-string-regexp": {"version": "1.0.5", "resolved": "http://r.npm.sankuai.com/escape-string-regexp/download/escape-string-regexp-1.0.5.tgz", "integrity": "sha1-G2HAViGQqN/2rjuyzwIAyhMLhtQ="}, "eslint": {"version": "5.5.0", "resolved": "http://r.npm.sankuai.com/eslint/download/eslint-5.5.0.tgz", "integrity": "sha1-hVf8zqtRQagZfan/2ZBPifZEJcY=", "dev": true, "requires": {"@babel/code-frame": "^7.0.0", "ajv": "^6.5.3", "chalk": "^2.1.0", "cross-spawn": "^6.0.5", "debug": "^3.1.0", "doctrine": "^2.1.0", "eslint-scope": "^4.0.0", "eslint-utils": "^1.3.1", "eslint-visitor-keys": "^1.0.0", "espree": "^4.0.0", "esquery": "^1.0.1", "esutils": "^2.0.2", "file-entry-cache": "^2.0.0", "functional-red-black-tree": "^1.0.1", "glob": "^7.1.2", "globals": "^11.7.0", "ignore": "^4.0.6", "imurmurhash": "^0.1.4", "inquirer": "^6.1.0", "is-resolvable": "^1.1.0", "js-yaml": "^3.12.0", "json-stable-stringify-without-jsonify": "^1.0.1", "levn": "^0.3.0", "lodash": "^4.17.5", "minimatch": "^3.0.4", "mkdirp": "^0.5.1", "natural-compare": "^1.4.0", "optionator": "^0.8.2", "path-is-inside": "^1.0.2", "pluralize": "^7.0.0", "progress": "^2.0.0", "regexpp": "^2.0.0", "require-uncached": "^1.0.3", "semver": "^5.5.1", "strip-ansi": "^4.0.0", "strip-json-comments": "^2.0.1", "table": "^4.0.3", "text-table": "^0.2.0"}}, "eslint-config-airbnb-base": {"version": "13.1.0", "resolved": "http://r.npm.sankuai.com/eslint-config-airbnb-base/download/eslint-config-airbnb-base-13.1.0.tgz", "integrity": "sha1-taG0gLgN+tFkM9bErYTmYFBSwFw=", "dev": true, "requires": {"eslint-restricted-globals": "^0.1.1", "object.assign": "^4.1.0", "object.entries": "^1.0.4"}}, "eslint-import-resolver-node": {"version": "0.3.2", "resolved": "http://r.npm.sankuai.com/eslint-import-resolver-node/download/eslint-import-resolver-node-0.3.2.tgz", "integrity": "sha1-WPFfuDm40FdsqYBBNHaqskcttmo=", "dev": true, "requires": {"debug": "^2.6.9", "resolve": "^1.5.0"}, "dependencies": {"debug": {"version": "2.6.9", "resolved": "http://r.npm.sankuai.com/debug/download/debug-2.6.9.tgz", "integrity": "sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8=", "dev": true, "requires": {"ms": "2.0.0"}}, "ms": {"version": "2.0.0", "resolved": "http://r.npm.sankuai.com/ms/download/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g=", "dev": true}}}, "eslint-module-utils": {"version": "2.2.0", "resolved": "http://r.npm.sankuai.com/eslint-module-utils/download/eslint-module-utils-2.2.0.tgz", "integrity": "sha1-snA2LNiLGkitMIl2zn+lTphBF0Y=", "dev": true, "requires": {"debug": "^2.6.8", "pkg-dir": "^1.0.0"}, "dependencies": {"debug": {"version": "2.6.9", "resolved": "http://r.npm.sankuai.com/debug/download/debug-2.6.9.tgz", "integrity": "sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8=", "dev": true, "requires": {"ms": "2.0.0"}}, "ms": {"version": "2.0.0", "resolved": "http://r.npm.sankuai.com/ms/download/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g=", "dev": true}}}, "eslint-plugin-html": {"version": "4.0.5", "resolved": "http://r.npm.sankuai.com/eslint-plugin-html/download/eslint-plugin-html-4.0.5.tgz", "integrity": "sha1-6Ox+FkhRJEYPO/8xIBb+sKVNllk=", "dev": true, "requires": {"htmlparser2": "^3.8.2"}}, "eslint-plugin-hybrid": {"version": "0.0.1", "resolved": "http://r.npm.sankuai.com/eslint-plugin-hybrid/download/eslint-plugin-hybrid-0.0.1.tgz", "integrity": "sha1-BRIfBidbnZArMyfw7rKYV3mMVng=", "requires": {"requireindex": "~1.1.0"}}, "eslint-plugin-import": {"version": "2.14.0", "resolved": "http://r.npm.sankuai.com/eslint-plugin-import/download/eslint-plugin-import-2.14.0.tgz", "integrity": "sha1-axdibS4+atUs/OiAeoRdFeIhEag=", "dev": true, "requires": {"contains-path": "^0.1.0", "debug": "^2.6.8", "doctrine": "1.5.0", "eslint-import-resolver-node": "^0.3.1", "eslint-module-utils": "^2.2.0", "has": "^1.0.1", "lodash": "^4.17.4", "minimatch": "^3.0.3", "read-pkg-up": "^2.0.0", "resolve": "^1.6.0"}, "dependencies": {"debug": {"version": "2.6.9", "resolved": "http://r.npm.sankuai.com/debug/download/debug-2.6.9.tgz", "integrity": "sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8=", "dev": true, "requires": {"ms": "2.0.0"}}, "doctrine": {"version": "1.5.0", "resolved": "http://r.npm.sankuai.com/doctrine/download/doctrine-1.5.0.tgz", "integrity": "sha1-N53Ocw9hZvds76TmcHoVmwLFpvo=", "dev": true, "requires": {"esutils": "^2.0.2", "isarray": "^1.0.0"}}, "ms": {"version": "2.0.0", "resolved": "http://r.npm.sankuai.com/ms/download/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g=", "dev": true}}}, "eslint-plugin-vue": {"version": "7.3.0", "resolved": "http://r.npm.sankuai.com/eslint-plugin-vue/download/eslint-plugin-vue-7.3.0.tgz", "integrity": "sha1-D68Pzw4bEFK/gA1N7kLWT1BnnLA=", "requires": {"eslint-utils": "^2.1.0", "natural-compare": "^1.4.0", "semver": "^7.3.2", "vue-eslint-parser": "^7.3.0"}, "dependencies": {"eslint-utils": {"version": "2.1.0", "resolved": "http://r.npm.sankuai.com/eslint-utils/download/eslint-utils-2.1.0.tgz", "integrity": "sha1-0t5eA0JOcH3BDHQGjd7a5wh0Gyc=", "requires": {"eslint-visitor-keys": "^1.1.0"}}, "eslint-visitor-keys": {"version": "1.3.0", "resolved": "http://r.npm.sankuai.com/eslint-visitor-keys/download/eslint-visitor-keys-1.3.0.tgz", "integrity": "sha1-MOvR73wv3/AcOk8VEESvJfqwUj4="}, "lru-cache": {"version": "6.0.0", "resolved": "http://r.npm.sankuai.com/lru-cache/download/lru-cache-6.0.0.tgz", "integrity": "sha1-bW/mVw69lqr5D8rR2vo7JWbbOpQ=", "requires": {"yallist": "^4.0.0"}}, "semver": {"version": "7.3.4", "resolved": "http://r.npm.sankuai.com/semver/download/semver-7.3.4.tgz", "integrity": "sha1-J6qn0uTKdkUvmNOt0JOnLJQ+3Jc=", "requires": {"lru-cache": "^6.0.0"}}, "yallist": {"version": "4.0.0", "resolved": "http://r.npm.sankuai.com/yallist/download/yallist-4.0.0.tgz", "integrity": "sha1-m7knkNnA7/7GO+c1GeEaNQGaOnI="}}}, "eslint-restricted-globals": {"version": "0.1.1", "resolved": "http://r.npm.sankuai.com/eslint-restricted-globals/download/eslint-restricted-globals-0.1.1.tgz", "integrity": "sha1-NfDVy8ZMLj7WLpO0saevBbp+1Nc=", "dev": true}, "eslint-scope": {"version": "4.0.0", "resolved": "http://r.npm.sankuai.com/eslint-scope/download/eslint-scope-4.0.0.tgz", "integrity": "sha1-UL8wcekzi83EMzF5Sgy1M/ATYXI=", "dev": true, "requires": {"esrecurse": "^4.1.0", "estraverse": "^4.1.1"}}, "eslint-utils": {"version": "1.3.1", "resolved": "http://r.npm.sankuai.com/eslint-utils/download/eslint-utils-1.3.1.tgz", "integrity": "sha1-moUbqJ7nxGA0b5fPiTnHKYgn5RI=", "dev": true}, "eslint-visitor-keys": {"version": "1.0.0", "resolved": "http://r.npm.sankuai.com/eslint-visitor-keys/download/eslint-visitor-keys-1.0.0.tgz", "integrity": "sha1-PzGA+y4pEBdxastMnW1bXDSmqB0=", "dev": true}, "espree": {"version": "4.0.0", "resolved": "http://r.npm.sankuai.com/espree/download/espree-4.0.0.tgz", "integrity": "sha1-JTmY8goPgttdhmOFeZ2RKoOjZjQ=", "dev": true, "requires": {"acorn": "^5.6.0", "acorn-jsx": "^4.1.1"}}, "esprima": {"version": "4.0.1", "resolved": "http://r.npm.sankuai.com/esprima/download/esprima-4.0.1.tgz", "integrity": "sha1-E7BM2z5sXRnfkatph6hpVhmwqnE=", "dev": true}, "esquery": {"version": "1.0.1", "resolved": "http://r.npm.sankuai.com/esquery/download/esquery-1.0.1.tgz", "integrity": "sha1-QGxRZYsfWZGl+bYrHcJbAOPlxwg=", "requires": {"estraverse": "^4.0.0"}}, "esrecurse": {"version": "4.2.1", "resolved": "http://r.npm.sankuai.com/esrecurse/download/esrecurse-4.2.1.tgz", "integrity": "sha1-AHo7n9vCs7uH5IeeoZyS/b05Qs8=", "dev": true, "requires": {"estraverse": "^4.1.0"}}, "estraverse": {"version": "4.2.0", "resolved": "http://r.npm.sankuai.com/estraverse/download/estraverse-4.2.0.tgz", "integrity": "sha1-De4/7TH81GlhjOc0IJn8GvoL2xM="}, "estree-walker": {"version": "1.0.1", "resolved": "http://r.npm.sankuai.com/estree-walker/download/estree-walker-1.0.1.tgz", "integrity": "sha1-MbxdYSyWtwQQa0d+bdXYqhOMtwA="}, "esutils": {"version": "2.0.2", "resolved": "http://r.npm.sankuai.com/esutils/download/esutils-2.0.2.tgz", "integrity": "sha1-Cr9PHKpbyx96nYrMbepPqqBLrJs=", "dev": true}, "etag": {"version": "1.8.1", "resolved": "https://registry.npmjs.org/etag/-/etag-1.8.1.tgz", "integrity": "sha1-Qa4u62XvpiJorr/qg6x9eSmbCIc="}, "eventemitter3": {"version": "3.1.0", "resolved": "https://registry.npmjs.org/eventemitter3/-/eventemitter3-3.1.0.tgz", "integrity": "sha512-ivIvhpq/Y0uSjcHDcOIccjmYjGLcP09MFGE7ysAwkAvkXfpZlC985pH2/ui64DKazbTW/4kN3yqozUxlXzI6cA==", "dev": true}, "events": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/events/-/events-1.1.1.tgz", "integrity": "sha1-nr23Y1rQmccNzEwqH1AEKI6L2SQ=", "dev": true}, "eventsource": {"version": "0.1.6", "resolved": "https://registry.npmjs.org/eventsource/-/eventsource-0.1.6.tgz", "integrity": "sha1-Cs7ehJ7X3RzMMsgRuxG5RNTykjI=", "dev": true, "requires": {"original": ">=0.0.5"}}, "evp_bytestokey": {"version": "1.0.3", "resolved": "https://registry.npmjs.org/evp_bytestokey/-/evp_bytestokey-1.0.3.tgz", "integrity": "sha512-/f2Go4TognH/KvCISP7OUsHn85hT9nUkxxA9BEWxFn+Oj9o8ZNLm/40hdlgSLyuOimsrTKLUMEorQexp/aPQeA==", "dev": true, "requires": {"md5.js": "^1.3.4", "safe-buffer": "^5.1.1"}}, "execa": {"version": "0.10.0", "resolved": "https://registry.npmjs.org/execa/-/execa-0.10.0.tgz", "integrity": "sha512-7XOMnz8Ynx1gGo/3hyV9loYNPWM94jG3+3T3Y8tsfSstFmETmENCMU/A/zj8Lyaj1lkgEepKepvd6240tBRvlw==", "dev": true, "requires": {"cross-spawn": "^6.0.0", "get-stream": "^3.0.0", "is-stream": "^1.1.0", "npm-run-path": "^2.0.0", "p-finally": "^1.0.0", "signal-exit": "^3.0.0", "strip-eof": "^1.0.0"}}, "expand-brackets": {"version": "2.1.4", "resolved": "https://registry.npmjs.org/expand-brackets/-/expand-brackets-2.1.4.tgz", "integrity": "sha1-t3c14xXOMPa27/D4OwQVGiJEliI=", "dev": true, "requires": {"debug": "^2.3.3", "define-property": "^0.2.5", "extend-shallow": "^2.0.1", "posix-character-classes": "^0.1.0", "regex-not": "^1.0.0", "snapdragon": "^0.8.1", "to-regex": "^3.0.1"}, "dependencies": {"debug": {"version": "2.6.9", "resolved": "https://registry.npmjs.org/debug/-/debug-2.6.9.tgz", "integrity": "sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA==", "dev": true, "requires": {"ms": "2.0.0"}}, "define-property": {"version": "0.2.5", "resolved": "https://registry.npmjs.org/define-property/-/define-property-0.2.5.tgz", "integrity": "sha1-w1se+RjsPJkPmlvFe+BKrOxcgRY=", "dev": true, "requires": {"is-descriptor": "^0.1.0"}}, "extend-shallow": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/extend-shallow/-/extend-shallow-2.0.1.tgz", "integrity": "sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8=", "dev": true, "requires": {"is-extendable": "^0.1.0"}}, "ms": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/ms/-/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g=", "dev": true}}}, "express": {"version": "4.18.2", "resolved": "http://r.npm.sankuai.com/express/download/express-4.18.2.tgz", "integrity": "sha1-P6vggpbpMMeWwZ48UWl5OGup/Vk=", "requires": {"accepts": "~1.3.8", "array-flatten": "1.1.1", "body-parser": "1.20.1", "content-disposition": "0.5.4", "content-type": "~1.0.4", "cookie": "0.5.0", "cookie-signature": "1.0.6", "debug": "2.6.9", "depd": "2.0.0", "encodeurl": "~1.0.2", "escape-html": "~1.0.3", "etag": "~1.8.1", "finalhandler": "1.2.0", "fresh": "0.5.2", "http-errors": "2.0.0", "merge-descriptors": "1.0.1", "methods": "~1.1.2", "on-finished": "2.4.1", "parseurl": "~1.3.3", "path-to-regexp": "0.1.7", "proxy-addr": "~2.0.7", "qs": "6.11.0", "range-parser": "~1.2.1", "safe-buffer": "5.2.1", "send": "0.18.0", "serve-static": "1.15.0", "setprototypeof": "1.2.0", "statuses": "2.0.1", "type-is": "~1.6.18", "utils-merge": "1.0.1", "vary": "~1.1.2"}, "dependencies": {"accepts": {"version": "1.3.8", "resolved": "http://r.npm.sankuai.com/accepts/download/accepts-1.3.8.tgz", "integrity": "sha1-C/C+EltnAUrcsLCSHmLbe//hay4=", "requires": {"mime-types": "~2.1.34", "negotiator": "0.6.3"}}, "array-flatten": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/array-flatten/-/array-flatten-1.1.1.tgz", "integrity": "sha1-ml9pkFGx5wczKPKgCJaLZOopVdI="}, "debug": {"version": "2.6.9", "resolved": "http://r.npm.sankuai.com/debug/download/debug-2.6.9.tgz", "integrity": "sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8=", "requires": {"ms": "2.0.0"}}, "depd": {"version": "2.0.0", "resolved": "http://r.npm.sankuai.com/depd/download/depd-2.0.0.tgz", "integrity": "sha1-tpYWPMdXVg0JzyLMj60Vcbeedt8="}, "http-errors": {"version": "2.0.0", "resolved": "http://r.npm.sankuai.com/http-errors/download/http-errors-2.0.0.tgz", "integrity": "sha1-t3dKFIbvc892Z6ya4IWMASxXudM=", "requires": {"depd": "2.0.0", "inherits": "2.0.4", "setprototypeof": "1.2.0", "statuses": "2.0.1", "toidentifier": "1.0.1"}}, "inherits": {"version": "2.0.4", "resolved": "http://r.npm.sankuai.com/inherits/download/inherits-2.0.4.tgz", "integrity": "sha1-D6LGT5MpF8NDOg3tVTY6rjdBa3w="}, "mime-db": {"version": "1.52.0", "resolved": "http://r.npm.sankuai.com/mime-db/download/mime-db-1.52.0.tgz", "integrity": "sha1-u6vNwChZ9JhzAchW4zh85exDv3A="}, "mime-types": {"version": "2.1.35", "resolved": "http://r.npm.sankuai.com/mime-types/download/mime-types-2.1.35.tgz", "integrity": "sha1-OBqHG2KnNEUGYK497uRIE/cNlZo=", "requires": {"mime-db": "1.52.0"}}, "ms": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/ms/-/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g="}, "negotiator": {"version": "0.6.3", "resolved": "http://r.npm.sankuai.com/negotiator/download/negotiator-0.6.3.tgz", "integrity": "sha1-WOMjpy/twNb5zU0x/kn1FHlZDM0="}, "parseurl": {"version": "1.3.3", "resolved": "http://r.npm.sankuai.com/parseurl/download/parseurl-1.3.3.tgz", "integrity": "sha1-naGee+6NEt/wUT7Vt2lXeTvC6NQ="}, "range-parser": {"version": "1.2.1", "resolved": "http://r.npm.sankuai.com/range-parser/download/range-parser-1.2.1.tgz", "integrity": "sha1-PPNwI9GZ4cJNGlW4SADC8+ZGgDE="}, "safe-buffer": {"version": "5.2.1", "resolved": "http://r.npm.sankuai.com/safe-buffer/download/safe-buffer-5.2.1.tgz", "integrity": "sha1-Hq+fqb2x/dTsdfWPnNtOa3gn7sY="}, "setprototypeof": {"version": "1.2.0", "resolved": "http://r.npm.sankuai.com/setprototypeof/download/setprototypeof-1.2.0.tgz", "integrity": "sha1-ZsmiSnP5/CjL5msJ/tPTPcrxtCQ="}, "statuses": {"version": "2.0.1", "resolved": "http://r.npm.sankuai.com/statuses/download/statuses-2.0.1.tgz", "integrity": "sha1-VcsADM8dSHKL0jxoWgY5mM8aG2M="}}}, "extend": {"version": "3.0.2", "resolved": "https://registry.npm.taobao.org/extend/download/extend-3.0.2.tgz", "integrity": "sha1-+LETa0Bx+9jrFAr/hYsQGewpFfo="}, "extend-shallow": {"version": "3.0.2", "resolved": "https://registry.npmjs.org/extend-shallow/-/extend-shallow-3.0.2.tgz", "integrity": "sha1-Jqcarwc7OfshJxcnRhMcJwQCjbg=", "dev": true, "requires": {"assign-symbols": "^1.0.0", "is-extendable": "^1.0.1"}, "dependencies": {"is-extendable": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/is-extendable/-/is-extendable-1.0.1.tgz", "integrity": "sha512-arnXMxT1hhoKo9k1LZdmlNyJdDDfy2v0fXjFlmok4+i8ul/6WlbVge9bhM74OpNPQPMGUToDtz+KXa1PneJxOA==", "dev": true, "requires": {"is-plain-object": "^2.0.4"}}}}, "external-editor": {"version": "3.0.3", "resolved": "http://r.npm.sankuai.com/external-editor/download/external-editor-3.0.3.tgz", "integrity": "sha1-WGbbKal4Jtvkvzr9JAcOrZ6kOic=", "requires": {"chardet": "^0.7.0", "iconv-lite": "^0.4.24", "tmp": "^0.0.33"}}, "extglob": {"version": "2.0.4", "resolved": "https://registry.npmjs.org/extglob/-/extglob-2.0.4.tgz", "integrity": "sha512-Nmb6QXkELsuBr24CJSkilo6UHHgbekK5UiZgfE6UHD3Eb27YC6oD+bhcT+tJ6cl8dmsgdQxnWlcry8ksBIBLpw==", "dev": true, "requires": {"array-unique": "^0.3.2", "define-property": "^1.0.0", "expand-brackets": "^2.1.4", "extend-shallow": "^2.0.1", "fragment-cache": "^0.2.1", "regex-not": "^1.0.0", "snapdragon": "^0.8.1", "to-regex": "^3.0.1"}, "dependencies": {"define-property": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/define-property/-/define-property-1.0.0.tgz", "integrity": "sha1-dp66rz9KY6rTr56NMEybvnm/sOY=", "dev": true, "requires": {"is-descriptor": "^1.0.0"}}, "extend-shallow": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/extend-shallow/-/extend-shallow-2.0.1.tgz", "integrity": "sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8=", "dev": true, "requires": {"is-extendable": "^0.1.0"}}, "is-accessor-descriptor": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/is-accessor-descriptor/-/is-accessor-descriptor-1.0.0.tgz", "integrity": "sha512-m5hnHTkcVsPfqx3AKlyttIPb7J+XykHvJP2B9bZDjlhLIoEq4XoK64Vg7boZlVWYK6LUY94dYPEE7Lh0ZkZKcQ==", "dev": true, "requires": {"kind-of": "^6.0.0"}}, "is-data-descriptor": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/is-data-descriptor/-/is-data-descriptor-1.0.0.tgz", "integrity": "sha512-jbRXy1FmtAoCjQkVmIVYwuuqDFUbaOeDjmed1tOGPrsMhtJA4rD9tkgA0F1qJ3gRFRXcHYVkdeaP50Q5rE/jLQ==", "dev": true, "requires": {"kind-of": "^6.0.0"}}, "is-descriptor": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/is-descriptor/-/is-descriptor-1.0.2.tgz", "integrity": "sha512-2eis5WqQGV7peooDyLmNEPUrps9+SXX5c9pL3xEB+4e9HnGuDa7mB7kHxHw4CbqS9k1T2hOH3miL8n8WtiYVtg==", "dev": true, "requires": {"is-accessor-descriptor": "^1.0.0", "is-data-descriptor": "^1.0.0", "kind-of": "^6.0.2"}}}}, "extsprintf": {"version": "1.3.0", "resolved": "https://registry.npmjs.org/extsprintf/-/extsprintf-1.3.0.tgz", "integrity": "sha1-lpGEQOMEGnpBT4xS48V06zw+HgU="}, "fast-deep-equal": {"version": "2.0.1", "resolved": "http://r.npm.sankuai.com/fast-deep-equal/download/fast-deep-equal-2.0.1.tgz", "integrity": "sha1-ewUhjd+WZ79/Nwv3/bLLFf3Qqkk=", "dev": true}, "fast-diff": {"version": "1.1.2", "resolved": "http://r.npm.sankuai.com/fast-diff/download/fast-diff-1.1.2.tgz", "integrity": "sha1-S2LEK44D3j+EhGC2OQeZIGldAVQ="}, "fast-json-stable-stringify": {"version": "2.0.0", "resolved": "http://r.npm.sankuai.com/fast-json-stable-stringify/download/fast-json-stable-stringify-2.0.0.tgz", "integrity": "sha1-1RQsDK7msRifh9OnYREGT4bIu/I="}, "fast-levenshtein": {"version": "2.0.6", "resolved": "http://r.npm.sankuai.com/fast-levenshtein/download/fast-levenshtein-2.0.6.tgz", "integrity": "sha1-PYpcZog6FqMMqGQ+hR8Zuqd5eRc=", "dev": true}, "fastparse": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/fastparse/-/fastparse-1.1.1.tgz", "integrity": "sha1-0eJkOzipTXWDtHkGDmxK/8lAcfg=", "dev": true}, "faye-websocket": {"version": "0.10.0", "resolved": "https://registry.npmjs.org/faye-websocket/-/faye-websocket-0.10.0.tgz", "integrity": "sha1-TkkvjQTftviQA1B/btvy1QHnxvQ=", "dev": true, "requires": {"websocket-driver": ">=0.5.1"}}, "figures": {"version": "2.0.0", "resolved": "http://r.npm.sankuai.com/figures/download/figures-2.0.0.tgz", "integrity": "sha1-OrGi0qYsi/tDGgyUy3l6L84nyWI=", "dev": true, "requires": {"escape-string-regexp": "^1.0.5"}}, "file-entry-cache": {"version": "2.0.0", "resolved": "http://r.npm.sankuai.com/file-entry-cache/download/file-entry-cache-2.0.0.tgz", "integrity": "sha1-w5KZDD5oR4PYOLjISkXYoEhFg2E=", "dev": true, "requires": {"flat-cache": "^1.2.1", "object-assign": "^4.0.1"}}, "file-loader": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/file-loader/-/file-loader-2.0.0.tgz", "integrity": "sha512-YCsBfd1ZGCyonOKLxPiKPdu+8ld9HAaMEvJewzz+b2eTF7uL5Zm/HdBF6FjCrpCMRq25Mi0U1gl4pwn2TlH7hQ==", "dev": true, "requires": {"loader-utils": "^1.0.2", "schema-utils": "^1.0.0"}, "dependencies": {"schema-utils": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/schema-utils/-/schema-utils-1.0.0.tgz", "integrity": "sha512-i27Mic4KovM/lnGsy8whRCHhc7VicJajAjTrYg11K9zfZXnYIt4k5F+kZkwjnrhKzLic/HLU4j11mjsz2G/75g==", "dev": true, "requires": {"ajv": "^6.1.0", "ajv-errors": "^1.0.0", "ajv-keywords": "^3.1.0"}}}}, "file-uri-to-path": {"version": "1.0.0", "resolved": "http://r.npm.sankuai.com/file-uri-to-path/download/file-uri-to-path-1.0.0.tgz", "integrity": "sha1-VTp7hEb/b2hDWcRF8eN6BdrMM90="}, "fill-range": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/fill-range/-/fill-range-4.0.0.tgz", "integrity": "sha1-1USBHUKPmOsGpj3EAtJAPDKMOPc=", "dev": true, "requires": {"extend-shallow": "^2.0.1", "is-number": "^3.0.0", "repeat-string": "^1.6.1", "to-regex-range": "^2.1.0"}, "dependencies": {"extend-shallow": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/extend-shallow/-/extend-shallow-2.0.1.tgz", "integrity": "sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8=", "dev": true, "requires": {"is-extendable": "^0.1.0"}}}}, "filter-obj": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/filter-obj/-/filter-obj-1.1.0.tgz", "integrity": "sha512-8rXg1ZnX7xzy2NGDVkBVaAy+lSlPNwad13BtgSlLuxfIslyt5Vg64U7tFcCt4WS1R0hvtnQybT/IyCkGZ3DpXQ==", "dev": true}, "finalhandler": {"version": "1.2.0", "resolved": "http://r.npm.sankuai.com/finalhandler/download/finalhandler-1.2.0.tgz", "integrity": "sha1-fSP+VzGyB7RkDk/NAK7B+SB6ezI=", "requires": {"debug": "2.6.9", "encodeurl": "~1.0.2", "escape-html": "~1.0.3", "on-finished": "2.4.1", "parseurl": "~1.3.3", "statuses": "2.0.1", "unpipe": "~1.0.0"}, "dependencies": {"debug": {"version": "2.6.9", "resolved": "http://r.npm.sankuai.com/debug/download/debug-2.6.9.tgz", "integrity": "sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8=", "requires": {"ms": "2.0.0"}}, "ms": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/ms/-/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g="}, "parseurl": {"version": "1.3.3", "resolved": "http://r.npm.sankuai.com/parseurl/download/parseurl-1.3.3.tgz", "integrity": "sha1-naGee+6NEt/wUT7Vt2lXeTvC6NQ="}, "statuses": {"version": "2.0.1", "resolved": "http://r.npm.sankuai.com/statuses/download/statuses-2.0.1.tgz", "integrity": "sha1-VcsADM8dSHKL0jxoWgY5mM8aG2M="}}}, "find-cache-dir": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/find-cache-dir/-/find-cache-dir-1.0.0.tgz", "integrity": "sha1-kojj6ePMN0hxfTnq3hfPcfww7m8=", "dev": true, "requires": {"commondir": "^1.0.1", "make-dir": "^1.0.0", "pkg-dir": "^2.0.0"}, "dependencies": {"find-up": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/find-up/-/find-up-2.1.0.tgz", "integrity": "sha1-RdG35QbHF93UgndaK3eSCjwMV6c=", "dev": true, "requires": {"locate-path": "^2.0.0"}}, "pkg-dir": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/pkg-dir/-/pkg-dir-2.0.0.tgz", "integrity": "sha1-9tXREJ4Z1j7fQo4L1X4Sd3YVM0s=", "dev": true, "requires": {"find-up": "^2.1.0"}}}}, "find-up": {"version": "1.1.2", "resolved": "http://r.npm.sankuai.com/find-up/download/find-up-1.1.2.tgz", "integrity": "sha1-ay6YIrGizgpgq2TWEOzK1TyyTQ8=", "dev": true, "requires": {"path-exists": "^2.0.0", "pinkie-promise": "^2.0.0"}}, "flat-cache": {"version": "1.3.0", "resolved": "http://r.npm.sankuai.com/flat-cache/download/flat-cache-1.3.0.tgz", "integrity": "sha1-0wMLMrOBVPTjt+nHCfSQ9++XxIE=", "dev": true, "requires": {"circular-json": "^0.3.1", "del": "^2.0.2", "graceful-fs": "^4.1.2", "write": "^0.2.1"}}, "flatten": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/flatten/-/flatten-1.0.2.tgz", "integrity": "sha1-2uRqnXj74lKSJYzB54CkHZXAN4I=", "dev": true}, "flush-write-stream": {"version": "1.0.3", "resolved": "https://registry.npmjs.org/flush-write-stream/-/flush-write-stream-1.0.3.tgz", "integrity": "sha512-calZMC10u0FMUqoiunI2AiGIIUtUIvifNwkHhNupZH4cbNnW1Itkoh/Nf5HFYmDrwWPjrUxpkZT0KhuCq0jmGw==", "dev": true, "requires": {"inherits": "^2.0.1", "readable-stream": "^2.0.4"}}, "follow-redirects": {"version": "1.5.8", "resolved": "https://registry.npmjs.org/follow-redirects/-/follow-redirects-1.5.8.tgz", "integrity": "sha512-sy1mXPmv7kLAMKW/8XofG7o9T+6gAjzdZK4AJF6ryqQYUa/hnzgiypoeUecZ53x7XiqKNEpNqLtS97MshW2nxg==", "requires": {"debug": "=3.1.0"}, "dependencies": {"debug": {"version": "3.1.0", "resolved": "https://registry.npmjs.org/debug/-/debug-3.1.0.tgz", "integrity": "sha512-OX8XqP7/1a9cqkxYw2yXss15f26NKWBpDXQd0/uK/KPqdQhxbPa994hnzjcE2VqQpDslf55723cKPUOGSmMY3g==", "requires": {"ms": "2.0.0"}}, "ms": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/ms/-/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g="}}}, "for-in": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/for-in/-/for-in-1.0.2.tgz", "integrity": "sha1-gQaNKVqBQuwKxybG4iAMMPttXoA=", "dev": true}, "for-own": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/for-own/-/for-own-1.0.0.tgz", "integrity": "sha1-xjMy9BXO3EsE2/5wz4NklMU8tEs=", "dev": true, "requires": {"for-in": "^1.0.1"}}, "forever-agent": {"version": "0.6.1", "resolved": "https://registry.npmjs.org/forever-agent/-/forever-agent-0.6.1.tgz", "integrity": "sha1-+8cfDEGt6zf5bFd60e1C2P2sypE="}, "form-data": {"version": "2.3.3", "resolved": "https://registry.npm.taobao.org/form-data/download/form-data-2.3.3.tgz", "integrity": "sha1-3M5SwF9kTymManq5Nr1yTO/786Y=", "requires": {"asynckit": "^0.4.0", "combined-stream": "^1.0.6", "mime-types": "^2.1.12"}}, "forwarded": {"version": "0.2.0", "resolved": "http://r.npm.sankuai.com/forwarded/download/forwarded-0.2.0.tgz", "integrity": "sha1-ImmTZCiq1MFcfr6XeahL8LKoGBE="}, "fragment-cache": {"version": "0.2.1", "resolved": "https://registry.npmjs.org/fragment-cache/-/fragment-cache-0.2.1.tgz", "integrity": "sha1-QpD60n8T6Jvn8zeZxrxaCr//DRk=", "dev": true, "requires": {"map-cache": "^0.2.2"}}, "fresh": {"version": "0.5.2", "resolved": "https://registry.npmjs.org/fresh/-/fresh-0.5.2.tgz", "integrity": "sha1-PYyt2Q2XZWn6g1qx+OSyOhBWBac="}, "friendly-errors-webpack-plugin": {"version": "1.7.0", "resolved": "https://registry.npmjs.org/friendly-errors-webpack-plugin/-/friendly-errors-webpack-plugin-1.7.0.tgz", "integrity": "sha512-K27M3VK30wVoOarP651zDmb93R9zF28usW4ocaK3mfQeIEI5BPht/EzZs5E8QLLwbLRJQMwscAjDxYPb1FuNiw==", "dev": true, "requires": {"chalk": "^1.1.3", "error-stack-parser": "^2.0.0", "string-width": "^2.0.0"}, "dependencies": {"ansi-regex": {"version": "2.1.1", "resolved": "https://registry.npmjs.org/ansi-regex/-/ansi-regex-2.1.1.tgz", "integrity": "sha1-w7M6te42DYbg5ijwRorn7yfWVN8=", "dev": true}, "ansi-styles": {"version": "2.2.1", "resolved": "https://registry.npmjs.org/ansi-styles/-/ansi-styles-2.2.1.tgz", "integrity": "sha1-tDLdM1i2NM914eRmQ2gkBTPB3b4=", "dev": true}, "chalk": {"version": "1.1.3", "resolved": "http://registry.npmjs.org/chalk/-/chalk-1.1.3.tgz", "integrity": "sha1-qBFcVeSnAv5NFQq9OHKCKn4J/Jg=", "dev": true, "requires": {"ansi-styles": "^2.2.1", "escape-string-regexp": "^1.0.2", "has-ansi": "^2.0.0", "strip-ansi": "^3.0.0", "supports-color": "^2.0.0"}}, "strip-ansi": {"version": "3.0.1", "resolved": "https://registry.npmjs.org/strip-ansi/-/strip-ansi-3.0.1.tgz", "integrity": "sha1-ajhfuIU9lS1f8F0Oiq+UJ43GPc8=", "dev": true, "requires": {"ansi-regex": "^2.0.0"}}, "supports-color": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/supports-color/-/supports-color-2.0.0.tgz", "integrity": "sha1-U10EXOa2Nj+kARcIRimZXp3zJMc=", "dev": true}}}, "from2": {"version": "2.3.0", "resolved": "https://registry.npmjs.org/from2/-/from2-2.3.0.tgz", "integrity": "sha1-i/tVAr3kpNNs/e6gB/zKIdfjgq8=", "dev": true, "requires": {"inherits": "^2.0.1", "readable-stream": "^2.0.0"}}, "fs": {"version": "0.0.1-security", "resolved": "http://r.npm.sankuai.com/fs/download/fs-0.0.1-security.tgz", "integrity": "sha1-invTcYa23d84E/I4WLV+yq9eQdQ="}, "fs-write-stream-atomic": {"version": "1.0.10", "resolved": "https://registry.npmjs.org/fs-write-stream-atomic/-/fs-write-stream-atomic-1.0.10.tgz", "integrity": "sha1-tH31NJPvkR33VzHnCp3tAYnbQMk=", "dev": true, "requires": {"graceful-fs": "^4.1.2", "iferr": "^0.1.5", "imurmurhash": "^0.1.4", "readable-stream": "1 || 2"}}, "fs.realpath": {"version": "1.0.0", "resolved": "http://r.npm.sankuai.com/fs.realpath/download/fs.realpath-1.0.0.tgz", "integrity": "sha1-FQStJSMVjKpA20onh8sBQRmU6k8=", "dev": true}, "fsevents": {"version": "1.2.4", "resolved": "https://registry.npmjs.org/fsevents/-/fsevents-1.2.4.tgz", "integrity": "sha512-z8H8/diyk76B7q5wg+Ud0+CqzcAF3mBBI/bA5ne5zrRUUIvNkJY//D3BqyH571KuAC4Nr7Rw7CjWX4r0y9DvNg==", "dev": true, "optional": true, "requires": {"nan": "^2.9.2", "node-pre-gyp": "^0.10.0"}, "dependencies": {"abbrev": {"version": "1.1.1", "resolved": false, "integrity": "sha512-nne9/IiQ/hzIhY6pdDnbBtz7DjPTKrY00P/zvPSm5pOFkl6xuGrGnXn/VtTNNfNtAfZ9/1RtehkszU9qcTii0Q==", "dev": true, "optional": true}, "ansi-regex": {"version": "2.1.1", "resolved": false, "integrity": "sha1-w7M6te42DYbg5ijwRorn7yfWVN8=", "dev": true, "optional": true}, "aproba": {"version": "1.2.0", "resolved": false, "integrity": "sha512-Y9J6ZjXtoYh8RnXVCMOU/ttDmk1aBjunq9vO0ta5x85WDQiQfUF9sIPBITdbiiIVcBo03Hi3jMxigBtsddlXRw==", "dev": true, "optional": true}, "are-we-there-yet": {"version": "1.1.4", "resolved": false, "integrity": "sha1-u13KOCu5TwXhUZQ3PRb9O6HKEQ0=", "dev": true, "optional": true, "requires": {"delegates": "^1.0.0", "readable-stream": "^2.0.6"}}, "balanced-match": {"version": "1.0.0", "resolved": false, "integrity": "sha1-ibTRmasr7kneFk6gK4nORi1xt2c=", "dev": true, "optional": true}, "brace-expansion": {"version": "1.1.11", "resolved": false, "integrity": "sha512-iCuPHDFgrHX7H2vEI/5xpz07zSHB00TpugqhmYtVmMO6518mCuRMoOYFldEBl0g187ufozdaHgWKcYFb61qGiA==", "dev": true, "optional": true, "requires": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}}, "chownr": {"version": "1.0.1", "resolved": false, "integrity": "sha1-4qdQQqlVGQi+vSW4Uj1fl2nXkYE=", "dev": true, "optional": true}, "code-point-at": {"version": "1.1.0", "resolved": false, "integrity": "sha1-DQcLTQQ6W+ozovGkDi7bPZpMz3c=", "dev": true, "optional": true}, "concat-map": {"version": "0.0.1", "resolved": false, "integrity": "sha1-2Klr13/Wjfd5OnMDajug1UBdR3s=", "dev": true, "optional": true}, "console-control-strings": {"version": "1.1.0", "resolved": false, "integrity": "sha1-PXz0Rk22RG6mRL9LOVB/mFEAjo4=", "dev": true, "optional": true}, "core-util-is": {"version": "1.0.2", "resolved": false, "integrity": "sha1-tf1UIgqivFq1eqtxQMlAdUUDwac=", "dev": true, "optional": true}, "debug": {"version": "2.6.9", "resolved": false, "integrity": "sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA==", "dev": true, "optional": true, "requires": {"ms": "2.0.0"}}, "deep-extend": {"version": "0.5.1", "resolved": false, "integrity": "sha512-N8vBdOa+DF7zkRrDCsaOXoCs/E2fJfx9B9MrKnnSiHNh4ws7eSys6YQE4KvT1cecKmOASYQBhbKjeuDD9lT81w==", "dev": true, "optional": true}, "delegates": {"version": "1.0.0", "resolved": false, "integrity": "sha1-hMbhWbgZBP3KWaDvRM2HDTElD5o=", "dev": true, "optional": true}, "detect-libc": {"version": "1.0.3", "resolved": false, "integrity": "sha1-+hN8S9aY7fVc1c0CrFWfkaTEups=", "dev": true, "optional": true}, "fs-minipass": {"version": "1.2.5", "resolved": false, "integrity": "sha512-JhBl0skXjUPCFH7x6x61gQxrKyXsxB5gcgePLZCwfyCGGsTISMoIeObbrvVeP6Xmyaudw4TT43qV2Gz+iyd2oQ==", "dev": true, "optional": true, "requires": {"minipass": "^2.2.1"}}, "fs.realpath": {"version": "1.0.0", "resolved": false, "integrity": "sha1-FQStJSMVjKpA20onh8sBQRmU6k8=", "dev": true, "optional": true}, "gauge": {"version": "2.7.4", "resolved": false, "integrity": "sha1-LANAXHU4w51+s3sxcCLjJfsBi/c=", "dev": true, "optional": true, "requires": {"aproba": "^1.0.3", "console-control-strings": "^1.0.0", "has-unicode": "^2.0.0", "object-assign": "^4.1.0", "signal-exit": "^3.0.0", "string-width": "^1.0.1", "strip-ansi": "^3.0.1", "wide-align": "^1.1.0"}}, "glob": {"version": "7.1.2", "resolved": false, "integrity": "sha512-MJTUg1kjuLeQCJ+ccE4Vpa6kKVXkPYJ2mOCQyUuKLcLQsdrMCpBPUi8qVE6+YuaJkozeA9NusTAw3hLr8Xe5EQ==", "dev": true, "optional": true, "requires": {"fs.realpath": "^1.0.0", "inflight": "^1.0.4", "inherits": "2", "minimatch": "^3.0.4", "once": "^1.3.0", "path-is-absolute": "^1.0.0"}}, "has-unicode": {"version": "2.0.1", "resolved": false, "integrity": "sha1-4Ob+aijPUROIVeCG0Wkedx3iqLk=", "dev": true, "optional": true}, "iconv-lite": {"version": "0.4.21", "resolved": false, "integrity": "sha512-En5V9za5mBt2oUA03WGD3TwDv0MKAruqsuxstbMUZaj9W9k/m1CV/9py3l0L5kw9Bln8fdHQmzHSYtvpvTLpKw==", "dev": true, "optional": true, "requires": {"safer-buffer": "^2.1.0"}}, "ignore-walk": {"version": "3.0.1", "resolved": false, "integrity": "sha512-DTVlMx3IYPe0/JJcYP7Gxg7ttZZu3IInhuEhbchuqneY9wWe5Ojy2mXLBaQFUQmo0AW2r3qG7m1mg86js+gnlQ==", "dev": true, "optional": true, "requires": {"minimatch": "^3.0.4"}}, "inflight": {"version": "1.0.6", "resolved": false, "integrity": "sha1-Sb1jMdfQLQwJvJEKEHW6gWW1bfk=", "dev": true, "optional": true, "requires": {"once": "^1.3.0", "wrappy": "1"}}, "inherits": {"version": "2.0.3", "resolved": false, "integrity": "sha1-Yzwsg+PaQqUC9SRmAiSA9CCCYd4=", "dev": true, "optional": true}, "ini": {"version": "1.3.5", "resolved": false, "integrity": "sha512-RZY5huIKCMRWDUqZlEi72f/lmXKMvuszcMBduliQ3nnWbx9X/ZBQO7DijMEYS9EhHBb2qacRUMtC7svLwe0lcw==", "dev": true, "optional": true}, "is-fullwidth-code-point": {"version": "1.0.0", "resolved": false, "integrity": "sha1-754xOG8DGn8NZDr4L95QxFfvAMs=", "dev": true, "optional": true, "requires": {"number-is-nan": "^1.0.0"}}, "isarray": {"version": "1.0.0", "resolved": false, "integrity": "sha1-u5NdSFgsuhaMBoNJV6VKPgcSTxE=", "dev": true, "optional": true}, "minimatch": {"version": "3.0.4", "resolved": false, "integrity": "sha512-yJHVQEhyqPLUTgt9B83PXu6W3rx4MvvHvSUvToogpwoGDOUQ+yDrR0HRot+yOCdCO7u4hX3pWft6kWBBcqh0UA==", "dev": true, "optional": true, "requires": {"brace-expansion": "^1.1.7"}}, "minimist": {"version": "0.0.8", "resolved": false, "integrity": "sha1-hX/Kv8M5fSYluCKCYuhqp6ARsF0=", "dev": true, "optional": true}, "minipass": {"version": "2.2.4", "resolved": false, "integrity": "sha512-hzXIWWet/BzWhYs2b+u7dRHlruXhwdgvlTMDKC6Cb1U7ps6Ac6yQlR39xsbjWJE377YTCtKwIXIpJ5oP+j5y8g==", "dev": true, "optional": true, "requires": {"safe-buffer": "^5.1.1", "yallist": "^3.0.0"}}, "minizlib": {"version": "1.1.0", "resolved": false, "integrity": "sha512-4T6Ur/GctZ27nHfpt9THOdRZNgyJ9FZchYO1ceg5S8Q3DNLCKYy44nCZzgCJgcvx2UM8czmqak5BCxJMrq37lA==", "dev": true, "optional": true, "requires": {"minipass": "^2.2.1"}}, "mkdirp": {"version": "0.5.1", "resolved": false, "integrity": "sha1-MAV0OOrGz3+MR2fzhkjWaX11yQM=", "dev": true, "optional": true, "requires": {"minimist": "0.0.8"}}, "ms": {"version": "2.0.0", "resolved": false, "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g=", "dev": true, "optional": true}, "needle": {"version": "2.2.0", "resolved": false, "integrity": "sha512-eFagy6c+TYayorXw/qtAdSvaUpEbBsDwDyxYFgLZ0lTojfH7K+OdBqAF7TAFwDokJaGpubpSGG0wO3iC0XPi8w==", "dev": true, "optional": true, "requires": {"debug": "^2.1.2", "iconv-lite": "^0.4.4", "sax": "^1.2.4"}}, "node-pre-gyp": {"version": "0.10.0", "resolved": false, "integrity": "sha512-G7kEonQLRbcA/mOoFoxvlMrw6Q6dPf92+t/l0DFSMuSlDoWaI9JWIyPwK0jyE1bph//CUEL65/Fz1m2vJbmjQQ==", "dev": true, "optional": true, "requires": {"detect-libc": "^1.0.2", "mkdirp": "^0.5.1", "needle": "^2.2.0", "nopt": "^4.0.1", "npm-packlist": "^1.1.6", "npmlog": "^4.0.2", "rc": "^1.1.7", "rimraf": "^2.6.1", "semver": "^5.3.0", "tar": "^4"}}, "nopt": {"version": "4.0.1", "resolved": false, "integrity": "sha1-0NRoWv1UFRk8jHUFYC0NF81kR00=", "dev": true, "optional": true, "requires": {"abbrev": "1", "osenv": "^0.1.4"}}, "npm-bundled": {"version": "1.0.3", "resolved": false, "integrity": "sha512-ByQ3oJ/5ETLyglU2+8dBObvhfWXX8dtPZDMePCahptliFX2iIuhyEszyFk401PZUNQH20vvdW5MLjJxkwU80Ow==", "dev": true, "optional": true}, "npm-packlist": {"version": "1.1.10", "resolved": false, "integrity": "sha512-AQC0Dyhzn4EiYEfIUjCdMl0JJ61I2ER9ukf/sLxJUcZHfo+VyEfz2rMJgLZSS1v30OxPQe1cN0LZA1xbcaVfWA==", "dev": true, "optional": true, "requires": {"ignore-walk": "^3.0.1", "npm-bundled": "^1.0.1"}}, "npmlog": {"version": "4.1.2", "resolved": false, "integrity": "sha512-2uUqazuKlTaSI/dC8AzicUck7+IrEaOnN/e0jd3Xtt1KcGpwx30v50mL7oPyr/h9bL3E4aZccVwpwP+5W9Vjkg==", "dev": true, "optional": true, "requires": {"are-we-there-yet": "~1.1.2", "console-control-strings": "~1.1.0", "gauge": "~2.7.3", "set-blocking": "~2.0.0"}}, "number-is-nan": {"version": "1.0.1", "resolved": false, "integrity": "sha1-CXtgK1NCKlIsGvuHkDGDNpQaAR0=", "dev": true, "optional": true}, "object-assign": {"version": "4.1.1", "resolved": false, "integrity": "sha1-IQmtx5ZYh8/AXLvUQsrIv7s2CGM=", "dev": true, "optional": true}, "once": {"version": "1.4.0", "resolved": false, "integrity": "sha1-WDsap3WWHUsROsF9nFC6753Xa9E=", "dev": true, "optional": true, "requires": {"wrappy": "1"}}, "os-homedir": {"version": "1.0.2", "resolved": false, "integrity": "sha1-/7xJiDNuDoM94MFox+8VISGqf7M=", "dev": true, "optional": true}, "os-tmpdir": {"version": "1.0.2", "resolved": false, "integrity": "sha1-u+Z0BseaqFxc/sdm/lc0VV36EnQ=", "dev": true, "optional": true}, "osenv": {"version": "0.1.5", "resolved": false, "integrity": "sha512-0CWcCECdMVc2Rw3U5w9ZjqX6ga6ubk1xDVKxtBQPK7wis/0F2r9T6k4ydGYhecl7YUBxBVxhL5oisPsNxAPe2g==", "dev": true, "optional": true, "requires": {"os-homedir": "^1.0.0", "os-tmpdir": "^1.0.0"}}, "path-is-absolute": {"version": "1.0.1", "resolved": false, "integrity": "sha1-F0uSaHNVNP+8es5r9TpanhtcX18=", "dev": true, "optional": true}, "process-nextick-args": {"version": "2.0.0", "resolved": false, "integrity": "sha512-MtEC1TqN0EU5nephaJ4rAtThHtC86dNN9qCuEhtshvpVBkAW5ZO7BASN9REnF9eoXGcRub+pFuKEpOHE+HbEMw==", "dev": true, "optional": true}, "rc": {"version": "1.2.7", "resolved": false, "integrity": "sha512-LdLD8xD4zzLsAT5xyushXDNscEjB7+2ulnl8+r1pnESlYtlJtVSoCMBGr30eDRJ3+2Gq89jK9P9e4tCEH1+ywA==", "dev": true, "optional": true, "requires": {"deep-extend": "^0.5.1", "ini": "~1.3.0", "minimist": "^1.2.0", "strip-json-comments": "~2.0.1"}, "dependencies": {"minimist": {"version": "1.2.0", "resolved": false, "integrity": "sha1-o1AIsg9BOD7sH7kU9M1d95omQoQ=", "dev": true, "optional": true}}}, "readable-stream": {"version": "2.3.6", "resolved": false, "integrity": "sha512-tQtKA9WIAhBF3+VLAseyMqZeBjW0AHJoxOtYqSUZNJxauErmLbVm2FW1y+J/YA9dUrAC39ITejlZWhVIwawkKw==", "dev": true, "optional": true, "requires": {"core-util-is": "~1.0.0", "inherits": "~2.0.3", "isarray": "~1.0.0", "process-nextick-args": "~2.0.0", "safe-buffer": "~5.1.1", "string_decoder": "~1.1.1", "util-deprecate": "~1.0.1"}}, "rimraf": {"version": "2.6.2", "resolved": false, "integrity": "sha512-lreewLK/BlghmxtfH36YYVg1i8IAce4TI7oao75I1g245+6BctqTVQiBP3YUJ9C6DQOXJmkYR9X9fCLtCOJc5w==", "dev": true, "optional": true, "requires": {"glob": "^7.0.5"}}, "safe-buffer": {"version": "5.1.1", "resolved": false, "integrity": "sha512-kKvNJn6Mm93gAczWVJg7wH+wGYWNrDHdWvpUmHyEsgCtIwwo3bqPtV4tR5tuPaUhTOo/kvhVwd8XwwOllGYkbg==", "dev": true, "optional": true}, "safer-buffer": {"version": "2.1.2", "resolved": false, "integrity": "sha512-YZo3K82SD7Riyi0E1EQPojLz7kpepnSQI9IyPbHHg1XXXevb5dJI7tpyN2ADxGcQbHG7vcyRHk0cbwqcQriUtg==", "dev": true, "optional": true}, "sax": {"version": "1.2.4", "resolved": false, "integrity": "sha512-NqVDv9TpANUjFm0N8uM5GxL36UgKi9/atZw+x7YFnQ8ckwFGKrl4xX4yWtrey3UJm5nP1kUbnYgLopqWNSRhWw==", "dev": true, "optional": true}, "semver": {"version": "5.5.0", "resolved": false, "integrity": "sha512-4SJ3dm0WAwWy/NVeioZh5AntkdJoWKxHxcmyP622fOkgHa4z3R0TdBJICINyaSDE6uNwVc8gZr+ZinwZAH4xIA==", "dev": true, "optional": true}, "set-blocking": {"version": "2.0.0", "resolved": false, "integrity": "sha1-BF+XgtARrppoA93TgrJDkrPYkPc=", "dev": true, "optional": true}, "signal-exit": {"version": "3.0.2", "resolved": false, "integrity": "sha1-tf3AjxKH6hF4Yo5BXiUTK3NkbG0=", "dev": true, "optional": true}, "string-width": {"version": "1.0.2", "resolved": false, "integrity": "sha1-EYvfW4zcUaKn5w0hHgfisLmxB9M=", "dev": true, "optional": true, "requires": {"code-point-at": "^1.0.0", "is-fullwidth-code-point": "^1.0.0", "strip-ansi": "^3.0.0"}}, "string_decoder": {"version": "1.1.1", "resolved": false, "integrity": "sha512-n/ShnvDi6FHbbVfviro+WojiFzv+s8MPMHBczVePfUpDJLwoLT0ht1l4YwBCbi8pJAveEEdnkHyPyTP/mzRfwg==", "dev": true, "optional": true, "requires": {"safe-buffer": "~5.1.0"}}, "strip-ansi": {"version": "3.0.1", "resolved": false, "integrity": "sha1-ajhfuIU9lS1f8F0Oiq+UJ43GPc8=", "dev": true, "optional": true, "requires": {"ansi-regex": "^2.0.0"}}, "strip-json-comments": {"version": "2.0.1", "resolved": false, "integrity": "sha1-PFMZQukIwml8DsNEhYwobHygpgo=", "dev": true, "optional": true}, "tar": {"version": "4.4.1", "resolved": false, "integrity": "sha512-O+v1r9yN4tOsvl90p5HAP4AEqbYhx4036AGMm075fH9F8Qwi3oJ+v4u50FkT/KkvywNGtwkk0zRI+8eYm1X/xg==", "dev": true, "optional": true, "requires": {"chownr": "^1.0.1", "fs-minipass": "^1.2.5", "minipass": "^2.2.4", "minizlib": "^1.1.0", "mkdirp": "^0.5.0", "safe-buffer": "^5.1.1", "yallist": "^3.0.2"}}, "util-deprecate": {"version": "1.0.2", "resolved": false, "integrity": "sha1-RQ1Nyfpw3nMnYvvS1KKJgUGaDM8=", "dev": true, "optional": true}, "wide-align": {"version": "1.1.2", "resolved": false, "integrity": "sha512-ijDLlyQ7s6x1JgCLur53osjm/UXUYD9+0PbYKrBsYisYXzCxN+HC3mYDNy/dWdmf3AwqwU3CXwDCvsNgGK1S0w==", "dev": true, "optional": true, "requires": {"string-width": "^1.0.2"}}, "wrappy": {"version": "1.0.2", "resolved": false, "integrity": "sha1-tSQ9jz7BqjXxNkYFvA0QNuMKtp8=", "dev": true, "optional": true}, "yallist": {"version": "3.0.2", "resolved": false, "integrity": "sha1-hFK0u36Dx8GI2AQcGoN8dz1ti7k=", "dev": true, "optional": true}}}, "fstream": {"version": "1.0.12", "resolved": "https://registry.npm.taobao.org/fstream/download/fstream-1.0.12.tgz", "integrity": "sha1-Touo7i1Ivk99DeUFRVVI6uWTIEU=", "dev": true, "requires": {"graceful-fs": "^4.1.2", "inherits": "~2.0.0", "mkdirp": ">=0.5 0", "rimraf": "2"}}, "function-bind": {"version": "1.1.1", "resolved": "http://r.npm.sankuai.com/function-bind/download/function-bind-1.1.1.tgz", "integrity": "sha1-pWiZ0+o8m6uHS7l3O3xe3pL0iV0="}, "functional-red-black-tree": {"version": "1.0.1", "resolved": "http://r.npm.sankuai.com/functional-red-black-tree/download/functional-red-black-tree-1.0.1.tgz", "integrity": "sha1-GwqzvVU7Kg1jmdKcDj6gslIHgyc=", "dev": true}, "gauge": {"version": "2.7.4", "resolved": "https://registry.npmjs.org/gauge/-/gauge-2.7.4.tgz", "integrity": "sha1-LANAXHU4w51+s3sxcCLjJfsBi/c=", "dev": true, "requires": {"aproba": "^1.0.3", "console-control-strings": "^1.0.0", "has-unicode": "^2.0.0", "object-assign": "^4.1.0", "signal-exit": "^3.0.0", "string-width": "^1.0.1", "strip-ansi": "^3.0.1", "wide-align": "^1.1.0"}, "dependencies": {"ansi-regex": {"version": "2.1.1", "resolved": "https://registry.npmjs.org/ansi-regex/-/ansi-regex-2.1.1.tgz", "integrity": "sha1-w7M6te42DYbg5ijwRorn7yfWVN8=", "dev": true}, "is-fullwidth-code-point": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/is-fullwidth-code-point/-/is-fullwidth-code-point-1.0.0.tgz", "integrity": "sha1-754xOG8DGn8NZDr4L95QxFfvAMs=", "dev": true, "requires": {"number-is-nan": "^1.0.0"}}, "string-width": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/string-width/-/string-width-1.0.2.tgz", "integrity": "sha1-EYvfW4zcUaKn5w0hHgfisLmxB9M=", "dev": true, "requires": {"code-point-at": "^1.0.0", "is-fullwidth-code-point": "^1.0.0", "strip-ansi": "^3.0.0"}}, "strip-ansi": {"version": "3.0.1", "resolved": "https://registry.npmjs.org/strip-ansi/-/strip-ansi-3.0.1.tgz", "integrity": "sha1-ajhfuIU9lS1f8F0Oiq+UJ43GPc8=", "dev": true, "requires": {"ansi-regex": "^2.0.0"}}}}, "gaze": {"version": "1.1.3", "resolved": "https://registry.npm.taobao.org/gaze/download/gaze-1.1.3.tgz", "integrity": "sha1-xEFzPhO5J6yMD/C0w7Az8ogSkko=", "dev": true, "requires": {"globule": "^1.0.0"}}, "get-caller-file": {"version": "1.0.3", "resolved": "https://registry.npmjs.org/get-caller-file/-/get-caller-file-1.0.3.tgz", "integrity": "sha512-3t6rVToeoZfYSGd8YoLFR2DJkiQrIiUrGcjvFX2mDw3bn6k2OtwHN0TNCLbBO+w8qTvimhDkv+LSscbJY1vE6w==", "dev": true}, "get-intrinsic": {"version": "1.1.3", "resolved": "http://r.npm.sankuai.com/get-intrinsic/download/get-intrinsic-1.1.3.tgz", "integrity": "sha1-BjyEMprZPoOJPH9PJD72P/o1E4U=", "requires": {"function-bind": "^1.1.1", "has": "^1.0.3", "has-symbols": "^1.0.3"}, "dependencies": {"has-symbols": {"version": "1.0.3", "resolved": "http://r.npm.sankuai.com/has-symbols/download/has-symbols-1.0.3.tgz", "integrity": "sha1-u3ssQ0klHc6HsSX3vfh0qnyLOfg="}}}, "get-stdin": {"version": "4.0.1", "resolved": "https://registry.npmjs.org/get-stdin/-/get-stdin-4.0.1.tgz", "integrity": "sha1-uWjGsKBDhDJJAui/Gl3zJXmkUP4=", "dev": true}, "get-stream": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/get-stream/-/get-stream-3.0.0.tgz", "integrity": "sha1-jpQ9E1jcN1VQVOy+LtsFqhdO3hQ=", "dev": true}, "get-value": {"version": "2.0.6", "resolved": "https://registry.npmjs.org/get-value/-/get-value-2.0.6.tgz", "integrity": "sha1-3BXKHGcjh8p2vTesCjlbogQqLCg=", "dev": true}, "getpass": {"version": "0.1.7", "resolved": "https://registry.npmjs.org/getpass/-/getpass-0.1.7.tgz", "integrity": "sha1-Xv+OPmhNVprkyysSgmBOi6YhSfo=", "requires": {"assert-plus": "^1.0.0"}}, "git-raw-commits": {"version": "1.3.6", "resolved": "http://r.npm.sankuai.com/git-raw-commits/download/git-raw-commits-1.3.6.tgz", "integrity": "sha1-J8NaMqZ3d8Hs1BKiOabBnXG5Wv8=", "dev": true, "requires": {"dargs": "^4.0.1", "lodash.template": "^4.0.2", "meow": "^4.0.0", "split2": "^2.0.0", "through2": "^2.0.0"}, "dependencies": {"camelcase-keys": {"version": "4.2.0", "resolved": "http://r.npm.sankuai.com/camelcase-keys/download/camelcase-keys-4.2.0.tgz", "integrity": "sha1-oqpfsa9oh1glnDLBQUJteJI7m3c=", "dev": true, "requires": {"camelcase": "^4.1.0", "map-obj": "^2.0.0", "quick-lru": "^1.0.0"}}, "find-up": {"version": "2.1.0", "resolved": "http://r.npm.sankuai.com/find-up/download/find-up-2.1.0.tgz", "integrity": "sha1-RdG35QbHF93UgndaK3eSCjwMV6c=", "dev": true, "requires": {"locate-path": "^2.0.0"}}, "indent-string": {"version": "3.2.0", "resolved": "http://r.npm.sankuai.com/indent-string/download/indent-string-3.2.0.tgz", "integrity": "sha1-Sl/W0nzDMvN+VBmlBNu4NxBckok=", "dev": true}, "load-json-file": {"version": "4.0.0", "resolved": "http://r.npm.sankuai.com/load-json-file/download/load-json-file-4.0.0.tgz", "integrity": "sha1-L19Fq5HjMhYjT9U62rZo607AmTs=", "dev": true, "requires": {"graceful-fs": "^4.1.2", "parse-json": "^4.0.0", "pify": "^3.0.0", "strip-bom": "^3.0.0"}}, "map-obj": {"version": "2.0.0", "resolved": "http://r.npm.sankuai.com/map-obj/download/map-obj-2.0.0.tgz", "integrity": "sha1-plzSkIepJZi4eRJXpSPgISIqwfk=", "dev": true}, "meow": {"version": "4.0.1", "resolved": "http://r.npm.sankuai.com/meow/download/meow-4.0.1.tgz", "integrity": "sha1-1IWY9vSxRy81v2MXqVlFrONH+XU=", "dev": true, "requires": {"camelcase-keys": "^4.0.0", "decamelize-keys": "^1.0.0", "loud-rejection": "^1.0.0", "minimist": "^1.1.3", "minimist-options": "^3.0.1", "normalize-package-data": "^2.3.4", "read-pkg-up": "^3.0.0", "redent": "^2.0.0", "trim-newlines": "^2.0.0"}}, "minimist": {"version": "1.2.0", "resolved": "http://r.npm.sankuai.com/minimist/download/minimist-1.2.0.tgz", "integrity": "sha1-o1AIsg9BOD7sH7kU9M1d95omQoQ=", "dev": true}, "parse-json": {"version": "4.0.0", "resolved": "http://r.npm.sankuai.com/parse-json/download/parse-json-4.0.0.tgz", "integrity": "sha1-vjX1Qlvh9/bHRxhPmKeIy5lHfuA=", "dev": true, "requires": {"error-ex": "^1.3.1", "json-parse-better-errors": "^1.0.1"}}, "path-type": {"version": "3.0.0", "resolved": "http://r.npm.sankuai.com/path-type/download/path-type-3.0.0.tgz", "integrity": "sha1-zvMdyOCho7sNEFwM2Xzzv0f0428=", "dev": true, "requires": {"pify": "^3.0.0"}}, "pify": {"version": "3.0.0", "resolved": "http://r.npm.sankuai.com/pify/download/pify-3.0.0.tgz", "integrity": "sha1-5aSs0sEB/fPZpNB/DbxNtJ3SgXY=", "dev": true}, "read-pkg": {"version": "3.0.0", "resolved": "http://r.npm.sankuai.com/read-pkg/download/read-pkg-3.0.0.tgz", "integrity": "sha1-nLxoaXj+5l0WwA4rGcI3/Pbjg4k=", "dev": true, "requires": {"load-json-file": "^4.0.0", "normalize-package-data": "^2.3.2", "path-type": "^3.0.0"}}, "read-pkg-up": {"version": "3.0.0", "resolved": "http://r.npm.sankuai.com/read-pkg-up/download/read-pkg-up-3.0.0.tgz", "integrity": "sha1-PtSWaF26D4/hGNBpHcUfSh/5bwc=", "dev": true, "requires": {"find-up": "^2.0.0", "read-pkg": "^3.0.0"}}, "redent": {"version": "2.0.0", "resolved": "http://r.npm.sankuai.com/redent/download/redent-2.0.0.tgz", "integrity": "sha1-wbIAe0LVfrE4kHmzyDM2OdXhzKo=", "dev": true, "requires": {"indent-string": "^3.0.0", "strip-indent": "^2.0.0"}}, "strip-indent": {"version": "2.0.0", "resolved": "http://r.npm.sankuai.com/strip-indent/download/strip-indent-2.0.0.tgz", "integrity": "sha1-XvjbKV0B5u1sv3qrlpmNeCJSe2g=", "dev": true}, "trim-newlines": {"version": "2.0.0", "resolved": "http://r.npm.sankuai.com/trim-newlines/download/trim-newlines-2.0.0.tgz", "integrity": "sha1-tAPQuRvlDDMd/EuC7s6yLD3hbSA=", "dev": true}}}, "glob": {"version": "7.1.3", "resolved": "http://r.npm.sankuai.com/glob/download/glob-7.1.3.tgz", "integrity": "sha1-OWCDLT8VdBCDQtr9OmezMsCWnfE=", "dev": true, "requires": {"fs.realpath": "^1.0.0", "inflight": "^1.0.4", "inherits": "2", "minimatch": "^3.0.4", "once": "^1.3.0", "path-is-absolute": "^1.0.0"}}, "glob-parent": {"version": "3.1.0", "resolved": "https://registry.npmjs.org/glob-parent/-/glob-parent-3.1.0.tgz", "integrity": "sha1-nmr2KZ2NO9K9QEMIMr0RPfkGxa4=", "dev": true, "requires": {"is-glob": "^3.1.0", "path-dirname": "^1.0.0"}, "dependencies": {"is-glob": {"version": "3.1.0", "resolved": "https://registry.npmjs.org/is-glob/-/is-glob-3.1.0.tgz", "integrity": "sha1-e6WuJCF4BKxwcHuWkiVnSGzD6Eo=", "dev": true, "requires": {"is-extglob": "^2.1.0"}}}}, "global-dirs": {"version": "0.1.1", "resolved": "http://r.npm.sankuai.com/global-dirs/download/global-dirs-0.1.1.tgz", "integrity": "sha1-sxnA3UYH81PzvpzKTHL8FIxJ9EU=", "dev": true, "requires": {"ini": "^1.3.4"}}, "global-modules-path": {"version": "2.3.0", "resolved": "https://registry.npmjs.org/global-modules-path/-/global-modules-path-2.3.0.tgz", "integrity": "sha512-HchvMJNYh9dGSCy8pOQ2O8u/hoXaL+0XhnrwH0RyLiSXMMTl9W3N6KUU73+JFOg5PGjtzl6VZzUQsnrpm7Szag==", "dev": true}, "globals": {"version": "11.7.0", "resolved": "http://r.npm.sankuai.com/globals/download/globals-11.7.0.tgz", "integrity": "sha1-pYP6pDBVsayncZFL9oJY4vwSVnM=", "dev": true}, "globby": {"version": "5.0.0", "resolved": "http://r.npm.sankuai.com/globby/download/globby-5.0.0.tgz", "integrity": "sha1-69hGZ8oNuzMLmbz8aOrCvFQ3Dg0=", "dev": true, "requires": {"array-union": "^1.0.1", "arrify": "^1.0.0", "glob": "^7.0.3", "object-assign": "^4.0.1", "pify": "^2.0.0", "pinkie-promise": "^2.0.0"}}, "globule": {"version": "1.3.2", "resolved": "https://registry.npm.taobao.org/globule/download/globule-1.3.2.tgz?cache=0&sync_timestamp=1591641966255&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fglobule%2Fdownload%2Fglobule-1.3.2.tgz", "integrity": "sha1-2L3Z6eTu+PluJFmZpd7n612FKcQ=", "dev": true, "requires": {"glob": "~7.1.1", "lodash": "~4.17.10", "minimatch": "~3.0.2"}}, "graceful-fs": {"version": "4.1.11", "resolved": "http://r.npm.sankuai.com/graceful-fs/download/graceful-fs-4.1.11.tgz", "integrity": "sha1-Dovf5NHduIVNZOBOp8AOKgJuVlg=", "dev": true}, "handle-thing": {"version": "1.2.5", "resolved": "https://registry.npmjs.org/handle-thing/-/handle-thing-1.2.5.tgz", "integrity": "sha1-/Xqtcmvxpf0W38KbL3pmAdJxOcQ=", "dev": true}, "har-schema": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/har-schema/-/har-schema-2.0.0.tgz", "integrity": "sha1-qUwiJOvKwEeCoNkDVSHyRzW37JI="}, "har-validator": {"version": "5.1.5", "resolved": "https://registry.npm.taobao.org/har-validator/download/har-validator-5.1.5.tgz?cache=0&sync_timestamp=1596084327526&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fhar-validator%2Fdownload%2Fhar-validator-5.1.5.tgz", "integrity": "sha1-HwgDufjLIMD6E4It8ezds2veHv0=", "requires": {"ajv": "^6.12.3", "har-schema": "^2.0.0"}, "dependencies": {"ajv": {"version": "6.12.6", "resolved": "https://registry.npm.taobao.org/ajv/download/ajv-6.12.6.tgz?cache=0&sync_timestamp=1603561531202&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fajv%2Fdownload%2Fajv-6.12.6.tgz", "integrity": "sha1-uvWmLoArB9l3A0WG+MO69a3ybfQ=", "requires": {"fast-deep-equal": "^3.1.1", "fast-json-stable-stringify": "^2.0.0", "json-schema-traverse": "^0.4.1", "uri-js": "^4.2.2"}}, "fast-deep-equal": {"version": "3.1.3", "resolved": "https://registry.npm.taobao.org/fast-deep-equal/download/fast-deep-equal-3.1.3.tgz", "integrity": "sha1-On1WtVnWy8PrUSMlJE5hmmXGxSU="}}}, "has": {"version": "1.0.3", "resolved": "http://r.npm.sankuai.com/has/download/has-1.0.3.tgz", "integrity": "sha1-ci18v8H2qoJB8W3YFOAR4fQeh5Y=", "requires": {"function-bind": "^1.1.1"}}, "has-ansi": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/has-ansi/-/has-ansi-2.0.0.tgz", "integrity": "sha1-NPUEnOHs3ysGSa8+8k5F7TVBbZE=", "dev": true, "requires": {"ansi-regex": "^2.0.0"}, "dependencies": {"ansi-regex": {"version": "2.1.1", "resolved": "https://registry.npmjs.org/ansi-regex/-/ansi-regex-2.1.1.tgz", "integrity": "sha1-w7M6te42DYbg5ijwRorn7yfWVN8=", "dev": true}}}, "has-flag": {"version": "3.0.0", "resolved": "http://r.npm.sankuai.com/has-flag/download/has-flag-3.0.0.tgz", "integrity": "sha1-tdRU3CGZriJWmfNGfloH87lVuv0=", "dev": true}, "has-symbols": {"version": "1.0.0", "resolved": "http://r.npm.sankuai.com/has-symbols/download/has-symbols-1.0.0.tgz", "integrity": "sha1-uhqPGvKg/DllD1yFA2dwQSIGO0Q=", "dev": true}, "has-unicode": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/has-unicode/-/has-unicode-2.0.1.tgz", "integrity": "sha1-4Ob+aijPUROIVeCG0Wkedx3iqLk=", "dev": true}, "has-value": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/has-value/-/has-value-1.0.0.tgz", "integrity": "sha1-GLKB2lhbHFxR3vJMkw7SmgvmsXc=", "dev": true, "requires": {"get-value": "^2.0.6", "has-values": "^1.0.0", "isobject": "^3.0.0"}}, "has-values": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/has-values/-/has-values-1.0.0.tgz", "integrity": "sha1-lbC2P+whRmGab+V/51Yo1aOe/k8=", "dev": true, "requires": {"is-number": "^3.0.0", "kind-of": "^4.0.0"}, "dependencies": {"kind-of": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/kind-of/-/kind-of-4.0.0.tgz", "integrity": "sha1-IIE989cSkosgc3hpGkUGb65y3Vc=", "dev": true, "requires": {"is-buffer": "^1.1.5"}}}}, "hash-base": {"version": "3.0.4", "resolved": "https://registry.npmjs.org/hash-base/-/hash-base-3.0.4.tgz", "integrity": "sha1-X8hoaEfs1zSZQDMZprCj8/auSRg=", "dev": true, "requires": {"inherits": "^2.0.1", "safe-buffer": "^5.0.1"}}, "hash-sum": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/hash-sum/-/hash-sum-1.0.2.tgz", "integrity": "sha1-M7QHd3VMZDJXPBIMw4CLvRDUfwQ=", "dev": true}, "hash.js": {"version": "1.1.5", "resolved": "https://registry.npmjs.org/hash.js/-/hash.js-1.1.5.tgz", "integrity": "sha512-eWI5HG9Np+eHV1KQhisXWwM+4EPPYe5dFX1UZZH7k/E3JzDEazVH+VGlZi6R94ZqImq+A3D1mCEtrFIfg/E7sA==", "dev": true, "requires": {"inherits": "^2.0.3", "minimalistic-assert": "^1.0.1"}}, "hasown": {"version": "2.0.1", "resolved": "http://r.npm.sankuai.com/hasown/download/hasown-2.0.1.tgz", "integrity": "sha1-JvSPA53iwPjTNWwiP7jVAlNRn6o=", "requires": {"function-bind": "^1.1.2"}, "dependencies": {"function-bind": {"version": "1.1.2", "resolved": "http://r.npm.sankuai.com/function-bind/download/function-bind-1.1.2.tgz", "integrity": "sha1-LALYZNl/PqbIgwxGTL0Rq26rehw="}}}, "he": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/he/-/he-1.1.1.tgz", "integrity": "sha1-k0EP0hsAlzUVH4howvJx80J+I/0=", "dev": true}, "hex-color-regex": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/hex-color-regex/-/hex-color-regex-1.1.0.tgz", "integrity": "sha512-l9sfDFsuqtOqKDsQdqrMRk0U85RZc0RtOR9yPI7mRVOa4FsR/BVnZ0shmQRM96Ji99kYZP/7hn1cedc1+ApsTQ==", "dev": true}, "highlight.js": {"version": "9.18.5", "resolved": "http://r.npm.sankuai.com/highlight.js/download/highlight.js-9.18.5.tgz", "integrity": "sha1-0Yo1mGfzeME41oGe38KorNXymCU="}, "hmac-drbg": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/hmac-drbg/-/hmac-drbg-1.0.1.tgz", "integrity": "sha1-0nRXAQJabHdabFRXk+1QL8DGSaE=", "dev": true, "requires": {"hash.js": "^1.0.3", "minimalistic-assert": "^1.0.0", "minimalistic-crypto-utils": "^1.0.1"}}, "hosted-git-info": {"version": "2.7.1", "resolved": "http://r.npm.sankuai.com/hosted-git-info/download/hosted-git-info-2.7.1.tgz", "integrity": "sha1-l/I2l3vW4SVAiTD/bePuxigewEc=", "dev": true}, "hpack.js": {"version": "2.1.6", "resolved": "https://registry.npmjs.org/hpack.js/-/hpack.js-2.1.6.tgz", "integrity": "sha1-h3dMCUnlE/QuhFdbPEVoH63ioLI=", "dev": true, "requires": {"inherits": "^2.0.1", "obuf": "^1.0.0", "readable-stream": "^2.0.1", "wbuf": "^1.1.0"}}, "hsl-regex": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/hsl-regex/-/hsl-regex-1.0.0.tgz", "integrity": "sha1-1JMwx4ntgZ4nakwNJy3/owsY/m4=", "dev": true}, "hsla-regex": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/hsla-regex/-/hsla-regex-1.0.0.tgz", "integrity": "sha1-wc56MWjIxmFAM6S194d/OyJfnDg=", "dev": true}, "html-comment-regex": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/html-comment-regex/-/html-comment-regex-1.1.1.tgz", "integrity": "sha1-ZouTd26q5V696POtRkswekljYl4=", "dev": true}, "html-entities": {"version": "1.2.1", "resolved": "https://registry.npmjs.org/html-entities/-/html-entities-1.2.1.tgz", "integrity": "sha1-DfKTUfByEWNRXfueVUPl9u7VFi8=", "dev": true}, "html-minifier": {"version": "3.5.20", "resolved": "https://registry.npmjs.org/html-minifier/-/html-minifier-3.5.20.tgz", "integrity": "sha512-ZmgNLaTp54+HFKkONyLFEfs5dd/ZOtlquKaTnqIWFmx3Av5zG6ZPcV2d0o9XM2fXOTxxIf6eDcwzFFotke/5zA==", "dev": true, "requires": {"camel-case": "3.0.x", "clean-css": "4.2.x", "commander": "2.17.x", "he": "1.1.x", "param-case": "2.1.x", "relateurl": "0.2.x", "uglify-js": "3.4.x"}, "dependencies": {"commander": {"version": "2.17.1", "resolved": "https://registry.npmjs.org/commander/-/commander-2.17.1.tgz", "integrity": "sha512-wPMUt6FnH2yzG95SA6mzjQOEKUU3aLaDEmzs1ti+1E9h+CsrZghRlqEM/EJ4KscsQVG8uNN4uVreUeT8+drlgg==", "dev": true}, "source-map": {"version": "0.6.1", "resolved": "https://registry.npmjs.org/source-map/-/source-map-0.6.1.tgz", "integrity": "sha512-UjgapumWlbMhkBgzT7Ykc5YXUT46F0iKu8SGXq0bcwP5dz/h0Plj6enJqjz1Zbq2l5WaqYnrVbwWOWMyF3F47g==", "dev": true}, "uglify-js": {"version": "3.4.9", "resolved": "https://registry.npmjs.org/uglify-js/-/uglify-js-3.4.9.tgz", "integrity": "sha512-8<PERSON><PERSON>sbKOtEbnJsTyv6LE6m6ZKniqMiFWmm9sRbopbkGs3gMPPfd3Fh8iIA4Ykv5MgaTbqHr4BaoGLJLZNhsrW1Q==", "dev": true, "requires": {"commander": "~2.17.1", "source-map": "~0.6.1"}}}}, "html-webpack-plugin": {"version": "4.0.0-beta.2", "resolved": "https://registry.npmjs.org/html-webpack-plugin/-/html-webpack-plugin-4.0.0-beta.2.tgz", "integrity": "sha512-153QgkvYPOc1X5/v1GFPcq7GTinNheGA1lMZUGRMFkwIQ4kegGna+wQ0ByJ8uNgw4u1aEg9FtsSKs4AzsYMi9g==", "dev": true, "requires": {"html-minifier": "^3.5.20", "loader-utils": "^1.1.0", "lodash": "^4.17.11", "pretty-error": "^2.1.1", "tapable": "^1.1.0", "util.promisify": "1.0.0"}}, "htmlparser2": {"version": "3.9.2", "resolved": "http://r.npm.sankuai.com/htmlparser2/download/htmlparser2-3.9.2.tgz", "integrity": "sha1-G9+HrMoPP55T+k/M6w9LTLsAszg=", "requires": {"domelementtype": "^1.3.0", "domhandler": "^2.3.0", "domutils": "^1.5.1", "entities": "^1.1.1", "inherits": "^2.0.1", "readable-stream": "^2.0.2"}}, "http-deceiver": {"version": "1.2.7", "resolved": "https://registry.npmjs.org/http-deceiver/-/http-deceiver-1.2.7.tgz", "integrity": "sha1-+nFolEq5pRnTN8sL7HKE3D5yPYc=", "dev": true}, "http-errors": {"version": "1.6.3", "resolved": "http://registry.npmjs.org/http-errors/-/http-errors-1.6.3.tgz", "integrity": "sha1-i1VoC7S+KDoLW/TqLjhYC+HZMg0=", "dev": true, "requires": {"depd": "~1.1.2", "inherits": "2.0.3", "setprototypeof": "1.1.0", "statuses": ">= 1.4.0 < 2"}}, "http-parser-js": {"version": "0.4.13", "resolved": "https://registry.npmjs.org/http-parser-js/-/http-parser-js-0.4.13.tgz", "integrity": "sha1-O9bW/ebjFyyTNMOzO2wZPYD+ETc=", "dev": true}, "http-proxy": {"version": "1.17.0", "resolved": "https://registry.npmjs.org/http-proxy/-/http-proxy-1.17.0.tgz", "integrity": "sha512-Taqn+3nNvYRfJ3bGvKfBSRwy1v6eePlm3oc/aWVxZp57DQr5Eq3xhKJi7Z4hZpS8PC3H4qI+Yly5EmFacGuA/g==", "dev": true, "requires": {"eventemitter3": "^3.0.0", "follow-redirects": "^1.0.0", "requires-port": "^1.0.0"}}, "http-proxy-middleware": {"version": "0.18.0", "resolved": "http://registry.npmjs.org/http-proxy-middleware/-/http-proxy-middleware-0.18.0.tgz", "integrity": "sha512-Fs25KVMPAIIcgjMZkVHJoKg9VcXcC1C8yb9JUgeDvVXY0S/zgVIhMb+qVswDIgtJe2DfckMSY2d6TuTEutlk6Q==", "dev": true, "requires": {"http-proxy": "^1.16.2", "is-glob": "^4.0.0", "lodash": "^4.17.5", "micromatch": "^3.1.9"}}, "http-signature": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/http-signature/-/http-signature-1.2.0.tgz", "integrity": "sha1-muzZJRFHcvPZW2WmCruPfBj7rOE=", "requires": {"assert-plus": "^1.0.0", "jsprim": "^1.2.2", "sshpk": "^1.7.0"}}, "https-browserify": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/https-browserify/-/https-browserify-1.0.0.tgz", "integrity": "sha1-7AbBDgo0wPL68Zn3/X/Hj//QPHM=", "dev": true}, "husky": {"version": "3.1.0", "resolved": "http://r.npm.sankuai.com/husky/download/husky-3.1.0.tgz", "integrity": "sha1-X6rVIKuGBYLtlPDBp38PBMkLV8A=", "dev": true, "requires": {"chalk": "^2.4.2", "ci-info": "^2.0.0", "cosmiconfig": "^5.2.1", "execa": "^1.0.0", "get-stdin": "^7.0.0", "opencollective-postinstall": "^2.0.2", "pkg-dir": "^4.2.0", "please-upgrade-node": "^3.2.0", "read-pkg": "^5.2.0", "run-node": "^1.0.0", "slash": "^3.0.0"}, "dependencies": {"caller-path": {"version": "2.0.0", "resolved": "http://r.npm.sankuai.com/caller-path/download/caller-path-2.0.0.tgz", "integrity": "sha1-Ro+DBE42mrIBD6xfBs7uFbsssfQ=", "dev": true, "requires": {"caller-callsite": "^2.0.0"}}, "chalk": {"version": "2.4.2", "resolved": "http://r.npm.sankuai.com/chalk/download/chalk-2.4.2.tgz", "integrity": "sha1-zUJUFnelQzPPVBpJEIwUMrRMlCQ=", "dev": true, "requires": {"ansi-styles": "^3.2.1", "escape-string-regexp": "^1.0.5", "supports-color": "^5.3.0"}}, "cosmiconfig": {"version": "5.2.1", "resolved": "http://r.npm.sankuai.com/cosmiconfig/download/cosmiconfig-5.2.1.tgz", "integrity": "sha1-BA9yaAnFked6F8CjYmykW08Wixo=", "dev": true, "requires": {"import-fresh": "^2.0.0", "is-directory": "^0.3.1", "js-yaml": "^3.13.1", "parse-json": "^4.0.0"}}, "execa": {"version": "1.0.0", "resolved": "http://r.npm.sankuai.com/execa/download/execa-1.0.0.tgz", "integrity": "sha1-xiNqW7TfbW8V6I5/AXeYIWdJ3dg=", "dev": true, "requires": {"cross-spawn": "^6.0.0", "get-stream": "^4.0.0", "is-stream": "^1.1.0", "npm-run-path": "^2.0.0", "p-finally": "^1.0.0", "signal-exit": "^3.0.0", "strip-eof": "^1.0.0"}}, "find-up": {"version": "4.1.0", "resolved": "http://r.npm.sankuai.com/find-up/download/find-up-4.1.0.tgz", "integrity": "sha1-l6/n1s3AvFkoWEt8jXsW6KmqXRk=", "dev": true, "requires": {"locate-path": "^5.0.0", "path-exists": "^4.0.0"}}, "get-stdin": {"version": "7.0.0", "resolved": "http://r.npm.sankuai.com/get-stdin/download/get-stdin-7.0.0.tgz", "integrity": "sha1-jV3pjxUXGhJcXlFmQ8em0OqKlvY=", "dev": true}, "get-stream": {"version": "4.1.0", "resolved": "http://r.npm.sankuai.com/get-stream/download/get-stream-4.1.0.tgz", "integrity": "sha1-wbJVV189wh1Zv8ec09K0axw6VLU=", "dev": true, "requires": {"pump": "^3.0.0"}}, "import-fresh": {"version": "2.0.0", "resolved": "http://r.npm.sankuai.com/import-fresh/download/import-fresh-2.0.0.tgz", "integrity": "sha1-2BNVwVYS04bGH53dOSLUMEgipUY=", "dev": true, "requires": {"caller-path": "^2.0.0", "resolve-from": "^3.0.0"}}, "js-yaml": {"version": "3.13.1", "resolved": "http://r.npm.sankuai.com/js-yaml/download/js-yaml-3.13.1.tgz", "integrity": "sha1-r/FRswv9+o5J4F2iLnQV6d+jeEc=", "dev": true, "requires": {"argparse": "^1.0.7", "esprima": "^4.0.0"}}, "locate-path": {"version": "5.0.0", "resolved": "http://r.npm.sankuai.com/locate-path/download/locate-path-5.0.0.tgz", "integrity": "sha1-Gvujlq/WdqbUJQTQpno6frn2KqA=", "dev": true, "requires": {"p-locate": "^4.1.0"}}, "normalize-package-data": {"version": "2.5.0", "resolved": "http://r.npm.sankuai.com/normalize-package-data/download/normalize-package-data-2.5.0.tgz", "integrity": "sha1-5m2xg4sgDB38IzIl0SyzZSDiNKg=", "dev": true, "requires": {"hosted-git-info": "^2.1.4", "resolve": "^1.10.0", "semver": "2 || 3 || 4 || 5", "validate-npm-package-license": "^3.0.1"}}, "p-limit": {"version": "2.2.1", "resolved": "http://r.npm.sankuai.com/p-limit/download/p-limit-2.2.1.tgz", "integrity": "sha1-qgeniMwxUck5tRMfY1cPDdIAlTc=", "dev": true, "requires": {"p-try": "^2.0.0"}}, "p-locate": {"version": "4.1.0", "resolved": "http://r.npm.sankuai.com/p-locate/download/p-locate-4.1.0.tgz", "integrity": "sha1-o0KLtwiLOmApL2aRkni3wpetTwc=", "dev": true, "requires": {"p-limit": "^2.2.0"}}, "p-try": {"version": "2.2.0", "resolved": "http://r.npm.sankuai.com/p-try/download/p-try-2.2.0.tgz", "integrity": "sha1-yyhoVA4xPWHeWPr741zpAE1VQOY=", "dev": true}, "parse-json": {"version": "4.0.0", "resolved": "http://r.npm.sankuai.com/parse-json/download/parse-json-4.0.0.tgz", "integrity": "sha1-vjX1Qlvh9/bHRxhPmKeIy5lHfuA=", "dev": true, "requires": {"error-ex": "^1.3.1", "json-parse-better-errors": "^1.0.1"}}, "path-exists": {"version": "4.0.0", "resolved": "http://r.npm.sankuai.com/path-exists/download/path-exists-4.0.0.tgz", "integrity": "sha1-UTvb4tO5XXdi6METfvoZXGxhtbM=", "dev": true}, "pkg-dir": {"version": "4.2.0", "resolved": "http://r.npm.sankuai.com/pkg-dir/download/pkg-dir-4.2.0.tgz", "integrity": "sha1-8JkTPfft5CLoHR2ESCcO6z5CYfM=", "dev": true, "requires": {"find-up": "^4.0.0"}}, "pump": {"version": "3.0.0", "resolved": "http://r.npm.sankuai.com/pump/download/pump-3.0.0.tgz", "integrity": "sha1-tKIRaBW94vTh6mAjVOjHVWUQemQ=", "dev": true, "requires": {"end-of-stream": "^1.1.0", "once": "^1.3.1"}}, "read-pkg": {"version": "5.2.0", "resolved": "http://r.npm.sankuai.com/read-pkg/download/read-pkg-5.2.0.tgz", "integrity": "sha1-e/KVQ4yloz5WzTDgU7NO5yUMk8w=", "dev": true, "requires": {"@types/normalize-package-data": "^2.4.0", "normalize-package-data": "^2.5.0", "parse-json": "^5.0.0", "type-fest": "^0.6.0"}, "dependencies": {"parse-json": {"version": "5.0.0", "resolved": "http://r.npm.sankuai.com/parse-json/download/parse-json-5.0.0.tgz", "integrity": "sha1-c+URTJhtFD76NxLU6iTbmkJm9g8=", "dev": true, "requires": {"@babel/code-frame": "^7.0.0", "error-ex": "^1.3.1", "json-parse-better-errors": "^1.0.1", "lines-and-columns": "^1.1.6"}}}}, "resolve": {"version": "1.13.1", "resolved": "http://r.npm.sankuai.com/resolve/download/resolve-1.13.1.tgz", "integrity": "sha1-vgqkwGrNUwg1BauzX01mkyqzXRY=", "dev": true, "requires": {"path-parse": "^1.0.6"}}, "resolve-from": {"version": "3.0.0", "resolved": "http://r.npm.sankuai.com/resolve-from/download/resolve-from-3.0.0.tgz", "integrity": "sha1-six699nWiBvItuZTM17rywoYh0g=", "dev": true}, "slash": {"version": "3.0.0", "resolved": "http://r.npm.sankuai.com/slash/download/slash-3.0.0.tgz", "integrity": "sha1-ZTm+hwwWWtvVJAIg2+Nh8bxNRjQ=", "dev": true}}}, "iconv-lite": {"version": "0.4.24", "resolved": "http://r.npm.sankuai.com/iconv-lite/download/iconv-lite-0.4.24.tgz", "integrity": "sha1-ICK0sl+93CHS9SSXSkdKr+czkIs=", "requires": {"safer-buffer": ">= 2.1.2 < 3"}}, "icss-replace-symbols": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/icss-replace-symbols/-/icss-replace-symbols-1.1.0.tgz", "integrity": "sha1-Bupvg2ead0njhs/h/oEq5dsiPe0=", "dev": true}, "icss-utils": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/icss-utils/-/icss-utils-2.1.0.tgz", "integrity": "sha1-g/Cg7DeL8yRheLbCrZE28TWxyWI=", "dev": true, "requires": {"postcss": "^6.0.1"}}, "ieee754": {"version": "1.1.12", "resolved": "https://registry.npmjs.org/ieee754/-/ieee754-1.1.12.tgz", "integrity": "sha512-GguP+DRY+pJ3soyIiGPTvdiVXjZ+DbXOxGpXn3eMvNW4x4irjqXm4wHKscC+TfxSJ0yw/S1F24tqdMNsMZTiLA==", "dev": true}, "iferr": {"version": "0.1.5", "resolved": "https://registry.npmjs.org/iferr/-/iferr-0.1.5.tgz", "integrity": "sha1-xg7taebY/bazEEofy8ocGS3FtQE=", "dev": true}, "ignore": {"version": "4.0.6", "resolved": "http://r.npm.sankuai.com/ignore/download/ignore-4.0.6.tgz", "integrity": "sha1-dQ49tYYgh7RzfrrIIH/9HvJ7Jfw=", "dev": true}, "image-preview-vue": {"version": "1.2.11", "resolved": "http://r.npm.sankuai.com/image-preview-vue/download/image-preview-vue-1.2.11.tgz", "integrity": "sha1-TTmHQLh+3wWR0nZbF3Az5C/QJl0=", "requires": {"core-js": "^3.6.5", "vue": "^2.6.11"}, "dependencies": {"core-js": {"version": "3.36.0", "resolved": "http://r.npm.sankuai.com/core-js/download/core-js-3.36.0.tgz", "integrity": "sha1-51L6CwtGKgeH1W6dc/gLD3wN3mg="}, "vue": {"version": "2.7.16", "resolved": "http://r.npm.sankuai.com/vue/download/vue-2.7.16.tgz", "integrity": "sha1-mMYN6d75nA49qNrlmzBOrUO5Z8k=", "requires": {"@vue/compiler-sfc": "2.7.16", "csstype": "^3.1.0"}}}}, "import-cwd": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/import-cwd/-/import-cwd-2.1.0.tgz", "integrity": "sha1-qmzzbnInYShcs3HsZRn1PiQ1sKk=", "dev": true, "requires": {"import-from": "^2.1.0"}}, "import-fresh": {"version": "3.2.1", "resolved": "http://r.npm.sankuai.com/import-fresh/download/import-fresh-3.2.1.tgz", "integrity": "sha1-Yz/2GFBueTr1rJG/SLcmd+FcvmY=", "dev": true, "requires": {"parent-module": "^1.0.0", "resolve-from": "^4.0.0"}, "dependencies": {"resolve-from": {"version": "4.0.0", "resolved": "http://r.npm.sankuai.com/resolve-from/download/resolve-from-4.0.0.tgz", "integrity": "sha1-SrzYUq0y3Xuqv+m0DgCjbbXzkuY=", "dev": true}}}, "import-from": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/import-from/-/import-from-2.1.0.tgz", "integrity": "sha1-M1238qev/VOqpHHUuAId7ja387E=", "dev": true, "requires": {"resolve-from": "^3.0.0"}, "dependencies": {"resolve-from": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/resolve-from/-/resolve-from-3.0.0.tgz", "integrity": "sha1-six699nWiBvItuZTM17rywoYh0g=", "dev": true}}}, "import-local": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/import-local/-/import-local-2.0.0.tgz", "integrity": "sha512-b6s04m3O+s3CGSbqDIyP4R6aAwAeYlVq9+WUWep6iHa8ETRf9yei1U48C5MmfJmV9AiLYYBKPMq/W+/WRpQmCQ==", "dev": true, "requires": {"pkg-dir": "^3.0.0", "resolve-cwd": "^2.0.0"}, "dependencies": {"find-up": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/find-up/-/find-up-3.0.0.tgz", "integrity": "sha512-1yD6RmLI1XBfxugvORwlck6f75tYL+iR0jqwsOrOxMZyGYqUuDhJ0l4AXdO1iX/FTs9cBAMEk1gWSEx1kSbylg==", "dev": true, "requires": {"locate-path": "^3.0.0"}}, "locate-path": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/locate-path/-/locate-path-3.0.0.tgz", "integrity": "sha512-7AO748wWnIhNqAuaty2ZWHkQHRSNfPVIsPIfwEOWO22AmaoVrWavlOcMR5nzTLNYvp36X220/maaRsrec1G65A==", "dev": true, "requires": {"p-locate": "^3.0.0", "path-exists": "^3.0.0"}}, "p-limit": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/p-limit/-/p-limit-2.0.0.tgz", "integrity": "sha512-fl5s52lI5ahKCernzzIyAP0QAZbGIovtVHGwpcu1Jr/EpzLVDI2myISHwGqK7m8uQFugVWSrbxH7XnhGtvEc+A==", "dev": true, "requires": {"p-try": "^2.0.0"}}, "p-locate": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/p-locate/-/p-locate-3.0.0.tgz", "integrity": "sha512-x+12w/To+4GFfgJhBEpiDcLozRJGegY+Ei7/z0tSLkMmxGZNybVMSfWj9aJn8Z5Fc7dBUNJOOVgPv2H7IwulSQ==", "dev": true, "requires": {"p-limit": "^2.0.0"}}, "p-try": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/p-try/-/p-try-2.0.0.tgz", "integrity": "sha512-hMp0onDKIajHfIkdRk3P4CdCmErkYAxxDtP3Wx/4nZ3aGlau2VKh3mZpcuFkH27WQkL/3WBCPOktzA9ZOAnMQQ==", "dev": true}, "path-exists": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/path-exists/-/path-exists-3.0.0.tgz", "integrity": "sha1-zg6+ql94yxiSXqfYENe1mwEP1RU=", "dev": true}, "pkg-dir": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/pkg-dir/-/pkg-dir-3.0.0.tgz", "integrity": "sha512-/E57AYkoeQ25qkxMj5PBOVgF8Kiu/h7cYS30Z5+R7WaiCCBfLq58ZI/dSeaEKb9WVJV5n/03QwrN3IeWIFllvw==", "dev": true, "requires": {"find-up": "^3.0.0"}}}}, "imurmurhash": {"version": "0.1.4", "resolved": "http://r.npm.sankuai.com/imurmurhash/download/imurmurhash-0.1.4.tgz", "integrity": "sha1-khi5srkoojixPcT7a21XbyMUU+o=", "dev": true}, "in-publish": {"version": "2.0.1", "resolved": "https://registry.npm.taobao.org/in-publish/download/in-publish-2.0.1.tgz", "integrity": "sha1-lIsaU1yAMFYc6lIvc/ePS+NX4Aw=", "dev": true}, "indent-string": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/indent-string/-/indent-string-2.1.0.tgz", "integrity": "sha1-ji1INIdCEhtKghi3oTfppSBJ3IA=", "dev": true, "requires": {"repeating": "^2.0.0"}}, "indexes-of": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/indexes-of/-/indexes-of-1.0.1.tgz", "integrity": "sha1-8w9xbI4r00bHtn0985FVZqfAVgc=", "dev": true}, "indexof": {"version": "0.0.1", "resolved": "https://registry.npmjs.org/indexof/-/indexof-0.0.1.tgz", "integrity": "sha1-gtwzbSMrkGIXnQWrMpOmYFn9Q10=", "dev": true}, "inflight": {"version": "1.0.6", "resolved": "http://r.npm.sankuai.com/inflight/download/inflight-1.0.6.tgz", "integrity": "sha1-Sb1jMdfQLQwJvJEKEHW6gWW1bfk=", "dev": true, "requires": {"once": "^1.3.0", "wrappy": "1"}}, "inherits": {"version": "2.0.3", "resolved": "http://r.npm.sankuai.com/inherits/download/inherits-2.0.3.tgz", "integrity": "sha1-Yzwsg+PaQqUC9SRmAiSA9CCCYd4="}, "ini": {"version": "1.3.5", "resolved": "http://r.npm.sankuai.com/ini/download/ini-1.3.5.tgz", "integrity": "sha1-7uJfVtscnsYIXgwid4CD9Zar+Sc=", "dev": true}, "inquirer": {"version": "6.2.0", "resolved": "http://r.npm.sankuai.com/inquirer/download/inquirer-6.2.0.tgz", "integrity": "sha1-Ua3Nd29mE2ncHolIWcJWCiJKvdg=", "dev": true, "requires": {"ansi-escapes": "^3.0.0", "chalk": "^2.0.0", "cli-cursor": "^2.1.0", "cli-width": "^2.0.0", "external-editor": "^3.0.0", "figures": "^2.0.0", "lodash": "^4.17.10", "mute-stream": "0.0.7", "run-async": "^2.2.0", "rxjs": "^6.1.0", "string-width": "^2.1.0", "strip-ansi": "^4.0.0", "through": "^2.3.6"}}, "int64-convert": {"version": "0.1.2", "resolved": "http://r.npm.sankuai.com/int64-convert/download/int64-convert-0.1.2.tgz", "integrity": "sha1-DwClpEMSaMhSkYla+5I+TI0hjiY="}, "internal-ip": {"version": "3.0.1", "resolved": "https://registry.npmjs.org/internal-ip/-/internal-ip-3.0.1.tgz", "integrity": "sha512-NXXgESC2nNVtU+pqmC9e6R8B1GpKxzsAQhffvh5AL79qKnodd+L7tnEQmTiUAVngqLalPbSqRA7XGIEL5nCd0Q==", "dev": true, "requires": {"default-gateway": "^2.6.0", "ipaddr.js": "^1.5.2"}}, "interpret": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/interpret/-/interpret-1.1.0.tgz", "integrity": "sha1-ftGxQQxqDg94z5XTuEQMY/eLhhQ=", "dev": true}, "invariant": {"version": "2.2.4", "resolved": "https://registry.npmjs.org/invariant/-/invariant-2.2.4.tgz", "integrity": "sha512-phJfQVBuaJM5raOpJjSfkiD6BpbCE4Ns//LaXl6wGYtUBY83nWS6Rf9tXm2e8VaK60JEjYldbPif/A2B1C2gNA==", "dev": true, "requires": {"loose-envify": "^1.0.0"}}, "invert-kv": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/invert-kv/-/invert-kv-2.0.0.tgz", "integrity": "sha512-wPVv/y/QQ/Uiirj/vh3oP+1Ww+AWehmi1g5fFWGPF6IpCBCDVrhgHRMvrLfdYcwDh3QJbGXDW4JAuzxElLSqKA==", "dev": true}, "ip": {"version": "1.1.5", "resolved": "https://registry.npmjs.org/ip/-/ip-1.1.5.tgz", "integrity": "sha1-vd7XARQpCCjAoDnnLvJfWq7ENUo=", "dev": true}, "ip-regex": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/ip-regex/-/ip-regex-2.1.0.tgz", "integrity": "sha1-+ni/XS5pE8kRzp+BnuUUa7bYROk=", "dev": true}, "ipaddr.js": {"version": "1.8.0", "resolved": "https://registry.npmjs.org/ipaddr.js/-/ipaddr.js-1.8.0.tgz", "integrity": "sha1-6qM9bd16zo9/b+DJygRA5wZzix4=", "dev": true}, "is-absolute-url": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/is-absolute-url/-/is-absolute-url-2.1.0.tgz", "integrity": "sha1-UFMN+4T8yap9vnhS6Do3uTufKqY=", "dev": true}, "is-accessor-descriptor": {"version": "0.1.6", "resolved": "https://registry.npmjs.org/is-accessor-descriptor/-/is-accessor-descriptor-0.1.6.tgz", "integrity": "sha1-qeEss66Nh2cn7u84Q/igiXtcmNY=", "dev": true, "requires": {"kind-of": "^3.0.2"}, "dependencies": {"kind-of": {"version": "3.2.2", "resolved": "https://registry.npmjs.org/kind-of/-/kind-of-3.2.2.tgz", "integrity": "sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ=", "dev": true, "requires": {"is-buffer": "^1.1.5"}}}}, "is-arrayish": {"version": "0.2.1", "resolved": "http://r.npm.sankuai.com/is-arrayish/download/is-arrayish-0.2.1.tgz", "integrity": "sha1-d8mYQFJ6qOyxqLppe4BkWnqSap0=", "dev": true}, "is-binary-path": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/is-binary-path/-/is-binary-path-1.0.1.tgz", "integrity": "sha1-dfFmQrSA8YenEcgUFh/TpKdlWJg=", "dev": true, "requires": {"binary-extensions": "^1.0.0"}}, "is-buffer": {"version": "1.1.6", "resolved": "https://registry.npmjs.org/is-buffer/-/is-buffer-1.1.6.tgz", "integrity": "sha512-NcdALwpXkTm5Zvvbk7owOUSvVvBKDgKP5/ewfXEznmQFfs4ZRmanOeKBTjRVjka3QFoN6XJ+9F3USqfHqTaU5w=="}, "is-builtin-module": {"version": "1.0.0", "resolved": "http://r.npm.sankuai.com/is-builtin-module/download/is-builtin-module-1.0.0.tgz", "integrity": "sha1-VAVy0096wxGfj3bDDLwbHgN6/74=", "dev": true, "requires": {"builtin-modules": "^1.0.0"}}, "is-callable": {"version": "1.1.4", "resolved": "http://r.npm.sankuai.com/is-callable/download/is-callable-1.1.4.tgz", "integrity": "sha1-HhrfIZ4e62hNaR+dagX/DTCiTXU=", "dev": true}, "is-color-stop": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/is-color-stop/-/is-color-stop-1.1.0.tgz", "integrity": "sha1-z/9HGu5N1cnhWFmPvhKWe1za00U=", "dev": true, "requires": {"css-color-names": "^0.0.4", "hex-color-regex": "^1.1.0", "hsl-regex": "^1.0.0", "hsla-regex": "^1.0.0", "rgb-regex": "^1.0.1", "rgba-regex": "^1.0.0"}}, "is-core-module": {"version": "2.13.1", "resolved": "http://r.npm.sankuai.com/is-core-module/download/is-core-module-2.13.1.tgz", "integrity": "sha1-rQ11Msb+qdoevcgnQtdFJcYnM4Q=", "requires": {"hasown": "^2.0.0"}}, "is-data-descriptor": {"version": "0.1.4", "resolved": "https://registry.npmjs.org/is-data-descriptor/-/is-data-descriptor-0.1.4.tgz", "integrity": "sha1-C17mSDiOLIYCgueT8YVv7D8wG1Y=", "dev": true, "requires": {"kind-of": "^3.0.2"}, "dependencies": {"kind-of": {"version": "3.2.2", "resolved": "https://registry.npmjs.org/kind-of/-/kind-of-3.2.2.tgz", "integrity": "sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ=", "dev": true, "requires": {"is-buffer": "^1.1.5"}}}}, "is-date-object": {"version": "1.0.1", "resolved": "http://r.npm.sankuai.com/is-date-object/download/is-date-object-1.0.1.tgz", "integrity": "sha1-mqIOtq7rv/d/vTPnTKAbM1gdOhY=", "dev": true}, "is-descriptor": {"version": "0.1.6", "resolved": "https://registry.npmjs.org/is-descriptor/-/is-descriptor-0.1.6.tgz", "integrity": "sha512-avDYr0SB3DwO9zsMov0gKCESFYqCnE4hq/4z3TdUlukEy5t9C0YRq7HLrsN52NAcqXKaepeCD0n+B0arnVG3Hg==", "dev": true, "requires": {"is-accessor-descriptor": "^0.1.6", "is-data-descriptor": "^0.1.4", "kind-of": "^5.0.0"}, "dependencies": {"kind-of": {"version": "5.1.0", "resolved": "https://registry.npmjs.org/kind-of/-/kind-of-5.1.0.tgz", "integrity": "sha512-NGEErnH6F2vUuXDh+OlbcKW7/wOcfdRHaZ7VWtqCztfHri/++YKmP51OdWeGPuqCOba6kk2OTe5d02VmTB80Pw==", "dev": true}}}, "is-directory": {"version": "0.3.1", "resolved": "https://registry.npmjs.org/is-directory/-/is-directory-0.3.1.tgz", "integrity": "sha1-YTObbyR1/Hcv2cnYP1yFddwVSuE=", "dev": true}, "is-extendable": {"version": "0.1.1", "resolved": "https://registry.npmjs.org/is-extendable/-/is-extendable-0.1.1.tgz", "integrity": "sha1-YrEQ4omkcUGOPsNqYX1HLjAd/Ik=", "dev": true}, "is-extglob": {"version": "2.1.1", "resolved": "https://registry.npmjs.org/is-extglob/-/is-extglob-2.1.1.tgz", "integrity": "sha1-qIwCU1eR8C7TfHahueqXc8gz+MI=", "dev": true}, "is-finite": {"version": "1.1.0", "resolved": "https://registry.npm.taobao.org/is-finite/download/is-finite-1.1.0.tgz", "integrity": "sha1-kEE1x3+0LAZB1qobzbxNqo2ggvM=", "dev": true}, "is-fullwidth-code-point": {"version": "2.0.0", "resolved": "http://r.npm.sankuai.com/is-fullwidth-code-point/download/is-fullwidth-code-point-2.0.0.tgz", "integrity": "sha1-o7MKXE8ZkYMWeqq5O+764937ZU8=", "dev": true}, "is-glob": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/is-glob/-/is-glob-4.0.0.tgz", "integrity": "sha1-lSHHaEXMJhCoUgPd8ICpWML/q8A=", "dev": true, "requires": {"is-extglob": "^2.1.1"}}, "is-module": {"version": "1.0.0", "resolved": "http://r.npm.sankuai.com/is-module/download/is-module-1.0.0.tgz", "integrity": "sha1-Mlj7afeMFNW4FdZkM2tM/7ZEFZE="}, "is-number": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/is-number/-/is-number-3.0.0.tgz", "integrity": "sha1-JP1iAaR4LPUFYcgQJ2r8fRLXEZU=", "dev": true, "requires": {"kind-of": "^3.0.2"}, "dependencies": {"kind-of": {"version": "3.2.2", "resolved": "https://registry.npmjs.org/kind-of/-/kind-of-3.2.2.tgz", "integrity": "sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ=", "dev": true, "requires": {"is-buffer": "^1.1.5"}}}}, "is-obj": {"version": "1.0.1", "resolved": "http://registry.npmjs.org/is-obj/-/is-obj-1.0.1.tgz", "integrity": "sha1-PkcprB9f3gJc19g6iW2rn09n2w8=", "dev": true}, "is-path-cwd": {"version": "1.0.0", "resolved": "http://r.npm.sankuai.com/is-path-cwd/download/is-path-cwd-1.0.0.tgz", "integrity": "sha1-0iXsIxMuie3Tj9p2dHLmLmXxEG0=", "dev": true}, "is-path-in-cwd": {"version": "1.0.1", "resolved": "http://r.npm.sankuai.com/is-path-in-cwd/download/is-path-in-cwd-1.0.1.tgz", "integrity": "sha1-WsSLNF72dTOb1sekipEhELJBz1I=", "dev": true, "requires": {"is-path-inside": "^1.0.0"}}, "is-path-inside": {"version": "1.0.1", "resolved": "http://r.npm.sankuai.com/is-path-inside/download/is-path-inside-1.0.1.tgz", "integrity": "sha1-jvW33lBDej/cprToZe96pVy0gDY=", "dev": true, "requires": {"path-is-inside": "^1.0.1"}}, "is-plain-obj": {"version": "1.1.0", "resolved": "http://r.npm.sankuai.com/is-plain-obj/download/is-plain-obj-1.1.0.tgz", "integrity": "sha1-caUMhCnfync8kqOQpKA7OfzVHT4=", "dev": true}, "is-plain-object": {"version": "2.0.4", "resolved": "https://registry.npmjs.org/is-plain-object/-/is-plain-object-2.0.4.tgz", "integrity": "sha512-h5PpgXkWitc38BBMYawTYMWJHFZJVnBquFE57xFpjB8pJFiF6gZ+bU+WyI/yqXiFR5mdLsgYNaPe8uao6Uv9Og==", "dev": true, "requires": {"isobject": "^3.0.1"}}, "is-promise": {"version": "2.1.0", "resolved": "http://r.npm.sankuai.com/is-promise/download/is-promise-2.1.0.tgz", "integrity": "sha1-eaKp7OfwlugPNtKy87wWwf9L8/o=", "dev": true}, "is-regex": {"version": "1.0.4", "resolved": "http://r.npm.sankuai.com/is-regex/download/is-regex-1.0.4.tgz", "integrity": "sha1-VRdIm1RwkbCTDglWVM7SXul+lJE=", "dev": true, "requires": {"has": "^1.0.1"}}, "is-resolvable": {"version": "1.1.0", "resolved": "http://r.npm.sankuai.com/is-resolvable/download/is-resolvable-1.1.0.tgz", "integrity": "sha1-+xj4fOH+uSUWnJpAfBkxijIG7Yg=", "dev": true}, "is-stream": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/is-stream/-/is-stream-1.1.0.tgz", "integrity": "sha1-EtSj3U5o4Lec6428hBc66A2RykQ=", "dev": true}, "is-svg": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/is-svg/-/is-svg-3.0.0.tgz", "integrity": "sha512-gi4iHK53LR2ujhLVVj+37Ykh9GLqYHX6JOVXbLAucaG/Cqw9xwdFOjDM2qeifLs1sF1npXXFvDu0r5HNgCMrzQ==", "dev": true, "requires": {"html-comment-regex": "^1.1.0"}}, "is-symbol": {"version": "1.0.1", "resolved": "http://r.npm.sankuai.com/is-symbol/download/is-symbol-1.0.1.tgz", "integrity": "sha1-PMWfAAJRlLarLjjbrmaJJWtmBXI=", "dev": true}, "is-text-path": {"version": "1.0.1", "resolved": "http://r.npm.sankuai.com/is-text-path/download/is-text-path-1.0.1.tgz", "integrity": "sha1-Thqg+1G/vLPpJogAE5cgLBd1tm4=", "dev": true, "requires": {"text-extensions": "^1.0.0"}}, "is-typedarray": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/is-typedarray/-/is-typedarray-1.0.0.tgz", "integrity": "sha1-5HnICFjfDBsR3dppQPlgEfzaSpo="}, "is-utf8": {"version": "0.2.1", "resolved": "https://registry.npmjs.org/is-utf8/-/is-utf8-0.2.1.tgz", "integrity": "sha1-Sw2hRCEE0bM2NA6AeX6GXPOffXI=", "dev": true}, "is-windows": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/is-windows/-/is-windows-1.0.2.tgz", "integrity": "sha512-eXK1UInq2bPmjyX6e3VHIzMLobc4J94i4AWn+Hpq3OU5KkrRC96OAcR3PRJ/pGu6m8TRnBHP9dkXQVsT/COVIA==", "dev": true}, "is-wsl": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/is-wsl/-/is-wsl-1.1.0.tgz", "integrity": "sha1-HxbkqiKwTRM2tmGIpmrzxgDDpm0=", "dev": true}, "isarray": {"version": "1.0.0", "resolved": "http://r.npm.sankuai.com/isarray/download/isarray-1.0.0.tgz", "integrity": "sha1-u5NdSFgsuhaMBoNJV6VKPgcSTxE="}, "isexe": {"version": "2.0.0", "resolved": "http://r.npm.sankuai.com/isexe/download/isexe-2.0.0.tgz", "integrity": "sha1-6PvzdNxVb/iUehDcsFctYz8s+hA=", "dev": true}, "isobject": {"version": "3.0.1", "resolved": "https://registry.npmjs.org/isobject/-/isobject-3.0.1.tgz", "integrity": "sha1-TkMekrEalzFjaqH5yNHMvP2reN8=", "dev": true}, "isstream": {"version": "0.1.2", "resolved": "https://registry.npmjs.org/isstream/-/isstream-0.1.2.tgz", "integrity": "sha1-R+Y/evVa+m+S4VAOaQ64uFKcCZo="}, "js-base64": {"version": "2.6.4", "resolved": "https://registry.npm.taobao.org/js-base64/download/js-base64-2.6.4.tgz?cache=0&sync_timestamp=1599897619557&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fjs-base64%2Fdownload%2Fjs-base64-2.6.4.tgz", "integrity": "sha1-9OaGxd4eofhn28rT1G2WlCjfmMQ=", "dev": true}, "js-calendar": {"version": "1.2.3", "resolved": "http://r.npm.sankuai.com/js-calendar/download/js-calendar-1.2.3.tgz", "integrity": "sha1-pYOwZEtOaVujlPNE0QPbzHp6fT4="}, "js-levenshtein": {"version": "1.1.3", "resolved": "https://registry.npmjs.org/js-levenshtein/-/js-levenshtein-1.1.3.tgz", "integrity": "sha512-/812MXr9RBtMObviZ8gQBhHO8MOrGj8HlEE+4ccMTElNA/6I3u39u+bhny55Lk921yn44nSZFy9naNLElL5wgQ==", "dev": true}, "js-tokens": {"version": "4.0.0", "resolved": "http://r.npm.sankuai.com/js-tokens/download/js-tokens-4.0.0.tgz", "integrity": "sha1-GSA/tZmR35jjoocFDUZHzerzJJk=", "dev": true}, "js-yaml": {"version": "3.12.0", "resolved": "http://r.npm.sankuai.com/js-yaml/download/js-yaml-3.12.0.tgz", "integrity": "sha1-6u1lbsg0TxD1J8a/obbiJE3hZ9E=", "dev": true, "requires": {"argparse": "^1.0.7", "esprima": "^4.0.0"}}, "jsbn": {"version": "0.1.1", "resolved": "https://registry.npmjs.org/jsbn/-/jsbn-0.1.1.tgz", "integrity": "sha1-peZUwuWi3rXyAdls77yoDA7y9RM="}, "jsesc": {"version": "2.5.1", "resolved": "http://r.npm.sankuai.com/jsesc/download/jsesc-2.5.1.tgz", "integrity": "sha1-5CGiqOINawgZ3yiQj3glJrlt0f4=", "dev": true}, "json-parse-better-errors": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/json-parse-better-errors/-/json-parse-better-errors-1.0.2.tgz", "integrity": "sha512-mrqyZKfX5EhL7hvqcV6WG1yYjnjeuYDzDhhcAAUrq8Po85NBQBJP+ZDUT75qZQ98IkUoBqdkExkukOU7Ts2wrw==", "dev": true}, "json-schema": {"version": "0.2.3", "resolved": "https://registry.npmjs.org/json-schema/-/json-schema-0.2.3.tgz", "integrity": "sha1-tIDIkuWaLwWVTOcnvT8qTogvnhM="}, "json-schema-traverse": {"version": "0.4.1", "resolved": "http://r.npm.sankuai.com/json-schema-traverse/download/json-schema-traverse-0.4.1.tgz", "integrity": "sha1-afaofZUTq4u4/mO9sJecRI5oRmA="}, "json-stable-stringify-without-jsonify": {"version": "1.0.1", "resolved": "http://r.npm.sankuai.com/json-stable-stringify-without-jsonify/download/json-stable-stringify-without-jsonify-1.0.1.tgz", "integrity": "sha1-nbe1lJatPzz+8wp1FC0tkwrXJlE=", "dev": true}, "json-stringify-safe": {"version": "5.0.1", "resolved": "https://registry.npmjs.org/json-stringify-safe/-/json-stringify-safe-5.0.1.tgz", "integrity": "sha1-Epai1Y/UXxmg9s4B1lcB4sc1tus="}, "json3": {"version": "3.3.2", "resolved": "https://registry.npmjs.org/json3/-/json3-3.3.2.tgz", "integrity": "sha1-PAQ0dD35Pi9cQq7nsZvLSDV19OE=", "dev": true}, "json5": {"version": "0.5.1", "resolved": "https://registry.npmjs.org/json5/-/json5-0.5.1.tgz", "integrity": "sha1-Hq3nrMASA0rYTiOWdn6tn6VJWCE=", "dev": true}, "jsonparse": {"version": "1.3.1", "resolved": "http://r.npm.sankuai.com/jsonparse/download/jsonparse-1.3.1.tgz", "integrity": "sha1-P02uSpH6wxX3EGL4UhzCOfE2YoA=", "dev": true}, "jsprim": {"version": "1.4.1", "resolved": "https://registry.npmjs.org/jsprim/-/jsprim-1.4.1.tgz", "integrity": "sha1-MT5mvB5cwG5Di8G3SZwuXFastqI=", "requires": {"assert-plus": "1.0.0", "extsprintf": "1.3.0", "json-schema": "0.2.3", "verror": "1.10.0"}}, "killable": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/killable/-/killable-1.0.1.tgz", "integrity": "sha512-LzqtLKlUwirEUyl/nicirVmNiPvYs7l5n8wOPP7fyJVpUPkvCnW/vuiXGpylGUlnPDnB7311rARzAt3Mhswpjg==", "dev": true}, "kind-of": {"version": "6.0.2", "resolved": "https://registry.npmjs.org/kind-of/-/kind-of-6.0.2.tgz", "integrity": "sha512-s5kLOcnH0XqDO+FvuaLX8DDjZ18CGFk7VygH40QoKPUQhW4e2rvM0rwUq0t8IQDOwYSeLK01U90OjzBTme2QqA==", "dev": true}, "last-call-webpack-plugin": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/last-call-webpack-plugin/-/last-call-webpack-plugin-3.0.0.tgz", "integrity": "sha512-7K<PERSON>2l2GIZa9p2spzPIVZBYyNKkN+e/SQPpnjlTiPhdbDW3F86tdKKELxKpzJ5sgU19wQWsACULZmpTPYHeWO5w==", "dev": true, "requires": {"lodash": "^4.17.5", "webpack-sources": "^1.1.0"}}, "lcid": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/lcid/-/lcid-2.0.0.tgz", "integrity": "sha512-avPEb8P8EGnwXKClwsNUgryVjllcRqtMYa49NTsbQagYuT1DcXnl1915oxWjoyGrXR6zH/Y0Zc96xWsPcoDKeA==", "dev": true, "requires": {"invert-kv": "^2.0.0"}}, "levn": {"version": "0.3.0", "resolved": "http://r.npm.sankuai.com/levn/download/levn-0.3.0.tgz", "integrity": "sha1-OwmSTt+fCDwEkP3UwLxEIeBHZO4=", "dev": true, "requires": {"prelude-ls": "~1.1.2", "type-check": "~0.3.2"}}, "line-reader": {"version": "0.2.4", "resolved": "http://r.npm.sankuai.com/line-reader/download/line-reader-0.2.4.tgz", "integrity": "sha1-xDkrWH3qOFgMlnhXDm6OSfzlJiI="}, "lines-and-columns": {"version": "1.1.6", "resolved": "http://r.npm.sankuai.com/lines-and-columns/download/lines-and-columns-1.1.6.tgz", "integrity": "sha1-HADHQ7QzzQpOgHWPe2SldEDZ/wA=", "dev": true}, "load-json-file": {"version": "2.0.0", "resolved": "http://r.npm.sankuai.com/load-json-file/download/load-json-file-2.0.0.tgz", "integrity": "sha1-eUfkIUmvgNaWy/eXvKq8/h/inKg=", "dev": true, "requires": {"graceful-fs": "^4.1.2", "parse-json": "^2.2.0", "pify": "^2.0.0", "strip-bom": "^3.0.0"}}, "loader-runner": {"version": "2.3.0", "resolved": "https://registry.npmjs.org/loader-runner/-/loader-runner-2.3.0.tgz", "integrity": "sha1-9IKuqC1UPgeSFwDVpG7yb9rGuKI=", "dev": true}, "loader-utils": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/loader-utils/-/loader-utils-1.1.0.tgz", "integrity": "sha1-yYrvSIvM7aL/teLeZG1qdUQp9c0=", "dev": true, "requires": {"big.js": "^3.1.3", "emojis-list": "^2.0.0", "json5": "^0.5.0"}}, "locate-path": {"version": "2.0.0", "resolved": "http://r.npm.sankuai.com/locate-path/download/locate-path-2.0.0.tgz", "integrity": "sha1-K1aLJl7slExtnA3pw9u7ygNUzY4=", "dev": true, "requires": {"p-locate": "^2.0.0", "path-exists": "^3.0.0"}, "dependencies": {"path-exists": {"version": "3.0.0", "resolved": "http://r.npm.sankuai.com/path-exists/download/path-exists-3.0.0.tgz", "integrity": "sha1-zg6+ql94yxiSXqfYENe1mwEP1RU=", "dev": true}}}, "lodash": {"version": "4.17.11", "resolved": "http://r.npm.sankuai.com/lodash/download/lodash-4.17.11.tgz", "integrity": "sha1-s56mIp72B+zYniyN8SU2iRysm40=", "dev": true}, "lodash._reinterpolate": {"version": "3.0.0", "resolved": "http://r.npm.sankuai.com/lodash._reinterpolate/download/lodash._reinterpolate-3.0.0.tgz", "integrity": "sha1-DM8tiRZq8Ds2Y8eWU4t1rG4RTZ0=", "dev": true}, "lodash.camelcase": {"version": "4.3.0", "resolved": "https://registry.npmjs.org/lodash.camelcase/-/lodash.camelcase-4.3.0.tgz", "integrity": "sha1-soqmKIorn8ZRA1x3EfZathkDMaY=", "dev": true}, "lodash.debounce": {"version": "4.0.8", "resolved": "https://registry.npmjs.org/lodash.debounce/-/lodash.debounce-4.0.8.tgz", "integrity": "sha1-gteb/zCmfEAF/9XiUVMArZyk168=", "dev": true}, "lodash.memoize": {"version": "4.1.2", "resolved": "https://registry.npmjs.org/lodash.memoize/-/lodash.memoize-4.1.2.tgz", "integrity": "sha1-vMbEmkKihA7Zl/Mj6tpezRguC/4=", "dev": true}, "lodash.tail": {"version": "4.1.1", "resolved": "https://registry.npmjs.org/lodash.tail/-/lodash.tail-4.1.1.tgz", "integrity": "sha1-0jM6NtnncXyK0vfKyv7HwytERmQ=", "dev": true}, "lodash.template": {"version": "4.5.0", "resolved": "http://r.npm.sankuai.com/lodash.template/download/lodash.template-4.5.0.tgz", "integrity": "sha1-+XYZXPPzR9DV9SSDVp/oAxzM6Ks=", "dev": true, "requires": {"lodash._reinterpolate": "^3.0.0", "lodash.templatesettings": "^4.0.0"}}, "lodash.templatesettings": {"version": "4.2.0", "resolved": "http://r.npm.sankuai.com/lodash.templatesettings/download/lodash.templatesettings-4.2.0.tgz", "integrity": "sha1-5IExDwSdPPbUfpEq0JMTsVTw+zM=", "dev": true, "requires": {"lodash._reinterpolate": "^3.0.0"}}, "lodash.throttle": {"version": "4.1.1", "resolved": "http://r.npm.sankuai.com/lodash.throttle/download/lodash.throttle-4.1.1.tgz", "integrity": "sha1-wj6RtxAkKscMN/HhzaknTMOb8vQ="}, "lodash.uniq": {"version": "4.5.0", "resolved": "https://registry.npmjs.org/lodash.uniq/-/lodash.uniq-4.5.0.tgz", "integrity": "sha1-0CJTc662Uq3BvILklFM5qEJ1R3M=", "dev": true}, "loglevel": {"version": "1.6.1", "resolved": "https://registry.npmjs.org/loglevel/-/loglevel-1.6.1.tgz", "integrity": "sha1-4PyVEztu8nbNyIh82vJKpvFW+Po=", "dev": true}, "loose-envify": {"version": "1.4.0", "resolved": "https://registry.npmjs.org/loose-envify/-/loose-envify-1.4.0.tgz", "integrity": "sha512-lyuxPGr/Wfhrlem2CL/UcnUc1zcqKAImBDzukY7Y5F/yQiNdko6+fRLevlw1HgMySw7f611UIY408EtxRSoK3Q==", "dev": true, "requires": {"js-tokens": "^3.0.0 || ^4.0.0"}}, "loud-rejection": {"version": "1.6.0", "resolved": "https://registry.npmjs.org/loud-rejection/-/loud-rejection-1.6.0.tgz", "integrity": "sha1-W0b4AUft7leIcPCG0Eghz5mOVR8=", "dev": true, "requires": {"currently-unhandled": "^0.4.1", "signal-exit": "^3.0.0"}}, "lower-case": {"version": "1.1.4", "resolved": "https://registry.npmjs.org/lower-case/-/lower-case-1.1.4.tgz", "integrity": "sha1-miyr0bno4K6ZOkv31YdcOcQujqw=", "dev": true}, "lru-cache": {"version": "4.1.3", "resolved": "https://registry.npmjs.org/lru-cache/-/lru-cache-4.1.3.tgz", "integrity": "sha512-fFEhvcgzuIoJVUF8fYr5KR0YqxD238zgObTps31YdADwPPAp82a4M8TrckkWyx7ekNlf9aBcVn81cFwwXngrJA==", "dev": true, "requires": {"pseudomap": "^1.0.2", "yallist": "^2.1.2"}}, "make-dir": {"version": "1.3.0", "resolved": "https://registry.npmjs.org/make-dir/-/make-dir-1.3.0.tgz", "integrity": "sha512-2w31R7SJtieJJnQtGc7RVL2StM2vGYVfqUOvUDxH6bC6aJTxPxTF0GnIgCyu7tjockiUWAYQRbxa7vKn34s5sQ==", "dev": true, "requires": {"pify": "^3.0.0"}, "dependencies": {"pify": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/pify/-/pify-3.0.0.tgz", "integrity": "sha1-5aSs0sEB/fPZpNB/DbxNtJ3SgXY=", "dev": true}}}, "mamacro": {"version": "0.0.3", "resolved": "https://registry.npmjs.org/mamacro/-/mamacro-0.0.3.tgz", "integrity": "sha512-qMEwh+UujcQ+kbz3T6V+wAmO2U8veoq2w+3wY8MquqwVA3jChfwY+Tk52GZKDfACEPjuZ7r2oJLejwpt8jtwTA==", "dev": true}, "map-age-cleaner": {"version": "0.1.2", "resolved": "https://registry.npmjs.org/map-age-cleaner/-/map-age-cleaner-0.1.2.tgz", "integrity": "sha512-UN1dNocxQq44IhJyMI4TU8phc2m9BddacHRPRjKGLYaF0jqd3xLz0jS0skpAU9WgYyoR4gHtUpzytNBS385FWQ==", "dev": true, "requires": {"p-defer": "^1.0.0"}}, "map-cache": {"version": "0.2.2", "resolved": "https://registry.npmjs.org/map-cache/-/map-cache-0.2.2.tgz", "integrity": "sha1-wyq9C9ZSXZsFFkW7TyasXcmKDb8=", "dev": true}, "map-obj": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/map-obj/-/map-obj-1.0.1.tgz", "integrity": "sha1-2TPOuSBdgr3PSIb2dCvcK03qFG0=", "dev": true}, "map-visit": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/map-visit/-/map-visit-1.0.0.tgz", "integrity": "sha1-7Nyo8TFE5mDxtb1B8S80edmN+48=", "dev": true, "requires": {"object-visit": "^1.0.0"}}, "md5.js": {"version": "1.3.4", "resolved": "https://registry.npmjs.org/md5.js/-/md5.js-1.3.4.tgz", "integrity": "sha1-6b296UogpawYsENA/Fdk1bCdkB0=", "dev": true, "requires": {"hash-base": "^3.0.0", "inherits": "^2.0.1"}}, "mdn-data": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/mdn-data/-/mdn-data-1.2.0.tgz", "integrity": "sha512-esDqNvsJB2q5V28+u7NdtdMg6Rmg4khQmAVSjUiX7BY/7haIv0K2yWM43hYp0or+3nvG7+UaTF1JHz31hgU1TA==", "dev": true}, "media-typer": {"version": "0.3.0", "resolved": "https://registry.npmjs.org/media-typer/-/media-typer-0.3.0.tgz", "integrity": "sha1-hxDXrwqmJvj/+hzgAWhUUmMlV0g="}, "mem": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/mem/-/mem-4.0.0.tgz", "integrity": "sha512-WQxG/5xYc3tMbYLXoXPm81ET2WDULiU5FxbuIoNbJqLOOI8zehXFdZuiUEgfdrU2mVB1pxBZUGlYORSrpuJreA==", "dev": true, "requires": {"map-age-cleaner": "^0.1.1", "mimic-fn": "^1.0.0", "p-is-promise": "^1.1.0"}}, "memory-fs": {"version": "0.4.1", "resolved": "https://registry.npmjs.org/memory-fs/-/memory-fs-0.4.1.tgz", "integrity": "sha1-OpoguEYlI+RHz7x+i7gO1me/xVI=", "dev": true, "requires": {"errno": "^0.1.3", "readable-stream": "^2.0.1"}}, "meow": {"version": "3.7.0", "resolved": "https://registry.npmjs.org/meow/-/meow-3.7.0.tgz", "integrity": "sha1-cstmi0JSKCkKu/qFaJJYcwioAfs=", "dev": true, "requires": {"camelcase-keys": "^2.0.0", "decamelize": "^1.1.2", "loud-rejection": "^1.0.0", "map-obj": "^1.0.1", "minimist": "^1.1.3", "normalize-package-data": "^2.3.4", "object-assign": "^4.0.1", "read-pkg-up": "^1.0.1", "redent": "^1.0.0", "trim-newlines": "^1.0.0"}, "dependencies": {"decamelize": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/decamelize/-/decamelize-1.2.0.tgz", "integrity": "sha1-9lNNFRSCabIDUue+4m9QH5oZEpA=", "dev": true}, "load-json-file": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/load-json-file/-/load-json-file-1.1.0.tgz", "integrity": "sha1-lWkFcI1YtLq0wiYbBPWfMcmTdMA=", "dev": true, "requires": {"graceful-fs": "^4.1.2", "parse-json": "^2.2.0", "pify": "^2.0.0", "pinkie-promise": "^2.0.0", "strip-bom": "^2.0.0"}}, "minimist": {"version": "1.2.5", "resolved": "https://registry.npm.taobao.org/minimist/download/minimist-1.2.5.tgz", "integrity": "sha1-Z9ZgFLZqaoqqDAg8X9WN9OTpdgI=", "dev": true}, "path-type": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/path-type/-/path-type-1.1.0.tgz", "integrity": "sha1-WcRPfuSR2nBNpBXaWkBwuk+P5EE=", "dev": true, "requires": {"graceful-fs": "^4.1.2", "pify": "^2.0.0", "pinkie-promise": "^2.0.0"}}, "read-pkg": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/read-pkg/-/read-pkg-1.1.0.tgz", "integrity": "sha1-9f+qXs0pyzHAR0vKfXVra7KePyg=", "dev": true, "requires": {"load-json-file": "^1.0.0", "normalize-package-data": "^2.3.2", "path-type": "^1.0.0"}}, "read-pkg-up": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/read-pkg-up/-/read-pkg-up-1.0.1.tgz", "integrity": "sha1-nWPBMnbAZZGNV/ACpX9AobZD+wI=", "dev": true, "requires": {"find-up": "^1.0.0", "read-pkg": "^1.0.0"}}, "strip-bom": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/strip-bom/-/strip-bom-2.0.0.tgz", "integrity": "sha1-YhmoVhZSBJHzV4i9vxRHqZx+aw4=", "dev": true, "requires": {"is-utf8": "^0.2.0"}}}}, "merge-descriptors": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/merge-descriptors/-/merge-descriptors-1.0.1.tgz", "integrity": "sha1-sAqqVW3YtEVoFQ7J0blT8/kMu2E="}, "merge-source-map": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/merge-source-map/-/merge-source-map-1.1.0.tgz", "integrity": "sha512-Qkcp7P2ygktpMPh2mCQZaf3jhN6D3Z/qVZHSdWvQ+2Ef5HgRAPBO57A77+ENm0CPx2+1Ce/MYKi3ymqdfuqibw==", "dev": true, "requires": {"source-map": "^0.6.1"}, "dependencies": {"source-map": {"version": "0.6.1", "resolved": "https://registry.npmjs.org/source-map/-/source-map-0.6.1.tgz", "integrity": "sha512-UjgapumWlbMhkBgzT7Ykc5YXUT46F0iKu8SGXq0bcwP5dz/h0Plj6enJqjz1Zbq2l5WaqYnrVbwWOWMyF3F47g==", "dev": true}}}, "methods": {"version": "1.1.2", "resolved": "https://registry.npmjs.org/methods/-/methods-1.1.2.tgz", "integrity": "sha1-VSmk1nZUE07cxSZmVoNbD4Ua/O4="}, "micromatch": {"version": "3.1.10", "resolved": "https://registry.npmjs.org/micromatch/-/micromatch-3.1.10.tgz", "integrity": "sha512-MWikgl9n9M3w+bpsY3He8L+w9eF9338xRl8IAO5viDizwSzziFEyUzo2xrrloB64ADbTf8uA8vRqqttDTOmccg==", "dev": true, "requires": {"arr-diff": "^4.0.0", "array-unique": "^0.3.2", "braces": "^2.3.1", "define-property": "^2.0.2", "extend-shallow": "^3.0.2", "extglob": "^2.0.4", "fragment-cache": "^0.2.1", "kind-of": "^6.0.2", "nanomatch": "^1.2.9", "object.pick": "^1.3.0", "regex-not": "^1.0.0", "snapdragon": "^0.8.1", "to-regex": "^3.0.2"}}, "miller-rabin": {"version": "4.0.1", "resolved": "https://registry.npmjs.org/miller-rabin/-/miller-rabin-4.0.1.tgz", "integrity": "sha512-115fLhvZVqWwHPbClyntxEVfVDfl9DLLTuJvq3g2O/Oxi8AiNouAHvDSzHS0viUJc+V5vm3eq91Xwqn9dp4jRA==", "dev": true, "requires": {"bn.js": "^4.0.0", "brorand": "^1.0.1"}}, "mime": {"version": "2.3.1", "resolved": "https://registry.npmjs.org/mime/-/mime-2.3.1.tgz", "integrity": "sha512-OEUllcVoydBHGN1z84yfQDimn58pZNNNXgZlHXSboxMlFvgI6MXSWpWKpFRra7H1HxpVhHTkrghfRW49k6yjeg==", "dev": true}, "mime-db": {"version": "1.36.0", "resolved": "https://registry.npmjs.org/mime-db/-/mime-db-1.36.0.tgz", "integrity": "sha512-L+xvyD9MkoYMXb1jAmzI/lWYAxAMCPvIBSWur0PZ5nOf5euahRLVqH//FKW9mWp2lkqUgYiXPgkzfMUFi4zVDw=="}, "mime-types": {"version": "2.1.20", "resolved": "https://registry.npmjs.org/mime-types/-/mime-types-2.1.20.tgz", "integrity": "sha512-HrkrPaP9vGuWbLK1B1FfgAkbqNjIuy4eHlIYnFi7kamZyLLrGlo2mpcx0bBmNpKqBtYtAfGbodDddIgddSJC2A==", "requires": {"mime-db": "~1.36.0"}}, "mime2": {"version": "0.0.11", "resolved": "http://r.npm.sankuai.com/mime2/download/mime2-0.0.11.tgz", "integrity": "sha1-rnWhOGEU/9UdnpfEgUZDGOl9h8U="}, "mimic-fn": {"version": "1.2.0", "resolved": "http://r.npm.sankuai.com/mimic-fn/download/mimic-fn-1.2.0.tgz", "integrity": "sha1-ggyGo5M0ZA6ZUWkovQP8qIBX0CI=", "dev": true}, "mini-css-extract-plugin": {"version": "0.4.2", "resolved": "https://registry.npmjs.org/mini-css-extract-plugin/-/mini-css-extract-plugin-0.4.2.tgz", "integrity": "sha512-ots7URQH4wccfJq9Ssrzu2+qupbncAce4TmTzunI9CIwlQMp2XI+WNUw6xWF6MMAGAm1cbUVINrSjATaVMyKXg==", "dev": true, "requires": {"loader-utils": "^1.1.0", "schema-utils": "^1.0.0", "webpack-sources": "^1.1.0"}, "dependencies": {"schema-utils": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/schema-utils/-/schema-utils-1.0.0.tgz", "integrity": "sha512-i27Mic4KovM/lnGsy8whRCHhc7VicJajAjTrYg11K9zfZXnYIt4k5F+kZkwjnrhKzLic/HLU4j11mjsz2G/75g==", "dev": true, "requires": {"ajv": "^6.1.0", "ajv-errors": "^1.0.0", "ajv-keywords": "^3.1.0"}}}}, "minimalistic-assert": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/minimalistic-assert/-/minimalistic-assert-1.0.1.tgz", "integrity": "sha512-UtJcAD4yEaGtjPezWuO9wC4nwUnVH/8/Im3yEHQP4b67cXlD/Qr9hdITCU1xDbSEXg2XKNaP8jsReV7vQd00/A==", "dev": true}, "minimalistic-crypto-utils": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/minimalistic-crypto-utils/-/minimalistic-crypto-utils-1.0.1.tgz", "integrity": "sha1-9sAMHAsIIkblxNmd+4x8CDsrWCo=", "dev": true}, "minimatch": {"version": "3.0.4", "resolved": "http://r.npm.sankuai.com/minimatch/download/minimatch-3.0.4.tgz", "integrity": "sha1-UWbihkV/AzBgZL5Ul+jbsMPTIIM=", "dev": true, "requires": {"brace-expansion": "^1.1.7"}}, "minimist": {"version": "0.0.8", "resolved": "http://r.npm.sankuai.com/minimist/download/minimist-0.0.8.tgz", "integrity": "sha1-hX/Kv8M5fSYluCKCYuhqp6ARsF0=", "dev": true}, "minimist-options": {"version": "3.0.2", "resolved": "http://r.npm.sankuai.com/minimist-options/download/minimist-options-3.0.2.tgz", "integrity": "sha1-+6TIGRM54T7PTWG+sD8HAQPz2VQ=", "dev": true, "requires": {"arrify": "^1.0.1", "is-plain-obj": "^1.1.0"}}, "mississippi": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/mississippi/-/mississippi-2.0.0.tgz", "integrity": "sha512-zHo8v+otD1J10j/tC+VNoGK9keCuByhKovAvdn74dmxJl9+mWHnx6EMsDN4lgRoMI/eYo2nchAxniIbUPb5onw==", "dev": true, "requires": {"concat-stream": "^1.5.0", "duplexify": "^3.4.2", "end-of-stream": "^1.1.0", "flush-write-stream": "^1.0.0", "from2": "^2.1.0", "parallel-transform": "^1.1.0", "pump": "^2.0.1", "pumpify": "^1.3.3", "stream-each": "^1.1.0", "through2": "^2.0.0"}}, "mixin-deep": {"version": "1.3.1", "resolved": "https://registry.npmjs.org/mixin-deep/-/mixin-deep-1.3.1.tgz", "integrity": "sha512-8ZItLHeEgaqEvd5lYBXfm4EZSFCX29Jb9K+lAHhDKzReKBQKj3R+7NOF6tjqYi9t4oI8VUfaWITJQm86wnXGNQ==", "dev": true, "requires": {"for-in": "^1.0.2", "is-extendable": "^1.0.1"}, "dependencies": {"is-extendable": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/is-extendable/-/is-extendable-1.0.1.tgz", "integrity": "sha512-arnXMxT1hhoKo9k1LZdmlNyJdDDfy2v0fXjFlmok4+i8ul/6WlbVge9bhM74OpNPQPMGUToDtz+KXa1PneJxOA==", "dev": true, "requires": {"is-plain-object": "^2.0.4"}}}}, "mixin-object": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/mixin-object/-/mixin-object-2.0.1.tgz", "integrity": "sha1-T7lJRB2rGCVA8f4DW6YOGUel5X4=", "dev": true, "requires": {"for-in": "^0.1.3", "is-extendable": "^0.1.1"}, "dependencies": {"for-in": {"version": "0.1.8", "resolved": "https://registry.npmjs.org/for-in/-/for-in-0.1.8.tgz", "integrity": "sha1-2Hc5COMSVhCZUrH9ubP6hn0ndeE=", "dev": true}}}, "mkdirp": {"version": "0.5.1", "resolved": "http://r.npm.sankuai.com/mkdirp/download/mkdirp-0.5.1.tgz", "integrity": "sha1-MAV0OOrGz3+MR2fzhkjWaX11yQM=", "dev": true, "requires": {"minimist": "0.0.8"}}, "moment": {"version": "2.29.4", "resolved": "https://registry.npmjs.org/moment/-/moment-2.29.4.tgz", "integrity": "sha512-5LC9SOxjSc2HF6vO2CyuTDNivEdoz2IvyJJGj6X8DJ0eFyfszE0QiEd+iXmBvUP3WHxSjFH/vIsA0EN00cgr8w=="}, "move-concurrently": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/move-concurrently/-/move-concurrently-1.0.1.tgz", "integrity": "sha1-viwAX9oy4LKa8fBdfEszIUxwH5I=", "dev": true, "requires": {"aproba": "^1.1.1", "copy-concurrently": "^1.0.0", "fs-write-stream-atomic": "^1.0.8", "mkdirp": "^0.5.1", "rimraf": "^2.5.4", "run-queue": "^1.0.3"}}, "ms": {"version": "2.1.1", "resolved": "http://r.npm.sankuai.com/ms/download/ms-2.1.1.tgz", "integrity": "sha1-MKWGTrPrsKZvLr5tcnrwagnYbgo=", "dev": true}, "mss-sdk": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/mss-sdk/-/mss-sdk-2.0.0.tgz", "integrity": "sha1-vOVVtHN+WGzZ1R3p7fFe5Aq3M3Q=", "dev": true, "requires": {"mss-sdk-apis": ">= 1.0.0", "xml2js": "0.2.6", "xmlbuilder": "0.4.2"}}, "mss-sdk-apis": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/mss-sdk-apis/-/mss-sdk-apis-1.0.0.tgz", "integrity": "sha1-lSRjxmN3mjL3FDQm50plqVKyn5c=", "dev": true}, "multicast-dns": {"version": "6.2.3", "resolved": "https://registry.npmjs.org/multicast-dns/-/multicast-dns-6.2.3.tgz", "integrity": "sha512-ji6J5enbMyGRHIAkAOu3WdV8nggqviKCEKtXcOqfphZZtQrmHKycfynJ2V7eVPUA4NhJ6V7Wf4TmGbTwKE9B6g==", "dev": true, "requires": {"dns-packet": "^1.3.1", "thunky": "^1.0.2"}}, "multicast-dns-service-types": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/multicast-dns-service-types/-/multicast-dns-service-types-1.1.0.tgz", "integrity": "sha1-iZ8R2WhuXgXLkbNdXw5jt3PPyQE=", "dev": true}, "mute-stream": {"version": "0.0.7", "resolved": "http://r.npm.sankuai.com/mute-stream/download/mute-stream-0.0.7.tgz", "integrity": "sha1-MHXOk7whuPq0PhvE2n6BFe0ee6s=", "dev": true}, "nan": {"version": "2.11.0", "resolved": "https://registry.npmjs.org/nan/-/nan-2.11.0.tgz", "integrity": "sha512-F4miItu2rGnV2ySkXOQoA8FKz/SR2Q2sWP0sbTxNxz/tuokeC8WxOhPMcwi0qIyGtVn/rrSeLbvVkznqCdwYnw==", "dev": true, "optional": true}, "nanoid": {"version": "3.3.7", "resolved": "http://r.npm.sankuai.com/nanoid/download/nanoid-3.3.7.tgz", "integrity": "sha1-0MMBppG8jVTvoKIibM8/4v1la9g="}, "nanomatch": {"version": "1.2.13", "resolved": "https://registry.npmjs.org/nanomatch/-/nanomatch-1.2.13.tgz", "integrity": "sha512-fpoe2T0RbHwBTBUOftAfBPaDEi06ufaUai0mE6Yn1kacc3SnTErfb/h+X94VXzI64rKFHYImXSvdwGGCmwOqCA==", "dev": true, "requires": {"arr-diff": "^4.0.0", "array-unique": "^0.3.2", "define-property": "^2.0.2", "extend-shallow": "^3.0.2", "fragment-cache": "^0.2.1", "is-windows": "^1.0.2", "kind-of": "^6.0.2", "object.pick": "^1.3.0", "regex-not": "^1.0.0", "snapdragon": "^0.8.1", "to-regex": "^3.0.1"}}, "natural-compare": {"version": "1.4.0", "resolved": "http://r.npm.sankuai.com/natural-compare/download/natural-compare-1.4.0.tgz", "integrity": "sha1-Sr6/7tdUHywnrPspvbvRXI1bpPc="}, "negotiator": {"version": "0.6.1", "resolved": "https://registry.npmjs.org/negotiator/-/negotiator-0.6.1.tgz", "integrity": "sha1-KzJxhOiZIQEXeyhWP7XnECrNDKk=", "dev": true}, "neo-async": {"version": "2.5.2", "resolved": "https://registry.npmjs.org/neo-async/-/neo-async-2.5.2.tgz", "integrity": "sha512-vdqTKI9GBIYcAEbFAcpKPErKINfPF5zIuz3/niBfq8WUZjpT2tytLlFVrBgWdOtqI4uaA/Rb6No0hux39XXDuw==", "dev": true}, "nice-try": {"version": "1.0.5", "resolved": "http://r.npm.sankuai.com/nice-try/download/nice-try-1.0.5.tgz", "integrity": "sha1-ozeKdpbOfSI+iPybdkvX7xCJ42Y=", "dev": true}, "no-case": {"version": "2.3.2", "resolved": "https://registry.npmjs.org/no-case/-/no-case-2.3.2.tgz", "integrity": "sha512-rmTZ9kz+f3rCvK2TD1Ue/oZlns7OGoIWP4fc3llxxRXlOkHKoWPPWJOfFYpITabSow43QJbRIoHQXtt10VldyQ==", "dev": true, "requires": {"lower-case": "^1.1.1"}}, "node-forge": {"version": "0.7.5", "resolved": "https://registry.npmjs.org/node-forge/-/node-forge-0.7.5.tgz", "integrity": "sha512-MmbQJ2MTESTjt3Gi/3yG1wGpIMhUfcIypUCGtTizFR9IiccFwxSpfp0vtIZlkFclEqERemxfnSdZEMR9VqqEFQ==", "dev": true}, "node-gyp": {"version": "3.8.0", "resolved": "https://registry.npm.taobao.org/node-gyp/download/node-gyp-3.8.0.tgz?cache=0&sync_timestamp=1602898466165&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fnode-gyp%2Fdownload%2Fnode-gyp-3.8.0.tgz", "integrity": "sha1-VAMEJhwzDoDQ1e3OJTpoyzlkIYw=", "dev": true, "requires": {"fstream": "^1.0.0", "glob": "^7.0.3", "graceful-fs": "^4.1.2", "mkdirp": "^0.5.0", "nopt": "2 || 3", "npmlog": "0 || 1 || 2 || 3 || 4", "osenv": "0", "request": "^2.87.0", "rimraf": "2", "semver": "~5.3.0", "tar": "^2.0.0", "which": "1"}, "dependencies": {"semver": {"version": "5.3.0", "resolved": "https://registry.npmjs.org/semver/-/semver-5.3.0.tgz", "integrity": "sha1-myzl094C0XxgEq0yaqa00M9U+U8=", "dev": true}}}, "node-libs-browser": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/node-libs-browser/-/node-libs-browser-2.1.0.tgz", "integrity": "sha512-5AzFzdoIMb89hBGMZglEegffzgRg+ZFoUmisQ8HI4j1KDdpx13J0taNp2y9xPbur6W61gepGDDotGBVQ7mfUCg==", "dev": true, "requires": {"assert": "^1.1.1", "browserify-zlib": "^0.2.0", "buffer": "^4.3.0", "console-browserify": "^1.1.0", "constants-browserify": "^1.0.0", "crypto-browserify": "^3.11.0", "domain-browser": "^1.1.1", "events": "^1.0.0", "https-browserify": "^1.0.0", "os-browserify": "^0.3.0", "path-browserify": "0.0.0", "process": "^0.11.10", "punycode": "^1.2.4", "querystring-es3": "^0.2.0", "readable-stream": "^2.3.3", "stream-browserify": "^2.0.1", "stream-http": "^2.7.2", "string_decoder": "^1.0.0", "timers-browserify": "^2.0.4", "tty-browserify": "0.0.0", "url": "^0.11.0", "util": "^0.10.3", "vm-browserify": "0.0.4"}, "dependencies": {"punycode": {"version": "1.4.1", "resolved": "https://registry.npmjs.org/punycode/-/punycode-1.4.1.tgz", "integrity": "sha1-wNWmOycYgArY4esPpSachN1BhF4=", "dev": true}}}, "node-releases": {"version": "1.0.0-alpha.11", "resolved": "https://registry.npmjs.org/node-releases/-/node-releases-1.0.0-alpha.11.tgz", "integrity": "sha512-CaViu+2FqTNYOYNihXa5uPS/zry92I3vPU4nCB6JB3OeZ2UGtOpF5gRwuN4+m3hbEcL47bOXyun1jX2iC+3uEQ==", "dev": true, "requires": {"semver": "^5.3.0"}}, "node-sass": {"version": "4.14.1", "resolved": "https://registry.npm.taobao.org/node-sass/download/node-sass-4.14.1.tgz?cache=0&sync_timestamp=1589682128498&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fnode-sass%2Fdownload%2Fnode-sass-4.14.1.tgz", "integrity": "sha1-mch+wu+3BH7WOPtMnbfzpC4iF7U=", "dev": true, "requires": {"async-foreach": "^0.1.3", "chalk": "^1.1.1", "cross-spawn": "^3.0.0", "gaze": "^1.0.0", "get-stdin": "^4.0.1", "glob": "^7.0.3", "in-publish": "^2.0.0", "lodash": "^4.17.15", "meow": "^3.7.0", "mkdirp": "^0.5.1", "nan": "^2.13.2", "node-gyp": "^3.8.0", "npmlog": "^4.0.0", "request": "^2.88.0", "sass-graph": "2.2.5", "stdout-stream": "^1.4.0", "true-case-path": "^1.0.2"}, "dependencies": {"ansi-regex": {"version": "2.1.1", "resolved": "https://registry.npmjs.org/ansi-regex/-/ansi-regex-2.1.1.tgz", "integrity": "sha1-w7M6te42DYbg5ijwRorn7yfWVN8=", "dev": true}, "ansi-styles": {"version": "2.2.1", "resolved": "https://registry.npmjs.org/ansi-styles/-/ansi-styles-2.2.1.tgz", "integrity": "sha1-tDLdM1i2NM914eRmQ2gkBTPB3b4=", "dev": true}, "chalk": {"version": "1.1.3", "resolved": "http://registry.npmjs.org/chalk/-/chalk-1.1.3.tgz", "integrity": "sha1-qBFcVeSnAv5NFQq9OHKCKn4J/Jg=", "dev": true, "requires": {"ansi-styles": "^2.2.1", "escape-string-regexp": "^1.0.2", "has-ansi": "^2.0.0", "strip-ansi": "^3.0.0", "supports-color": "^2.0.0"}}, "cross-spawn": {"version": "3.0.1", "resolved": "https://registry.npmjs.org/cross-spawn/-/cross-spawn-3.0.1.tgz", "integrity": "sha1-ElYDfsufDF9549bvE14wdwGEuYI=", "dev": true, "requires": {"lru-cache": "^4.0.1", "which": "^1.2.9"}}, "lodash": {"version": "4.17.20", "resolved": "https://registry.npm.taobao.org/lodash/download/lodash-4.17.20.tgz?cache=0&sync_timestamp=1597336097104&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Flodash%2Fdownload%2Flodash-4.17.20.tgz", "integrity": "sha1-tEqbYpe8tpjxxRo1RaKzs2jVnFI=", "dev": true}, "nan": {"version": "2.14.2", "resolved": "https://registry.npm.taobao.org/nan/download/nan-2.14.2.tgz?cache=0&sync_timestamp=1602591642885&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fnan%2Fdownload%2Fnan-2.14.2.tgz", "integrity": "sha1-9TdkAGlRaPTMaUrJOT0MlYXu6hk=", "dev": true}, "strip-ansi": {"version": "3.0.1", "resolved": "https://registry.npmjs.org/strip-ansi/-/strip-ansi-3.0.1.tgz", "integrity": "sha1-ajhfuIU9lS1f8F0Oiq+UJ43GPc8=", "dev": true, "requires": {"ansi-regex": "^2.0.0"}}, "supports-color": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/supports-color/-/supports-color-2.0.0.tgz", "integrity": "sha1-U10EXOa2Nj+kARcIRimZXp3zJMc=", "dev": true}}}, "nopt": {"version": "3.0.6", "resolved": "https://registry.npmjs.org/nopt/-/nopt-3.0.6.tgz", "integrity": "sha1-xkZdvwirzU2zWTF/eaxopkayj/k=", "dev": true, "requires": {"abbrev": "1"}}, "normalize-package-data": {"version": "2.4.0", "resolved": "http://r.npm.sankuai.com/normalize-package-data/download/normalize-package-data-2.4.0.tgz", "integrity": "sha1-EvlaMH1YNSB1oEkHuErIvpisAS8=", "dev": true, "requires": {"hosted-git-info": "^2.1.4", "is-builtin-module": "^1.0.0", "semver": "2 || 3 || 4 || 5", "validate-npm-package-license": "^3.0.1"}}, "normalize-path": {"version": "2.1.1", "resolved": "https://registry.npmjs.org/normalize-path/-/normalize-path-2.1.1.tgz", "integrity": "sha1-GrKLVW4Zg2Oowab35vogE3/mrtk=", "dev": true, "requires": {"remove-trailing-separator": "^1.0.1"}}, "normalize-range": {"version": "0.1.2", "resolved": "https://registry.npmjs.org/normalize-range/-/normalize-range-0.1.2.tgz", "integrity": "sha1-LRDAa9/TEuqXd2laTShDlFa3WUI=", "dev": true}, "normalize-url": {"version": "3.3.0", "resolved": "https://registry.npmjs.org/normalize-url/-/normalize-url-3.3.0.tgz", "integrity": "sha512-U+JJi7duF1o+u2pynbp2zXDW2/PADgC30f0GsHZtRh+HOcXHnw137TrNlyxxRvWW5fjKd3bcLHPxofWuCjaeZg==", "dev": true}, "normalize-wheel": {"version": "1.0.1", "resolved": "http://r.npm.sankuai.com/normalize-wheel/download/normalize-wheel-1.0.1.tgz", "integrity": "sha1-rsiGr/2wRQcNhWRH32Ls+GFG7EU="}, "npm-run-path": {"version": "2.0.2", "resolved": "https://registry.npmjs.org/npm-run-path/-/npm-run-path-2.0.2.tgz", "integrity": "sha1-NakjLfo11wZ7TLLd8jV7GHFTbF8=", "dev": true, "requires": {"path-key": "^2.0.0"}}, "npmlog": {"version": "4.1.2", "resolved": "https://registry.npm.taobao.org/npmlog/download/npmlog-4.1.2.tgz", "integrity": "sha1-CKfyqL9zRgR3mp76StXMcXq7lUs=", "dev": true, "requires": {"are-we-there-yet": "~1.1.2", "console-control-strings": "~1.1.0", "gauge": "~2.7.3", "set-blocking": "~2.0.0"}}, "nth-check": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/nth-check/-/nth-check-1.0.1.tgz", "integrity": "sha1-mSms32KPwsQQmN6rgqxYDPFJquQ=", "dev": true, "requires": {"boolbase": "~1.0.0"}}, "num2fraction": {"version": "1.2.2", "resolved": "https://registry.npmjs.org/num2fraction/-/num2fraction-1.2.2.tgz", "integrity": "sha1-b2gragJ6Tp3fpFZM0lidHU5mnt4=", "dev": true}, "number-is-nan": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/number-is-nan/-/number-is-nan-1.0.1.tgz", "integrity": "sha1-CXtgK1NCKlIsGvuHkDGDNpQaAR0=", "dev": true}, "oauth-sign": {"version": "0.9.0", "resolved": "https://registry.npm.taobao.org/oauth-sign/download/oauth-sign-0.9.0.tgz", "integrity": "sha1-R6ewFrqmi1+g7PPe4IqFxnmsZFU="}, "object-assign": {"version": "4.1.1", "resolved": "http://r.npm.sankuai.com/object-assign/download/object-assign-4.1.1.tgz", "integrity": "sha1-IQmtx5ZYh8/AXLvUQsrIv7s2CGM=", "dev": true}, "object-copy": {"version": "0.1.0", "resolved": "https://registry.npmjs.org/object-copy/-/object-copy-0.1.0.tgz", "integrity": "sha1-fn2Fi3gb18mRpBupde04EnVOmYw=", "dev": true, "requires": {"copy-descriptor": "^0.1.0", "define-property": "^0.2.5", "kind-of": "^3.0.3"}, "dependencies": {"define-property": {"version": "0.2.5", "resolved": "https://registry.npmjs.org/define-property/-/define-property-0.2.5.tgz", "integrity": "sha1-w1se+RjsPJkPmlvFe+BKrOxcgRY=", "dev": true, "requires": {"is-descriptor": "^0.1.0"}}, "kind-of": {"version": "3.2.2", "resolved": "https://registry.npmjs.org/kind-of/-/kind-of-3.2.2.tgz", "integrity": "sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ=", "dev": true, "requires": {"is-buffer": "^1.1.5"}}}}, "object-inspect": {"version": "1.12.2", "resolved": "http://r.npm.sankuai.com/object-inspect/download/object-inspect-1.12.2.tgz", "integrity": "sha1-wGQfJjlFMvKKuNeWq5VOQ8AJqOo="}, "object-keys": {"version": "1.0.12", "resolved": "http://r.npm.sankuai.com/object-keys/download/object-keys-1.0.12.tgz", "integrity": "sha1-CcU4VTd1dTEMymL1W7M0q/97PtI=", "dev": true}, "object-visit": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/object-visit/-/object-visit-1.0.1.tgz", "integrity": "sha1-95xEk68MU3e1n+OdOV5BBC3QRbs=", "dev": true, "requires": {"isobject": "^3.0.0"}}, "object.assign": {"version": "4.1.0", "resolved": "http://r.npm.sankuai.com/object.assign/download/object.assign-4.1.0.tgz", "integrity": "sha1-lovxEA15Vrs8oIbwBvhGs7xACNo=", "dev": true, "requires": {"define-properties": "^1.1.2", "function-bind": "^1.1.1", "has-symbols": "^1.0.0", "object-keys": "^1.0.11"}}, "object.entries": {"version": "1.0.4", "resolved": "http://r.npm.sankuai.com/object.entries/download/object.entries-1.0.4.tgz", "integrity": "sha1-G/mk3SKI9bM/Opk9JXZh8F0WGl8=", "dev": true, "requires": {"define-properties": "^1.1.2", "es-abstract": "^1.6.1", "function-bind": "^1.1.0", "has": "^1.0.1"}}, "object.getownpropertydescriptors": {"version": "2.0.3", "resolved": "https://registry.npmjs.org/object.getownpropertydescriptors/-/object.getownpropertydescriptors-2.0.3.tgz", "integrity": "sha1-h1jIRvW0B62rDyNuCYbxSwUcqhY=", "dev": true, "requires": {"define-properties": "^1.1.2", "es-abstract": "^1.5.1"}}, "object.pick": {"version": "1.3.0", "resolved": "https://registry.npmjs.org/object.pick/-/object.pick-1.3.0.tgz", "integrity": "sha1-h6EKxMFpS9Lhy/U1kaZhQftd10c=", "dev": true, "requires": {"isobject": "^3.0.1"}}, "object.values": {"version": "1.0.4", "resolved": "https://registry.npmjs.org/object.values/-/object.values-1.0.4.tgz", "integrity": "sha1-5STaCbT2b/Bd9FdUbscqyZ8TBpo=", "dev": true, "requires": {"define-properties": "^1.1.2", "es-abstract": "^1.6.1", "function-bind": "^1.1.0", "has": "^1.0.1"}}, "obuf": {"version": "1.1.2", "resolved": "https://registry.npmjs.org/obuf/-/obuf-1.1.2.tgz", "integrity": "sha512-PX1wu0AmAdPqOL1mWhqmlOd8kOIZQwGZw6rh7uby9fTc5lhaOWFLX3I6R1hrF9k3zUY40e6igsLGkDXK92LJNg==", "dev": true}, "on-finished": {"version": "2.4.1", "resolved": "http://r.npm.sankuai.com/on-finished/download/on-finished-2.4.1.tgz", "integrity": "sha1-WMjEQRblSEWtV/FKsQsDUzGErD8=", "requires": {"ee-first": "1.1.1"}}, "on-headers": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/on-headers/-/on-headers-1.0.1.tgz", "integrity": "sha1-ko9dD0cNSTQmUepnlLCFfBAGk/c=", "dev": true}, "once": {"version": "1.4.0", "resolved": "http://r.npm.sankuai.com/once/download/once-1.4.0.tgz", "integrity": "sha1-WDsap3WWHUsROsF9nFC6753Xa9E=", "dev": true, "requires": {"wrappy": "1"}}, "onetime": {"version": "2.0.1", "resolved": "http://r.npm.sankuai.com/onetime/download/onetime-2.0.1.tgz", "integrity": "sha1-BnQoIw/WdEOyeUsiu6UotoZ5YtQ=", "dev": true, "requires": {"mimic-fn": "^1.0.0"}}, "opencollective-postinstall": {"version": "2.0.2", "resolved": "http://r.npm.sankuai.com/opencollective-postinstall/download/opencollective-postinstall-2.0.2.tgz", "integrity": "sha1-Vlfxvt5ptuM6RZObBh61PTxsOok=", "dev": true}, "opn": {"version": "5.3.0", "resolved": "https://registry.npmjs.org/opn/-/opn-5.3.0.tgz", "integrity": "sha512-bYJHo/LOmoTd+pfiYhfZDnf9zekVJrY+cnS2a5F2x+w5ppvTqObojTP7WiFG+kVZs9Inw+qQ/lw7TroWwhdd2g==", "dev": true, "requires": {"is-wsl": "^1.1.0"}}, "optimize-css-assets-webpack-plugin": {"version": "5.0.1", "resolved": "https://registry.npmjs.org/optimize-css-assets-webpack-plugin/-/optimize-css-assets-webpack-plugin-5.0.1.tgz", "integrity": "sha512-Rqm6sSjWtx9FchdP0uzTQDc7GXDKnwVEGoSxjezPkzMewx7gEWE9IMUYKmigTRC4U3RaNSwYVnUDLuIdtTpm0A==", "dev": true, "requires": {"cssnano": "^4.1.0", "last-call-webpack-plugin": "^3.0.0"}}, "optionator": {"version": "0.8.2", "resolved": "http://r.npm.sankuai.com/optionator/download/optionator-0.8.2.tgz", "integrity": "sha1-NkxeQJ0/TWMB1sC0wFu6UBgK62Q=", "dev": true, "requires": {"deep-is": "~0.1.3", "fast-levenshtein": "~2.0.4", "levn": "~0.3.0", "prelude-ls": "~1.1.2", "type-check": "~0.3.2", "wordwrap": "~1.0.0"}}, "original": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/original/-/original-1.0.2.tgz", "integrity": "sha512-hyBVl6iqqUOJ8FqRe+l/gS8H+kKYjrEndd5Pm1MfBtsEKA038HkkdbAl/72EAXGyonD/PFsvmVG+EvcIpliMBg==", "dev": true, "requires": {"url-parse": "^1.4.3"}}, "os-browserify": {"version": "0.3.0", "resolved": "https://registry.npmjs.org/os-browserify/-/os-browserify-0.3.0.tgz", "integrity": "sha1-hUNzx/XCMVkU/Jv8a9gjj92h7Cc=", "dev": true}, "os-homedir": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/os-homedir/-/os-homedir-1.0.2.tgz", "integrity": "sha1-/7xJiDNuDoM94MFox+8VISGqf7M=", "dev": true}, "os-locale": {"version": "3.0.1", "resolved": "https://registry.npmjs.org/os-locale/-/os-locale-3.0.1.tgz", "integrity": "sha512-7g5e7dmXPtzcP4bgsZ8ixDVqA7oWYuEz4lOSujeWyliPai4gfVDiFIcwBg3aGCPnmSGfzOKTK3ccPn0CKv3DBw==", "dev": true, "requires": {"execa": "^0.10.0", "lcid": "^2.0.0", "mem": "^4.0.0"}}, "os-tmpdir": {"version": "1.0.2", "resolved": "http://r.npm.sankuai.com/os-tmpdir/download/os-tmpdir-1.0.2.tgz", "integrity": "sha1-u+Z0BseaqFxc/sdm/lc0VV36EnQ="}, "osenv": {"version": "0.1.5", "resolved": "https://registry.npm.taobao.org/osenv/download/osenv-0.1.5.tgz", "integrity": "sha1-hc36+uso6Gd/QW4odZK18/SepBA=", "dev": true, "requires": {"os-homedir": "^1.0.0", "os-tmpdir": "^1.0.0"}}, "p-defer": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/p-defer/-/p-defer-1.0.0.tgz", "integrity": "sha1-n26xgvbJqozXQwBKfU+WsZaw+ww=", "dev": true}, "p-finally": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/p-finally/-/p-finally-1.0.0.tgz", "integrity": "sha1-P7z7FbiZpEEjs0ttzBi3JDNqLK4=", "dev": true}, "p-is-promise": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/p-is-promise/-/p-is-promise-1.1.0.tgz", "integrity": "sha1-nJRWmJ6fZYgBewQ01WCXZ1w9oF4=", "dev": true}, "p-limit": {"version": "1.3.0", "resolved": "http://r.npm.sankuai.com/p-limit/download/p-limit-1.3.0.tgz", "integrity": "sha1-uGvV8MJWkJEcdZD8v8IBDVSzzLg=", "dev": true, "requires": {"p-try": "^1.0.0"}}, "p-locate": {"version": "2.0.0", "resolved": "http://r.npm.sankuai.com/p-locate/download/p-locate-2.0.0.tgz", "integrity": "sha1-IKAQOyIqcMj9OcwuWAaA893l7EM=", "dev": true, "requires": {"p-limit": "^1.1.0"}}, "p-map": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/p-map/-/p-map-1.2.0.tgz", "integrity": "sha512-r6zKACMNhjPJMTl8KcFH4li//gkrXWfbD6feV8l6doRHlzljFWGJ2AP6iKaCJXyZmAUMOPtvbW7EXkbWO/pLEA==", "dev": true}, "p-try": {"version": "1.0.0", "resolved": "http://r.npm.sankuai.com/p-try/download/p-try-1.0.0.tgz", "integrity": "sha1-y8ec26+P1CKOE/Yh8rGiN8GyB7M=", "dev": true}, "pako": {"version": "1.0.6", "resolved": "https://registry.npmjs.org/pako/-/pako-1.0.6.tgz", "integrity": "sha512-lQe48YPsMJAig+yngZ87Lus+NF+3mtu7DVOBu6b/gHO1YpKwIj5AWjZ/TOS7i46HD/UixzWb1zeWDZfGZ3iYcg==", "dev": true}, "parallel-transform": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/parallel-transform/-/parallel-transform-1.1.0.tgz", "integrity": "sha1-1BDwZbBdojCB/NEPKIVMKb2jOwY=", "dev": true, "requires": {"cyclist": "~0.2.2", "inherits": "^2.0.3", "readable-stream": "^2.1.5"}}, "param-case": {"version": "2.1.1", "resolved": "https://registry.npmjs.org/param-case/-/param-case-2.1.1.tgz", "integrity": "sha1-35T9jPZTHs915r75oIWPvHK+Ikc=", "dev": true, "requires": {"no-case": "^2.2.0"}}, "parchment": {"version": "1.1.4", "resolved": "http://r.npm.sankuai.com/parchment/download/parchment-1.1.4.tgz", "integrity": "sha1-rt7Xq5OP6SHUw0vDOc4RaLwv/eU="}, "parent-module": {"version": "1.0.1", "resolved": "http://r.npm.sankuai.com/parent-module/download/parent-module-1.0.1.tgz", "integrity": "sha1-aR0nCeeMefrjoVZiJFLQB2LKqqI=", "dev": true, "requires": {"callsites": "^3.0.0"}, "dependencies": {"callsites": {"version": "3.1.0", "resolved": "http://r.npm.sankuai.com/callsites/download/callsites-3.1.0.tgz", "integrity": "sha1-s2MKvYlDQy9Us/BRkjjjPNffL3M=", "dev": true}}}, "parse-asn1": {"version": "5.1.1", "resolved": "http://registry.npmjs.org/parse-asn1/-/parse-asn1-5.1.1.tgz", "integrity": "sha512-KPx7flKXg775zZpnp9SxJlz00gTd4BmJ2yJufSc44gMCRrRQ7NSzAcSJQfifuOLgW6bEi+ftrALtsgALeB2Adw==", "dev": true, "requires": {"asn1.js": "^4.0.0", "browserify-aes": "^1.0.0", "create-hash": "^1.1.0", "evp_bytestokey": "^1.0.0", "pbkdf2": "^3.0.3"}}, "parse-json": {"version": "2.2.0", "resolved": "http://r.npm.sankuai.com/parse-json/download/parse-json-2.2.0.tgz", "integrity": "sha1-9ID0BDTvgHQfhGkJn43qGPVaTck=", "dev": true, "requires": {"error-ex": "^1.2.0"}}, "parseurl": {"version": "1.3.2", "resolved": "https://registry.npmjs.org/parseurl/-/parseurl-1.3.2.tgz", "integrity": "sha1-/CidTtiZMRlGDBViUyYs3I3mW/M=", "dev": true}, "pascalcase": {"version": "0.1.1", "resolved": "https://registry.npmjs.org/pascalcase/-/pascalcase-0.1.1.tgz", "integrity": "sha1-s2PlXoAGym/iF4TS2yK9FdeRfxQ=", "dev": true}, "path-browserify": {"version": "0.0.0", "resolved": "https://registry.npmjs.org/path-browserify/-/path-browserify-0.0.0.tgz", "integrity": "sha1-oLhwcpquIUAFt9UDLsLLuw+0RRo=", "dev": true}, "path-dirname": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/path-dirname/-/path-dirname-1.0.2.tgz", "integrity": "sha1-zDPSTVJeCZpTiMAzbG4yuRYGCeA=", "dev": true}, "path-exists": {"version": "2.1.0", "resolved": "http://r.npm.sankuai.com/path-exists/download/path-exists-2.1.0.tgz", "integrity": "sha1-D+tsZPD8UY2adU3V77YscCJ2H0s=", "dev": true, "requires": {"pinkie-promise": "^2.0.0"}}, "path-is-absolute": {"version": "1.0.1", "resolved": "http://r.npm.sankuai.com/path-is-absolute/download/path-is-absolute-1.0.1.tgz", "integrity": "sha1-F0uSaHNVNP+8es5r9TpanhtcX18=", "dev": true}, "path-is-inside": {"version": "1.0.2", "resolved": "http://r.npm.sankuai.com/path-is-inside/download/path-is-inside-1.0.2.tgz", "integrity": "sha1-NlQX3t5EQw0cEa9hAn+s8HS9/FM=", "dev": true}, "path-key": {"version": "2.0.1", "resolved": "http://r.npm.sankuai.com/path-key/download/path-key-2.0.1.tgz", "integrity": "sha1-QRyttXTFoUDTpLGRDUDYDMn0C0A=", "dev": true}, "path-parse": {"version": "1.0.6", "resolved": "http://r.npm.sankuai.com/path-parse/download/path-parse-1.0.6.tgz", "integrity": "sha1-1i27VnlAXXLEc37FhgDp3c8G0kw=", "dev": true}, "path-to-regexp": {"version": "0.1.7", "resolved": "https://registry.npmjs.org/path-to-regexp/-/path-to-regexp-0.1.7.tgz", "integrity": "sha1-32BBeABfUi8V60SQ5yR6G/qmf4w="}, "path-type": {"version": "2.0.0", "resolved": "http://r.npm.sankuai.com/path-type/download/path-type-2.0.0.tgz", "integrity": "sha1-8BLMuEFbcJb8LaoQVMPXI4lZTHM=", "dev": true, "requires": {"pify": "^2.0.0"}}, "pbkdf2": {"version": "3.0.16", "resolved": "https://registry.npmjs.org/pbkdf2/-/pbkdf2-3.0.16.tgz", "integrity": "sha512-y4CXP3thSxqf7c0qmOF+9UeOTrifiVTIM+u7NWlq+PRsHbr7r7dpCmvzrZxa96JJUNi0Y5w9VqG5ZNeCVMoDcA==", "dev": true, "requires": {"create-hash": "^1.1.2", "create-hmac": "^1.1.4", "ripemd160": "^2.0.1", "safe-buffer": "^5.0.1", "sha.js": "^2.4.8"}}, "performance-now": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/performance-now/-/performance-now-2.1.0.tgz", "integrity": "sha1-Ywn04OX6kT7BxpMHrjZLSzd8nns="}, "picocolors": {"version": "1.0.0", "resolved": "http://r.npm.sankuai.com/picocolors/download/picocolors-1.0.0.tgz", "integrity": "sha1-y1vcdP8/UYkiNur3nWi8RFZKuBw="}, "picomatch": {"version": "2.3.1", "resolved": "http://r.npm.sankuai.com/picomatch/download/picomatch-2.3.1.tgz", "integrity": "sha1-O6ODNzNkbZ0+SZWUbBNlpn+wekI="}, "pify": {"version": "2.3.0", "resolved": "http://r.npm.sankuai.com/pify/download/pify-2.3.0.tgz", "integrity": "sha1-7RQaasBDqEnqWISY59yosVMw6Qw=", "dev": true}, "pinkie": {"version": "2.0.4", "resolved": "http://r.npm.sankuai.com/pinkie/download/pinkie-2.0.4.tgz", "integrity": "sha1-clVrgM+g1IqXToDnckjoDtT3+HA=", "dev": true}, "pinkie-promise": {"version": "2.0.1", "resolved": "http://r.npm.sankuai.com/pinkie-promise/download/pinkie-promise-2.0.1.tgz", "integrity": "sha1-ITXW36ejWMBprJsXh3YogihFD/o=", "dev": true, "requires": {"pinkie": "^2.0.0"}}, "pkg-dir": {"version": "1.0.0", "resolved": "http://r.npm.sankuai.com/pkg-dir/download/pkg-dir-1.0.0.tgz", "integrity": "sha1-ektQio1bstYp1EcFb/TpyTFM89Q=", "dev": true, "requires": {"find-up": "^1.0.0"}}, "please-upgrade-node": {"version": "3.2.0", "resolved": "http://r.npm.sankuai.com/please-upgrade-node/download/please-upgrade-node-3.2.0.tgz", "integrity": "sha1-rt3T+ZTJM+StmLmdmlVu+g4v6UI=", "dev": true, "requires": {"semver-compare": "^1.0.0"}}, "pluralize": {"version": "7.0.0", "resolved": "http://r.npm.sankuai.com/pluralize/download/pluralize-7.0.0.tgz", "integrity": "sha1-KYuJ34uTsCIdv0Ia0rGx6iP8Z3c=", "dev": true}, "popper.js": {"version": "1.16.1", "resolved": "http://r.npm.sankuai.com/popper.js/download/popper.js-1.16.1.tgz", "integrity": "sha1-KiI8s9x7YhPXQOQDcr5A3kPmWxs="}, "portfinder": {"version": "1.0.17", "resolved": "https://registry.npmjs.org/portfinder/-/portfinder-1.0.17.tgz", "integrity": "sha512-syFcRIRzVI1BoEFOCaAiizwDolh1S1YXSodsVhncbhjzjZQulhczNRbqnUl9N31Q4dKGOXsNDqxC2BWBgSMqeQ==", "dev": true, "requires": {"async": "^1.5.2", "debug": "^2.2.0", "mkdirp": "0.5.x"}, "dependencies": {"debug": {"version": "2.6.9", "resolved": "https://registry.npmjs.org/debug/-/debug-2.6.9.tgz", "integrity": "sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA==", "dev": true, "requires": {"ms": "2.0.0"}}, "ms": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/ms/-/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g=", "dev": true}}}, "posix-character-classes": {"version": "0.1.1", "resolved": "https://registry.npmjs.org/posix-character-classes/-/posix-character-classes-0.1.1.tgz", "integrity": "sha1-AerA/jta9xoqbAL+q7jB/vfgDqs=", "dev": true}, "postcss": {"version": "6.0.23", "resolved": "https://registry.npmjs.org/postcss/-/postcss-6.0.23.tgz", "integrity": "sha512-soOk1h6J3VMTZtVeVpv15/Hpdl2cBLX3CAw4TAbkpTJiNPk9YP/zWcD1ND+xEtvyuuvKzbxliTOIyvkSeSJ6ag==", "dev": true, "requires": {"chalk": "^2.4.1", "source-map": "^0.6.1", "supports-color": "^5.4.0"}, "dependencies": {"source-map": {"version": "0.6.1", "resolved": "https://registry.npmjs.org/source-map/-/source-map-0.6.1.tgz", "integrity": "sha512-UjgapumWlbMhkBgzT7Ykc5YXUT46F0iKu8SGXq0bcwP5dz/h0Plj6enJqjz1Zbq2l5WaqYnrVbwWOWMyF3F47g==", "dev": true}}}, "postcss-calc": {"version": "6.0.1", "resolved": "https://registry.npmjs.org/postcss-calc/-/postcss-calc-6.0.1.tgz", "integrity": "sha1-PSQXG79udinUIqQ26/5t2VEfQzA=", "dev": true, "requires": {"css-unit-converter": "^1.1.1", "postcss": "^6.0.0", "postcss-selector-parser": "^2.2.2", "reduce-css-calc": "^2.0.0"}, "dependencies": {"postcss-selector-parser": {"version": "2.2.3", "resolved": "https://registry.npmjs.org/postcss-selector-parser/-/postcss-selector-parser-2.2.3.tgz", "integrity": "sha1-+UN3iGBsPJrO4W/+jYsWKX8nu5A=", "dev": true, "requires": {"flatten": "^1.0.2", "indexes-of": "^1.0.1", "uniq": "^1.0.1"}}}}, "postcss-colormin": {"version": "4.0.1", "resolved": "https://registry.npmjs.org/postcss-colormin/-/postcss-colormin-4.0.1.tgz", "integrity": "sha1-bxwYoBVbxpYT8v8ThD4uSuj/C74=", "dev": true, "requires": {"browserslist": "^4.0.0", "color": "^3.0.0", "has": "^1.0.0", "postcss": "^6.0.0", "postcss-value-parser": "^3.0.0"}}, "postcss-convert-values": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/postcss-convert-values/-/postcss-convert-values-4.0.0.tgz", "integrity": "sha1-d9d9mu0dxOaVbmUcw0nVMwWHb2I=", "dev": true, "requires": {"postcss": "^6.0.0", "postcss-value-parser": "^3.0.0"}}, "postcss-discard-comments": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/postcss-discard-comments/-/postcss-discard-comments-4.0.0.tgz", "integrity": "sha1-loSimedrPpMmPvj9KtvxocCP2I0=", "dev": true, "requires": {"postcss": "^6.0.0"}}, "postcss-discard-duplicates": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/postcss-discard-duplicates/-/postcss-discard-duplicates-4.0.0.tgz", "integrity": "sha1-QvPCZ/hfqQngQsNXZ+z9Zcsr1yw=", "dev": true, "requires": {"postcss": "^6.0.0"}}, "postcss-discard-empty": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/postcss-discard-empty/-/postcss-discard-empty-4.0.0.tgz", "integrity": "sha1-VeGKWcdBKOOMfSgEvPpAVmEfuX8=", "dev": true, "requires": {"postcss": "^6.0.0"}}, "postcss-discard-overridden": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/postcss-discard-overridden/-/postcss-discard-overridden-4.0.0.tgz", "integrity": "sha1-Sgv4WXh4TPH4HtLBwf2dlkodofo=", "dev": true, "requires": {"postcss": "^6.0.0"}}, "postcss-load-config": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/postcss-load-config/-/postcss-load-config-2.0.0.tgz", "integrity": "sha512-V5JBLzw406BB8UIfsAWSK2KSwIJ5yoEIVFb4gVkXci0QdKgA24jLmHZ/ghe/GgX0lJ0/D1uUK1ejhzEY94MChQ==", "dev": true, "requires": {"cosmiconfig": "^4.0.0", "import-cwd": "^2.0.0"}}, "postcss-loader": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/postcss-loader/-/postcss-loader-3.0.0.tgz", "integrity": "sha512-cLWoDEY5OwHcAjDnkyRQzAXfs2jrKjXpO/HQFcc5b5u/r7aa471wdmChmwfnv7x2u840iat/wi0lQ5nbRgSkUA==", "dev": true, "requires": {"loader-utils": "^1.1.0", "postcss": "^7.0.0", "postcss-load-config": "^2.0.0", "schema-utils": "^1.0.0"}, "dependencies": {"postcss": {"version": "7.0.2", "resolved": "https://registry.npmjs.org/postcss/-/postcss-7.0.2.tgz", "integrity": "sha512-fmaUY5370keLUTx+CnwRxtGiuFTcNBLQBqr1oE3WZ/euIYmGAo0OAgOhVJ3ByDnVmOR3PK+0V9VebzfjRIUcqw==", "dev": true, "requires": {"chalk": "^2.4.1", "source-map": "^0.6.1", "supports-color": "^5.4.0"}}, "schema-utils": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/schema-utils/-/schema-utils-1.0.0.tgz", "integrity": "sha512-i27Mic4KovM/lnGsy8whRCHhc7VicJajAjTrYg11K9zfZXnYIt4k5F+kZkwjnrhKzLic/HLU4j11mjsz2G/75g==", "dev": true, "requires": {"ajv": "^6.1.0", "ajv-errors": "^1.0.0", "ajv-keywords": "^3.1.0"}}, "source-map": {"version": "0.6.1", "resolved": "https://registry.npmjs.org/source-map/-/source-map-0.6.1.tgz", "integrity": "sha512-UjgapumWlbMhkBgzT7Ykc5YXUT46F0iKu8SGXq0bcwP5dz/h0Plj6enJqjz1Zbq2l5WaqYnrVbwWOWMyF3F47g==", "dev": true}}}, "postcss-merge-longhand": {"version": "4.0.5", "resolved": "https://registry.npmjs.org/postcss-merge-longhand/-/postcss-merge-longhand-4.0.5.tgz", "integrity": "sha512-tw2obF6I2VhXhPMObQc1QpQO850m3arhqP3PcBAU7Tx70v73QF6brs9uK0XKMNuC7BPo6DW+fh07cGhrLL57HA==", "dev": true, "requires": {"css-color-names": "0.0.4", "postcss": "^6.0.0", "postcss-value-parser": "^3.0.0", "stylehacks": "^4.0.0"}}, "postcss-merge-rules": {"version": "4.0.1", "resolved": "https://registry.npmjs.org/postcss-merge-rules/-/postcss-merge-rules-4.0.1.tgz", "integrity": "sha1-Qw/Vmz8u0uivzQsxJ47aOYVKuxA=", "dev": true, "requires": {"browserslist": "^4.0.0", "caniuse-api": "^3.0.0", "cssnano-util-same-parent": "^4.0.0", "postcss": "^6.0.0", "postcss-selector-parser": "^3.0.0", "vendors": "^1.0.0"}}, "postcss-minify-font-values": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/postcss-minify-font-values/-/postcss-minify-font-values-4.0.0.tgz", "integrity": "sha1-TMM9KD1qgXWQNudX75gdksvYW+0=", "dev": true, "requires": {"postcss": "^6.0.0", "postcss-value-parser": "^3.0.0"}}, "postcss-minify-gradients": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/postcss-minify-gradients/-/postcss-minify-gradients-4.0.0.tgz", "integrity": "sha1-P8ORZDnSepu4Bm23za2AFlDrCQ4=", "dev": true, "requires": {"cssnano-util-get-arguments": "^4.0.0", "is-color-stop": "^1.0.0", "postcss": "^6.0.0", "postcss-value-parser": "^3.0.0"}}, "postcss-minify-params": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/postcss-minify-params/-/postcss-minify-params-4.0.0.tgz", "integrity": "sha1-BekWbuSMBa9lGYnOhNOcG015BnQ=", "dev": true, "requires": {"alphanum-sort": "^1.0.0", "cssnano-util-get-arguments": "^4.0.0", "postcss": "^6.0.0", "postcss-value-parser": "^3.0.0", "uniqs": "^2.0.0"}}, "postcss-minify-selectors": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/postcss-minify-selectors/-/postcss-minify-selectors-4.0.0.tgz", "integrity": "sha1-sen2xGNBbT/Nyybnt4XZX2FXiq0=", "dev": true, "requires": {"alphanum-sort": "^1.0.0", "has": "^1.0.0", "postcss": "^6.0.0", "postcss-selector-parser": "^3.0.0"}}, "postcss-modules-extract-imports": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/postcss-modules-extract-imports/-/postcss-modules-extract-imports-1.2.0.tgz", "integrity": "sha1-ZhQOzs447wa/DT41XWm/WdFB6oU=", "dev": true, "requires": {"postcss": "^6.0.1"}}, "postcss-modules-local-by-default": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/postcss-modules-local-by-default/-/postcss-modules-local-by-default-1.2.0.tgz", "integrity": "sha1-99gMOYxaOT+nlkRmvRlQCn1hwGk=", "dev": true, "requires": {"css-selector-tokenizer": "^0.7.0", "postcss": "^6.0.1"}}, "postcss-modules-scope": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/postcss-modules-scope/-/postcss-modules-scope-1.1.0.tgz", "integrity": "sha1-1upkmUx5+XtipytCb75gVqGUu5A=", "dev": true, "requires": {"css-selector-tokenizer": "^0.7.0", "postcss": "^6.0.1"}}, "postcss-modules-values": {"version": "1.3.0", "resolved": "https://registry.npmjs.org/postcss-modules-values/-/postcss-modules-values-1.3.0.tgz", "integrity": "sha1-7P+p1+GSUYOJ9CrQ6D9yrsRW6iA=", "dev": true, "requires": {"icss-replace-symbols": "^1.1.0", "postcss": "^6.0.1"}}, "postcss-normalize-charset": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/postcss-normalize-charset/-/postcss-normalize-charset-4.0.0.tgz", "integrity": "sha1-JFJyknAtXoEp6vo9HeSe1RpqtzA=", "dev": true, "requires": {"postcss": "^6.0.0"}}, "postcss-normalize-display-values": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/postcss-normalize-display-values/-/postcss-normalize-display-values-4.0.0.tgz", "integrity": "sha1-lQ4Me+NEV3ChYP/9a2ZEw8DNj4k=", "dev": true, "requires": {"cssnano-util-get-match": "^4.0.0", "postcss": "^6.0.0", "postcss-value-parser": "^3.0.0"}}, "postcss-normalize-positions": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/postcss-normalize-positions/-/postcss-normalize-positions-4.0.0.tgz", "integrity": "sha1-7pNDq5gbgixjq3JhXszNCFZERaM=", "dev": true, "requires": {"cssnano-util-get-arguments": "^4.0.0", "has": "^1.0.0", "postcss": "^6.0.0", "postcss-value-parser": "^3.0.0"}}, "postcss-normalize-repeat-style": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/postcss-normalize-repeat-style/-/postcss-normalize-repeat-style-4.0.0.tgz", "integrity": "sha1-txHFks8W+vn/V15C+hALZ5kIPv8=", "dev": true, "requires": {"cssnano-util-get-arguments": "^4.0.0", "cssnano-util-get-match": "^4.0.0", "postcss": "^6.0.0", "postcss-value-parser": "^3.0.0"}}, "postcss-normalize-string": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/postcss-normalize-string/-/postcss-normalize-string-4.0.0.tgz", "integrity": "sha1-cYy20wpvrGrGqDDjLAbAfbxm/l0=", "dev": true, "requires": {"has": "^1.0.0", "postcss": "^6.0.0", "postcss-value-parser": "^3.0.0"}}, "postcss-normalize-timing-functions": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/postcss-normalize-timing-functions/-/postcss-normalize-timing-functions-4.0.0.tgz", "integrity": "sha1-A1HymIaqmB1D2RssK9GuptCvbSM=", "dev": true, "requires": {"cssnano-util-get-match": "^4.0.0", "postcss": "^6.0.0", "postcss-value-parser": "^3.0.0"}}, "postcss-normalize-unicode": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/postcss-normalize-unicode/-/postcss-normalize-unicode-4.0.0.tgz", "integrity": "sha1-Ws1dR7rqXRdnSyzMSuUWb6iM35c=", "dev": true, "requires": {"postcss": "^6.0.0", "postcss-value-parser": "^3.0.0"}}, "postcss-normalize-url": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/postcss-normalize-url/-/postcss-normalize-url-4.0.0.tgz", "integrity": "sha1-t6nIrSbPJmlMFG6y1ovQz0mVbw0=", "dev": true, "requires": {"is-absolute-url": "^2.0.0", "normalize-url": "^3.0.0", "postcss": "^6.0.0", "postcss-value-parser": "^3.0.0"}}, "postcss-normalize-whitespace": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/postcss-normalize-whitespace/-/postcss-normalize-whitespace-4.0.0.tgz", "integrity": "sha1-HafnaxCuY8EYJ/oE/Du0oe/pnMA=", "dev": true, "requires": {"postcss": "^6.0.0", "postcss-value-parser": "^3.0.0"}}, "postcss-ordered-values": {"version": "4.1.0", "resolved": "https://registry.npmjs.org/postcss-ordered-values/-/postcss-ordered-values-4.1.0.tgz", "integrity": "sha512-gbqbEiONKKJgoOKhtzBjFqmHSzviPL4rv0ACVcFS7wxWXBY07agFXRQ7Y3eMGV0ZORzQXp2NGnj0c+imJG0NcA==", "dev": true, "requires": {"cssnano-util-get-arguments": "^4.0.0", "postcss": "^6.0.0", "postcss-value-parser": "^3.0.0"}}, "postcss-reduce-initial": {"version": "4.0.1", "resolved": "https://registry.npmjs.org/postcss-reduce-initial/-/postcss-reduce-initial-4.0.1.tgz", "integrity": "sha1-8tWPUM6isMXcEnjW6l7Q/1gpwpM=", "dev": true, "requires": {"browserslist": "^4.0.0", "caniuse-api": "^3.0.0", "has": "^1.0.0", "postcss": "^6.0.0"}}, "postcss-reduce-transforms": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/postcss-reduce-transforms/-/postcss-reduce-transforms-4.0.0.tgz", "integrity": "sha1-9kX8dEDDUnT0DegQThStcWPt8Yg=", "dev": true, "requires": {"cssnano-util-get-match": "^4.0.0", "has": "^1.0.0", "postcss": "^6.0.0", "postcss-value-parser": "^3.0.0"}}, "postcss-selector-parser": {"version": "3.1.1", "resolved": "https://registry.npmjs.org/postcss-selector-parser/-/postcss-selector-parser-3.1.1.tgz", "integrity": "sha1-T4dfSvsMllc9XPTXQBGu4lCn6GU=", "dev": true, "requires": {"dot-prop": "^4.1.1", "indexes-of": "^1.0.1", "uniq": "^1.0.1"}}, "postcss-svgo": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/postcss-svgo/-/postcss-svgo-4.0.0.tgz", "integrity": "sha1-wLutAlIPxjbJ14sOhAPi5RXDIoU=", "dev": true, "requires": {"is-svg": "^3.0.0", "postcss": "^6.0.0", "postcss-value-parser": "^3.0.0", "svgo": "^1.0.0"}}, "postcss-unique-selectors": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/postcss-unique-selectors/-/postcss-unique-selectors-4.0.0.tgz", "integrity": "sha1-BMHpdkx1h0JhMDQCxB8Ol2n8VQE=", "dev": true, "requires": {"alphanum-sort": "^1.0.0", "postcss": "^6.0.0", "uniqs": "^2.0.0"}}, "postcss-value-parser": {"version": "3.3.0", "resolved": "https://registry.npmjs.org/postcss-value-parser/-/postcss-value-parser-3.3.0.tgz", "integrity": "sha1-h/OPnxj3dKSrTIojL1xc6IcqnRU=", "dev": true}, "prelude-ls": {"version": "1.1.2", "resolved": "http://r.npm.sankuai.com/prelude-ls/download/prelude-ls-1.1.2.tgz", "integrity": "sha1-IZMqVJ9eUv/ZqCf1cOBL5iqX2lQ=", "dev": true}, "prettier": {"version": "1.13.7", "resolved": "https://registry.npmjs.org/prettier/-/prettier-1.13.7.tgz", "integrity": "sha512-KIU72UmYPGk4MujZGYMFwinB7lOf2LsDNGSOC8ufevsrPLISrZbNJlWstRi3m0AMuszbH+EFSQ/r6w56RSPK6w==", "dev": true}, "pretty-error": {"version": "2.1.1", "resolved": "https://registry.npmjs.org/pretty-error/-/pretty-error-2.1.1.tgz", "integrity": "sha1-X0+HyPkeWuPzuoerTPXgOxoX8aM=", "dev": true, "requires": {"renderkid": "^2.0.1", "utila": "~0.4"}}, "private": {"version": "0.1.8", "resolved": "https://registry.npmjs.org/private/-/private-0.1.8.tgz", "integrity": "sha512-VvivMrbvd2nKkiG38qjULzlc+4Vx4wm/whI9pQD35YrARNnhxeiRktSOhSukRLFNlzg6Br/cJPet5J/u19r/mg==", "dev": true}, "process": {"version": "0.11.10", "resolved": "https://registry.npmjs.org/process/-/process-0.11.10.tgz", "integrity": "sha1-czIwDoQBYb2j5podHZGn1LwW8YI=", "dev": true}, "process-nextick-args": {"version": "2.0.0", "resolved": "http://r.npm.sankuai.com/process-nextick-args/download/process-nextick-args-2.0.0.tgz", "integrity": "sha1-o31zL0JxtKsa0HDTVQjoKQeI/6o="}, "progress": {"version": "2.0.0", "resolved": "http://r.npm.sankuai.com/progress/download/progress-2.0.0.tgz", "integrity": "sha1-ihvjZr+Pwj2yvSPxDG/pILQ4nR8=", "dev": true}, "promise-inflight": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/promise-inflight/-/promise-inflight-1.0.1.tgz", "integrity": "sha1-mEcocL8igTL8vdhoEputEsPAKeM=", "dev": true}, "properties-parser": {"version": "0.3.1", "resolved": "http://r.npm.sankuai.com/properties-parser/download/properties-parser-0.3.1.tgz", "integrity": "sha1-ExbpU5/7/ZOEXjabIRAiq9R4dxo=", "requires": {"string.prototype.codepointat": "^0.2.0"}}, "proxy-addr": {"version": "2.0.7", "resolved": "http://r.npm.sankuai.com/proxy-addr/download/proxy-addr-2.0.7.tgz", "integrity": "sha1-8Z/mnOqzEe65S0LnDowgcPm6ECU=", "requires": {"forwarded": "0.2.0", "ipaddr.js": "1.9.1"}, "dependencies": {"ipaddr.js": {"version": "1.9.1", "resolved": "http://r.npm.sankuai.com/ipaddr.js/download/ipaddr.js-1.9.1.tgz", "integrity": "sha1-v/OFQ+64mEglB5/zoqjmy9RngbM="}}}, "prr": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/prr/-/prr-1.0.1.tgz", "integrity": "sha1-0/wRS6BplaRexok/SEzrHXj19HY=", "dev": true}, "pseudomap": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/pseudomap/-/pseudomap-1.0.2.tgz", "integrity": "sha1-8FKijacOYYkX7wqKw0wa5aaChrM=", "dev": true}, "psl": {"version": "1.8.0", "resolved": "https://registry.npm.taobao.org/psl/download/psl-1.8.0.tgz", "integrity": "sha1-kyb4vPsBOtzABf3/BWrM4CDlHCQ="}, "public-encrypt": {"version": "4.0.2", "resolved": "http://registry.npmjs.org/public-encrypt/-/public-encrypt-4.0.2.tgz", "integrity": "sha512-4kJ5Esocg8X3h8YgJsKAuoesBgB7mqH3eowiDzMUPKiRDDE7E/BqqZD1hnTByIaAFiwAw246YEltSq7tdrOH0Q==", "dev": true, "requires": {"bn.js": "^4.1.0", "browserify-rsa": "^4.0.0", "create-hash": "^1.1.0", "parse-asn1": "^5.0.0", "randombytes": "^2.0.1"}}, "pump": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/pump/-/pump-2.0.1.tgz", "integrity": "sha512-ruPMNRkN3MHP1cWJc9OWr+T/xDP0jhXYCLfJcBuX54hhfIBnaQmAUMfDcG4DM5UMWByBbJY69QSphm3jtDKIkA==", "dev": true, "requires": {"end-of-stream": "^1.1.0", "once": "^1.3.1"}}, "pumpify": {"version": "1.5.1", "resolved": "https://registry.npmjs.org/pumpify/-/pumpify-1.5.1.tgz", "integrity": "sha512-oClZI37HvuUJJxSKKrC17bZ9Cu0ZYhEAGPsPUy9KlMUmv9dKX2o77RUmq7f3XjIxbwyGwYzbzQ1L2Ks8sIradQ==", "dev": true, "requires": {"duplexify": "^3.6.0", "inherits": "^2.0.3", "pump": "^2.0.0"}}, "punycode": {"version": "2.1.1", "resolved": "http://r.npm.sankuai.com/punycode/download/punycode-2.1.1.tgz", "integrity": "sha1-tYsBCsQMIsVldhbI0sLALHv0eew="}, "q": {"version": "1.5.1", "resolved": "https://registry.npmjs.org/q/-/q-1.5.1.tgz", "integrity": "sha1-fjL3W0E4EpHQRhHxvxQQmsAGUdc=", "dev": true}, "qr.js": {"version": "0.0.0", "resolved": "http://r.npm.sankuai.com/qr.js/download/qr.js-0.0.0.tgz", "integrity": "sha1-ys6GOG9ZoNuAUPqQ2baw6IoeNk8="}, "qs": {"version": "6.11.0", "resolved": "http://r.npm.sankuai.com/qs/download/qs-6.11.0.tgz", "integrity": "sha1-/Q2WNEb3pl4TZ+AavYVClFPww3o=", "requires": {"side-channel": "^1.0.4"}}, "query-string": {"version": "7.1.1", "resolved": "https://registry.npmjs.org/query-string/-/query-string-7.1.1.tgz", "integrity": "sha512-MplouLRDHBZSG9z7fpuAAcI7aAYjDLhtsiVZsevsfaHWDS2IDdORKbSd1kWUA+V4zyva/HZoSfpwnYMMQDhb0w==", "dev": true, "requires": {"decode-uri-component": "^0.2.0", "filter-obj": "^1.1.0", "split-on-first": "^1.0.0", "strict-uri-encode": "^2.0.0"}}, "querystring": {"version": "0.2.0", "resolved": "https://registry.npmjs.org/querystring/-/querystring-0.2.0.tgz", "integrity": "sha1-sgmEkgO7Jd+CDadW50cAWHhSFiA=", "dev": true}, "querystring-es3": {"version": "0.2.1", "resolved": "https://registry.npmjs.org/querystring-es3/-/querystring-es3-0.2.1.tgz", "integrity": "sha1-nsYfeQSYdXB9aUFFlv2Qek1xHnM=", "dev": true}, "querystringify": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/querystringify/-/querystringify-2.0.0.tgz", "integrity": "sha512-eTPo5t/4bgaMNZxyjWx6N2a6AuE0mq51KWvpc7nU/MAqixcI6v6KrGUKES0HaomdnolQBBXU/++X6/QQ9KL4tw==", "dev": true}, "quick-lru": {"version": "1.1.0", "resolved": "http://r.npm.sankuai.com/quick-lru/download/quick-lru-1.1.0.tgz", "integrity": "sha1-Q2CxfGETatOAeDl/8RQW4Ybc+7g=", "dev": true}, "quill": {"version": "1.3.7", "resolved": "http://r.npm.sankuai.com/quill/download/quill-1.3.7.tgz", "integrity": "sha1-2lsvOixHDpMjQM2/NmjJ8h+Shug=", "requires": {"clone": "^2.1.1", "deep-equal": "^1.0.1", "eventemitter3": "^2.0.3", "extend": "^3.0.2", "parchment": "^1.1.4", "quill-delta": "^3.6.2"}, "dependencies": {"eventemitter3": {"version": "2.0.3", "resolved": "http://r.npm.sankuai.com/eventemitter3/download/eventemitter3-2.0.3.tgz", "integrity": "sha1-teEHm1n7XhuidxwKmTvgYKWMmbo="}}}, "quill-delta": {"version": "3.6.3", "resolved": "http://r.npm.sankuai.com/quill-delta/download/quill-delta-3.6.3.tgz", "integrity": "sha1-sZ/SuJQSMBxg4f8hPY2GDqwPEDI=", "requires": {"deep-equal": "^1.0.1", "extend": "^3.0.2", "fast-diff": "1.1.2"}}, "randombytes": {"version": "2.0.6", "resolved": "https://registry.npmjs.org/randombytes/-/randombytes-2.0.6.tgz", "integrity": "sha512-CIQ5OFxf4Jou6uOKe9t1AOgqpeU5fd70A8NPdHSGeYXqXsPe6peOwI0cUl88RWZ6sP1vPMV3avd/R6cZ5/sP1A==", "dev": true, "requires": {"safe-buffer": "^5.1.0"}}, "randomfill": {"version": "1.0.4", "resolved": "https://registry.npmjs.org/randomfill/-/randomfill-1.0.4.tgz", "integrity": "sha512-87lcbR8+MhcWcUiQ+9e+Rwx8MyR2P7qnt15ynUlbm3TU/fjbgz4GsvfSUDTemtCCtVCqb4ZcEFlyPNTh9bBTLw==", "dev": true, "requires": {"randombytes": "^2.0.5", "safe-buffer": "^5.1.0"}}, "range-parser": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/range-parser/-/range-parser-1.2.0.tgz", "integrity": "sha1-9JvmtIeJTdxA3MlKMi9hEJLgDV4=", "dev": true}, "raw-body": {"version": "2.5.1", "resolved": "http://r.npm.sankuai.com/raw-body/download/raw-body-2.5.1.tgz", "integrity": "sha1-/hsWKLGBtwAhXl/UI4n5i3E5KFc=", "requires": {"bytes": "3.1.2", "http-errors": "2.0.0", "iconv-lite": "0.4.24", "unpipe": "1.0.0"}, "dependencies": {"bytes": {"version": "3.1.2", "resolved": "http://r.npm.sankuai.com/bytes/download/bytes-3.1.2.tgz", "integrity": "sha1-iwvuuYYFrfGxKPpDhkA8AJ4CIaU="}, "depd": {"version": "2.0.0", "resolved": "http://r.npm.sankuai.com/depd/download/depd-2.0.0.tgz", "integrity": "sha1-tpYWPMdXVg0JzyLMj60Vcbeedt8="}, "http-errors": {"version": "2.0.0", "resolved": "http://r.npm.sankuai.com/http-errors/download/http-errors-2.0.0.tgz", "integrity": "sha1-t3dKFIbvc892Z6ya4IWMASxXudM=", "requires": {"depd": "2.0.0", "inherits": "2.0.4", "setprototypeof": "1.2.0", "statuses": "2.0.1", "toidentifier": "1.0.1"}}, "inherits": {"version": "2.0.4", "resolved": "http://r.npm.sankuai.com/inherits/download/inherits-2.0.4.tgz", "integrity": "sha1-D6LGT5MpF8NDOg3tVTY6rjdBa3w="}, "setprototypeof": {"version": "1.2.0", "resolved": "http://r.npm.sankuai.com/setprototypeof/download/setprototypeof-1.2.0.tgz", "integrity": "sha1-ZsmiSnP5/CjL5msJ/tPTPcrxtCQ="}, "statuses": {"version": "2.0.1", "resolved": "http://r.npm.sankuai.com/statuses/download/statuses-2.0.1.tgz", "integrity": "sha1-VcsADM8dSHKL0jxoWgY5mM8aG2M="}}}, "read-pkg": {"version": "2.0.0", "resolved": "http://r.npm.sankuai.com/read-pkg/download/read-pkg-2.0.0.tgz", "integrity": "sha1-jvHAYjxqbbDcZxPEv6xGMysjaPg=", "dev": true, "requires": {"load-json-file": "^2.0.0", "normalize-package-data": "^2.3.2", "path-type": "^2.0.0"}}, "read-pkg-up": {"version": "2.0.0", "resolved": "http://r.npm.sankuai.com/read-pkg-up/download/read-pkg-up-2.0.0.tgz", "integrity": "sha1-a3KoBImE4MQeeVEP1en6mbO1Sb4=", "dev": true, "requires": {"find-up": "^2.0.0", "read-pkg": "^2.0.0"}, "dependencies": {"find-up": {"version": "2.1.0", "resolved": "http://r.npm.sankuai.com/find-up/download/find-up-2.1.0.tgz", "integrity": "sha1-RdG35QbHF93UgndaK3eSCjwMV6c=", "dev": true, "requires": {"locate-path": "^2.0.0"}}}}, "readable-stream": {"version": "2.3.6", "resolved": "http://r.npm.sankuai.com/readable-stream/download/readable-stream-2.3.6.tgz", "integrity": "sha1-sRwn2IuP8fvgcGQ8+UsMea4bCq8=", "requires": {"core-util-is": "~1.0.0", "inherits": "~2.0.3", "isarray": "~1.0.0", "process-nextick-args": "~2.0.0", "safe-buffer": "~5.1.1", "string_decoder": "~1.1.1", "util-deprecate": "~1.0.1"}}, "readdirp": {"version": "2.2.1", "resolved": "https://registry.npmjs.org/readdirp/-/readdirp-2.2.1.tgz", "integrity": "sha512-1JU/8q+VgFZyxwrJ+SVIOsh+KywWGpds3NTqikiKpDMZWScmAYyKIgqkO+ARvNWJfXeXR1zxz7aHF4u4CyH6vQ==", "dev": true, "requires": {"graceful-fs": "^4.1.11", "micromatch": "^3.1.10", "readable-stream": "^2.0.2"}}, "redent": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/redent/-/redent-1.0.0.tgz", "integrity": "sha1-z5Fqsf1fHxbfsggi3W7H9zDCr94=", "dev": true, "requires": {"indent-string": "^2.1.0", "strip-indent": "^1.0.1"}}, "reduce-css-calc": {"version": "2.1.4", "resolved": "https://registry.npmjs.org/reduce-css-calc/-/reduce-css-calc-2.1.4.tgz", "integrity": "sha512-i/vWQbyd3aJRmip9OVSN9V6nIjLf/gg/ctxb0CpvHWtcRysFl/ngDBQD+rqavxdw/doScA3GMBXhzkHQ4GCzFQ==", "dev": true, "requires": {"css-unit-converter": "^1.1.1", "postcss-value-parser": "^3.3.0"}}, "regenerate": {"version": "1.4.0", "resolved": "https://registry.npmjs.org/regenerate/-/regenerate-1.4.0.tgz", "integrity": "sha512-1G6jJVDWrt0rK99kBjvEtziZNCICAuvIPkSiUFIQxVP06RCVpq3dmDo2oi6ABpYaDYaTRr67BEhL8r1wgEZZKg==", "dev": true}, "regenerate-unicode-properties": {"version": "7.0.0", "resolved": "https://registry.npmjs.org/regenerate-unicode-properties/-/regenerate-unicode-properties-7.0.0.tgz", "integrity": "sha512-s5NGghCE4itSlUS+0WUj88G6cfMVMmH8boTPNvABf8od+2dhT9WDlWu8n01raQAJZMOK8Ch6jSexaRO7swd6aw==", "dev": true, "requires": {"regenerate": "^1.4.0"}}, "regenerator-runtime": {"version": "0.11.1", "resolved": "https://registry.npmjs.org/regenerator-runtime/-/regenerator-runtime-0.11.1.tgz", "integrity": "sha512-MguG95oij0fC3QV3URf4V2SDYGJhJnJGqvIIgdECeODCT98wSWDAJ94SSuVpYQUoTcGUIL6L4yNB7j1DFFHSBg=="}, "regenerator-transform": {"version": "0.13.3", "resolved": "https://registry.npmjs.org/regenerator-transform/-/regenerator-transform-0.13.3.tgz", "integrity": "sha512-5ipTrZFSq5vU2YoGoww4uaRVAK4wyYC4TSICibbfEPOruUu8FFP7ErV0BjmbIOEpn3O/k9na9UEdYR/3m7N6uA==", "dev": true, "requires": {"private": "^0.1.6"}}, "regex-not": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/regex-not/-/regex-not-1.0.2.tgz", "integrity": "sha512-J6SDjUgDxQj5NusnOtdFxDwN/+HWykR8GELwctJ7mdqhcyy1xEc4SRFHUXvxTp661YaVKAjfRLZ9cCqS6tn32A==", "dev": true, "requires": {"extend-shallow": "^3.0.2", "safe-regex": "^1.1.0"}}, "regexpp": {"version": "2.0.0", "resolved": "http://r.npm.sankuai.com/regexpp/download/regexpp-2.0.0.tgz", "integrity": "sha1-sqdTSoXKGwM7z1zp/45W1OB1U2U=", "dev": true}, "regexpu-core": {"version": "4.2.0", "resolved": "https://registry.npmjs.org/regexpu-core/-/regexpu-core-4.2.0.tgz", "integrity": "sha512-Z835VSnJJ46CNBttalHD/dB+Sj2ezmY6Xp38npwU87peK6mqOzOpV8eYktdkLTEkzzD+JsTcxd84ozd8I14+rw==", "dev": true, "requires": {"regenerate": "^1.4.0", "regenerate-unicode-properties": "^7.0.0", "regjsgen": "^0.4.0", "regjsparser": "^0.3.0", "unicode-match-property-ecmascript": "^1.0.4", "unicode-match-property-value-ecmascript": "^1.0.2"}}, "regjsgen": {"version": "0.4.0", "resolved": "https://registry.npmjs.org/regjsgen/-/regjsgen-0.4.0.tgz", "integrity": "sha512-X51Lte1gCYUdlwhF28+2YMO0U6WeN0GLpgpA7LK7mbdDnkQYiwvEpmpe0F/cv5L14EbxgrdayAG3JETBv0dbXA==", "dev": true}, "regjsparser": {"version": "0.3.0", "resolved": "https://registry.npmjs.org/regjsparser/-/regjsparser-0.3.0.tgz", "integrity": "sha512-zza72oZBBHzt64G7DxdqrOo/30bhHkwMUoT0WqfGu98XLd7N+1tsy5MJ96Bk4MD0y74n629RhmrGW6XlnLLwCA==", "dev": true, "requires": {"jsesc": "~0.5.0"}, "dependencies": {"jsesc": {"version": "0.5.0", "resolved": "https://registry.npmjs.org/jsesc/-/jsesc-0.5.0.tgz", "integrity": "sha1-597mbjXW/Bb3EP6R1c9p9w8IkR0=", "dev": true}}}, "relateurl": {"version": "0.2.7", "resolved": "https://registry.npmjs.org/relateurl/-/relateurl-0.2.7.tgz", "integrity": "sha1-VNvzd+UUQKypCkzSdGANP/LYiKk=", "dev": true}, "remove-markdown": {"version": "0.2.2", "resolved": "http://r.npm.sankuai.com/remove-markdown/download/remove-markdown-0.2.2.tgz", "integrity": "sha1-ZrDO66n7d8qWNrsbAwfOIaMqEqY="}, "remove-trailing-separator": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/remove-trailing-separator/-/remove-trailing-separator-1.1.0.tgz", "integrity": "sha1-wkvOKig62tW8P1jg1IJJuSN52O8=", "dev": true}, "renderkid": {"version": "2.0.2", "resolved": "https://registry.npmjs.org/renderkid/-/renderkid-2.0.2.tgz", "integrity": "sha512-FsygIxevi1jSiPY9h7vZmBFUbAOcbYm9UwyiLNdVsLRs/5We9Ob5NMPbGYUTWiLq5L+ezlVdE0A8bbME5CWTpg==", "dev": true, "requires": {"css-select": "^1.1.0", "dom-converter": "~0.2", "htmlparser2": "~3.3.0", "strip-ansi": "^3.0.0", "utila": "^0.4.0"}, "dependencies": {"ansi-regex": {"version": "2.1.1", "resolved": "https://registry.npmjs.org/ansi-regex/-/ansi-regex-2.1.1.tgz", "integrity": "sha1-w7M6te42DYbg5ijwRorn7yfWVN8=", "dev": true}, "domhandler": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/domhandler/-/domhandler-2.1.0.tgz", "integrity": "sha1-0mRvXlf2w7qxHPbLBdPArPdBJZQ=", "dev": true, "requires": {"domelementtype": "1"}}, "domutils": {"version": "1.1.6", "resolved": "https://registry.npmjs.org/domutils/-/domutils-1.1.6.tgz", "integrity": "sha1-vdw94Jm5ou+sxRxiPyj0FuzFdIU=", "dev": true, "requires": {"domelementtype": "1"}}, "htmlparser2": {"version": "3.3.0", "resolved": "https://registry.npmjs.org/htmlparser2/-/htmlparser2-3.3.0.tgz", "integrity": "sha1-zHDQWln2VC5D8OaFyYLhTJJKnv4=", "dev": true, "requires": {"domelementtype": "1", "domhandler": "2.1", "domutils": "1.1", "readable-stream": "1.0"}}, "isarray": {"version": "0.0.1", "resolved": "https://registry.npmjs.org/isarray/-/isarray-0.0.1.tgz", "integrity": "sha1-ihis/Kmo9Bd+Cav8YDiTmwXR7t8=", "dev": true}, "readable-stream": {"version": "1.0.34", "resolved": "http://registry.npmjs.org/readable-stream/-/readable-stream-1.0.34.tgz", "integrity": "sha1-Elgg40vIQtLyqq+v5MKRbuMsFXw=", "dev": true, "requires": {"core-util-is": "~1.0.0", "inherits": "~2.0.1", "isarray": "0.0.1", "string_decoder": "~0.10.x"}}, "string_decoder": {"version": "0.10.31", "resolved": "https://registry.npmjs.org/string_decoder/-/string_decoder-0.10.31.tgz", "integrity": "sha1-YuIDvEF2bGwoyfyEMB2rHFMQ+pQ=", "dev": true}, "strip-ansi": {"version": "3.0.1", "resolved": "https://registry.npmjs.org/strip-ansi/-/strip-ansi-3.0.1.tgz", "integrity": "sha1-ajhfuIU9lS1f8F0Oiq+UJ43GPc8=", "dev": true, "requires": {"ansi-regex": "^2.0.0"}}}}, "repeat-element": {"version": "1.1.3", "resolved": "https://registry.npmjs.org/repeat-element/-/repeat-element-1.1.3.tgz", "integrity": "sha512-ahGq0ZnV5m5XtZLMb+vP76kcAM5nkLqk0lpqAuojSKGgQtn4eRi4ZZGm2olo2zKFH+sMsWaqOCW1dqAnOru72g==", "dev": true}, "repeat-string": {"version": "1.6.1", "resolved": "https://registry.npmjs.org/repeat-string/-/repeat-string-1.6.1.tgz", "integrity": "sha1-jcrkcOHIirwtYA//Sndihtp15jc=", "dev": true}, "repeating": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/repeating/-/repeating-2.0.1.tgz", "integrity": "sha1-UhTFOpJtNVJwdSf7q0FdvAjQbdo=", "dev": true, "requires": {"is-finite": "^1.0.0"}}, "request": {"version": "2.88.2", "resolved": "https://registry.npm.taobao.org/request/download/request-2.88.2.tgz", "integrity": "sha1-1zyRhzHLWofaBH4gcjQUb2ZNErM=", "requires": {"aws-sign2": "~0.7.0", "aws4": "^1.8.0", "caseless": "~0.12.0", "combined-stream": "~1.0.6", "extend": "~3.0.2", "forever-agent": "~0.6.1", "form-data": "~2.3.2", "har-validator": "~5.1.3", "http-signature": "~1.2.0", "is-typedarray": "~1.0.0", "isstream": "~0.1.2", "json-stringify-safe": "~5.0.1", "mime-types": "~2.1.19", "oauth-sign": "~0.9.0", "performance-now": "^2.1.0", "qs": "~6.5.2", "safe-buffer": "^5.1.2", "tough-cookie": "~2.5.0", "tunnel-agent": "^0.6.0", "uuid": "^3.3.2"}, "dependencies": {"qs": {"version": "6.5.2", "resolved": "https://registry.npm.taobao.org/qs/download/qs-6.5.2.tgz", "integrity": "sha1-yzroBuh0BERYTvFUzo7pjUA/PjY="}}}, "require-directory": {"version": "2.1.1", "resolved": "https://registry.npmjs.org/require-directory/-/require-directory-2.1.1.tgz", "integrity": "sha1-jGStX9MNqxyXbiNE/+f3kqam30I=", "dev": true}, "require-from-string": {"version": "2.0.2", "resolved": "https://registry.npmjs.org/require-from-string/-/require-from-string-2.0.2.tgz", "integrity": "sha512-Xf0nWe6RseziFMu+Ap9biiUbmplq6S9/p+7w7YXP/JBHhrUDDUhwa+vANyubuqfZWTveU//DYVGsDG7RKL/vEw==", "dev": true}, "require-main-filename": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/require-main-filename/-/require-main-filename-1.0.1.tgz", "integrity": "sha1-l/cXtp1IeE9fUmpsWqj/3aBVpNE=", "dev": true}, "require-uncached": {"version": "1.0.3", "resolved": "http://r.npm.sankuai.com/require-uncached/download/require-uncached-1.0.3.tgz", "integrity": "sha1-Tg1W1slmL9MeQwEcS5WqSZVUIdM=", "dev": true, "requires": {"caller-path": "^0.1.0", "resolve-from": "^1.0.0"}}, "requireindex": {"version": "1.1.0", "resolved": "http://r.npm.sankuai.com/requireindex/download/requireindex-1.1.0.tgz", "integrity": "sha1-5UBLgVV+91225JxacgBIk/4D4WI="}, "requires-port": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/requires-port/-/requires-port-1.0.0.tgz", "integrity": "sha1-kl0mAdOaxIXgkc8NpcbmlNw9yv8=", "dev": true}, "resize-observer-polyfill": {"version": "1.5.1", "resolved": "http://r.npm.sankuai.com/resize-observer-polyfill/download/resize-observer-polyfill-1.5.1.tgz", "integrity": "sha1-DpAg3T0hAkRY1OvSfiPkAmmBBGQ="}, "resolve": {"version": "1.8.1", "resolved": "http://r.npm.sankuai.com/resolve/download/resolve-1.8.1.tgz", "integrity": "sha1-gvHsGaQjrB+9CAsLqwa6NuhKeiY=", "dev": true, "requires": {"path-parse": "^1.0.5"}}, "resolve-cwd": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/resolve-cwd/-/resolve-cwd-2.0.0.tgz", "integrity": "sha1-AKn3OHVW4nA46uIyyqNypqWbZlo=", "dev": true, "requires": {"resolve-from": "^3.0.0"}, "dependencies": {"resolve-from": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/resolve-from/-/resolve-from-3.0.0.tgz", "integrity": "sha1-six699nWiBvItuZTM17rywoYh0g=", "dev": true}}}, "resolve-from": {"version": "1.0.1", "resolved": "http://r.npm.sankuai.com/resolve-from/download/resolve-from-1.0.1.tgz", "integrity": "sha1-Jsv+k10a7uq7Kbw/5a6wHpPUQiY=", "dev": true}, "resolve-global": {"version": "1.0.0", "resolved": "http://r.npm.sankuai.com/resolve-global/download/resolve-global-1.0.0.tgz", "integrity": "sha1-oqed9K8so/Sb93753azTItrRklU=", "dev": true, "requires": {"global-dirs": "^0.1.1"}}, "resolve-url": {"version": "0.2.1", "resolved": "https://registry.npmjs.org/resolve-url/-/resolve-url-0.2.1.tgz", "integrity": "sha1-LGN/53yJOv0qZj/iGqkIAGjiBSo=", "dev": true}, "restore-cursor": {"version": "2.0.0", "resolved": "http://r.npm.sankuai.com/restore-cursor/download/restore-cursor-2.0.0.tgz", "integrity": "sha1-n37ih/gv0ybU/RYpI9YhKe7g368=", "dev": true, "requires": {"onetime": "^2.0.0", "signal-exit": "^3.0.2"}}, "ret": {"version": "0.1.15", "resolved": "https://registry.npmjs.org/ret/-/ret-0.1.15.tgz", "integrity": "sha512-TTlYpa+OL+vMMNG24xSlQGEJ3B/RzEfUlLct7b5G/ytav+wPrplCpVMFuwzXbkecJrb6IYo1iFb0S9v37754mg==", "dev": true}, "rgb-regex": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/rgb-regex/-/rgb-regex-1.0.1.tgz", "integrity": "sha1-wODWiC3w4jviVKR16O3UGRX+rrE=", "dev": true}, "rgba-regex": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/rgba-regex/-/rgba-regex-1.0.0.tgz", "integrity": "sha1-QzdOLiyglosO8VI0YLfXMP8i7rM=", "dev": true}, "rimraf": {"version": "2.6.2", "resolved": "http://r.npm.sankuai.com/rimraf/download/rimraf-2.6.2.tgz", "integrity": "sha1-LtgVDSShbqhlHm1u8PR8QVjOejY=", "dev": true, "requires": {"glob": "^7.0.5"}}, "ripemd160": {"version": "2.0.2", "resolved": "https://registry.npmjs.org/ripemd160/-/ripemd160-2.0.2.tgz", "integrity": "sha512-ii4iagi25WusVoiC4B4lq7pbXfAp3D9v5CwfkY33vffw2+pkDjY1D8GaN7spsxvCSx8dkPqOZCEZyfxcmJG2IA==", "dev": true, "requires": {"hash-base": "^3.0.0", "inherits": "^2.0.1"}}, "run-async": {"version": "2.3.0", "resolved": "http://r.npm.sankuai.com/run-async/download/run-async-2.3.0.tgz", "integrity": "sha1-A3GrSuC91yDUFm19/aZP96RFpsA=", "dev": true, "requires": {"is-promise": "^2.1.0"}}, "run-node": {"version": "1.0.0", "resolved": "http://r.npm.sankuai.com/run-node/download/run-node-1.0.0.tgz", "integrity": "sha1-RrULlGoqotSUeuHYhumFb9nKvl4=", "dev": true}, "run-queue": {"version": "1.0.3", "resolved": "https://registry.npmjs.org/run-queue/-/run-queue-1.0.3.tgz", "integrity": "sha1-6Eg5bwV9Ij8kOGkkYY4laUFh7Ec=", "dev": true, "requires": {"aproba": "^1.1.1"}}, "rxjs": {"version": "6.3.2", "resolved": "http://r.npm.sankuai.com/rxjs/download/rxjs-6.3.2.tgz", "integrity": "sha1-amiLFsTm6YDmLqgF7DBkjhxgkH8=", "dev": true, "requires": {"tslib": "^1.9.0"}}, "safe-buffer": {"version": "5.1.2", "resolved": "http://r.npm.sankuai.com/safe-buffer/download/safe-buffer-5.1.2.tgz", "integrity": "sha1-mR7GnSluAxN0fVm9/St0XDX4go0="}, "safe-regex": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/safe-regex/-/safe-regex-1.1.0.tgz", "integrity": "sha1-QKNmnzsHfR6UPURinhV91IAjvy4=", "dev": true, "requires": {"ret": "~0.1.10"}}, "safer-buffer": {"version": "2.1.2", "resolved": "http://r.npm.sankuai.com/safer-buffer/download/safer-buffer-2.1.2.tgz", "integrity": "sha1-RPoWGwGHuVSd2Eu5GAL5vYOFzWo="}, "sass-graph": {"version": "2.2.5", "resolved": "https://registry.npm.taobao.org/sass-graph/download/sass-graph-2.2.5.tgz", "integrity": "sha1-qYHIdEa4MZ2W3OBnHkh4eb0kwug=", "dev": true, "requires": {"glob": "^7.0.0", "lodash": "^4.0.0", "scss-tokenizer": "^0.2.3", "yargs": "^13.3.2"}, "dependencies": {"ansi-regex": {"version": "4.1.0", "resolved": "https://registry.npm.taobao.org/ansi-regex/download/ansi-regex-4.1.0.tgz", "integrity": "sha1-i5+PCM8ay4Q3Vqg5yox+MWjFGZc=", "dev": true}, "camelcase": {"version": "5.3.1", "resolved": "https://registry.npm.taobao.org/camelcase/download/camelcase-5.3.1.tgz?cache=0&sync_timestamp=1603921799543&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcamelcase%2Fdownload%2Fcamelcase-5.3.1.tgz", "integrity": "sha1-48mzFWnhBoEd8kL3FXJaH0xJQyA=", "dev": true}, "cliui": {"version": "5.0.0", "resolved": "https://registry.npm.taobao.org/cliui/download/cliui-5.0.0.tgz?cache=0&sync_timestamp=1602861369245&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcliui%2Fdownload%2Fcliui-5.0.0.tgz", "integrity": "sha1-3u/P2y6AB4SqNPRvoI4GhRx7u8U=", "dev": true, "requires": {"string-width": "^3.1.0", "strip-ansi": "^5.2.0", "wrap-ansi": "^5.1.0"}}, "decamelize": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/decamelize/-/decamelize-1.2.0.tgz", "integrity": "sha1-9lNNFRSCabIDUue+4m9QH5oZEpA=", "dev": true}, "find-up": {"version": "3.0.0", "resolved": "https://registry.npm.taobao.org/find-up/download/find-up-3.0.0.tgz?cache=0&sync_timestamp=1597170071453&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Ffind-up%2Fdownload%2Ffind-up-3.0.0.tgz", "integrity": "sha1-SRafHXmTQwZG2mHsxa41XCHJe3M=", "dev": true, "requires": {"locate-path": "^3.0.0"}}, "get-caller-file": {"version": "2.0.5", "resolved": "https://registry.npm.taobao.org/get-caller-file/download/get-caller-file-2.0.5.tgz", "integrity": "sha1-T5RBKoLbMvNuOwuXQfipf+sDH34=", "dev": true}, "locate-path": {"version": "3.0.0", "resolved": "https://registry.npm.taobao.org/locate-path/download/locate-path-3.0.0.tgz?cache=0&sync_timestamp=1597081904643&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Flocate-path%2Fdownload%2Flocate-path-3.0.0.tgz", "integrity": "sha1-2+w7OrdZdYBxtY/ln8QYca8hQA4=", "dev": true, "requires": {"p-locate": "^3.0.0", "path-exists": "^3.0.0"}}, "p-limit": {"version": "2.3.0", "resolved": "https://registry.npm.taobao.org/p-limit/download/p-limit-2.3.0.tgz?cache=0&sync_timestamp=1594559720897&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fp-limit%2Fdownload%2Fp-limit-2.3.0.tgz", "integrity": "sha1-PdM8ZHohT9//2DWTPrCG2g3CHbE=", "dev": true, "requires": {"p-try": "^2.0.0"}}, "p-locate": {"version": "3.0.0", "resolved": "https://registry.npm.taobao.org/p-locate/download/p-locate-3.0.0.tgz?cache=0&sync_timestamp=1597081785924&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fp-locate%2Fdownload%2Fp-locate-3.0.0.tgz", "integrity": "sha1-Mi1poFwCZLJZl9n0DNiokasAZKQ=", "dev": true, "requires": {"p-limit": "^2.0.0"}}, "p-try": {"version": "2.2.0", "resolved": "https://registry.npm.taobao.org/p-try/download/p-try-2.2.0.tgz", "integrity": "sha1-yyhoVA4xPWHeWPr741zpAE1VQOY=", "dev": true}, "path-exists": {"version": "3.0.0", "resolved": "https://registry.npm.taobao.org/path-exists/download/path-exists-3.0.0.tgz", "integrity": "sha1-zg6+ql94yxiSXqfYENe1mwEP1RU=", "dev": true}, "require-main-filename": {"version": "2.0.0", "resolved": "https://registry.npm.taobao.org/require-main-filename/download/require-main-filename-2.0.0.tgz", "integrity": "sha1-0LMp7MfMD2Fkn2IhW+aa9UqomJs=", "dev": true}, "string-width": {"version": "3.1.0", "resolved": "https://registry.npm.taobao.org/string-width/download/string-width-3.1.0.tgz", "integrity": "sha1-InZ74htirxCBV0MG9prFG2IgOWE=", "dev": true, "requires": {"emoji-regex": "^7.0.1", "is-fullwidth-code-point": "^2.0.0", "strip-ansi": "^5.1.0"}}, "strip-ansi": {"version": "5.2.0", "resolved": "https://registry.npm.taobao.org/strip-ansi/download/strip-ansi-5.2.0.tgz?cache=0&sync_timestamp=1596697387823&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fstrip-ansi%2Fdownload%2Fstrip-ansi-5.2.0.tgz", "integrity": "sha1-jJpTb+tq/JYr36WxBKUJHBrZwK4=", "dev": true, "requires": {"ansi-regex": "^4.1.0"}}, "wrap-ansi": {"version": "5.1.0", "resolved": "https://registry.npm.taobao.org/wrap-ansi/download/wrap-ansi-5.1.0.tgz", "integrity": "sha1-H9H2cjXVttD+54EFYAG/tpTAOwk=", "dev": true, "requires": {"ansi-styles": "^3.2.0", "string-width": "^3.0.0", "strip-ansi": "^5.0.0"}}, "yargs": {"version": "13.3.2", "resolved": "https://registry.npm.taobao.org/yargs/download/yargs-13.3.2.tgz?cache=0&sync_timestamp=1602805524183&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fyargs%2Fdownload%2Fyargs-13.3.2.tgz", "integrity": "sha1-rX/+/sGqWVZayRX4Lcyzipwxot0=", "dev": true, "requires": {"cliui": "^5.0.0", "find-up": "^3.0.0", "get-caller-file": "^2.0.1", "require-directory": "^2.1.1", "require-main-filename": "^2.0.0", "set-blocking": "^2.0.0", "string-width": "^3.0.0", "which-module": "^2.0.0", "y18n": "^4.0.0", "yargs-parser": "^13.1.2"}}, "yargs-parser": {"version": "13.1.2", "resolved": "https://registry.npm.taobao.org/yargs-parser/download/yargs-parser-13.1.2.tgz?cache=0&sync_timestamp=1602861369408&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fyargs-parser%2Fdownload%2Fyargs-parser-13.1.2.tgz", "integrity": "sha1-Ew8JcC667vJlDVTObj5XBvek+zg=", "dev": true, "requires": {"camelcase": "^5.0.0", "decamelize": "^1.2.0"}}}}, "sass-loader": {"version": "7.1.0", "resolved": "https://registry.npmjs.org/sass-loader/-/sass-loader-7.1.0.tgz", "integrity": "sha512-+G+BKGglmZM2GUSfT9TLuEp6tzehHPjAMoRRItOojWIqIGPloVCMhNIQuG639eJ+y033PaGTSjLaTHts8Kw79w==", "dev": true, "requires": {"clone-deep": "^2.0.1", "loader-utils": "^1.0.1", "lodash.tail": "^4.1.1", "neo-async": "^2.5.0", "pify": "^3.0.0", "semver": "^5.5.0"}, "dependencies": {"pify": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/pify/-/pify-3.0.0.tgz", "integrity": "sha1-5aSs0sEB/fPZpNB/DbxNtJ3SgXY=", "dev": true}}}, "sax": {"version": "0.4.2", "resolved": "http://registry.npmjs.org/sax/-/sax-0.4.2.tgz", "integrity": "sha1-OfO2AXM9a+yXEFskKipA/Wl4rDw=", "dev": true}, "schema-utils": {"version": "0.4.7", "resolved": "https://registry.npmjs.org/schema-utils/-/schema-utils-0.4.7.tgz", "integrity": "sha512-v/iwU6wvwGK8HbU9yi3/nhGzP0yGSuhQMzL6ySiec1FSrZZDkhm4noOSWzrNFo/jEc+SJY6jRTwuwbSXJPDUnQ==", "dev": true, "requires": {"ajv": "^6.1.0", "ajv-keywords": "^3.1.0"}}, "scss-tokenizer": {"version": "0.2.3", "resolved": "https://registry.npmjs.org/scss-tokenizer/-/scss-tokenizer-0.2.3.tgz", "integrity": "sha1-jrBtualyMzOCTT9VMGQRSYR85dE=", "dev": true, "requires": {"js-base64": "^2.1.8", "source-map": "^0.4.2"}, "dependencies": {"source-map": {"version": "0.4.4", "resolved": "https://registry.npmjs.org/source-map/-/source-map-0.4.4.tgz", "integrity": "sha1-66T12pwNyZneaAMti092FzZSA2s=", "dev": true, "requires": {"amdefine": ">=0.0.4"}}}}, "select-hose": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/select-hose/-/select-hose-2.0.0.tgz", "integrity": "sha1-Yl2GWPhlr0Psliv8N2o3NZpJlMo=", "dev": true}, "selfsigned": {"version": "1.10.3", "resolved": "https://registry.npmjs.org/selfsigned/-/selfsigned-1.10.3.tgz", "integrity": "sha512-vmZenZ+8Al3NLHkWnhBQ0x6BkML1eCP2xEi3JE+f3D9wW9fipD9NNJHYtE9XJM4TsPaHGZJIamrSI6MTg1dU2Q==", "dev": true, "requires": {"node-forge": "0.7.5"}}, "semver": {"version": "5.5.1", "resolved": "http://r.npm.sankuai.com/semver/download/semver-5.5.1.tgz", "integrity": "sha1-ff3YgUvbfKvHvg+x1zTPtmyUBHc="}, "semver-compare": {"version": "1.0.0", "resolved": "http://r.npm.sankuai.com/semver-compare/download/semver-compare-1.0.0.tgz", "integrity": "sha1-De4hahyUGrN+nvsXiPavxf9VN/w=", "dev": true}, "send": {"version": "0.18.0", "resolved": "http://r.npm.sankuai.com/send/download/send-0.18.0.tgz", "integrity": "sha1-ZwFnzGVLBfWqSnZ/kRO7NxvHBr4=", "requires": {"debug": "2.6.9", "depd": "2.0.0", "destroy": "1.2.0", "encodeurl": "~1.0.2", "escape-html": "~1.0.3", "etag": "~1.8.1", "fresh": "0.5.2", "http-errors": "2.0.0", "mime": "1.6.0", "ms": "2.1.3", "on-finished": "2.4.1", "range-parser": "~1.2.1", "statuses": "2.0.1"}, "dependencies": {"debug": {"version": "2.6.9", "resolved": "http://r.npm.sankuai.com/debug/download/debug-2.6.9.tgz", "integrity": "sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8=", "requires": {"ms": "2.0.0"}, "dependencies": {"ms": {"version": "2.0.0", "resolved": "http://r.npm.sankuai.com/ms/download/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g="}}}, "depd": {"version": "2.0.0", "resolved": "http://r.npm.sankuai.com/depd/download/depd-2.0.0.tgz", "integrity": "sha1-tpYWPMdXVg0JzyLMj60Vcbeedt8="}, "http-errors": {"version": "2.0.0", "resolved": "http://r.npm.sankuai.com/http-errors/download/http-errors-2.0.0.tgz", "integrity": "sha1-t3dKFIbvc892Z6ya4IWMASxXudM=", "requires": {"depd": "2.0.0", "inherits": "2.0.4", "setprototypeof": "1.2.0", "statuses": "2.0.1", "toidentifier": "1.0.1"}}, "inherits": {"version": "2.0.4", "resolved": "http://r.npm.sankuai.com/inherits/download/inherits-2.0.4.tgz", "integrity": "sha1-D6LGT5MpF8NDOg3tVTY6rjdBa3w="}, "mime": {"version": "1.6.0", "resolved": "http://r.npm.sankuai.com/mime/download/mime-1.6.0.tgz", "integrity": "sha1-Ms2eXGRVO9WNGaVor0Uqz/BJgbE="}, "ms": {"version": "2.1.3", "resolved": "http://r.npm.sankuai.com/ms/download/ms-2.1.3.tgz", "integrity": "sha1-V0yBOM4dK1hh8LRFedut1gxmFbI="}, "range-parser": {"version": "1.2.1", "resolved": "http://r.npm.sankuai.com/range-parser/download/range-parser-1.2.1.tgz", "integrity": "sha1-PPNwI9GZ4cJNGlW4SADC8+ZGgDE="}, "setprototypeof": {"version": "1.2.0", "resolved": "http://r.npm.sankuai.com/setprototypeof/download/setprototypeof-1.2.0.tgz", "integrity": "sha1-ZsmiSnP5/CjL5msJ/tPTPcrxtCQ="}, "statuses": {"version": "2.0.1", "resolved": "http://r.npm.sankuai.com/statuses/download/statuses-2.0.1.tgz", "integrity": "sha1-VcsADM8dSHKL0jxoWgY5mM8aG2M="}}}, "serialize-javascript": {"version": "1.5.0", "resolved": "https://registry.npmjs.org/serialize-javascript/-/serialize-javascript-1.5.0.tgz", "integrity": "sha512-Ga8c8NjAAp46Br4+0oZ2WxJCwIzwP60Gq1YPgU+39PiTVxyed/iKE/zyZI6+UlVYH5Q4PaQdHhcegIFPZTUfoQ==", "dev": true}, "serve-index": {"version": "1.9.1", "resolved": "https://registry.npmjs.org/serve-index/-/serve-index-1.9.1.tgz", "integrity": "sha1-03aNabHn2C5c4FD/9bRTvqEqkjk=", "dev": true, "requires": {"accepts": "~1.3.4", "batch": "0.6.1", "debug": "2.6.9", "escape-html": "~1.0.3", "http-errors": "~1.6.2", "mime-types": "~2.1.17", "parseurl": "~1.3.2"}, "dependencies": {"debug": {"version": "2.6.9", "resolved": "https://registry.npmjs.org/debug/-/debug-2.6.9.tgz", "integrity": "sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA==", "dev": true, "requires": {"ms": "2.0.0"}}, "ms": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/ms/-/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g=", "dev": true}}}, "serve-static": {"version": "1.15.0", "resolved": "http://r.npm.sankuai.com/serve-static/download/serve-static-1.15.0.tgz", "integrity": "sha1-+q7wjP/goaYvYMrQxOUTz/CslUA=", "requires": {"encodeurl": "~1.0.2", "escape-html": "~1.0.3", "parseurl": "~1.3.3", "send": "0.18.0"}, "dependencies": {"parseurl": {"version": "1.3.3", "resolved": "http://r.npm.sankuai.com/parseurl/download/parseurl-1.3.3.tgz", "integrity": "sha1-naGee+6NEt/wUT7Vt2lXeTvC6NQ="}}}, "set-blocking": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/set-blocking/-/set-blocking-2.0.0.tgz", "integrity": "sha1-BF+XgtARrppoA93TgrJDkrPYkPc=", "dev": true}, "set-value": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/set-value/-/set-value-2.0.0.tgz", "integrity": "sha512-hw0yxk9GT/Hr5yJEYnHNKYXkIA8mVJgd9ditYZCe16ZczcaELYYcfvaXesNACk2O8O0nTiPQcQhGUQj8JLzeeg==", "dev": true, "requires": {"extend-shallow": "^2.0.1", "is-extendable": "^0.1.1", "is-plain-object": "^2.0.3", "split-string": "^3.0.1"}, "dependencies": {"extend-shallow": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/extend-shallow/-/extend-shallow-2.0.1.tgz", "integrity": "sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8=", "dev": true, "requires": {"is-extendable": "^0.1.0"}}}}, "setimmediate": {"version": "1.0.5", "resolved": "https://registry.npmjs.org/setimmediate/-/setimmediate-1.0.5.tgz", "integrity": "sha1-KQy7Iy4waULX1+qbg3Mqt4VvgoU=", "dev": true}, "setprototypeof": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/setprototypeof/-/setprototypeof-1.1.0.tgz", "integrity": "sha512-BvE/TwpZX4FXExxOxZyRGQQv651MSwmWKZGqvmPcRIjDqWub67kTKuIMx43cZZrS/cBBzwBcNDWoFxt2XEFIpQ==", "dev": true}, "sha.js": {"version": "2.4.11", "resolved": "http://registry.npmjs.org/sha.js/-/sha.js-2.4.11.tgz", "integrity": "sha512-QMEp5B7cftE7APOjk5Y6xgrbWu+WkLVQwk8JNjZ8nKRciZaByEW6MubieAiToS7+dwvrjGhH8jRXz3MVd0AYqQ==", "dev": true, "requires": {"inherits": "^2.0.1", "safe-buffer": "^5.0.1"}}, "shallow-clone": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/shallow-clone/-/shallow-clone-1.0.0.tgz", "integrity": "sha512-oeXreoKR/SyNJtRJMAKPDSvd28OqEwG4eR/xc856cRGBII7gX9lvAqDxusPm0846z/w/hWYjI1NpKwJ00NHzRA==", "dev": true, "requires": {"is-extendable": "^0.1.1", "kind-of": "^5.0.0", "mixin-object": "^2.0.1"}, "dependencies": {"kind-of": {"version": "5.1.0", "resolved": "https://registry.npmjs.org/kind-of/-/kind-of-5.1.0.tgz", "integrity": "sha512-NGEErnH6F2vUuXDh+OlbcKW7/wOcfdRHaZ7VWtqCztfHri/++YKmP51OdWeGPuqCOba6kk2OTe5d02VmTB80Pw==", "dev": true}}}, "shebang-command": {"version": "1.2.0", "resolved": "http://r.npm.sankuai.com/shebang-command/download/shebang-command-1.2.0.tgz", "integrity": "sha1-RKrGW2lbAzmJaMOfNj/uXer98eo=", "dev": true, "requires": {"shebang-regex": "^1.0.0"}}, "shebang-regex": {"version": "1.0.0", "resolved": "http://r.npm.sankuai.com/shebang-regex/download/shebang-regex-1.0.0.tgz", "integrity": "sha1-2kL0l0DAtC2yypcoVxyxkMmO/qM=", "dev": true}, "side-channel": {"version": "1.0.4", "resolved": "http://r.npm.sankuai.com/side-channel/download/side-channel-1.0.4.tgz", "integrity": "sha1-785cj9wQTudRslxY1CkAEfpeos8=", "requires": {"call-bind": "^1.0.0", "get-intrinsic": "^1.0.2", "object-inspect": "^1.9.0"}}, "signal-exit": {"version": "3.0.2", "resolved": "http://r.npm.sankuai.com/signal-exit/download/signal-exit-3.0.2.tgz", "integrity": "sha1-tf3AjxKH6hF4Yo5BXiUTK3NkbG0="}, "simple-swizzle": {"version": "0.2.2", "resolved": "https://registry.npmjs.org/simple-swizzle/-/simple-swizzle-0.2.2.tgz", "integrity": "sha1-pNprY1/8zMoz9w0Xy5JZLeleVXo=", "dev": true, "requires": {"is-arrayish": "^0.3.1"}, "dependencies": {"is-arrayish": {"version": "0.3.2", "resolved": "https://registry.npmjs.org/is-arrayish/-/is-arrayish-0.3.2.tgz", "integrity": "sha512-eVRqCvVlZbuw3GrM63ovNSNAeA1K16kaR/LRY/92w0zxQ5/1YzwblUX652i4Xs9RwAGjW9d9y6X88t8OaAJfWQ==", "dev": true}}}, "slash": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/slash/-/slash-1.0.0.tgz", "integrity": "sha1-xB8vbDn8FtHNF61LXYlhFK5HDVU=", "dev": true}, "slice-ansi": {"version": "1.0.0", "resolved": "http://r.npm.sankuai.com/slice-ansi/download/slice-ansi-1.0.0.tgz", "integrity": "sha1-BE8aSdiEL/MHqta1Be0Xi9lQE00=", "dev": true, "requires": {"is-fullwidth-code-point": "^2.0.0"}}, "snapdragon": {"version": "0.8.2", "resolved": "https://registry.npmjs.org/snapdragon/-/snapdragon-0.8.2.tgz", "integrity": "sha512-FtyOnWN/wCHTVXOMwvSv26d+ko5vWlIDD6zoUJ7LW8vh+ZBC8QdljveRP+crNrtBwioEUWy/4dMtbBjA4ioNlg==", "dev": true, "requires": {"base": "^0.11.1", "debug": "^2.2.0", "define-property": "^0.2.5", "extend-shallow": "^2.0.1", "map-cache": "^0.2.2", "source-map": "^0.5.6", "source-map-resolve": "^0.5.0", "use": "^3.1.0"}, "dependencies": {"debug": {"version": "2.6.9", "resolved": "https://registry.npmjs.org/debug/-/debug-2.6.9.tgz", "integrity": "sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA==", "dev": true, "requires": {"ms": "2.0.0"}}, "define-property": {"version": "0.2.5", "resolved": "https://registry.npmjs.org/define-property/-/define-property-0.2.5.tgz", "integrity": "sha1-w1se+RjsPJkPmlvFe+BKrOxcgRY=", "dev": true, "requires": {"is-descriptor": "^0.1.0"}}, "extend-shallow": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/extend-shallow/-/extend-shallow-2.0.1.tgz", "integrity": "sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8=", "dev": true, "requires": {"is-extendable": "^0.1.0"}}, "ms": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/ms/-/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g=", "dev": true}}}, "snapdragon-node": {"version": "2.1.1", "resolved": "https://registry.npmjs.org/snapdragon-node/-/snapdragon-node-2.1.1.tgz", "integrity": "sha512-O27l4xaMYt/RSQ5TR3vpWCAB5Kb/czIcqUFOM/C4fYcLnbZUc1PkjTAMjof2pBWaSTwOUd6qUHcFGVGj7aIwnw==", "dev": true, "requires": {"define-property": "^1.0.0", "isobject": "^3.0.0", "snapdragon-util": "^3.0.1"}, "dependencies": {"define-property": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/define-property/-/define-property-1.0.0.tgz", "integrity": "sha1-dp66rz9KY6rTr56NMEybvnm/sOY=", "dev": true, "requires": {"is-descriptor": "^1.0.0"}}, "is-accessor-descriptor": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/is-accessor-descriptor/-/is-accessor-descriptor-1.0.0.tgz", "integrity": "sha512-m5hnHTkcVsPfqx3AKlyttIPb7J+XykHvJP2B9bZDjlhLIoEq4XoK64Vg7boZlVWYK6LUY94dYPEE7Lh0ZkZKcQ==", "dev": true, "requires": {"kind-of": "^6.0.0"}}, "is-data-descriptor": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/is-data-descriptor/-/is-data-descriptor-1.0.0.tgz", "integrity": "sha512-jbRXy1FmtAoCjQkVmIVYwuuqDFUbaOeDjmed1tOGPrsMhtJA4rD9tkgA0F1qJ3gRFRXcHYVkdeaP50Q5rE/jLQ==", "dev": true, "requires": {"kind-of": "^6.0.0"}}, "is-descriptor": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/is-descriptor/-/is-descriptor-1.0.2.tgz", "integrity": "sha512-2eis5WqQGV7peooDyLmNEPUrps9+SXX5c9pL3xEB+4e9HnGuDa7mB7kHxHw4CbqS9k1T2hOH3miL8n8WtiYVtg==", "dev": true, "requires": {"is-accessor-descriptor": "^1.0.0", "is-data-descriptor": "^1.0.0", "kind-of": "^6.0.2"}}}}, "snapdragon-util": {"version": "3.0.1", "resolved": "https://registry.npmjs.org/snapdragon-util/-/snapdragon-util-3.0.1.tgz", "integrity": "sha512-mbKkMdQKsjX4BAL4bRYTj21edOf8cN7XHdYUJEe+Zn99hVEYcMvKPct1IqNe7+AZPirn8BCDOQBHQZknqmKlZQ==", "dev": true, "requires": {"kind-of": "^3.2.0"}, "dependencies": {"kind-of": {"version": "3.2.2", "resolved": "https://registry.npmjs.org/kind-of/-/kind-of-3.2.2.tgz", "integrity": "sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ=", "dev": true, "requires": {"is-buffer": "^1.1.5"}}}}, "sockjs": {"version": "0.3.19", "resolved": "https://registry.npmjs.org/sockjs/-/sockjs-0.3.19.tgz", "integrity": "sha512-V48klKZl8T6MzatbLlzzRNhMepEys9Y4oGFpypBFFn1gLI/QQ9HtLLyWJNbPlwGLelOVOEijUbTTJeLLI59jLw==", "dev": true, "requires": {"faye-websocket": "^0.10.0", "uuid": "^3.0.1"}}, "sockjs-client": {"version": "1.1.5", "resolved": "https://registry.npmjs.org/sockjs-client/-/sockjs-client-1.1.5.tgz", "integrity": "sha1-G7fA9yIsQPQq3xT0RCy9Eml3GoM=", "dev": true, "requires": {"debug": "^2.6.6", "eventsource": "0.1.6", "faye-websocket": "~0.11.0", "inherits": "^2.0.1", "json3": "^3.3.2", "url-parse": "^1.1.8"}, "dependencies": {"debug": {"version": "2.6.9", "resolved": "https://registry.npmjs.org/debug/-/debug-2.6.9.tgz", "integrity": "sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA==", "dev": true, "requires": {"ms": "2.0.0"}}, "faye-websocket": {"version": "0.11.1", "resolved": "https://registry.npmjs.org/faye-websocket/-/faye-websocket-0.11.1.tgz", "integrity": "sha1-8O/hjE9W5PQK/H4Gxxn9XuYYjzg=", "dev": true, "requires": {"websocket-driver": ">=0.5.1"}}, "ms": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/ms/-/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g=", "dev": true}}}, "source-list-map": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/source-list-map/-/source-list-map-2.0.0.tgz", "integrity": "sha512-I2UmuJSRr/T8jisiROLU3A3ltr+swpniSmNPI4Ml3ZCX6tVnDsuZzK7F2hl5jTqbZBWCEKlj5HRQiPExXLgE8A==", "dev": true}, "source-map": {"version": "0.5.7", "resolved": "http://r.npm.sankuai.com/source-map/download/source-map-0.5.7.tgz", "integrity": "sha1-igOdLRAh0i0eoUyA2OpGi6LvP8w=", "dev": true}, "source-map-js": {"version": "1.0.2", "resolved": "http://r.npm.sankuai.com/source-map-js/download/source-map-js-1.0.2.tgz", "integrity": "sha1-rbw2HZxi3zgBJefxYfccgm8eSQw="}, "source-map-resolve": {"version": "0.5.2", "resolved": "https://registry.npmjs.org/source-map-resolve/-/source-map-resolve-0.5.2.tgz", "integrity": "sha512-MjqsvNwyz1s0k81Goz/9vRBe9SZdB09Bdw+/zYyO+3CuPk6fouTaxscHkgtE8jKvf01kVfl8riHzERQ/kefaSA==", "dev": true, "requires": {"atob": "^2.1.1", "decode-uri-component": "^0.2.0", "resolve-url": "^0.2.1", "source-map-url": "^0.4.0", "urix": "^0.1.0"}}, "source-map-url": {"version": "0.4.0", "resolved": "https://registry.npmjs.org/source-map-url/-/source-map-url-0.4.0.tgz", "integrity": "sha1-PpNdfd1zYxuXZZlW1VEo6HtQhKM=", "dev": true}, "spdx-correct": {"version": "3.0.0", "resolved": "http://r.npm.sankuai.com/spdx-correct/download/spdx-correct-3.0.0.tgz", "integrity": "sha1-BaW01xU6GVvJLDxCW2nzsqlSTII=", "dev": true, "requires": {"spdx-expression-parse": "^3.0.0", "spdx-license-ids": "^3.0.0"}}, "spdx-exceptions": {"version": "2.1.0", "resolved": "http://r.npm.sankuai.com/spdx-exceptions/download/spdx-exceptions-2.1.0.tgz", "integrity": "sha1-LHrmEFbHFKW5ubKyr30xHvXHj+k=", "dev": true}, "spdx-expression-parse": {"version": "3.0.0", "resolved": "http://r.npm.sankuai.com/spdx-expression-parse/download/spdx-expression-parse-3.0.0.tgz", "integrity": "sha1-meEZt6XaAOBUkcn6M4t5BII7QdA=", "dev": true, "requires": {"spdx-exceptions": "^2.1.0", "spdx-license-ids": "^3.0.0"}}, "spdx-license-ids": {"version": "3.0.1", "resolved": "http://r.npm.sankuai.com/spdx-license-ids/download/spdx-license-ids-3.0.1.tgz", "integrity": "sha1-4qMDI2ysVLBAMfp6WnnH5wHfhS8=", "dev": true}, "spdy": {"version": "3.4.7", "resolved": "https://registry.npmjs.org/spdy/-/spdy-3.4.7.tgz", "integrity": "sha1-Qv9B7OXMD5mjpsKKq7c/XDsDrLw=", "dev": true, "requires": {"debug": "^2.6.8", "handle-thing": "^1.2.5", "http-deceiver": "^1.2.7", "safe-buffer": "^5.0.1", "select-hose": "^2.0.0", "spdy-transport": "^2.0.18"}, "dependencies": {"debug": {"version": "2.6.9", "resolved": "https://registry.npmjs.org/debug/-/debug-2.6.9.tgz", "integrity": "sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA==", "dev": true, "requires": {"ms": "2.0.0"}}, "ms": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/ms/-/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g=", "dev": true}}}, "spdy-transport": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/spdy-transport/-/spdy-transport-2.1.0.tgz", "integrity": "sha512-bpUeGpZcmZ692rrTiqf9/2EUakI6/kXX1Rpe0ib/DyOzbiexVfXkw6GnvI9hVGvIwVaUhkaBojjCZwLNRGQg1g==", "dev": true, "requires": {"debug": "^2.6.8", "detect-node": "^2.0.3", "hpack.js": "^2.1.6", "obuf": "^1.1.1", "readable-stream": "^2.2.9", "safe-buffer": "^5.0.1", "wbuf": "^1.7.2"}, "dependencies": {"debug": {"version": "2.6.9", "resolved": "https://registry.npmjs.org/debug/-/debug-2.6.9.tgz", "integrity": "sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA==", "dev": true, "requires": {"ms": "2.0.0"}}, "ms": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/ms/-/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g=", "dev": true}}}, "split-on-first": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/split-on-first/-/split-on-first-1.1.0.tgz", "integrity": "sha512-43ZssAJaMusuKWL8sKUBQXHWOpq8d6CfN/u1p4gUzfJkM05C8rxTmYrkIPTXapZpORA6LkkzcUulJ8FqA7Uudw==", "dev": true}, "split-string": {"version": "3.1.0", "resolved": "https://registry.npmjs.org/split-string/-/split-string-3.1.0.tgz", "integrity": "sha512-NzNVhJDYpwceVVii8/Hu6DKfD2G+NrQHlS/V/qgv763EYudVwEcMQNxd2lh+0VrUByXN/oJkl5grOhYWvQUYiw==", "dev": true, "requires": {"extend-shallow": "^3.0.0"}}, "split2": {"version": "2.2.0", "resolved": "http://r.npm.sankuai.com/split2/download/split2-2.2.0.tgz", "integrity": "sha1-GGsldbz4PoW30YRldWI47k7kJJM=", "dev": true, "requires": {"through2": "^2.0.2"}}, "sprintf-js": {"version": "1.0.3", "resolved": "http://r.npm.sankuai.com/sprintf-js/download/sprintf-js-1.0.3.tgz", "integrity": "sha1-BOaSb2YolTVPPdAVIDYzuFcpfiw=", "dev": true}, "sshpk": {"version": "1.16.1", "resolved": "https://registry.npm.taobao.org/sshpk/download/sshpk-1.16.1.tgz", "integrity": "sha1-+2YcC+8ps520B2nuOfpwCT1vaHc=", "requires": {"asn1": "~0.2.3", "assert-plus": "^1.0.0", "bcrypt-pbkdf": "^1.0.0", "dashdash": "^1.12.0", "ecc-jsbn": "~0.1.1", "getpass": "^0.1.1", "jsbn": "~0.1.0", "safer-buffer": "^2.0.2", "tweetnacl": "~0.14.0"}}, "ssri": {"version": "5.3.0", "resolved": "https://registry.npmjs.org/ssri/-/ssri-5.3.0.tgz", "integrity": "sha512-XRSIPqLij52MtgoQavH/x/dU1qVKtWUAAZeOHsR9c2Ddi4XerFy3mc1alf+dLJKl9EUIm/Ht+EowFkTUOA6GAQ==", "dev": true, "requires": {"safe-buffer": "^5.1.1"}}, "stable": {"version": "0.1.8", "resolved": "https://registry.npmjs.org/stable/-/stable-0.1.8.tgz", "integrity": "sha512-ji9qxRnOVfcuLDySj9qzhGSEFVobyt1kIOSkj1qZzYLzq7Tos/oUUWvotUPQLlrsidqsK6tBH89Bc9kL5zHA6w==", "dev": true}, "stackframe": {"version": "1.0.4", "resolved": "https://registry.npmjs.org/stackframe/-/stackframe-1.0.4.tgz", "integrity": "sha512-to7oADIniaYwS3MhtCa/sQhrxidCCQiF/qp4/m5iN3ipf0Y7Xlri0f6eG29r08aL7JYl8n32AF3Q5GYBZ7K8vw==", "dev": true}, "static-extend": {"version": "0.1.2", "resolved": "https://registry.npmjs.org/static-extend/-/static-extend-0.1.2.tgz", "integrity": "sha1-YICcOcv/VTNyJv1eC1IPNB8ftcY=", "dev": true, "requires": {"define-property": "^0.2.5", "object-copy": "^0.1.0"}, "dependencies": {"define-property": {"version": "0.2.5", "resolved": "https://registry.npmjs.org/define-property/-/define-property-0.2.5.tgz", "integrity": "sha1-w1se+RjsPJkPmlvFe+BKrOxcgRY=", "dev": true, "requires": {"is-descriptor": "^0.1.0"}}}}, "statuses": {"version": "1.4.0", "resolved": "https://registry.npmjs.org/statuses/-/statuses-1.4.0.tgz", "integrity": "sha512-zhSCtt8v2NDrRlPQpCNtw/heZLtfUDqxBM1udqikb/Hbk52LK4nQSwr10u77iopCW5LsyHpuXS0GnEc48mLeew==", "dev": true}, "stdout-stream": {"version": "1.4.1", "resolved": "https://registry.npm.taobao.org/stdout-stream/download/stdout-stream-1.4.1.tgz", "integrity": "sha1-WsF0zdXNcmEEqgwLK9g4FdjVNd4=", "dev": true, "requires": {"readable-stream": "^2.0.1"}}, "stream-browserify": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/stream-browserify/-/stream-browserify-2.0.1.tgz", "integrity": "sha1-ZiZu5fm9uZQKTkUUyvtDu3Hlyds=", "dev": true, "requires": {"inherits": "~2.0.1", "readable-stream": "^2.0.2"}}, "stream-each": {"version": "1.2.3", "resolved": "https://registry.npmjs.org/stream-each/-/stream-each-1.2.3.tgz", "integrity": "sha512-vlMC2f8I2u/bZGqkdfLQW/13Zihpej/7PmSiMQsbYddxuTsJp8vRe2x2FvVExZg7FaOds43ROAuFJwPR4MTZLw==", "dev": true, "requires": {"end-of-stream": "^1.1.0", "stream-shift": "^1.0.0"}}, "stream-http": {"version": "2.8.3", "resolved": "https://registry.npmjs.org/stream-http/-/stream-http-2.8.3.tgz", "integrity": "sha512-+TSkfINHDo4J+ZobQLWiMouQYB+UVYFttRA94FpEzzJ7ZdqcL4uUUQ7WkdkI4DSozGmgBUE/a47L+38PenXhUw==", "dev": true, "requires": {"builtin-status-codes": "^3.0.0", "inherits": "^2.0.1", "readable-stream": "^2.3.6", "to-arraybuffer": "^1.0.0", "xtend": "^4.0.0"}}, "stream-shift": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/stream-shift/-/stream-shift-1.0.0.tgz", "integrity": "sha1-1cdSgl5TZ+eG944Y5EXqIjoVWVI=", "dev": true}, "strict-uri-encode": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/strict-uri-encode/-/strict-uri-encode-2.0.0.tgz", "integrity": "sha512-QwiXZgpRcKkhTj2Scnn++4PKtWsH0kpzZ62L2R6c/LUVYv7hVnZqcg2+sMuT6R7Jusu1vviK/MFsu6kNJfWlEQ==", "dev": true}, "string-width": {"version": "2.1.1", "resolved": "http://r.npm.sankuai.com/string-width/download/string-width-2.1.1.tgz", "integrity": "sha1-q5Pyeo3BPSjKyBXEYhQ6bZASrp4=", "dev": true, "requires": {"is-fullwidth-code-point": "^2.0.0", "strip-ansi": "^4.0.0"}}, "string.prototype.codepointat": {"version": "0.2.1", "resolved": "http://r.npm.sankuai.com/string.prototype.codepointat/download/string.prototype.codepointat-0.2.1.tgz", "integrity": "sha1-AErUTIr8cnUnsQjNRitNlxzUabw="}, "string_decoder": {"version": "1.1.1", "resolved": "http://r.npm.sankuai.com/string_decoder/download/string_decoder-1.1.1.tgz", "integrity": "sha1-nPFhG6YmhdcDCunkujQUnDrwP8g=", "requires": {"safe-buffer": "~5.1.0"}}, "strip-ansi": {"version": "4.0.0", "resolved": "http://r.npm.sankuai.com/strip-ansi/download/strip-ansi-4.0.0.tgz", "integrity": "sha1-qEeQIusaw2iocTibY1JixQXuNo8=", "dev": true, "requires": {"ansi-regex": "^3.0.0"}}, "strip-bom": {"version": "3.0.0", "resolved": "http://r.npm.sankuai.com/strip-bom/download/strip-bom-3.0.0.tgz", "integrity": "sha1-IzTBjpx1n3vdVv3vfprj1YjmjtM=", "dev": true}, "strip-eof": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/strip-eof/-/strip-eof-1.0.0.tgz", "integrity": "sha1-u0P/VZim6wXYm1n80SnJgzE2Br8=", "dev": true}, "strip-indent": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/strip-indent/-/strip-indent-1.0.1.tgz", "integrity": "sha1-DHlipq3vp7vUrDZkYKY4VSrhoKI=", "dev": true, "requires": {"get-stdin": "^4.0.1"}}, "strip-json-comments": {"version": "2.0.1", "resolved": "http://r.npm.sankuai.com/strip-json-comments/download/strip-json-comments-2.0.1.tgz", "integrity": "sha1-PFMZQukIwml8DsNEhYwobHygpgo=", "dev": true}, "style-loader": {"version": "0.23.0", "resolved": "https://registry.npmjs.org/style-loader/-/style-loader-0.23.0.tgz", "integrity": "sha512-uCcN7XWHkqwGVt7skpInW6IGO1tG6ReyFQ1Cseh0VcN6VdcFQi62aG/2F3Y9ueA8x4IVlfaSUxpmQXQD9QrEuQ==", "dev": true, "requires": {"loader-utils": "^1.1.0", "schema-utils": "^0.4.5"}}, "stylehacks": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/stylehacks/-/stylehacks-4.0.0.tgz", "integrity": "sha1-ZLMjlRxKJOX8ey7AbBN78y0VXoo=", "dev": true, "requires": {"browserslist": "^4.0.0", "postcss": "^6.0.0", "postcss-selector-parser": "^3.0.0"}}, "supports-color": {"version": "5.5.0", "resolved": "http://r.npm.sankuai.com/supports-color/download/supports-color-5.5.0.tgz", "integrity": "sha1-4uaaRKyHcveKHsCzW2id9lMO/I8=", "dev": true, "requires": {"has-flag": "^3.0.0"}}, "supports-preserve-symlinks-flag": {"version": "1.0.0", "resolved": "http://r.npm.sankuai.com/supports-preserve-symlinks-flag/download/supports-preserve-symlinks-flag-1.0.0.tgz", "integrity": "sha1-btpL00SjyUrqN21MwxvHcxEDngk="}, "svelte-adapter": {"version": "0.4.0", "resolved": "http://r.npm.sankuai.com/svelte-adapter/download/svelte-adapter-0.4.0.tgz", "integrity": "sha1-mo+WmDZbPpMowc13qlU+rScCSjs="}, "svgo": {"version": "1.0.5", "resolved": "https://registry.npmjs.org/svgo/-/svgo-1.0.5.tgz", "integrity": "sha512-nYrifviB77aNKDNKKyuay3M9aYiK6Hv5gJVDdjj2ZXTQmI8WZc8+UPLR5IpVlktJfSu3co/4XcWgrgI6seGBPg==", "dev": true, "requires": {"coa": "~2.0.1", "colors": "~1.1.2", "css-select": "~1.3.0-rc0", "css-select-base-adapter": "~0.1.0", "css-tree": "1.0.0-alpha25", "css-url-regex": "^1.1.0", "csso": "^3.5.0", "js-yaml": "~3.10.0", "mkdirp": "~0.5.1", "object.values": "^1.0.4", "sax": "~1.2.4", "stable": "~0.1.6", "unquote": "~1.1.1", "util.promisify": "~1.0.0"}, "dependencies": {"css-select": {"version": "1.3.0-rc0", "resolved": "https://registry.npmjs.org/css-select/-/css-select-1.3.0-rc0.tgz", "integrity": "sha1-b5MZaqrnN2ZuoQNqjLFKj8t6kjE=", "dev": true, "requires": {"boolbase": "^1.0.0", "css-what": "2.1", "domutils": "1.5.1", "nth-check": "^1.0.1"}}, "domutils": {"version": "1.5.1", "resolved": "https://registry.npmjs.org/domutils/-/domutils-1.5.1.tgz", "integrity": "sha1-3NhIiib1Y9YQeeSMn3t+Mjc2gs8=", "dev": true, "requires": {"dom-serializer": "0", "domelementtype": "1"}}, "js-yaml": {"version": "3.10.0", "resolved": "https://registry.npmjs.org/js-yaml/-/js-yaml-3.10.0.tgz", "integrity": "sha512-O2v52ffjLa9VeM43J4XocZE//WT9N0IiwDa3KSHH7Tu8CtH+1qM8SIZvnsTh6v+4yFy5KUY3BHUVwjpfAWsjIA==", "dev": true, "requires": {"argparse": "^1.0.7", "esprima": "^4.0.0"}}, "sax": {"version": "1.2.4", "resolved": "https://registry.npmjs.org/sax/-/sax-1.2.4.tgz", "integrity": "sha512-NqVDv9TpANUjFm0N8uM5GxL36UgKi9/atZw+x7YFnQ8ckwFGKrl4xX4yWtrey3UJm5nP1kUbnYgLopqWNSRhWw==", "dev": true}}}, "table": {"version": "4.0.3", "resolved": "http://r.npm.sankuai.com/table/download/table-4.0.3.tgz", "integrity": "sha1-ALXitgLxeUuayvnKkIp2OGp4E7w=", "dev": true, "requires": {"ajv": "^6.0.1", "ajv-keywords": "^3.0.0", "chalk": "^2.1.0", "lodash": "^4.17.4", "slice-ansi": "1.0.0", "string-width": "^2.1.1"}}, "tapable": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/tapable/-/tapable-1.1.0.tgz", "integrity": "sha512-IlqtmLVaZA2qab8epUXbVWRn3aB1imbDMJtjB3nu4X0NqPkcY/JH9ZtCBWKHWPxs8Svi9tyo8w2dBoi07qZbBA==", "dev": true}, "tar": {"version": "2.2.2", "resolved": "https://registry.npm.taobao.org/tar/download/tar-2.2.2.tgz?cache=0&sync_timestamp=1597445446483&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Ftar%2Fdownload%2Ftar-2.2.2.tgz", "integrity": "sha1-DKiEhWLHKZuLRG/2pNYM27I+3EA=", "dev": true, "requires": {"block-stream": "*", "fstream": "^1.0.12", "inherits": "2"}}, "text-extensions": {"version": "1.9.0", "resolved": "http://r.npm.sankuai.com/text-extensions/download/text-extensions-1.9.0.tgz", "integrity": "sha1-GFPkX+45yUXOb2w2stZZtaq8KiY=", "dev": true}, "text-table": {"version": "0.2.0", "resolved": "http://r.npm.sankuai.com/text-table/download/text-table-0.2.0.tgz", "integrity": "sha1-f17oI66AUgfACvLfSoTsP8+lcLQ=", "dev": true}, "throttle-debounce": {"version": "1.1.0", "resolved": "http://r.npm.sankuai.com/throttle-debounce/download/throttle-debounce-1.1.0.tgz", "integrity": "sha1-UYU9o3vmihVctugns1FKPEIuic0="}, "through": {"version": "2.3.8", "resolved": "http://r.npm.sankuai.com/through/download/through-2.3.8.tgz", "integrity": "sha1-DdTJ/6q8NXlgsbckEV1+Doai4fU="}, "through2": {"version": "2.0.3", "resolved": "https://registry.npmjs.org/through2/-/through2-2.0.3.tgz", "integrity": "sha1-AARWmzfHx0ujnEPzzteNGtlBQL4=", "dev": true, "requires": {"readable-stream": "^2.1.5", "xtend": "~4.0.1"}}, "thunky": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/thunky/-/thunky-1.0.2.tgz", "integrity": "sha1-qGLgGOP7HqLsP85dVWBc9X8kc3E=", "dev": true}, "timers-browserify": {"version": "2.0.10", "resolved": "https://registry.npmjs.org/timers-browserify/-/timers-browserify-2.0.10.tgz", "integrity": "sha512-YvC1SV1XdOUaL6gx5CoGroT3Gu49pK9+TZ38ErPldOWW4j49GI1HKs9DV+KGq/w6y+LZ72W1c8cKz2vzY+qpzg==", "dev": true, "requires": {"setimmediate": "^1.0.4"}}, "timsort": {"version": "0.3.0", "resolved": "https://registry.npmjs.org/timsort/-/timsort-0.3.0.tgz", "integrity": "sha1-QFQRqOfmM5/mTbmiNN4R3DHgK9Q=", "dev": true}, "tinycolor2": {"version": "1.6.0", "resolved": "http://r.npm.sankuai.com/tinycolor2/download/tinycolor2-1.6.0.tgz", "integrity": "sha1-+YAHRgFpsCY7lwcsWukkhM4C0J4="}, "tmp": {"version": "0.0.33", "resolved": "http://r.npm.sankuai.com/tmp/download/tmp-0.0.33.tgz", "integrity": "sha1-bTQzWIl2jSGyvNoKonfO07G/rfk=", "requires": {"os-tmpdir": "~1.0.2"}}, "to-arraybuffer": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/to-arraybuffer/-/to-arraybuffer-1.0.1.tgz", "integrity": "sha1-fSKbH8xjfkZsoIEYCDanqr/4P0M=", "dev": true}, "to-fast-properties": {"version": "2.0.0", "resolved": "http://r.npm.sankuai.com/to-fast-properties/download/to-fast-properties-2.0.0.tgz", "integrity": "sha1-3F5pjL0HkmW8c+A3doGk5Og/YW4=", "dev": true}, "to-object-path": {"version": "0.3.0", "resolved": "https://registry.npmjs.org/to-object-path/-/to-object-path-0.3.0.tgz", "integrity": "sha1-KXWIt7Dn4KwI4E5nL4XB9JmeF68=", "dev": true, "requires": {"kind-of": "^3.0.2"}, "dependencies": {"kind-of": {"version": "3.2.2", "resolved": "https://registry.npmjs.org/kind-of/-/kind-of-3.2.2.tgz", "integrity": "sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ=", "dev": true, "requires": {"is-buffer": "^1.1.5"}}}}, "to-regex": {"version": "3.0.2", "resolved": "https://registry.npmjs.org/to-regex/-/to-regex-3.0.2.tgz", "integrity": "sha512-FWtleNAtZ/Ki2qtqej2CXTOayOH9bHDQF+Q48VpWyDXjbYxA4Yz8iDB31zXOBUlOHHKidDbqGVrTUvQMPmBGBw==", "dev": true, "requires": {"define-property": "^2.0.2", "extend-shallow": "^3.0.2", "regex-not": "^1.0.2", "safe-regex": "^1.1.0"}}, "to-regex-range": {"version": "2.1.1", "resolved": "https://registry.npmjs.org/to-regex-range/-/to-regex-range-2.1.1.tgz", "integrity": "sha1-fIDBe53+vlmeJzZ+DU3VWQFB2zg=", "dev": true, "requires": {"is-number": "^3.0.0", "repeat-string": "^1.6.1"}}, "toidentifier": {"version": "1.0.1", "resolved": "http://r.npm.sankuai.com/toidentifier/download/toidentifier-1.0.1.tgz", "integrity": "sha1-O+NDIaiKgg7RvYDfqjPkefu43TU="}, "tough-cookie": {"version": "2.5.0", "resolved": "https://registry.npm.taobao.org/tough-cookie/download/tough-cookie-2.5.0.tgz", "integrity": "sha1-zZ+yoKodWhK0c72fuW+j3P9lreI=", "requires": {"psl": "^1.1.28", "punycode": "^2.1.1"}}, "trim-newlines": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/trim-newlines/-/trim-newlines-1.0.0.tgz", "integrity": "sha1-WIeWa7WCpFA6QetST301ARgVphM=", "dev": true}, "trim-off-newlines": {"version": "1.0.1", "resolved": "http://r.npm.sankuai.com/trim-off-newlines/download/trim-off-newlines-1.0.1.tgz", "integrity": "sha1-n5up2e+odkw4dpi8v+sshI8RrbM=", "dev": true}, "trim-right": {"version": "1.0.1", "resolved": "http://r.npm.sankuai.com/trim-right/download/trim-right-1.0.1.tgz", "integrity": "sha1-yy4SAwZ+DI3h9hQJS5/kVwTqYAM=", "dev": true}, "true-case-path": {"version": "1.0.3", "resolved": "https://registry.npm.taobao.org/true-case-path/download/true-case-path-1.0.3.tgz", "integrity": "sha1-+BO1qMhrQNpZYGcisUTjIleZ9H0=", "dev": true, "requires": {"glob": "^7.1.2"}}, "tslib": {"version": "1.9.3", "resolved": "http://r.npm.sankuai.com/tslib/download/tslib-1.9.3.tgz", "integrity": "sha1-1+TdeSRdhUKMTX5IIqeZF5VMooY="}, "tty-browserify": {"version": "0.0.0", "resolved": "https://registry.npmjs.org/tty-browserify/-/tty-browserify-0.0.0.tgz", "integrity": "sha1-oVe6QC2iTpv5V/mqadUk7tQpAaY=", "dev": true}, "tunnel-agent": {"version": "0.6.0", "resolved": "https://registry.npmjs.org/tunnel-agent/-/tunnel-agent-0.6.0.tgz", "integrity": "sha1-J6XeoGs2sEoKmWZ3SykIaPD8QP0=", "requires": {"safe-buffer": "^5.0.1"}}, "tweetnacl": {"version": "0.14.5", "resolved": "https://registry.npmjs.org/tweetnacl/-/tweetnacl-0.14.5.tgz", "integrity": "sha1-WuaBd/GS1EViadEIr6k/+HQ/T2Q="}, "type-check": {"version": "0.3.2", "resolved": "http://r.npm.sankuai.com/type-check/download/type-check-0.3.2.tgz", "integrity": "sha1-WITKtRLPHTVeP7eE8wgEsrUg23I=", "dev": true, "requires": {"prelude-ls": "~1.1.2"}}, "type-fest": {"version": "0.6.0", "resolved": "http://r.npm.sankuai.com/type-fest/download/type-fest-0.6.0.tgz", "integrity": "sha1-jSojcNPfiG61yQraHFv2GIrPg4s=", "dev": true}, "type-is": {"version": "1.6.18", "resolved": "http://r.npm.sankuai.com/type-is/download/type-is-1.6.18.tgz", "integrity": "sha1-TlUs0F3wlGfcvE73Od6J8s83wTE=", "requires": {"media-typer": "0.3.0", "mime-types": "~2.1.24"}, "dependencies": {"mime-db": {"version": "1.52.0", "resolved": "http://r.npm.sankuai.com/mime-db/download/mime-db-1.52.0.tgz", "integrity": "sha1-u6vNwChZ9JhzAchW4zh85exDv3A="}, "mime-types": {"version": "2.1.35", "resolved": "http://r.npm.sankuai.com/mime-types/download/mime-types-2.1.35.tgz", "integrity": "sha1-OBqHG2KnNEUGYK497uRIE/cNlZo=", "requires": {"mime-db": "1.52.0"}}}}, "typedarray": {"version": "0.0.6", "resolved": "https://registry.npmjs.org/typedarray/-/typedarray-0.0.6.tgz", "integrity": "sha1-hnrHTjhkGHsdPUfZlqeOxciDB3c=", "dev": true}, "uglify-es": {"version": "3.3.9", "resolved": "https://registry.npmjs.org/uglify-es/-/uglify-es-3.3.9.tgz", "integrity": "sha512-r+MU0rfv4L/0eeW3xZrd16t4NZfK8Ld4SWVglYBb7ez5uXFWHuVRs6xCTrf1yirs9a4j4Y27nn7SRfO6v67XsQ==", "dev": true, "requires": {"commander": "~2.13.0", "source-map": "~0.6.1"}, "dependencies": {"source-map": {"version": "0.6.1", "resolved": "https://registry.npmjs.org/source-map/-/source-map-0.6.1.tgz", "integrity": "sha512-UjgapumWlbMhkBgzT7Ykc5YXUT46F0iKu8SGXq0bcwP5dz/h0Plj6enJqjz1Zbq2l5WaqYnrVbwWOWMyF3F47g==", "dev": true}}}, "uglifyjs-webpack-plugin": {"version": "1.3.0", "resolved": "https://registry.npmjs.org/uglifyjs-webpack-plugin/-/uglifyjs-webpack-plugin-1.3.0.tgz", "integrity": "sha512-ovHIch0AMlxjD/97j9AYovZxG5wnHOPkL7T1GKochBADp/Zwc44pEWNqpKl1Loupp1WhFg7SlYmHZRUfdAacgw==", "dev": true, "requires": {"cacache": "^10.0.4", "find-cache-dir": "^1.0.0", "schema-utils": "^0.4.5", "serialize-javascript": "^1.4.0", "source-map": "^0.6.1", "uglify-es": "^3.3.4", "webpack-sources": "^1.1.0", "worker-farm": "^1.5.2"}, "dependencies": {"source-map": {"version": "0.6.1", "resolved": "https://registry.npmjs.org/source-map/-/source-map-0.6.1.tgz", "integrity": "sha512-UjgapumWlbMhkBgzT7Ykc5YXUT46F0iKu8SGXq0bcwP5dz/h0Plj6enJqjz1Zbq2l5WaqYnrVbwWOWMyF3F47g==", "dev": true}}}, "unicode-canonical-property-names-ecmascript": {"version": "1.0.4", "resolved": "https://registry.npmjs.org/unicode-canonical-property-names-ecmascript/-/unicode-canonical-property-names-ecmascript-1.0.4.tgz", "integrity": "sha512-jDrNnXWHd4oHiTZnx/ZG7gtUTVp+gCcTTKr8L0HjlwphROEW3+Him+IpvC+xcJEFegapiMZyZe02CyuOnRmbnQ==", "dev": true}, "unicode-match-property-ecmascript": {"version": "1.0.4", "resolved": "https://registry.npmjs.org/unicode-match-property-ecmascript/-/unicode-match-property-ecmascript-1.0.4.tgz", "integrity": "sha512-L4Qoh15vTfntsn4P1zqnHulG0LdXgjSO035fEpdtp6YxXhMT51Q6vgM5lYdG/5X3MjS+k/Y9Xw4SFCY9IkR0rg==", "dev": true, "requires": {"unicode-canonical-property-names-ecmascript": "^1.0.4", "unicode-property-aliases-ecmascript": "^1.0.4"}}, "unicode-match-property-value-ecmascript": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/unicode-match-property-value-ecmascript/-/unicode-match-property-value-ecmascript-1.0.2.tgz", "integrity": "sha512-Rx7yODZC1L/T8XKo/2kNzVAQaRE88AaMvI1EF/Xnj3GW2wzN6fop9DDWuFAKUVFH7vozkz26DzP0qyWLKLIVPQ==", "dev": true}, "unicode-property-aliases-ecmascript": {"version": "1.0.4", "resolved": "https://registry.npmjs.org/unicode-property-aliases-ecmascript/-/unicode-property-aliases-ecmascript-1.0.4.tgz", "integrity": "sha512-2WSLa6OdYd2ng8oqiGIWnJqyFArvhn+5vgx5GTxMbUYjCYKUcuKS62YLFF0R/BDGlB1yzXjQOLtPAfHsgirEpg==", "dev": true}, "union-value": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/union-value/-/union-value-1.0.0.tgz", "integrity": "sha1-XHHDTLW61dzr4+oM0IIHulqhrqQ=", "dev": true, "requires": {"arr-union": "^3.1.0", "get-value": "^2.0.6", "is-extendable": "^0.1.1", "set-value": "^0.4.3"}, "dependencies": {"extend-shallow": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/extend-shallow/-/extend-shallow-2.0.1.tgz", "integrity": "sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8=", "dev": true, "requires": {"is-extendable": "^0.1.0"}}, "set-value": {"version": "0.4.3", "resolved": "https://registry.npmjs.org/set-value/-/set-value-0.4.3.tgz", "integrity": "sha1-fbCPnT0i3H945Trzw79GZuzfzPE=", "dev": true, "requires": {"extend-shallow": "^2.0.1", "is-extendable": "^0.1.1", "is-plain-object": "^2.0.1", "to-object-path": "^0.3.0"}}}}, "uniq": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/uniq/-/uniq-1.0.1.tgz", "integrity": "sha1-sxxa6CVIRKOoKBVBzisEuGWnNP8=", "dev": true}, "uniqs": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/uniqs/-/uniqs-2.0.0.tgz", "integrity": "sha1-/+3ks2slKQaW5uFl1KWe25mOawI=", "dev": true}, "unique-filename": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/unique-filename/-/unique-filename-1.1.0.tgz", "integrity": "sha1-0F8v5AMlYIcfMOk8vnNe6iAVFPM=", "dev": true, "requires": {"unique-slug": "^2.0.0"}}, "unique-slug": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/unique-slug/-/unique-slug-2.0.0.tgz", "integrity": "sha1-22Z258fMBimHj/GWCXx4hVrp9Ks=", "dev": true, "requires": {"imurmurhash": "^0.1.4"}}, "unpipe": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/unpipe/-/unpipe-1.0.0.tgz", "integrity": "sha1-sr9O6FFKrmFltIF4KdIbLvSZBOw="}, "unquote": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/unquote/-/unquote-1.1.1.tgz", "integrity": "sha1-j97XMk7G6IoP+LkF58CYzcCG1UQ=", "dev": true}, "unset-value": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/unset-value/-/unset-value-1.0.0.tgz", "integrity": "sha1-g3aHP30jNRef+x5vw6jtDfyKtVk=", "dev": true, "requires": {"has-value": "^0.3.1", "isobject": "^3.0.0"}, "dependencies": {"has-value": {"version": "0.3.1", "resolved": "https://registry.npmjs.org/has-value/-/has-value-0.3.1.tgz", "integrity": "sha1-ex9YutpiyoJ+wKIHgCVlSEWZXh8=", "dev": true, "requires": {"get-value": "^2.0.3", "has-values": "^0.1.4", "isobject": "^2.0.0"}, "dependencies": {"isobject": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/isobject/-/isobject-2.1.0.tgz", "integrity": "sha1-8GVWEJaj8dou9GJy+BXIQNh+DIk=", "dev": true, "requires": {"isarray": "1.0.0"}}}}, "has-values": {"version": "0.1.4", "resolved": "https://registry.npmjs.org/has-values/-/has-values-0.1.4.tgz", "integrity": "sha1-bWHeldkd/Km5oCCJrThL/49it3E=", "dev": true}}}, "upath": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/upath/-/upath-1.1.0.tgz", "integrity": "sha512-bzpH/oBhoS/QI/YtbkqCg6VEiPYjSZtrHQM6/QnJS6OL9pKUFLqb3aFh4Scvwm45+7iAgiMkLhSbaZxUqmrprw==", "dev": true}, "upper-case": {"version": "1.1.3", "resolved": "https://registry.npmjs.org/upper-case/-/upper-case-1.1.3.tgz", "integrity": "sha1-9rRQHC7EzdJrp4vnIilh3ndiFZg=", "dev": true}, "uri-js": {"version": "4.2.2", "resolved": "http://r.npm.sankuai.com/uri-js/download/uri-js-4.2.2.tgz", "integrity": "sha1-lMVA4f93KVbiKZUHwBCupsiDjrA=", "requires": {"punycode": "^2.1.0"}}, "urijs": {"version": "1.19.9", "resolved": "http://r.npm.sankuai.com/urijs/download/urijs-1.19.9.tgz", "integrity": "sha1-OxWESDXeNzKGbUIHaMFvJL59PmU="}, "urix": {"version": "0.1.0", "resolved": "https://registry.npmjs.org/urix/-/urix-0.1.0.tgz", "integrity": "sha1-2pN/emLiH+wf0Y1Js1wpNQZ6bHI=", "dev": true}, "url": {"version": "0.11.0", "resolved": "https://registry.npmjs.org/url/-/url-0.11.0.tgz", "integrity": "sha1-ODjpfPxgUh63PFJajlW/3Z4uKPE=", "dev": true, "requires": {"punycode": "1.3.2", "querystring": "0.2.0"}, "dependencies": {"punycode": {"version": "1.3.2", "resolved": "https://registry.npmjs.org/punycode/-/punycode-1.3.2.tgz", "integrity": "sha1-llOgNvt8HuQjQvIyXM7v6jkmxI0=", "dev": true}}}, "url-join": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/url-join/-/url-join-4.0.0.tgz", "integrity": "sha1-TTNA6AfTdzvamZH4MFrNzCpmXSo=", "dev": true}, "url-parse": {"version": "1.4.3", "resolved": "https://registry.npmjs.org/url-parse/-/url-parse-1.4.3.tgz", "integrity": "sha512-rh+KuAW36YKo0vClhQzLLveoj8FwPJNu65xLb7Mrt+eZht0IPT0IXgSv8gcMegZ6NvjJUALf6Mf25POlMwD1Fw==", "dev": true, "requires": {"querystringify": "^2.0.0", "requires-port": "^1.0.0"}}, "use": {"version": "3.1.1", "resolved": "https://registry.npmjs.org/use/-/use-3.1.1.tgz", "integrity": "sha512-cwESVXlO3url9YWlFW/TA9cshCEhtu7IKJ/p5soJ/gGpj7vbvFrAY/eIioQ6Dw23KjZhYgiIo8HOs1nQ2vr/oQ==", "dev": true}, "util": {"version": "0.10.4", "resolved": "https://registry.npmjs.org/util/-/util-0.10.4.tgz", "integrity": "sha512-0Pm9hTQ3se5ll1XihRic3FDIku70C+iHUdT/W926rSgHV5QgXsYbKZN8MSC3tJtSkhuROzvsQjAaFENRXr+19A==", "dev": true, "requires": {"inherits": "2.0.3"}}, "util-deprecate": {"version": "1.0.2", "resolved": "http://r.npm.sankuai.com/util-deprecate/download/util-deprecate-1.0.2.tgz", "integrity": "sha1-RQ1Nyfpw3nMnYvvS1KKJgUGaDM8="}, "util.promisify": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/util.promisify/-/util.promisify-1.0.0.tgz", "integrity": "sha512-i+6qA2MPhvoKLuxnJNpXAGhg7HphQOSUq2LKMZD0m15EiskXUkMvKdF4Uui0WYeCUGea+o2cw/ZuwehtfsrNkA==", "dev": true, "requires": {"define-properties": "^1.1.2", "object.getownpropertydescriptors": "^2.0.3"}}, "utila": {"version": "0.4.0", "resolved": "https://registry.npmjs.org/utila/-/utila-0.4.0.tgz", "integrity": "sha1-ihagXURWV6Oupe7MWxKk+lN5dyw=", "dev": true}, "utils-merge": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/utils-merge/-/utils-merge-1.0.1.tgz", "integrity": "sha1-n5VxD1CiZ5R7LMwSR0HBAoQn5xM="}, "uuid": {"version": "3.3.2", "resolved": "https://registry.npmjs.org/uuid/-/uuid-3.3.2.tgz", "integrity": "sha512-yXJmeNaw3DnnKAOKJE51sL/ZaYfWJRl1pK9dr19YFCu0ObS231AB1/LbqTKRAQ5kw8A90rA6fr4riOUpTZvQZA=="}, "v-click-outside-x": {"version": "4.1.3", "resolved": "http://r.npm.sankuai.com/v-click-outside-x/download/v-click-outside-x-4.1.3.tgz", "integrity": "sha1-R5J5K6gJMMpy0Duxmgwr6p0966s="}, "v8-compile-cache": {"version": "2.0.2", "resolved": "https://registry.npmjs.org/v8-compile-cache/-/v8-compile-cache-2.0.2.tgz", "integrity": "sha512-1wFuMUIM16MDJRCrpbpuEPTUGmM5QMUg0cr3KFwra2XgOgFcPGDQHDh3CszSCD2Zewc/dh/pamNEW8CbfDebUw==", "dev": true}, "valid-url": {"version": "1.0.9", "resolved": "http://r.npm.sankuai.com/valid-url/download/valid-url-1.0.9.tgz", "integrity": "sha1-HBRHm0DxOXp1eC8RXkCGRHQzogA=", "dev": true}, "validate-git-branch": {"version": "0.0.6", "resolved": "http://r.npm.sankuai.com/validate-git-branch/download/validate-git-branch-0.0.6.tgz", "integrity": "sha1-5ipBw15bcguA94lhiqb8+3X6zfE=", "dev": true, "requires": {"chalk": "^2.4.1"}}, "validate-npm-package-license": {"version": "3.0.4", "resolved": "http://r.npm.sankuai.com/validate-npm-package-license/download/validate-npm-package-license-3.0.4.tgz", "integrity": "sha1-/JH2uce6FchX9MssXe/uw51PQQo=", "dev": true, "requires": {"spdx-correct": "^3.0.0", "spdx-expression-parse": "^3.0.0"}}, "vary": {"version": "1.1.2", "resolved": "https://registry.npmjs.org/vary/-/vary-1.1.2.tgz", "integrity": "sha1-IpnwLG3tMNSllhsLn3RSShj2NPw="}, "vendors": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/vendors/-/vendors-1.0.2.tgz", "integrity": "sha512-w/hry/368nO21AN9QljsaIhb9ZiZtZARoVH5f3CsFbawdLdayCgKRPup7CggujvySMxx0I91NOyxdVENohprLQ==", "dev": true}, "verror": {"version": "1.10.0", "resolved": "https://registry.npmjs.org/verror/-/verror-1.10.0.tgz", "integrity": "sha1-OhBcoXBTr1XW4nDB+CiGguGNpAA=", "requires": {"assert-plus": "^1.0.0", "core-util-is": "1.0.2", "extsprintf": "^1.2.0"}}, "vm-browserify": {"version": "0.0.4", "resolved": "https://registry.npmjs.org/vm-browserify/-/vm-browserify-0.0.4.tgz", "integrity": "sha1-XX6kW7755Kb/ZflUOOCofDV9WnM=", "dev": true, "requires": {"indexof": "0.0.1"}}, "vue": {"version": "2.5.17", "resolved": "https://registry.npmjs.org/vue/-/vue-2.5.17.tgz", "integrity": "sha512-mFbcWoDIJi0w0Za4emyLiW72Jae0yjANHbCVquMKijcavBGypqlF7zHRgMa5k4sesdv7hv2rB4JPdZfR+TPfhQ=="}, "vue-eslint-parser": {"version": "7.3.0", "resolved": "http://r.npm.sankuai.com/vue-eslint-parser/download/vue-eslint-parser-7.3.0.tgz", "integrity": "sha1-iUCFg52Z2BKW+ggdGWQ3M/I9dVk=", "requires": {"debug": "^4.1.1", "eslint-scope": "^5.0.0", "eslint-visitor-keys": "^1.1.0", "espree": "^6.2.1", "esquery": "^1.0.1", "lodash": "^4.17.15"}, "dependencies": {"acorn": {"version": "7.4.1", "resolved": "http://r.npm.sankuai.com/acorn/download/acorn-7.4.1.tgz", "integrity": "sha1-/q7SVZc9LndVW4PbwIhRpsY1IPo="}, "acorn-jsx": {"version": "5.3.1", "resolved": "http://r.npm.sankuai.com/acorn-jsx/download/acorn-jsx-5.3.1.tgz", "integrity": "sha1-/IZh4Rt6wVOcR9v+oucrOvNNJns="}, "debug": {"version": "4.3.1", "resolved": "http://r.npm.sankuai.com/debug/download/debug-4.3.1.tgz", "integrity": "sha1-8NIpxQXgxtjEmsVT0bE9wYP2su4=", "requires": {"ms": "2.1.2"}}, "eslint-scope": {"version": "5.1.1", "resolved": "http://r.npm.sankuai.com/eslint-scope/download/eslint-scope-5.1.1.tgz", "integrity": "sha1-54blmmbLkrP2wfsNUIqrF0hI9Iw=", "requires": {"esrecurse": "^4.3.0", "estraverse": "^4.1.1"}}, "eslint-visitor-keys": {"version": "1.3.0", "resolved": "http://r.npm.sankuai.com/eslint-visitor-keys/download/eslint-visitor-keys-1.3.0.tgz", "integrity": "sha1-MOvR73wv3/AcOk8VEESvJfqwUj4="}, "espree": {"version": "6.2.1", "resolved": "http://r.npm.sankuai.com/espree/download/espree-6.2.1.tgz", "integrity": "sha1-d/xy4f10SiBSwg84pbV1gy6Cc0o=", "requires": {"acorn": "^7.1.1", "acorn-jsx": "^5.2.0", "eslint-visitor-keys": "^1.1.0"}}, "esrecurse": {"version": "4.3.0", "resolved": "http://r.npm.sankuai.com/esrecurse/download/esrecurse-4.3.0.tgz", "integrity": "sha1-eteWTWeauyi+5yzsY3WLHF0smSE=", "requires": {"estraverse": "^5.2.0"}, "dependencies": {"estraverse": {"version": "5.2.0", "resolved": "http://r.npm.sankuai.com/estraverse/download/estraverse-5.2.0.tgz", "integrity": "sha1-MH30JUfmzHMk088DwVXVzbjFOIA="}}}, "lodash": {"version": "4.17.20", "resolved": "http://r.npm.sankuai.com/lodash/download/lodash-4.17.20.tgz", "integrity": "sha1-tEqbYpe8tpjxxRo1RaKzs2jVnFI="}, "ms": {"version": "2.1.2", "resolved": "http://r.npm.sankuai.com/ms/download/ms-2.1.2.tgz", "integrity": "sha1-0J0fNXtEP0kzgqjrPM0YOHKuYAk="}}}, "vue-hot-reload-api": {"version": "2.3.0", "resolved": "https://registry.npmjs.org/vue-hot-reload-api/-/vue-hot-reload-api-2.3.0.tgz", "integrity": "sha512-2j/t+wIbyVMP5NvctQoSUvLkYKoWAAk2QlQiilrM2a6/ulzFgdcLUJfTvs4XQ/3eZhHiBmmEojbjmM4AzZj8JA==", "dev": true}, "vue-loader": {"version": "15.4.2", "resolved": "https://registry.npmjs.org/vue-loader/-/vue-loader-15.4.2.tgz", "integrity": "sha512-nVV27GNIA9MeoD8yQ3dkUzwlAaAsWeYSWZHsu/K04KCD339lW0Jv2sJWsjj3721SP7sl2lYdPmjcHgkWQSp5bg==", "dev": true, "requires": {"@vue/component-compiler-utils": "^2.0.0", "hash-sum": "^1.0.2", "loader-utils": "^1.1.0", "vue-hot-reload-api": "^2.3.0", "vue-style-loader": "^4.1.0"}}, "vue-router": {"version": "3.0.1", "resolved": "https://registry.npmjs.org/vue-router/-/vue-router-3.0.1.tgz", "integrity": "sha512-vLLoY452L+JBpALMP5UHum9+7nzR9PeIBCghU9ZtJ1eWm6ieUI8Zb/DI3MYxH32bxkjzYV1LRjNv4qr8d+uX/w=="}, "vue-style-loader": {"version": "4.1.2", "resolved": "https://registry.npmjs.org/vue-style-loader/-/vue-style-loader-4.1.2.tgz", "integrity": "sha512-0ip8ge6Gzz/Bk0iHovU9XAUQaFt/G2B61bnWa2tCcqqdgfHs1lF9xXorFbE55Gmy92okFT+8bfmySuUOu13vxQ==", "dev": true, "requires": {"hash-sum": "^1.0.2", "loader-utils": "^1.0.2"}}, "vue-template-compiler": {"version": "2.5.17", "resolved": "https://registry.npmjs.org/vue-template-compiler/-/vue-template-compiler-2.5.17.tgz", "integrity": "sha512-63uI4syCwtGR5IJvZM0LN5tVsahrelomHtCxvRkZPJ/Tf3ADm1U1wG6KWycK3qCfqR+ygM5vewUvmJ0REAYksg==", "dev": true, "requires": {"de-indent": "^1.0.2", "he": "^1.1.0"}}, "vue-template-es2015-compiler": {"version": "1.6.0", "resolved": "https://registry.npmjs.org/vue-template-es2015-compiler/-/vue-template-es2015-compiler-1.6.0.tgz", "integrity": "sha512-x3LV3wdmmERhVCYy3quqA57NJW7F3i6faas++pJQWtknWT+n7k30F4TVdHvCLn48peTJFRvCpxs3UuFPqgeELg==", "dev": true}, "vue2-editor": {"version": "2.10.3", "resolved": "http://r.npm.sankuai.com/vue2-editor/download/vue2-editor-2.10.3.tgz", "integrity": "sha1-raYtXWI9txYTobYT87c4BW414Rc=", "requires": {"quill": "^1.3.6"}}, "vuex": {"version": "3.0.1", "resolved": "https://registry.npmjs.org/vuex/-/vuex-3.0.1.tgz", "integrity": "sha512-wLoqz0B7DSZtgbWL1ShIBBCjv22GV5U+vcBFox658g6V0s4wZV9P4YjCNyoHSyIBpj1f29JBoNQIqD82cR4O3w=="}, "watchpack": {"version": "1.6.0", "resolved": "https://registry.npmjs.org/watchpack/-/watchpack-1.6.0.tgz", "integrity": "sha512-i6dHe3EyLjMmDlU1/bGQpEw25XSjkJULPuAVKCbNRefQVq48yXKUpwg538F7AZTf9kyr57zj++pQFltUa5H7yA==", "dev": true, "requires": {"chokidar": "^2.0.2", "graceful-fs": "^4.1.2", "neo-async": "^2.5.0"}}, "wbuf": {"version": "1.7.3", "resolved": "https://registry.npmjs.org/wbuf/-/wbuf-1.7.3.tgz", "integrity": "sha512-O84QOnr0icsbFGLS0O3bI5FswxzRr8/gHwWkDlQFskhSPryQXvrTMxjxGP4+iWYoauLoBvfDpkrOauZ+0iZpDA==", "dev": true, "requires": {"minimalistic-assert": "^1.0.0"}}, "webpack": {"version": "4.19.0", "resolved": "https://registry.npmjs.org/webpack/-/webpack-4.19.0.tgz", "integrity": "sha512-Ak3mMGtA8F1ar4ZP6VCLiICNIPoillROGYstnEd+LzI5Tkvz0qTITeTMcAFjxyYsaxu98F97yrCWdcxRUMPAYw==", "dev": true, "requires": {"@webassemblyjs/ast": "1.7.6", "@webassemblyjs/helper-module-context": "1.7.6", "@webassemblyjs/wasm-edit": "1.7.6", "@webassemblyjs/wasm-parser": "1.7.6", "acorn": "^5.6.2", "acorn-dynamic-import": "^3.0.0", "ajv": "^6.1.0", "ajv-keywords": "^3.1.0", "chrome-trace-event": "^1.0.0", "enhanced-resolve": "^4.1.0", "eslint-scope": "^4.0.0", "json-parse-better-errors": "^1.0.2", "loader-runner": "^2.3.0", "loader-utils": "^1.1.0", "memory-fs": "~0.4.1", "micromatch": "^3.1.8", "mkdirp": "~0.5.0", "neo-async": "^2.5.0", "node-libs-browser": "^2.0.0", "schema-utils": "^0.4.4", "tapable": "^1.1.0", "uglifyjs-webpack-plugin": "^1.2.4", "watchpack": "^1.5.0", "webpack-sources": "^1.2.0"}}, "webpack-cli": {"version": "3.1.0", "resolved": "https://registry.npmjs.org/webpack-cli/-/webpack-cli-3.1.0.tgz", "integrity": "sha512-p5NeKDtYwjZozUWq6kGNs9w+Gtw/CPvyuXjXn2HMdz8Tie+krjEg8oAtonvIyITZdvpF7XG9xDHwscLr2c+ugQ==", "dev": true, "requires": {"chalk": "^2.4.1", "cross-spawn": "^6.0.5", "enhanced-resolve": "^4.0.0", "global-modules-path": "^2.1.0", "import-local": "^1.0.0", "inquirer": "^6.0.0", "interpret": "^1.1.0", "loader-utils": "^1.1.0", "supports-color": "^5.4.0", "v8-compile-cache": "^2.0.0", "yargs": "^12.0.1"}, "dependencies": {"find-up": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/find-up/-/find-up-2.1.0.tgz", "integrity": "sha1-RdG35QbHF93UgndaK3eSCjwMV6c=", "dev": true, "requires": {"locate-path": "^2.0.0"}}, "import-local": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/import-local/-/import-local-1.0.0.tgz", "integrity": "sha512-vAaZHieK9qjGo58agRBg+bhHX3hoTZU/Oa3GESWLz7t1U62fk63aHuDJJEteXoDeTCcPmUT+z38gkHPZkkmpmQ==", "dev": true, "requires": {"pkg-dir": "^2.0.0", "resolve-cwd": "^2.0.0"}}, "pkg-dir": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/pkg-dir/-/pkg-dir-2.0.0.tgz", "integrity": "sha1-9tXREJ4Z1j7fQo4L1X4Sd3YVM0s=", "dev": true, "requires": {"find-up": "^2.1.0"}}}}, "webpack-dev-middleware": {"version": "3.2.0", "resolved": "https://registry.npmjs.org/webpack-dev-middleware/-/webpack-dev-middleware-3.2.0.tgz", "integrity": "sha512-YJLMF/96TpKXaEQwaLEo+Z4NDK8aV133ROF6xp9pe3gQoS7sxfpXh4Rv9eC+8vCvWfmDjRQaMSlRPbO+9G6jgA==", "dev": true, "requires": {"loud-rejection": "^1.6.0", "memory-fs": "~0.4.1", "mime": "^2.3.1", "path-is-absolute": "^1.0.0", "range-parser": "^1.0.3", "url-join": "^4.0.0", "webpack-log": "^2.0.0"}, "dependencies": {"mime": {"version": "2.3.1", "resolved": "https://registry.npmjs.org/mime/-/mime-2.3.1.tgz", "integrity": "sha512-OEUllcVoydBHGN1z84yfQDimn58pZNNNXgZlHXSboxMlFvgI6MXSWpWKpFRra7H1HxpVhHTkrghfRW49k6yjeg==", "dev": true}}}, "webpack-dev-server": {"version": "3.1.8", "resolved": "https://registry.npmjs.org/webpack-dev-server/-/webpack-dev-server-3.1.8.tgz", "integrity": "sha512-c+tcJtDqnPdxCAzEEZKdIPmg3i5i7cAHe+B+0xFNK0BlCc2HF/unYccbU7xTgfGc5xxhCztCQzFmsqim+KhI+A==", "dev": true, "requires": {"ansi-html": "0.0.7", "bonjour": "^3.5.0", "chokidar": "^2.0.0", "compression": "^1.5.2", "connect-history-api-fallback": "^1.3.0", "debug": "^3.1.0", "del": "^3.0.0", "express": "^4.16.2", "html-entities": "^1.2.0", "http-proxy-middleware": "~0.18.0", "import-local": "^2.0.0", "internal-ip": "^3.0.1", "ip": "^1.1.5", "killable": "^1.0.0", "loglevel": "^1.4.1", "opn": "^5.1.0", "portfinder": "^1.0.9", "schema-utils": "^1.0.0", "selfsigned": "^1.9.1", "serve-index": "^1.7.2", "sockjs": "0.3.19", "sockjs-client": "1.1.5", "spdy": "^3.4.1", "strip-ansi": "^3.0.0", "supports-color": "^5.1.0", "webpack-dev-middleware": "3.2.0", "webpack-log": "^2.0.0", "yargs": "12.0.2"}, "dependencies": {"ansi-regex": {"version": "2.1.1", "resolved": "https://registry.npmjs.org/ansi-regex/-/ansi-regex-2.1.1.tgz", "integrity": "sha1-w7M6te42DYbg5ijwRorn7yfWVN8=", "dev": true}, "del": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/del/-/del-3.0.0.tgz", "integrity": "sha1-U+z2mf/LyzljdpGrE7rxYIGXZuU=", "dev": true, "requires": {"globby": "^6.1.0", "is-path-cwd": "^1.0.0", "is-path-in-cwd": "^1.0.0", "p-map": "^1.1.1", "pify": "^3.0.0", "rimraf": "^2.2.8"}}, "globby": {"version": "6.1.0", "resolved": "https://registry.npmjs.org/globby/-/globby-6.1.0.tgz", "integrity": "sha1-9abXDoOV4hyFj7BInWTfAkJNUGw=", "dev": true, "requires": {"array-union": "^1.0.1", "glob": "^7.0.3", "object-assign": "^4.0.1", "pify": "^2.0.0", "pinkie-promise": "^2.0.0"}, "dependencies": {"pify": {"version": "2.3.0", "resolved": "https://registry.npmjs.org/pify/-/pify-2.3.0.tgz", "integrity": "sha1-7RQaasBDqEnqWISY59yosVMw6Qw=", "dev": true}}}, "pify": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/pify/-/pify-3.0.0.tgz", "integrity": "sha1-5aSs0sEB/fPZpNB/DbxNtJ3SgXY=", "dev": true}, "schema-utils": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/schema-utils/-/schema-utils-1.0.0.tgz", "integrity": "sha512-i27Mic4KovM/lnGsy8whRCHhc7VicJajAjTrYg11K9zfZXnYIt4k5F+kZkwjnrhKzLic/HLU4j11mjsz2G/75g==", "dev": true, "requires": {"ajv": "^6.1.0", "ajv-errors": "^1.0.0", "ajv-keywords": "^3.1.0"}}, "strip-ansi": {"version": "3.0.1", "resolved": "https://registry.npmjs.org/strip-ansi/-/strip-ansi-3.0.1.tgz", "integrity": "sha1-ajhfuIU9lS1f8F0Oiq+UJ43GPc8=", "dev": true, "requires": {"ansi-regex": "^2.0.0"}}}}, "webpack-log": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/webpack-log/-/webpack-log-2.0.0.tgz", "integrity": "sha512-cX8G2vR/85UYG59FgkoMamwHUIkSSlV3bBMRsbxVXVUk2j6NleCKjQ/WE9eYg9WY4w25O9w8wKP4rzNZFmUcUg==", "dev": true, "requires": {"ansi-colors": "^3.0.0", "uuid": "^3.3.2"}}, "webpack-merge": {"version": "4.1.4", "resolved": "https://registry.npmjs.org/webpack-merge/-/webpack-merge-4.1.4.tgz", "integrity": "sha512-TmSe1HZKeOPey3oy1Ov2iS3guIZjWvMT2BBJDzzT5jScHTjVC3mpjJofgueEzaEd6ibhxRDD6MIblDr8tzh8iQ==", "dev": true, "requires": {"lodash": "^4.17.5"}}, "webpack-sources": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/webpack-sources/-/webpack-sources-1.2.0.tgz", "integrity": "sha512-9BZwxR85dNsjWz3blyxdOhTgtnQvv3OEs5xofI0wPYTwu5kaWxS08UuD1oI7WLBLpRO+ylf0ofnXLXWmGb2WMw==", "dev": true, "requires": {"source-list-map": "^2.0.0", "source-map": "~0.6.1"}, "dependencies": {"source-map": {"version": "0.6.1", "resolved": "https://registry.npmjs.org/source-map/-/source-map-0.6.1.tgz", "integrity": "sha512-UjgapumWlbMhkBgzT7Ykc5YXUT46F0iKu8SGXq0bcwP5dz/h0Plj6enJqjz1Zbq2l5WaqYnrVbwWOWMyF3F47g==", "dev": true}}}, "websocket-driver": {"version": "0.7.0", "resolved": "https://registry.npmjs.org/websocket-driver/-/websocket-driver-0.7.0.tgz", "integrity": "sha1-DK+dLXVdk67gSdS90NP+LMoqJOs=", "dev": true, "requires": {"http-parser-js": ">=0.4.0", "websocket-extensions": ">=0.1.1"}}, "websocket-extensions": {"version": "0.1.3", "resolved": "https://registry.npmjs.org/websocket-extensions/-/websocket-extensions-0.1.3.tgz", "integrity": "sha512-nqHUnMXmBzT0w570r2JpJxfiSD1IzoI+HGVdd3aZ0yNi3ngvQ4jv1dtHt5VGxfI2yj5yqImPhOK4vmIh2xMbGg==", "dev": true}, "whatwg-fetch": {"version": "3.6.20", "resolved": "http://r.npm.sankuai.com/whatwg-fetch/download/whatwg-fetch-3.6.20.tgz", "integrity": "sha1-WAzm15H6zskdN8cokJlaC0jTHHA="}, "which": {"version": "1.3.1", "resolved": "http://r.npm.sankuai.com/which/download/which-1.3.1.tgz", "integrity": "sha1-pFBD1U9YBTFtqNYvn1CRjT2nCwo=", "dev": true, "requires": {"isexe": "^2.0.0"}}, "which-module": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/which-module/-/which-module-2.0.0.tgz", "integrity": "sha1-2e8H3Od7mQK4o6j6SzHD4/fm6Ho=", "dev": true}, "wide-align": {"version": "1.1.3", "resolved": "https://registry.npm.taobao.org/wide-align/download/wide-align-1.1.3.tgz", "integrity": "sha1-rgdOa9wMFKQx6ATmJFScYzsABFc=", "dev": true, "requires": {"string-width": "^1.0.2 || 2"}}, "wordwrap": {"version": "1.0.0", "resolved": "http://r.npm.sankuai.com/wordwrap/download/wordwrap-1.0.0.tgz", "integrity": "sha1-J1hIEIkUVqQXHI0CJkQa3pDLyus=", "dev": true}, "worker-farm": {"version": "1.6.0", "resolved": "https://registry.npmjs.org/worker-farm/-/worker-farm-1.6.0.tgz", "integrity": "sha512-6w+3tHbM87WnSWnENBUvA2pxJPLhQUg5LKwUQHq3r+XPhIM+Gh2R5ycbwPCyuGbNg+lPgdcnQUhuC02kJCvffQ==", "dev": true, "requires": {"errno": "~0.1.7"}}, "wrap-ansi": {"version": "2.1.0", "resolved": "http://registry.npmjs.org/wrap-ansi/-/wrap-ansi-2.1.0.tgz", "integrity": "sha1-2Pw9KE3QV5T+hJc8rs3Rz4JP3YU=", "dev": true, "requires": {"string-width": "^1.0.1", "strip-ansi": "^3.0.1"}, "dependencies": {"ansi-regex": {"version": "2.1.1", "resolved": "https://registry.npmjs.org/ansi-regex/-/ansi-regex-2.1.1.tgz", "integrity": "sha1-w7M6te42DYbg5ijwRorn7yfWVN8=", "dev": true}, "is-fullwidth-code-point": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/is-fullwidth-code-point/-/is-fullwidth-code-point-1.0.0.tgz", "integrity": "sha1-754xOG8DGn8NZDr4L95QxFfvAMs=", "dev": true, "requires": {"number-is-nan": "^1.0.0"}}, "string-width": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/string-width/-/string-width-1.0.2.tgz", "integrity": "sha1-EYvfW4zcUaKn5w0hHgfisLmxB9M=", "dev": true, "requires": {"code-point-at": "^1.0.0", "is-fullwidth-code-point": "^1.0.0", "strip-ansi": "^3.0.0"}}, "strip-ansi": {"version": "3.0.1", "resolved": "https://registry.npmjs.org/strip-ansi/-/strip-ansi-3.0.1.tgz", "integrity": "sha1-ajhfuIU9lS1f8F0Oiq+UJ43GPc8=", "dev": true, "requires": {"ansi-regex": "^2.0.0"}}}}, "wrappy": {"version": "1.0.2", "resolved": "http://r.npm.sankuai.com/wrappy/download/wrappy-1.0.2.tgz", "integrity": "sha1-tSQ9jz7BqjXxNkYFvA0QNuMKtp8=", "dev": true}, "write": {"version": "0.2.1", "resolved": "http://r.npm.sankuai.com/write/download/write-0.2.1.tgz", "integrity": "sha1-X8A4KOJkzqP+kUVUdvejxWbLB1c=", "dev": true, "requires": {"mkdirp": "^0.5.1"}}, "xml2js": {"version": "0.2.6", "resolved": "https://registry.npmjs.org/xml2js/-/xml2js-0.2.6.tgz", "integrity": "sha1-0gnE5N2h/JxFIUHvQcB39a399sQ=", "dev": true, "requires": {"sax": "0.4.2"}}, "xmlbuilder": {"version": "0.4.2", "resolved": "https://registry.npmjs.org/xmlbuilder/-/xmlbuilder-0.4.2.tgz", "integrity": "sha1-F3bWXz/brUcKCNhgTN6xxOVA/4M=", "dev": true}, "xregexp": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/xregexp/-/xregexp-4.0.0.tgz", "integrity": "sha512-PHyM+sQouu7xspQQwELlGwwd05mXUFqwFYfqPO0cC7x4fxyHnnuetmQr6CjJiafIDoH4MogHb9dOoJzR/Y4rFg==", "dev": true}, "xtend": {"version": "4.0.1", "resolved": "https://registry.npmjs.org/xtend/-/xtend-4.0.1.tgz", "integrity": "sha1-pcbVMr5lbiPbgg77lDofBJmNY68=", "dev": true}, "xttp": {"version": "1.0.5", "resolved": "http://r.npm.sankuai.com/xttp/download/xttp-1.0.5.tgz", "integrity": "sha1-JpVtyoRHE2lYQxFOocnOcu7n99I=", "requires": {"iconv-lite": "^0.6.3", "mime2": "^0.0.11"}, "dependencies": {"iconv-lite": {"version": "0.6.3", "resolved": "http://r.npm.sankuai.com/iconv-lite/download/iconv-lite-0.6.3.tgz", "integrity": "sha1-pS+AvzjaGVLrXGgXkHGYcaGnJQE=", "requires": {"safer-buffer": ">= 2.1.2 < 3.0.0"}}}}, "y18n": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/y18n/-/y18n-4.0.0.tgz", "integrity": "sha512-r9S/ZyXu/Xu9q1tYlpsLIsa3EeLXXk0VwlxqTcFRfg9EhMW+17kbt9G0NrgCmhGb5vT2hyhJZLfDGx+7+5Uj/w==", "dev": true}, "yallist": {"version": "2.1.2", "resolved": "https://registry.npmjs.org/yallist/-/yallist-2.1.2.tgz", "integrity": "sha1-HBH5IY8HYImkfdUS+TxmmaaoHVI=", "dev": true}, "yargs": {"version": "12.0.2", "resolved": "https://registry.npmjs.org/yargs/-/yargs-12.0.2.tgz", "integrity": "sha512-e7SkEx6N6SIZ5c5H22RTZae61qtn3PYUE8JYbBFlK9sYmh3DMQ6E5ygtaG/2BW0JZi4WGgTR2IV5ChqlqrDGVQ==", "dev": true, "requires": {"cliui": "^4.0.0", "decamelize": "^2.0.0", "find-up": "^3.0.0", "get-caller-file": "^1.0.1", "os-locale": "^3.0.0", "require-directory": "^2.1.1", "require-main-filename": "^1.0.1", "set-blocking": "^2.0.0", "string-width": "^2.0.0", "which-module": "^2.0.0", "y18n": "^3.2.1 || ^4.0.0", "yargs-parser": "^10.1.0"}, "dependencies": {"find-up": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/find-up/-/find-up-3.0.0.tgz", "integrity": "sha512-1yD6RmLI1XBfxugvORwlck6f75tYL+iR0jqwsOrOxMZyGYqUuDhJ0l4AXdO1iX/FTs9cBAMEk1gWSEx1kSbylg==", "dev": true, "requires": {"locate-path": "^3.0.0"}}, "locate-path": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/locate-path/-/locate-path-3.0.0.tgz", "integrity": "sha512-7AO748wWnIhNqAuaty2ZWHkQHRSNfPVIsPIfwEOWO22AmaoVrWavlOcMR5nzTLNYvp36X220/maaRsrec1G65A==", "dev": true, "requires": {"p-locate": "^3.0.0", "path-exists": "^3.0.0"}}, "p-limit": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/p-limit/-/p-limit-2.0.0.tgz", "integrity": "sha512-fl5s52lI5ahKCernzzIyAP0QAZbGIovtVHGwpcu1Jr/EpzLVDI2myISHwGqK7m8uQFugVWSrbxH7XnhGtvEc+A==", "dev": true, "requires": {"p-try": "^2.0.0"}}, "p-locate": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/p-locate/-/p-locate-3.0.0.tgz", "integrity": "sha512-x+12w/To+4GFfgJhBEpiDcLozRJGegY+Ei7/z0tSLkMmxGZNybVMSfWj9aJn8Z5Fc7dBUNJOOVgPv2H7IwulSQ==", "dev": true, "requires": {"p-limit": "^2.0.0"}}, "p-try": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/p-try/-/p-try-2.0.0.tgz", "integrity": "sha512-hMp0onDKIajHfIkdRk3P4CdCmErkYAxxDtP3Wx/4nZ3aGlau2VKh3mZpcuFkH27WQkL/3WBCPOktzA9ZOAnMQQ==", "dev": true}, "path-exists": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/path-exists/-/path-exists-3.0.0.tgz", "integrity": "sha1-zg6+ql94yxiSXqfYENe1mwEP1RU=", "dev": true}}}, "yargs-parser": {"version": "10.1.0", "resolved": "https://registry.npmjs.org/yargs-parser/-/yargs-parser-10.1.0.tgz", "integrity": "sha512-VCIyR1wJoEBZUqk5PA+oOBF6ypbwh5aNB3I50guxAL/quggdfs4TtNHQrSazFA3fYZ+tEqfs0zIGlv0c/rgjbQ==", "dev": true, "requires": {"camelcase": "^4.1.0"}}}}