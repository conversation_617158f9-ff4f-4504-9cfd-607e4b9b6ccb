# 外卖商家结算-PC版

## 1. **项目概述**

### 项目名称
外卖商家结算-PC版 (waima<PERSON>_mfe_alchemist_pc)

### 项目简介
提供商家在PC端上查看日账单、历史账单、各账户信息等功能。

### 主要功能
- 商家对账信息查询
- 发票申请与管理
- 账单查询与管理
- 结算信息查看

### 技术栈说明
- 前端框架：Vue.js 2.x
- 状态管理：Vuex
- 路由管理：Vue Router
- UI组件库：@roo/roo-vue、@ss/mtd-vue
- 构建工具：Webpack 4
- 开发语言：JavaScript、SCSS

### 访问地址
- `prod` [线上地址](https://waimaie.meituan.com/#https://waimaieapp.meituan.com/finance/pc/index)
- `stage` [st 地址](https://e.waimai.st.sankuai.com/?ignoreSetRouterProxy=true#https://proxy.waimai.st.sankuai.com/finance/pc/index)
- `test` [test 地址](http://e.b.waimai.test.sankuai.com/?ignoreSetRouterProxy=true#http://e.platform.proxy.b.waimai.test.sankuai.com/finance/pc/index)

## 2. **快速开始**

### 环境要求
- Node.js 10.x
- npm/mnpm

### 安装步骤
```bash
# 使用node 10版本
mnpm install
```

### 配置说明
**ssoid 搬用**

**一定要将对应环境下的 ssoid Cookie 种到本地**，也可安装 chrome 插件搬运 [move-cookie](https://km.sankuai.com/page/89139164)。如将 develop（或其他）环境 `xianfu_waimai_ssoid` 的 cookie 移植到本地 127.0.0.1 域下

### 运行示例
#### 1. 登录 test 环境
开发联调阶段主要使用 test 环境的 cookie，因此需要先通过登录获取到 test 环境 cookie。
test 环境访问 url: http://e.b.waimai.test.sankuai.com
有默认账号，找组内人要。

#### 2. 修改 test 环境的 cookie 的域
主要是 acctId 、wmPoiId 、 token 这三个 cookie
```
因为现在test环境cookie不支持子域,为了在e.b.waimai.test.sankuai.com的子域使用主域的cookie,所以需要修改cookie.
打开chrome控制台的 Application=>Cookies,然后修改 key分别为 acctid和 wmPoild token这三个cookie,将其Domain由原先的 e.b.waimai.test.sankuai.com 改成 .e.b.waimai.test.sankuai.com
对,就是前面多加了一个点
```

强力推荐以下步骤（包含步骤 2，3，4，5）
这里额外提供一种思路（以 test 环境为例），修改 /etc/hosts 文件,加入 127.0.0.1 e.b.waimai.test.sankuai.com，此处也可使用https://github.com/oldj/SwitchHosts，该工具可方便的管理hosts。
然后使用 npm run start --test 启动服务，浏览器访问 e.b.waimai.test.sankuai.com/invoice.html#/invoice-apply/qualification-select
即可。

#### 3. 修改本机 host
修改 /etc/hosts 文件,加入 127.0.0.1 dev.e.b.waimai.test.sankuai.com

#### 4. 启动本地服务
选择对应环境的启动响应的命令即可。
每个环境的转发规则在根目录下的 wproxy.js 中

```shell
# 将接口转发到mock环境[接口将转发到yapi,修改mock数据请到http://yapi.sankuai.com/project/2487/interface/api]
npm run start -- mock

# 将接口转发到test环境
npm run start -- test

# 将接口转发到发票新工程的test环境
npm run start -- invoicetest
```

#### 5. 在浏览器访问链接
```
### 首页本地访问链接
dev.e.b.waimai.test.sankuai.com/invoice.html#/invoice-apply/qualification-select

### 对账信息
/billReconciliation.html#/account-flow?wmPoiId=610282

### 发票首页
/invoice.html#/invoice-apply/qualification-select
```

## 3. **文档结构**

### 目录结构说明
```
.
├── plus                                        # Plus 相关配置文件
│   ├── manifest-gray.yaml                      # 灰度发布模板
│   ├── manifest.yaml                           # 正式（非灰）模板
│   └── pre-build.sh                            # 构建脚本
├── src
│   ├── components                              # 项目通用组件
│   │   └── confirm-modal
│   │       └── index.vue
│   ├── lib                                     # 项目可复用逻辑
│   │   ├── bridge.js                           # B 端壳子桥协议
│   │   ├── filters.js                          # Vue filters
│   │   ├── has-invalid-charactor.js            #
│   │   ├── toast-error.js                      # 错误统一 toast 提示
│   │   └── utils.js
│   └── pages                                   # 页面代码
│       └── main                                # 每个 html 分一个目录
│           ├── store                           # store
│           │   ├── modules
│           │   │   └── config
│           │   └── index.js
│           ├── views
│           │   └── home                        # 顶级页面
│           │       ├── components              # 页面组件
│           │       │   └── card.vue
│           │       └── index.vue
│           ├── app.vue                         # 主视图
│           ├── index.html                      # html 模板
│           ├── index.js                        # 业务入口
│           └── router.js                       # 前端路由定义
├── static                                      # 纯 copy 的静态文件，目前只是一些跳板 html
│   ├── home-jumper.html
│   ├── ...
│   └── sku-jumper.html
├── webpack                                     # webpack 配置
│   ├── upload-cdn.js                           # 上传 CDN 脚本
│   ├── util.js
│   ├── webpack.config.base.js
│   ├── webpack.config.dev.js
│   └── webpack.config.prod.js
├── README.md
├── package-lock.json
└── package.json
```

### 核心模块说明
- **components**: 项目通用组件
- **lib**: 项目可复用逻辑
- **pages**: 页面代码
- **static**: 静态文件
- **webpack**: 构建配置

### API文档链接
- [商家对账 pc 端所有页面](https://km.sankuai.com/page/206900724)
- [结算业务介绍](https://km.sankuai.com/page/109009803)

### 架构设计文档
暂无

## 4. **开发指南**

### 开发环境搭建
请参考"快速开始"章节的安装步骤和配置说明。

### 代码规范
项目使用ESLint进行代码规范检查，配置文件为`.eslintrc.js`。

### 提交规范
项目使用commitlint进行提交规范检查，配置文件为`commitlint.config.js`。

分支命名规范：
```
功能(feat或fix或perf或refact)+jira+描述
如 refact/20180512-readme-optimize
```

### 测试规范
暂无

## 5. **部署说明**

### 部署环境要求
- Node.js 10.x环境

### 部署步骤
```bash
# 仅构建，结果在 build 目录
npm run build
```

### 配置说明
部署配置在`plus`目录下：
- `manifest-gray.yaml`: 灰度发布模板
- `manifest.yaml`: 正式（非灰）模板
- `pre-build.sh`: 构建脚本

### 监控说明
暂无

## 6. **其他说明**

### 鉴权相关说明
正常线上鉴权流程:

```
browser
   ↓
proxy         取 cookie/query 的 acctId 、wmPoiId 、 token 等参数鉴权，鉴权通过后转发到对应后端服务
   ↓
moneyeapi     结算后端服务
```

dev 开发域名请直接使用 moneyeapi 域名，只需设置 acctId 和 wmPoiId cookie 即可。
相关账号请找后端索要。

### 构建分析
```bash
# 生成 stats.json
npm run profile
# 分析 bundle
npm i -g webpack-bundle-analyzer
webpack-bundle-analyzer stats.json ./build -m static -r report.html
```