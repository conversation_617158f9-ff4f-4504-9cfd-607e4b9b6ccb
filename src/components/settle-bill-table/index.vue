<template>
    <table class="settle-bill-table">
        <colgroup>
            <col style="width: 130px;" />
            <col style="width: 100px;" />
            <col style="width: 120px;" />
        </colgroup>
        <col style="width: 200px;" />

        <thead>
            <tr>
                <th>时间</th>
                <th>账单金额</th>
                <th>操作</th>
                <th>账单信息</th>
            </tr>
        </thead>

        <tbody
            v-if="list.length === 0"
            key="empty"
        >
            <tr>
                <td
                    class="empty"
                    colspan="4"
                >
                    暂无数据
                </td>
            </tr>
        </tbody>

        <tbody
            v-for="(bill, idx) in list"
            :key="idx"
        >
            <tr
                v-for="(dailyBill, index) in bill.dailyBills"
                :key="dailyBill.dailyBillDate"
                :class="isDelay ? 'tableBorder' : ''"
            >
                <td>
                    {{ dailyBill.dailyBillDate }}
                    {{ dailyBill.dailyBillDate | weekday }}
                </td>
                <td>
                    <money :amount="dailyBill.dailyBillAmount" />
                </td>
                <td>
                    <router-link
                        v-lx-mc="viewBid && {
                            bid: viewBid,
                            options: {
                                cid,
                            },
                        }"
                        :to="{
                            name: 'daily-bill',
                            query: {
                                wmPoiId: wmPoiId,
                                dailyBillDate: dailyBill.dailyBillDate
                            }
                        }"
                    >
                        查看
                    </router-link>
                    <span class="separator">|</span>
                    <a
                        v-lx-mc="downloadBid && {
                            bid: downloadBid,
                            options: { cid },
                        }"
                        href="#noop"
                        @click.prevent="handleDownloadClick(dailyBill)"
                    >
                        下载
                    </a>
                </td>
                <td v-if="settleType == 'unSettleType' && isDelay == 1">预计 {{ dailyBill.settleDate }} 入账</td>
                <td
                    v-else-if="index === 0"
                    :rowspan="bill.dailyBills.length"
                >
                    <div class="bill-info">
                        <div class="bill-date">
                            {{ bill.settleBillStartDate }} 至 {{ bill.settleBillEndDate }}
                        </div>
                        <div class="bill-sum">
                            <money :amount="bill.totalAmount" />
                        </div>
                        <router-link
                            v-if="bill.settleState === 1"
                            v-lx-mc="settledBid && {
                                bid: settledBid,
                                options: {
                                    cid,
                                },
                            }"
                            :to="{ name: 'account-list' }"
                            class="bill-status settled"
                        >
                            已汇入余额
                        </router-link>
                        <div
                            v-else
                            class="bill-status"
                        >
                            预计 {{ bill.settleDate }} 入账
                        </div>
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
</template>

<script>
/* eslint-disable import/no-unresolved, import/extensions */
import request from '$src/utils/request';
import { toast } from '@roo/roo-vue';

import Money from '../money';

import toastError from '../../lib/toast-error';

/**
 * 结算账单表格 用于对账首页和已结算账单
 * list item 数据结构与后端适配:
 * {
 *   settleState,
 *   settleDate,
 *   settleBillStartDate,
 *   settleBillEndDate,
 *   dailyBills: [{
 *     dailyBillDate,
 *     dailyBillAmount,
 *   }]
 * }
 */
export default {
    name: 'SettleBillTable',
    components: {
        Money,
    },
    filters: {
        weekday(dateStr) {
            return `星期${'日一二三四五六'[new Date(dateStr).getDay()]}`;
        },
    },
    props: {
        cid: {
            type: String,
            default: null,
        },
        viewBid: {
            type: String,
            default: null,
        },
        downloadBid: {
            type: String,
            default: null,
        },
        settledBid: {
            type: String,
            default: null,
        },
        wmPoiId: {
            type: [String, Number],
            default: null,
        },
        list: {
            type: Array,
            default() {
                return [];
            },
        },
        settleType: {
            type: String,
            default: null,
        },
        isDelay: {
            type: Number,
            default: null,
        },
    },
    methods: {
        handleDownloadClick(item) {
            const params = {
                beginDate: item.dailyBillDate,
                endDate: item.dailyBillDate,
                wmPoiId: this.wmPoiId,
            };
            return request.get('/finance/pc/api/billDownload/createBillExportTask', { params })
                .then((res) => {
                    const { code, msg } = res.data;

                    if (code !== 0) {
                        throw new Error(msg);
                    }

                    toast.success('下载成功，请到下载专区下载');
                })
                .catch(toastError);
        },
    },
};
</script>

<style lang="scss">
.settle-bill-table {
    width: 100%;
    table-layout: fixed;
    border-collapse: collapse;
    background: #FFF;
    border: 1px solid #E9EAF2;
    font-size: 14px;

    tbody {
        border-bottom: 1px solid #E9EAF2;
    }

    thead {
        color: #858692;
        background: #F7F8FA;

        th {
            font-weight: normal;
        }
    }

    th, td {
        box-sizing: border-box;
        height: 40px;
        padding: 0px 20px;
        line-height: 20px;
        vertical-align: middle;

        &:nth-child(2) {
            padding-right: 60px;
            text-align: right;
        }
    }

    td:nth-child(3) {
        border-right: 1px dashed #E9EAF2;
    }

    .empty {
        height: 150px;
        text-align: center;
    }
}

.separator {
    color: #E9EAF2;
}

.tableBorder {
  border-bottom: 1px solid #E9EAF2;
}

.bill-info {
    padding: 20px 0;
}

.bill-date {
    color: #858692;
    font-size: 14px;
}

.bill-sum {
    // margin: 5px 0;
    font-size: 24px;
    line-height: 40px;
    color: #3F4156;
}

.bill-status {
    font-size: 14px;

    &:link,
    &:visited {
        color: #585A6E;
        text-decoration: none;
    }

    &:hover,
    &:active {
        color: #3F4156;
        text-decoration: none;
    }

    &.settled::after {
        content: '';
        display: inline-block;
        width: 8px;
        height: 8px;
        border-top: 1px solid #A2A4B3;
        border-right: 1px solid #A2A4B3;
        transform: rotate(45deg);
        margin-bottom: 1px;
    }
}
</style>
