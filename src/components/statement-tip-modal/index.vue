<!-- 补充协议公共弹窗，单店、多店复用 -->
<template>
    <roo-modal
        v-model="displayTipModal"
        class="tip-modal"
        size="normal"
        title="尊敬的美团商户您好："
        @close="displayTipModal=false"
    >
        <p :class="$style.tipModalContent">
            为确保发票合规，平台更新了发票补充协议，您可以直接在“下载模板”下载使用，旧发票补充协议模板将于2024年11月25日起停止使用。给您带来的不便敬请谅解，感谢您的理解与配合！
        </p>
    </roo-modal>
</template>

<script>
import moment from 'moment';

export default {
    name: 'StatementTipModal',
    data() {
        return {
            displayTipModal: true,
        };
    },
    mounted() {
        this.setTipModalVisible();
    },
    methods: {
        setTipModalVisible() {
            const targetDate = moment('2025-01-01', 'YYYY-MM-DD');
            const currentDate = moment();
            if (currentDate.isAfter(targetDate)) {
                this.displayTipModal = false;
            }
        },
    },
};
</script>

<style lang="scss" module>
.tip-modal-content{
    text-indent: 2em;
    padding-bottom: 20px;
}
:global(.tip-modal) {
    :global(.close){
        opacity: .7;
        color: #000000!important;
    }
}
</style>
