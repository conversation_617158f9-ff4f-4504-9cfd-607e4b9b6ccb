<template>
    <div class="coupon">
        <div class="coupon_wrap">
            <div class="coupon_header">
                <slot name="header">
                    <div class="coupon_title">
                        {{ title }}
                    </div>
                </slot>
            </div>
            <div class="coupon_body">
                <slot />
            </div>
        </div>
    </div>
</template>

<script>
export default {
    name: 'Coupon',
    props: {
        title: {
            type: String,
            default: '',
        },
    },
};
</script>

<style lang="scss">
.coupon {
    overflow: hidden;

    .coupon_wrap {
        position: relative;
        padding: 0 20px;
        border: 1px solid #F2EDE8;
        border-radius: 3px;
        background: #FDFCF9;
        font-size: 14px;
        color: #3F4156;

        &::before,
        &::after {
            content: '';
            position: absolute;
            top: 40px;
            display: block;
            width: 10px;
            height: 10px;
            border-radius: 100%;
            border: 1px solid #F2EDE8;
            background: #FFF;
        }

        &::before {
            left: -5px;
        }
        &::after {
            right: -5px;
        }
    }
}

.coupon_header {
    position: relative;
    min-height: 45px;
    border-bottom: 1px dashed #F2EDE8;
}

.coupon_title {
    font-size: 14px;
    line-height: 45px;
    color: #3F4156;
}

.coupon_body {
    padding: 20px 0;
}
</style>
