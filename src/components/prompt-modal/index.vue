<template>
    <roo-modal
        :class="$style.modal"
        :value="value"
        :backdrop="loading ? 'static' : true"
        :title="title"
        @input="$emit('input', $event)"
        @hidden="handleHidden"
    >
        <div
            v-if="message"
            :class="$style.mb20"
        >
            {{ message }}
        </div>

        <roo-input
            v-model="comment"
            :class="$style.textarea"
            :placeholder="placeholder"
            type="textarea"
            noresize
        />

        <template slot="footer">
            <roo-button
                :disabled="loading"
                type="text"
                @click="$emit('input', false)"
            >
                取消
            </roo-button>
            <roo-button
                :loading="loading"
                :disabled="required && !comment"
                @click="handleConfirm"
            >
                确定
            </roo-button>
        </template>
    </roo-modal>
</template>

<script>
export default {
    name: 'PromptModal',
    props: {
        value: {
            type: Boolean,
            required: true,
        },
        loading: {
            type: Boolean,
            default: false,
        },
        title: {
            type: String,
            default: '',
        },
        message: {
            type: String,
            default: '',
        },
        placeholder: {
            type: String,
            default: '',
        },
        required: {
            type: <PERSON>olean,
            default: false,
        },
    },
    data() {
        return {
            comment: '',
        };
    },
    // mounted() {
    //     document.body.appendChild(this.$el);
    // },
    // beforeDestroy() {
    //     const { parentNode } = this.$el;
    //     if (parentNode) {
    //         parentNode.removeChild(this.$el);
    //     }
    // },
    methods: {
        handleHidden() {
            this.comment = '';
            this.$emit('hidden');
        },
        handleConfirm() {
            this.$emit('confirm', this.comment);
        },
    },
};
</script>

<style lang="scss" module>
.mb20 {
    margin-bottom: 20px;
}
.textarea:global(.roo-input) {
    height: 100px;
}
</style>
