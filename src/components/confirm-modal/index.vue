<template>
    <roo-modal
        :value="value"
        :backdrop="loading ? 'static' : true"
        size="small"
        @input="$emit('input', $event)"
        @hidden="$emit('hidden', $event)"
    >
        <template slote="header">
            {{ title }}
        </template>

        <slot>
            {{ message }}
        </slot>

        <template slot="footer">
            <roo-button
                :disable="loading"
                type="hollow"
                @click="$emit('input', false)"
            >
                取消
            </roo-button>
            <roo-button
                :loading="loading"
                @click="$emit('confirm')"
            >
                {{ confirmText }}
            </roo-button>
        </template>
    </roo-modal>
</template>

<script>
// 能控制确认按钮 loading 状态的确认框

export default {
    name: 'ConfirmModal',
    props: {
        value: {
            type: Boolean,
            default: false,
        },
        title: {
            type: String,
            default: '',
        },
        message: {
            type: String,
            default: '',
        },
        loading: {
            type: Boolean,
            default: false,
        },
        confirmText: {
            type: String,
            default: '确认',
        },
    },
};
</script>
