<template>
    <span class="amount">
        <span
            v-if="showUnit"
            class="unit"
        >￥</span><span class="integer">{{ integer | formatNumberThousand }}</span><span class="decimal">.{{ decimal }}</span>
    </span>
</template>

<script>
import { formatNumberThousand } from '../../lib/utils';

function isString(val) {
    return typeof val === 'string' || val instanceof String;
}

/**
 * 金额格式化展示，整数部分添加千位分隔符，单位、小数部分缩小展示
 * example:
 * <money :amount="100000"> => ￥1,000.00
 * <money amount="123456.78"> => ￥123,456.78
 */
export default {
    name: 'Money',
    filters: {
        formatNumberThousand,
    },
    props: {
        amount: {
            type: [String, Number],
            required: true,
        },
        showUnit: {
            type: Boolean,
            default: true,
        },
    },
    computed: {
        integer() {
            if (isString(this.amount)) { // 字符串保留正负号
                const idx = this.amount.indexOf('.');
                return (idx > -1 ? this.amount.slice(0, idx) : this.amount) || '0';
            }
            return (this.amount / 100).toFixed(2).slice(0, -3);
        },
        decimal() {
            return (isString(this.amount) ? Number(this.amount) : (this.amount / 100)).toFixed(2).slice(-2);
        },
    },
};
</script>

<style lang="scss">
.amount {
    // font-size: 16px;

    .unit, .decimal {
        font-size: 0.75em;
    }
}
</style>
