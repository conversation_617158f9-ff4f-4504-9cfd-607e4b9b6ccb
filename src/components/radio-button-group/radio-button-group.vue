<template>
    <div
        :class="size"
        class="radio-button-group"
    >
        <slot />
    </div>
</template>

<script>
export default {
    name: 'RadioButtonGroup',
    props: {
        value: {
            type: [Number, String],
            default: null,
        },
        size: {
            type: String,
            default: null,
        },
    },
    data() {
        return {
            currentValue: this.value,
        };
    },
    watch: {
        value(val) {
            this.currentValue = val;
        },
    },
};
</script>

<style lang="scss">
.radio-button-group {
    display: inline-block;
    line-height: 1;
    vertical-align: middle;
    font-size: 0;
}

.radio-button {
    display: inline-block;
    min-width: 70px;
    padding: 7px 10px;
    border: 1px solid #E9EAF2;
    border-left: none;
    background: #FFF;
    color: #858692;
    font-size: 14px;
    line-height: 20px;
    text-align: center;
    cursor: pointer;

    &:first-child {
        border-left: 1px solid #E9EAF2;
        border-top-left-radius: 3px;
        border-bottom-left-radius: 3px;
    }
    &:last-child {
        border-top-right-radius: 3px;
        border-bottom-right-radius: 3px;
    }

    &:hover {
        background: #FAFAFA;
    }

    &.is-active {
        background: #585A6E;
        border-color: #585A6E;
        box-shadow: -1px 0 0 0 #585A6E;
        color: #FFF;
    }
}

.radio-button-group.small .radio-button {
    min-width: 50px;
    font-size: 12px;
    padding: 5px 10px;
}
.radio-button-group.mini .radio-button {
    min-width: 45px;
    font-size: 12px;
    padding: 3px 8px;
}
</style>
