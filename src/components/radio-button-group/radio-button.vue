<template>
    <div
        :class="active ? 'is-active' : null"
        class="radio-button"
        @click="handleClick"
    >
        <slot />
    </div>
</template>

<script>
export default {
    name: 'RadioButton',
    props: {
        val: {
            type: [String, Number],
            required: true,
        },
    },
    computed: {
        active() {
            return this.val === this.$parent.currentValue;
        },
    },
    methods: {
        handleClick() {
            if (this.active) {
                return;
            }
            this.$parent.currentValue = this.val;
            this.$parent.$emit('input', this.val);
            this.$parent.$emit('change', this.val);
        },
    },
};
</script>
