<template>
    <div>
        <roo-modal v-model="localDisplayModal" size="lg">
            <p class="unBindContent">
                短信验证码已发送至您账号绑定的手机号（尾号{{ maskedMobile }}），请输入验证码后，完成开票资质提交。
            </p>
            <div class="input-container">
                <input
                    v-model="smsCode"
                    placeholder="请输入短信验证码"
                    :class="sendSmsError ? 'sendSmsErrorInput' : 'input'"
                />
                <span v-if="checkSuccessCode" class="success">v</span>
                <div v-else>
                    <span v-if=" countdown > 0" class="send-codes">{{ countdown }}s后重新发送</span>
                    <span v-else class="send-code" @click="handleSendSms">获取验证码</span>
                </div>
            </div>
            <span v-if="sendSmsError" :class="sendSmsError ? 'sendSmsError' : ''">{{ sendSmsError }}</span>
            <div v-show="needVerify" class="yodaWrap">
                <div id="yoda-root" class="yodaModal"></div>
            </div>
            <!-- <mtd-tag v-if="yodaStatus === 'success'" theme="green" style="margin-top: 18px">
                验证成功
            </mtd-tag
            <mtd-tag v-if="yodaStatus === 'fail'" theme="red" style="margin-top: 18px">
                验证失败
            </mtd-tag> -->
            <!-- <pre v-if="yodaStatus">{{ yodaResult }}</pre> -->
            <div style="text-align: right; margin: 24px 0px">
                <roo-button
                    type="hollow"
                    @click="closeModal"
                >
                    取消
                </roo-button>
                <roo-button
                    type="brand"
                    :disabled="!canSubmit"
                    @click="handleSubmit"
                >
                    确认提交
                </roo-button>
            </div>
        </roo-modal>
        <roo-modal v-model="isBindingMobile">
            <div class="unBindTitle">
                <i class="roo-icon roo-icon-info-circle" style="font-size: 24px;color: #FF6A00"></i>
                <p>您的账号未绑定手机号，无法进行验证。</p>
            </div>
            <span class="unBindContent">自2025年1月23日起，商家在平台上新增或修改开票资质，提交时需要输入验证码以完成验证。请您绑定手机号后，再提交申请。</span>
            <div class="unBindBtn">
                <roo-button
                    type="hollow"
                    @click="isBindingMobile = false"
                >
                    取消
                </roo-button>
                <roo-button @click="goAccountSetting()">
                    去绑定
                </roo-button>
            </div>
        </roo-modal>
    </div>
</template>

<script>
import { toast } from '@roo/roo-vue';
import request from '../../pages/invoice/utils/request';
import { getCookieValue, getHosts } from '$lib/utils';
import loadSeedJs from '../../pages/invoice/utils/yoda';


export default {
    name: 'SmsYodaVerification',
    props: {
        displayModal: {
            type: Boolean,
            default: false,
        },
        checkSmsError: {
            type: String,
            required: true,
        },
        isSuccessCode: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {
            localDisplayModal: this.displayModal,
            smsCode: '',
            countdown: 0,
            timer: null,
            isValidCode: false,
            serverCode: '',
            mobile: '',
            isBindingMobile: false,
            yodaStatus: '',
            needVerify: false,
            yodaResult: {},
            smsCodes: '',
            sendSmsError: this.checkSmsError,
            checkSuccessCode: this.isSuccessCode,
            // env: 'test',
        };
    },
    computed: {
        maskedMobile() {
            return this.mobile ? this.mobile.slice(-4) : '';
        },
        canSubmit() {
            return this.smsCode && this.mobile;
        },
        isEnv() {
            const envMap = {
                prod: 'production',
                stage: 'staging',
                test: 'test',
            };
            return envMap[process.env.DEPLOY_ENV] || 'production';
        },
    },
    watch: {
        displayModal(newVal) {
            this.localDisplayModal = newVal;
        },
        smsCode(newVal) {
            this.smsCodes = newVal;
            this.$emit('update:smsCodes', newVal); // 传递给父组件
        },
        checkSmsError(newVal) {
            this.sendSmsError = newVal;
        },
        isSuccessCode(newVal) {
            this.checkSuccessCode = newVal;
        },
    },
    mounted() {
        loadSeedJs();
        window.yodaSucc = this.yodaSucc;
        window.yodaFail = this.yodaFail;
    },
    methods: {
        // 获取手机号
        async getMobile() {
            try {
                const res = await request.get('/finance/invoice/api/output/waimai/q/getMobile');
                const { code, data, msg } = res.data;
                if (code === 0) {
                    if (data) {
                        this.mobile = data.mobile;
                        this.localDisplayModal = true;
                        this.$emit('update:displayModal', true);
                        this.$emit('update:mobile', data.mobile);
                    } else {
                        this.isBindingMobile = true;
                    }
                } else {
                    toast.error(msg || '获取手机号失败');
                }
            } catch (err) {
                toast.error(err.message || '获取手机号失败');
            }
        },
        // 发送验证码
        async handleSendSms() {
            if (!this.mobile) {
                toast.error('手机号无效，请绑定有效的手机号');
                return;
            }
            try {
                this.isValidCode = false;
                const res = await request.get('/finance/invoice/api/output/waimai/q/sendVerificationCode', {
                    params: {
                        mobile: this.mobile,
                        ua: this.getUserAgent(),
                        uuid: getCookieValue('device_uuid'),
                    },
                });
                const { code, msg, data } = res.data;
                if (msg && msg.request_code) {
                    this.initYoda(msg.request_code);
                    return;
                }
                if (code === 0) {
                    this.serverCode = data;
                    this.startCountdown();
                } else {
                    if (msg) {
                        if (msg.code === 121038 || msg.code === 121047) {
                            toast.warn(msg.message || '操作过于频繁，请60秒后重试');
                            this.startCountdown();
                            return;
                        }
                        if (msg.message && !msg.request_code) {
                            toast.error(msg.message || '获取验证码失败');
                            return;
                        }
                    }
                    toast.error(msg || '获取验证码失败');
                }
            } catch (err) {
                toast.error(err.message || '获取验证码失败');
            }
        },
        startCountdown() {
            this.countdown = 60;
            this.timer = setInterval(() => {
                if (this.countdown > 0) {
                    this.countdown--;
                } else {
                    clearInterval(this.timer);
                }
            }, 1000);
        },

        initYoda(code) {
            this.yodaStatus = '';
            this.needVerify = true;
            const options = {
                requestCode: code,
                root: 'yoda-root',
                succCallbackFun: 'yodaSucc',
                failCallbackFun: 'yodaFail',
            };
            if (typeof window.YodaSeed === 'function') {
                window.YodaSeed(options, this.isEnv);
            } else {
                console.error('window.YodaSeed is not a function or not defined');
            }
        },
        yodaSucc(options) {
            this.yodaStatus = 'success';
            this.yodaResult = options;
            this.needVerify = false;
            this.$mtd.message.success('验证成功');
            this.handleSendSms();
        },
        yodaFail(options) {
            this.yodaStatus = 'fail';
            this.yodaResult = options;
            this.needVerify = false;
            this.$mtd.message.error('验证失败');
        },
        getUserAgent() {
            return navigator.userAgent;
        },
        handleSubmit() {
            this.$emit('submit');
        },
        closeModal() {
            this.localDisplayModal = false;
            this.$emit('update:displayModal', false);
        },
        goAccountSetting() {
            this.isBindingMobile = false;
            window.open(`${getHosts()}#/new_fe/business#/shop/accountSettings`, '_blank');
        },
    },
};
</script>

<style lang="scss">
.unBindTitle {
    display: flex;
    align-items: center;
    p {
        font-size: 16px;
        font-weight: 500;
        color: #222222;
        margin-left: 10px;
        font-family: PingFang SC;
    }
}
.unBindContent {
    font-family: PingFang SC;
    font-size: 14px;
    color: #222;
    margin-top: 7px;
    display: inline-block;
}
.unBindBtn {
    text-align: right;
    margin-bottom: 24px;
}
.smsWrapper {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-top: 12px;
    .roo-input {
        flex: 1;
    }
    .smsBtn {
        min-width: 100px;
        white-space: nowrap;
        &[disabled] {
            color: #c5c8ce;
            cursor: not-allowed;
        }
    }
}
.input{
    // border-color: red !important;
    // z-index: 999;
    border: 1px solid #cccc;
}
.input-container {
  position: relative;
  width: 279px;
  height: 36px;
}
.input-container input {
    margin-top: 10px;
    width: 100%;
    height: 100%;
    border-radius: 2px;
    opacity: 1;
    background: #FFFFFF;
}
.input-container .send-code {
  position: absolute;
  right: 10px;
  top: 80%;
  transform: translateY(-50%);
  cursor: pointer;
  color: #FA6400;
}
.input-container .send-codes {
  position: absolute;
  right: 10px;
  top: 80%;
  transform: translateY(-50%);
  cursor: pointer;
  color: #ccc;
}
.input-container .success {
    position: absolute;
    right: 10px;
    top: 80%;
    transform: translateY(-50%);
    cursor: pointer;
    width: 17.5px;
    height: 17.5px;
    border-radius: 100%;
    background: #00BF7F;
    color: #FFFFFF;
    display: inline-block;
    text-align: center;
    line-height: 14.5px;
}
.sendSmsError {
  border-color: red;
  color: red;
  margin-top: 10px;
  display: inline-block;
}
.sendSmsErrorInput {
  border: 1px solid red;
}
.codeModal {
    &:global(.roo-modal .roo-modal-dialog .roo-modal-content .roo-modal-body) {
        background: #eee;
    }
}
.yodaWrap{
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,.5);
    position: fixed;
    top: 0;
    left: 0;
    z-index: 999;
}
.yodaModal{
    margin: 10% auto;
}
</style>
