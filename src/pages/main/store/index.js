import Vue from 'vue';
import Vuex from 'vuex';

import home from './modules/home';
import download from './modules/download';
import accountList from './modules/account-list';
import accountFlow from './modules/account-flow';
import settleBillList from './modules/settle-bill-list';
import billDetail from './modules/bill-detail';
import withdraw from './modules/withdraw';
import withdrawDetail from './modules/withdraw-detail';
import mutilBillDetail from './modules/mutil-bill-detail';
import dailyBill from './modules/daily-bill';
import accountInfo from './modules/account-info';
import orderQuery from './modules/order-query';

Vue.use(Vuex);

const strict = process.env.NODE_ENV !== 'production';

export default new Vuex.Store({
    modules: {
        home,
        download,
        accountList,
        accountFlow,
        settleBillList,
        billDetail,
        withdraw,
        withdrawDetail,
        mutilBillDetail,
        dailyBill,
        accountInfo,
        orderQuery,
    },
    strict,
});
