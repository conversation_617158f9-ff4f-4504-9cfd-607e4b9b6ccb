import axios from 'axios';
/* eslint-disable import/no-unresolved, import/extensions */
import request from '$src/utils/request';

const { CancelToken } = axios;

const initState = {
    loading: false,
    settledBillList: {
        pageCount: 0,
        settleBillList: [],
    },
    unSettledBillList: [],
};

const mutations = {
    changeLoading(st, loading) {
        st.loading = loading;
    },
    changeSettledBillList(st, data) {
        st.settledBillList = data;
    },
    changeUnSettledBillList(st, data) {
        st.unSettledBillList = data;
    },
};


let source1 = null;
function getSettledBillList(parameters) {
    if (source1) {
        source1.cancel();
        source1 = null;
    }

    source1 = CancelToken.source();

    return request.get('/finance/pc/api/poiSettleBill/historySettleBillList', {
        params: parameters,
        cancelToken: source1.token,
    });
}

const actions = {
    fetchSettledBillList({ commit }, parameters) {
        commit('changeLoading', true);

        return getSettledBillList(parameters).then((res) => {
            commit('changeLoading', false);
            const { code, msg, data } = res.data;

            if (code !== 0 && code !== 2013) {
                throw new Error(msg);
            }

            commit('changeSettledBillList', data || {
                pageCount: 0,
                settleBillList: [],
            });// 空列表需要特殊处理
        }, (err) => {
            commit('changeLoading', false);

            throw err;
        });
    },
    fetchUnSettledBillList({ commit }, parameters) {
        commit('changeLoading', true);

        return request.get('/finance/pc/api/poiSettleBill/unSettleBill', {
            params: parameters,
        }).then((res) => {
            commit('changeLoading', false);
            const { code, msg, data } = res.data;

            if (code !== 0) {
                throw new Error(msg);
            }
            commit('changeUnSettledBillList', data && [data]);
        }, (err) => {
            commit('changeLoading', false);
            throw err;
        });
    },
};

export default {
    namespaced: true,
    state: initState,
    mutations,
    actions,
};
