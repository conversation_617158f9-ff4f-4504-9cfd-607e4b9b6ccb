import axios from 'axios';
/* eslint-disable import/no-unresolved, import/extensions */
import request from '$src/utils/request';

const { CancelToken } = axios;

const initState = {
    loading: false,
    shopDetails: null,
};

const mutations = {
    changeLoading(st, loading) {
        st.loading = loading;
    },
    changeShopDetail(st, data) {
        st.shopDetails = data;
    },
};


let source1 = null;
function getShopDetail(params) {
    if (source1) {
        source1.cancel();
        source1 = null;
    }

    source1 = CancelToken.source();

    return request.get('/finance/pc/api/poiSettleBill/settleSettingBillDetail', {
        params,
        cancelToken: source1.token,
    });
}


const actions = {
    fetchShopDetail({ commit }, params) {
        commit('changeLoading', true);

        return getShopDetail(params).then((res) => {
            commit('changeLoading', false);

            const { data, msg, code } = res.data;

            if (code !== 0) {
                throw new Error(msg);
            }

            commit('changeShopDetail', data);
        }, (err) => {
            commit('changeLoading', false);
            throw err;
        });
    },
};

export default {
    namespaced: true,
    state: initState,
    mutations,
    actions,
};
