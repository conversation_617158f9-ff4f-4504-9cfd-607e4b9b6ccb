/* eslint-disable import/no-unresolved, import/extensions */
import request from '$src/utils/request';

const initState = {
    loading: false,
    poiInfo: '',
    billList: [],
    wmPoiId: '',
    orderViewId: '',
};

const mutations = {
    changeLoading(st, loading) {
        st.loading = loading;
    },
    changePoiInfo(st, poiInfo) {
        st.poiInfo = poiInfo;
    },
    changeBillList(st, wmPoiBillChargeDynamicVoList) {
        st.billList = wmPoiBillChargeDynamicVoList;
    },
    changePoiId(st, wmPoiId) {
        st.wmPoiId = wmPoiId;
    },
    changeOrderViewId(st, orderViewId) {
        st.orderViewId = orderViewId;
    },
};

const actions = {
    fetchOrderInfo({ commit }, params) {
        commit('changeLoading', true);
        commit('changePoiInfo', '');
        commit('changeBillList', []);

        return request.get('/finance/pc/api/poiBillCharge/billChargeListForOrder', {
            params,
        }).then((res) => {
            commit('changeLoading', false);

            const { code, msg, data } = res.data;
            if (code !== 0) {
                throw new Error(msg);
            }

            commit('changePoiInfo', data.poiInfo);
            commit('changeBillList', data.wmPoiBillChargeDynamicVoList);
            commit('changePoiId', data.wmPoiId);
        }, (err) => {
            commit('changeLoading', false);

            throw err;
        });
    },
    changeOrderViewId({ commit }, orderViewId) {
        commit('changeOrderViewId', orderViewId);
    },
};

export default {
    namespaced: true,
    state: initState,
    mutations,
    actions,
};
