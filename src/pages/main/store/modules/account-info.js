/* eslint-disable import/no-unresolved, import/extensions */
import request from '$src/utils/request';

const initState = {
    loading: false,
    accountInfo: {
        settlePeriod: '',
        settleTypeName: '周期结算',
        minWithdrawAmount: '0.00',
        serviceManagerName: '',
        serviceManagerMobile: '',
        bankAccountName: '',
        bank: '',
        cardNo: '',
        payBindType: 1,
        walletId: 0,
        walletFlowUrl: '',
    },
};

const mutations = {
    changeLoading(st, loading) {
        st.loading = loading;
    },
    changeAccountInfo(st, data) {
        st.accountInfo = data;
    },
};

const actions = {
    fetchAccountInfo({ commit }, params) {
        commit('changeLoading', true);

        return request.get('/finance/pc/api/account/accountSettleSettingInfo', {
            params,
        }).then((res) => {
            commit('changeLoading', false);

            const { code, msg, data } = res.data;
            if (code !== 0) {
                throw new Error(msg);
            }

            commit('changeAccountInfo', data);
        }, (err) => {
            commit('changeLoading', false);

            throw err;
        });
    },
};

export default {
    namespaced: true,
    state: initState,
    mutations,
    actions,
};
