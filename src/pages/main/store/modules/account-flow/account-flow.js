
import axios from 'axios';
/* eslint-disable import/no-unresolved, import/extensions */
import request from '$src/utils/request';

const { CancelToken } = axios;

const initialState = {
    params: {},

    loading: false,

    total: 0,
    pageNo: 1,
    pageSize: 10,
    list: [],
};

const mutations = {
    changeParams(state, params) {
        state.params = params;
    },
    changeLoading(state, loading) {
        state.loading = loading;
    },
    changeTotal(state, total) {
        state.total = total || 0;
    },
    changePageNo(state, pageNo) {
        state.pageNo = pageNo || 1;
    },
    changeList(state, list) {
        state.list = list || [];
    },
};

// 根据 params 获取对应的 Url
function getApiUrl(params) {
    // 多门店
    if (params.financeAccountId) {
        return '/finance/pc/api/accountFlow/multiMainAccountFlows';
    }
    // 余额流水
    if (params.acctType === 1008) {
        return '/finance/pc/api/accountFlow/poiMainAccountFlows';
    }
    // 其他账户流水
    return '/finance/pc/api/accountFlow/assetAccountFlows';
}

let source = null;

function getAccountFlows(params) {
    if (source) {
        source.cancel();
        source = null;
    }

    source = CancelToken.source();

    const url = getApiUrl(params);
    return request.get(url, {
        params,
        cancelToken: source.token,
    });
}

const actions = {
    fetchAccountFlows({ state, commit }, pageNo) {
        const { params, pageSize } = state;

        commit('changeLoading', true);
        return getAccountFlows(Object.assign({ pageNo, pageSize }, params))
            .then((res) => {
                commit('changeLoading', false);

                const { code, msg, data } = res.data;

                if (code !== 0 && code !== 2002) { // 查询列表为空时 code 为 2002
                    throw new Error(msg);
                }

                commit('changeTotal', data.pageCount);
                commit('changeList', data.flowList || data.wmAssetAccountFlowResponseList);
                commit('changePageNo', pageNo);
            }, (err) => {
                commit('changeLoading', false);
                throw err;
            });
    },
};

export default {
    namespaced: true,
    state: initialState,
    mutations,
    actions,
};
