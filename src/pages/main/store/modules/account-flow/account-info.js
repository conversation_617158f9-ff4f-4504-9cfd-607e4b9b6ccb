/* eslint-disable import/no-unresolved, import/extensions */
import request from '$src/utils/request';

const DEFAULT_AVATAR = 'http://s3plus.meituan.net/v1/mss_c4375b35f5cb4e678b5b55a48c40cf9d/waimai-fem-service-market/waimai.png';

const smartPageSize = 5;

const initialState = {
    loading: false,

    name: '',
    avatar: DEFAULT_AVATAR,

    mainBalance: '0.00',
    showMainAccountRecharge: false,
    showMainAccountWithdraw: false,
    frozen: false,

    // 推广费账户 余额与按钮显示状态不一致
    showAdAccount: false,
    adBalance: '0.00',
    showAdAccountRecharge: false,
    showAutoTransfer: false,

    // 其他账户（保证金、配送费）显示逻辑与充值按钮一致
    showDepositAccount: false,
    depositBalance: '0.00',
    showDepositAccountRechargeFlag: false,
    supportDepositAccountSelfWithdraw: false,

    // 售后红包账户
    showFoodSafetyRedPacketAccount: false,
    foodSafetyRedPacketBalance: '0.00',

    // 推广红包账户
    showRedPacketAccount: false,
    redPacketBalance: '0.00',
    intelligentActivityBalance: '0.00',

    // 配送费充值按钮已下 最多展示余额
    showLogisticsAccount: false,
    logisticsBalance: '0.00',

    pageNo: 1,
    bussinessName: '',
    bussinessCount: 0,
    bussinessAmount: '',
    pageCount: 0,
    pageSize: 10,
    total: 0,
    poiIncomeTotalAmount: 0, // 商家节省金额总计
    platformPayTotalAmount: 0, // 平台代付金额
    poiRealIncomeTotalAmount: 0, // 商家实际入账金额
    orders: [],
    flowNo: -1,
    wmPoiId: -1,
    settleBillComment: 0, // 结算账期
    flowNo1: -1,
    smartPageSize,
    // 是否显示合规内容
    showSpecialItem: true,
};

const mutations = {
    changeLoading(state, loading) {
        state.loading = loading;
    },

    changeSmartInfo(state, data) {
        state.flowNo = data.flowNo;
        state.wmPoiId = data.wmPoiId;
        state.pageSize = data.pageSize;
        state.total = data.pageCount;
        state.pageNo = data.pageNo || 1;
        state.pageCount = data.pageCount;
        state.poiIncomeTotalAmount = data.poiIncomeTotalAmount; // 商家节省金额总计
        state.platformPayTotalAmount = data.platformPayTotalAmount; // 平台代付金额
        state.poiRealIncomeTotalAmount = data.poiRealIncomeTotalAmount; // 商家实际入账金额
        state.settleBillComment = data.settleBillComment; // 结算账期
        state.flowNo1 = data.flowNo1; // 流水单号
        state.orders = data.orders || [];
    },
    changeAccountInfo(state, data) {
        state.name = data.bankAccountName || '';
        state.avatar = data.picUrl || DEFAULT_AVATAR;

        state.mainBalance = data.mainBalance || '0.00';
        state.showMainAccountRecharge = !!data.showMainAccountRechargeFlag;
        state.showMainAccountWithdraw = !!data.supportMasterAccountSelfWithdraw;
        state.frozen = !!data.isFrozenFlag;

        state.showAdAccount = !!data.showAdAccount;
        state.adBalance = data.adBalance || '0.00';
        state.showAdAccountRecharge = !!data.showAdAccountRechargeFlag;
        state.showAutoTransfer = !!data.showAutoTransferFlag;

        state.showDepositAccount = !!data.showDepositAccount;
        state.showDepositAccountRechargeFlag = !!data.showDepositAccountRechargeFlag;
        state.supportDepositAccountSelfWithdraw = !!data.supportDepositAccountSelfWithdraw;
        state.depositBalance = data.depositBalance || '0.00';

        state.showRedPacketAccount = !!data.showAdRedPacketRechargeFlag;
        state.redPacketBalance = data.adRedPacketBalance || '0.00';
        state.highCommissionReturnBalance = data.highCommissionReturnBalance || '0.00'; // 推广红包账户 高佣返余额
        state.tradeToPromotionBalance = data.tradeToPromotionBalance || '0.00'; // 推广红包账户 交易费转推广费余额
        state.intelligentActivityBalance = data.intelligentActivityBalance || '0.00'; // 推广红包账户 智能活动金额

        state.showLogisticsAccount = !!data.showCrowdSourcingAccountRechargeFlag;
        state.logisticsBalance = data.logisticsBalance || '0.00';

        state.bussinessName = data.bussinessName || '';
        state.bussinessCount = data.bussinessCount || 0;
        state.bussinessAmount = data.bussinessAmount || '';

        // 是否展示售后红包账户
        state.showFoodSafetyRedPacketAccount = !!data.showFoodSafetyRedPacketAccount;
        // 售后红包账户金额
        state.foodSafetyRedPacketBalance = data.foodSafetyRedPacketBalance || '0.00';
    },
    resetSmart(state) {
        state.poiIncomeTotalAmount = 0;
        state.platformPayTotalAmount = 0;
        state.poiRealIncomeTotalAmount = 0;
        state.settleBillComment = 0;
        state.flowNo1 = -1;
        state.orders = [];
    },
    reset(state) {
        Object.assign(state, initialState);
    },
    changeShowSpecialItem(state, showSpecialItem) {
        state.showSpecialItem = showSpecialItem;
    },
};

const actions = {
    fetchAccountInfoByWmPoiId({ commit }, wmPoiId) {
        commit('reset');
        commit('changeLoading', true);
        return request
            .get('/finance/pc/api/account/poiAccountBaseInfo', {
                params: { wmPoiId },
            })
            .then(
                (res) => {
                    commit('changeLoading', false);

                    const { code, msg, data } = res.data;
                    if (code !== 0) {
                        throw new Error(msg);
                    }

                    commit('changeAccountInfo', data);
                },
                (err) => {
                    commit('changeLoading', false);
                    throw err;
                },
            );
    },
    fetchAccountInfoByAccountId({ commit }, financeAccountId) {
        commit('reset');
        commit('changeLoading', true);
        return request
            .get('/finance/pc/api/multiAccount/multiAccountBaseInfo', {
                params: { financeAccountId },
            })
            .then(
                (res) => {
                    commit('changeLoading', false);

                    const { code, msg, data } = res.data;
                    if (code !== 0) {
                        throw new Error(msg);
                    }

                    commit('changeAccountInfo', data);
                },
                (err) => {
                    commit('changeLoading', false);
                    throw err;
                },
            );
    },
    // 智能满减
    fetchSmartInfo({ commit }, params) {
        commit('resetSmart');
        const { flowNo, wmPoiId, pageNo } = params;
        return request
            .get('/finance/pc/api/poiBillCharge/adZNMJSettleBill', {
                params: {
                    flowNo,
                    wmPoiId,
                    pageNo,
                    pageSize: smartPageSize,
                },
            })
            .then(
                (res) => {
                    const { code, msg, data } = res.data;
                    if (code !== 0) {
                        throw new Error(msg);
                    }
                    data.flowNo1 = data.flowNo;
                    data.flowNo = flowNo;
                    data.wmPoiId = wmPoiId;
                    data.pageNo = pageNo;
                    commit('changeSmartInfo', data);
                },
                (err) => {
                    commit('changeLoading', false);
                    throw err;
                },
            );
    },
    /**
     * 合规检查专用方法，以下AcctId会隐藏部分功能
     * ad_demo的acctId=102647509
     * poi_demo的acctId=128929787
     */
    fetchShowSpecialItem({ commit }, params) {
        let show = true;
        const appType = 3;
        const { token, acctId, wmPoiId } = params;
        const url = `/gw/api/mock/condition?wmPoiId=${wmPoiId}&appType=${appType}&acctId=${acctId}&token=${token}`;
        request.get(url).then((res) => {
            const { data } = res;
            if (data && data.data && data.data.hidden === true) {
                show = false;
            }
            commit('changeShowSpecialItem', show);
        });
    },
};

export default {
    namespaced: true,
    state: Object.assign({}, initialState),
    mutations,
    actions,
};
