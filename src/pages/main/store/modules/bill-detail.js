/* eslint-disable import/no-unresolved, import/extensions */
import request from '$src/utils/request';

const initState = {
    loading: false,
    billDetails: null,
};

const mutations = {
    changeLoading(st, loading) {
        st.loading = loading;
    },
    changeBillDetail(st, data) {
        st.billDetails = data;
    },
};

const actions = {
    fetchBillDetail({ commit }, params) {
        commit('changeLoading', true);

        return request.get('/finance/pc/api/poiSettleBill/settleBillDetail', {
            params,
        }).then((res) => {
            commit('changeLoading', false);

            const { code, msg, data } = res.data;
            if (code !== 0) {
                throw new Error(msg);
            }

            commit('changeBillDetail', data);
        }, (err) => {
            commit('changeLoading', false);

            throw err;
        });
    },
};

export default{
    namespaced: true,
    state: initState,
    mutations,
    actions,
};
