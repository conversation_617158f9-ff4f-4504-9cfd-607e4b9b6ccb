import axios from 'axios';
/* eslint-disable import/no-unresolved, import/extensions */
import request from '$src/utils/request';

const { CancelToken } = axios;

const DEFAULT_FEE_TYPE_LIST = [{
    title: '菜品原价',
    typeCode: '1',
    tips: '订单中菜品金额总计',
}, {
    title: '包装费用',
    typeCode: '6',
    tips: '餐盒和打包袋费用',
}, {
    title: '商家活动支出',
    typeCode: '2',
    tips: '订单中商家补贴的活动款金额',
}, {
    title: '平台服务费',
    typeCode: '3',
    tips: '订单抽佣费用',
}, {
    title: '配送费',
    typeCode: '4',
    tips: null,
}, {
    title: '公益捐款',
    typeCode: '7',
    tips: null,
}];

const initialState = {
    // 表头
    feeTypeList: DEFAULT_FEE_TYPE_LIST,

    // loading 状态 默认置为 true ，动态化后列表的加载稍有延迟，非 loading 状态会展示暂无数据

    orderBillLoading: true,
    orderBillList: [],
    orderBillTotal: 0,
    orderBillPageNo: 0,
    orderBillPageSize: 10,

    otherBillLoading: true,
    otherBillList: [],
    otherBillTotal: 0,
    otherBillPageNo: 0,
    otherBillPageSize: 10,

    returnBillLoading: true,
    returnBillList: [],
    returnBillTotal: 0,
    returnBillPageNo: 0,
    returnBillPageSize: 10,
    unFinishTip: {},
};

const mutations = {
    changeFeeTypeList(state, list) {
        state.feeTypeList = list || DEFAULT_FEE_TYPE_LIST;
    },
    changeOrderBillLoading(state, loading) {
        state.orderBillLoading = loading;
    },
    changeOrderBillList(state, list) {
        state.orderBillList = list || [];
    },
    changeOrderBillTotal(state, total) {
        state.orderBillTotal = total;
    },
    changeOrderBillPageNo(state, pageNo) {
        state.orderBillPageNo = pageNo;
    },
    changeOtherBillLoading(state, loading) {
        state.otherBillLoading = loading;
    },
    changeOtherBillList(state, list) {
        state.otherBillList = list || [];
    },
    changeOtherBillTotal(state, total) {
        state.otherBillTotal = total;
    },
    changeOtherBillPageNo(state, pageNo) {
        state.otherBillPageNo = pageNo;
    },
    changeReturnBillLoading(state, loading) {
        state.returnBillLoading = loading;
    },
    changeReturnBillList(state, list) {
        state.returnBillList = list || [];
    },
    changeReturnBillTotal(state, total) {
        state.returnBillTotal = total;
    },
    changeReturnBillPageNo(state, pageNo) {
        state.returnBillPageNo = pageNo;
    },
    changeUnFinishTip(state, data) {
        state.unFinishTip = data;
    },
};

let orderSource = null;
let otherSource = null;
let returnSource = null;
let unFinishTipSource = null

const actions = {
    fetchOrderBillChargeList({ state, commit }, params) {
        if (orderSource) {
            orderSource.cancel();
            orderSource = null;
        }

        commit('changeOrderBillLoading', true);
        orderSource = CancelToken.source();
        return request.get('/finance/pc/api/poiBillCharge/billChargeListForWeb', {
            params: Object.assign({ pageSize: state.orderBillPageSize }, params),
            cancelToken: orderSource.token,
        }).then((res) => {
            commit('changeOrderBillLoading', false);

            const { code, msg, data } = res.data;

            if (code !== 0) {
                throw new Error(msg);
            }

            commit('changeFeeTypeList', data.wmPoiBillChargeTitleVoList);
            commit('changeOrderBillList', data.wmPoiBillChargeDynamicVoList);
            commit('changeOrderBillTotal', data.count);
            commit('changeOrderBillPageNo', params.pageNo);
        }, (err) => {
            commit('changeOrderBillLoading', false);
            throw err;
        });
    },
    fetchOtherBillChargeList({ state, commit }, params) {
        if (otherSource) {
            otherSource.cancel();
            otherSource = null;
        }

        commit('changeOtherBillLoading', true);
        otherSource = CancelToken.source();
        return request.get('/finance/pc/api/poiBillCharge/billChargeListForWeb', {
            params: Object.assign({ pageSize: state.otherBillPageSize }, params),
            cancelToken: otherSource.token,
        }).then((res) => {
            commit('changeOtherBillLoading', false);

            const { code, msg, data } = res.data;

            if (code !== 0) {
                throw new Error(msg);
            }
            commit('changeOtherBillList', data.wmPoiBillChargeDynamicVoList);
            commit('changeOtherBillTotal', data.count);
            commit('changeOtherBillPageNo', params.pageNo);
        }, (err) => {
            commit('changeOtherBillLoading', false);
            throw err;
        });
    },
    fetchReturnBillChargeList({ state, commit }, params) {
        if (returnSource) {
            returnSource.cancel();
            returnSource = null;
        }

        commit('changeReturnBillLoading', true);
        returnSource = CancelToken.source();
        return request.get('/finance/pc/api/poiBillCharge/billChargeListForWeb', {
            params: Object.assign({ pageSize: state.returnBillPageSize }, params),
            cancelToken: returnSource.token,
        }).then((res) => {
            commit('changeReturnBillLoading', false);

            const { code, msg, data } = res.data;

            if (code !== 0) {
                throw new Error(msg);
            }
            commit('changeReturnBillList', data.wmPoiBillChargeDynamicVoList);
            commit('changeReturnBillTotal', data.count);
            commit('changeReturnBillPageNo', params.pageNo);
        }, (err) => {
            commit('changeReturnBillLoading', false);
            throw err;
        });
    },
    fetchUnFinishTip({ state, commit }, params) {
        if (unFinishTipSource) {
            unFinishTipSource.cancel();
            unFinishTipSource = null;
        }

        unFinishTipSource = CancelToken.source();
        return request.get('/finance/v4/h5/api/dailyBill/unFinishTip', {
            params,
            cancelToken: unFinishTipSource.token,
        }).then((res) => {
            const { code, msg, data } = res.data;    
            if (code !== 0) {
                throw new Error(msg);
            }
            commit('changeUnFinishTip', data);
        }, (err) => {
            throw err;
        });
    },
};

export default {
    namespaced: true,
    state: initialState,
    mutations,
    actions,
};
