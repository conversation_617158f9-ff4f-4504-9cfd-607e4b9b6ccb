
import axios from 'axios';
/* eslint-disable import/no-unresolved, import/extensions */
import request from '$src/utils/request';

const { CancelToken } = axios;

const initialState = {
    orderBillLoading: false,
    orderBillList: [],
    orderBillTotal: 0,
    orderBillPageNo: 0,
    orderBillPageSize: 10,

    otherBillLoading: false,
    otherBillList: [],
    otherBillTotal: 0,
    otherBillPageNo: 0,
    otherBillPageSize: 10,
};

const mutations = {
    changeOrderBillLoading(state, loading) {
        state.orderBillLoading = loading;
    },
    changeOtherBillLoading(state, loading) {
        state.otherBillLoading = loading;
    },
    updateOrderBillList(state, data) {
        state.orderBillTotal = data.pageCount || 0;
        state.orderBillList = data.billList || [];
        state.orderBillPageNo = data.pageNo || 1;
    },
    updateOtherBillList(state, data) {
        state.otherBillTotal = data.pageCount || 0;
        state.otherBillList = data.billList || [];
        state.otherBillPageNo = data.pageNo || 1;
    },
};

let source1 = null;
let source2 = null;

const actions = {
    fetchOrderBillChargeList({ state, commit }, params) { // dailyBillDate, billChargeTypeCode, pageNo, wmPoiId
        if (source1) {
            source1.cancel();
            source1 = null;
        }

        commit('changeOrderBillLoading', true);
        source1 = CancelToken.source();
        return request.get('/finance/pc/api/poiBillCharge/billChargeList', {
            params: Object.assign({ pageSize: state.orderBillPageSize }, params),
            cancelToken: source1.token,
        }).then((res) => {
            source1 = null;

            const { code, data, msg } = res.data;

            if (code !== 0 && code !== 2009) {
                throw new Error(msg);
            }

            commit('changeOrderBillLoading', false);
            commit('updateOrderBillList', data || {});
        }, (err) => {
            source1 = null;
            throw err;
        });
    },

    fetchOtherBillChargeList({ state, commit }, params) { // dailyBillDate, billChargeTypeCode, pageNo, wmPoiId
        if (source2) {
            source2.cancel();
            source2 = null;
        }

        commit('changeOtherBillLoading', true);
        source2 = CancelToken.source();
        return request.get('/finance/pc/api/poiBillCharge/billChargeList', {
            params: Object.assign({ pageSize: state.otherBillPageSize }, params),
            cancelToken: source2.token,
        }).then((res) => {
            source2 = null;

            const { code, data, msg } = res.data;

            if (code !== 0 && code !== 2009) {
                throw new Error(msg);
            }

            commit('changeOtherBillLoading', false);
            commit('updateOtherBillList', data || {});
        }, (err) => {
            source2 = null;
            throw err;
        });
    },
};

export default {
    namespaced: true,
    state: initialState,
    mutations,
    actions,
};
