import axios from 'axios';
/* eslint-disable import/no-unresolved, import/extensions */
import request from '$src/utils/request';

const { CancelToken } = axios;

const initialState = {
    loading: false,

    billChargeTypeDetails: [],
};

const mutations = {
    changeLoading(state, loading) {
        state.loading = loading;
    },
    changeBillChargeTypeDetails(state, list) {
        state.billChargeTypeDetails = list || [];
    },
};

let source = null;

const actions = {
    fetchBillChargeSummary({ commit }, params) {
        if (source) {
            source.cancel();
            source = null;
        }

        commit('changeLoading', true);
        source = CancelToken.source();
        return request.get('/finance/pc/api/poiBillCharge/billChargeSummaryDetailsForWeb', {
            params,
            cancelToken: source.token,
        }).then((res) => {
            commit('changeLoading', false);

            const { code, msg, data } = res.data;

            if (code !== 0) {
                throw new Error(msg);
            }

            commit('changeBillChargeTypeDetails', data.billChargeTypeDetails || []);
        }, (err) => {
            commit('changeLoading', false);
            throw err;
        });
    },
};

// 求全部订单的和
function merge(a, b, c, d) {
    const dailyBillFeeDetails = [];
    const aLen = a && a.dailyBillFeeDetails && a.dailyBillFeeDetails.length;
    const bLen = b && b.dailyBillFeeDetails && b.dailyBillFeeDetails.length;
    const cLen = c && c.dailyBillFeeDetails && c.dailyBillFeeDetails.length;
    const dLen = d && d.dailyBillFeeDetails && d.dailyBillFeeDetails.length;
    const forLen = aLen || bLen || cLen || dLen;

    if (forLen) {
        for (let i = 0; i < forLen; ++i) {
            const af = a && a.dailyBillFeeDetails && a.dailyBillFeeDetails[i];
            const bf = b && b.dailyBillFeeDetails && b.dailyBillFeeDetails[i];
            const cf = c && c.dailyBillFeeDetails && c.dailyBillFeeDetails[i];
            const df = d && d.dailyBillFeeDetails && d.dailyBillFeeDetails[i];
            dailyBillFeeDetails.push({
                billFeeTypeCode: af && af.billFeeTypeCode,
                billFeeTypeName: af && af.billFeeTypeName,
                feeAmountSum: (af ? af.feeAmountSum : 0) + (bf ? bf.feeAmountSum : 0) + (cf ? cf.feeAmountSum : 0) + (df ? df.feeAmountSum : 0),
                feeCount: (af ? af.feeCount : 0) + (bf ? bf.feeCount : 0) + (cf ? cf.feeCount : 0) + (df ? df.feeCount : 0),
            });
        }
    }
    return {
        billChargeCount: (a ? a.billChargeCount : 0) + (b ? b.billChargeCount : 0) + (c ? c.billChargeCount : 0) + (d ? d.billChargeCount : 0),
        billChargeAmountSum: (a ? a.billChargeAmountSum : 0) + (b ? b.billChargeAmountSum : 0) + (c ? c.billChargeAmountSum : 0) + (d ? d.billChargeAmountSum : 0),
        dailyBillFeeDetails,
    };
}

const getters = {
    /**
     * 所有订单类筛选项
     */
    orderAllFilter(state) {
        const arr = [];
        const { billChargeTypeDetails } = state;
        for (let i = 0; i < billChargeTypeDetails.length; ++i) {
            const item = billChargeTypeDetails[i];

            if (item.orderCategory === 1) {
                arr.push({
                    code: `${item.billChargeTypeCode}`,
                    name: item.billChargeTypeName,
                });
            }
        }

        return arr.map(x => x.code).join(',') || '1,2,16,17'; // 没有其他费用类型
    },
    /**
     * 所有其他类筛选项
     */
    otherAllFilter(state) {
        const arr = [];
        const { billChargeTypeDetails } = state;
        for (let i = 0; i < billChargeTypeDetails.length; ++i) {
            const item = billChargeTypeDetails[i];

            if (item.orderCategory !== 1 && item.orderCategory !== 3) {
                arr.push({
                    code: `${item.billChargeTypeCode}`,
                    name: item.billChargeTypeName,
                });
            }
        }

        return arr.map(x => x.code).join(',') || '3'; // 没有其他费用类型
    },
    /**
     * 所有服务费返还激励筛选项
     */
    returnAllFilter(state) {
        const arr = [];
        const { billChargeTypeDetails } = state;
        for (let i = 0; i < billChargeTypeDetails.length; ++i) {
            const item = billChargeTypeDetails[i];

            if (item.orderCategory === 3) {
                arr.push({
                    code: `${item.billChargeTypeCode}`,
                    name: item.billChargeTypeName,
                });
            }
        }

        return arr.map(x => x.code).join(',') || '33'; // 没有其他费用类型
    },
    /**
     * orderCategory = 1 ，为订单类别
     */
    orderFilters(state) {
        const arr = [];

        const { billChargeTypeDetails } = state;
        for (let i = 0; i < billChargeTypeDetails.length; ++i) {
            const item = billChargeTypeDetails[i];

            if (item.orderCategory === 1) {
                arr.push({
                    code: `${item.billChargeTypeCode}`,
                    name: item.billChargeTypeName,
                });
            }
        }

        return arr;
    },
    /**
     * orderCategory != 1 && orderCategory != 3 ，为其他类别
     */
    otherFilters(state) {
        const arr = [];

        const { billChargeTypeDetails } = state;
        for (let i = 0; i < billChargeTypeDetails.length; ++i) {
            const item = billChargeTypeDetails[i];

            if (item.orderCategory !== 1 && item.orderCategory !== 3) {
                arr.push({
                    code: `${item.billChargeTypeCode}`,
                    name: item.billChargeTypeName,
                });
            }
        }

        return arr;
    },
    /**
     * orderCategory = 3 ，为服务返还
     */
    returnFilters(state) {
        const arr = [];

        const { billChargeTypeDetails } = state;
        for (let i = 0; i < billChargeTypeDetails.length; ++i) {
            const item = billChargeTypeDetails[i];

            if (item.orderCategory === 3) {
                arr.push({
                    code: `${item.billChargeTypeCode}`,
                    name: item.billChargeTypeName,
                });
            }
        }

        return arr;
    },
    /**
     * 外卖订单 = 1
     */
    orderBillChargeSummary(state) {
        const { billChargeTypeDetails } = state;
        for (let i = 0; i < billChargeTypeDetails.length; ++i) {
            const item = billChargeTypeDetails[i];

            if (item.billChargeTypeCode === 1) {
                return item;
            }
        }
        return null;
    },
    /**
     * 订单退款 = 2
     */
    refundBillChargeSummary(state) {
        const { billChargeTypeDetails } = state;
        for (let i = 0; i < billChargeTypeDetails.length; ++i) {
            const item = billChargeTypeDetails[i];

            if (item.billChargeTypeCode === 2) {
                return item;
            }
        }
        return null;
    },
    /**
     * 部分退款 = 16
     */
    refundPartBillChargeSummary(state) {
        const { billChargeTypeDetails } = state;
        for (let i = 0; i < billChargeTypeDetails.length; ++i) {
            const item = billChargeTypeDetails[i];

            if (item.billChargeTypeCode === 16) {
                return item;
            }
        }
        return null;
    },
    /**
     * 部分退款冲抵 = 17
     */
    refundOffsetBillChargeSummary(state) {
        const { billChargeTypeDetails } = state;
        for (let i = 0; i < billChargeTypeDetails.length; ++i) {
            const item = billChargeTypeDetails[i];

            if (item.billChargeTypeCode === 17) {
                return item;
            }
        }
        return null;
    },
    allBillChargeSummary(state, {
        orderBillChargeSummary, refundBillChargeSummary, refundPartBillChargeSummary, refundOffsetBillChargeSummary,
    }) {
        // if (orderBillChargeSummary && !refundBillChargeSummary) {
        //     return orderBillChargeSummary;
        // } else if (!orderBillChargeSummary && refundBillChargeSummary) {
        //     return refundBillChargeSummary;
        // }
        if (orderBillChargeSummary || refundBillChargeSummary || refundPartBillChargeSummary || refundOffsetBillChargeSummary) {
            return merge(orderBillChargeSummary, refundBillChargeSummary, refundPartBillChargeSummary, refundOffsetBillChargeSummary);
        }
        return null;
    },
};

export default {
    namespaced: true,
    state: initialState,
    mutations,
    actions,
    getters,
};
