/**
 * 旧版未动态化逻辑
 */

import axios from 'axios';
/* eslint-disable import/no-unresolved, import/extensions */
import request from '$src/utils/request';

const { CancelToken } = axios;

// 从 billChargeTypeDetails 获取对应 code 的金额
function getFeeAmount(arr, code) {
    for (let i = 0; i < arr.length; i += 1) {
        if (arr[i].billFeeTypeCode === code) {
            return arr[i].feeAmountSum;
        }
    }
    return 0;
}

const initialState = {
    loading: false,

    orderBillChargeCount: 0,
    orderBillChargeAmountSum: 0,
    orderFoodAmount: 0,
    orderBoxAmount: 0,
    orderActAmount: 0,
    orderPlatformCommissionAmount: 0,
    orderShippingAmount: 0,
    orderDonationAmount: 0,

    refundBillChargeCount: 0,
    refundBillChargeAmountSum: 0,
    refundFoodAmount: 0,
    refundBoxAmount: 0,
    refundActAmount: 0,
    refundPlatformCommissionAmount: 0,
    refundShippingAmount: 0,
    refundDonationAmount: 0,
};

const mutations = {
    changeLoading(state, loading) {
        state.loading = loading;
    },
    updateBillChargeSummary(state, data) {
        // 先清空之前的数据
        state.orderBillChargeCount = 0;
        state.orderBillChargeAmountSum = 0;
        state.orderFoodAmount = 0;
        state.orderBoxAmount = 0;
        state.orderActAmount = 0;
        state.orderPlatformCommissionAmount = 0;
        state.orderShippingAmount = 0;
        state.orderDonationAmount = 0;
        state.refundBillChargeCount = 0;
        state.refundBillChargeAmountSum = 0;
        state.refundFoodAmount = 0;
        state.refundBoxAmount = 0;
        state.refundActAmount = 0;
        state.refundPlatformCommissionAmount = 0;
        state.refundShippingAmount = 0;
        state.refundDonationAmount = 0;

        // 有时可能没有退款订单
        for (let i = 0; i < data.length; i += 1) {
            if (data[i].billChargeTypeCode === 1) {
                const { dailyBillFeeDetails, billChargeCount, billChargeAmountSum } = data[i];

                state.orderBillChargeCount = billChargeCount;
                state.orderBillChargeAmountSum = billChargeAmountSum;

                state.orderFoodAmount = getFeeAmount(dailyBillFeeDetails, 1);
                state.orderBoxAmount = getFeeAmount(dailyBillFeeDetails, 6);
                state.orderActAmount = getFeeAmount(dailyBillFeeDetails, 2);
                state.orderPlatformCommissionAmount = getFeeAmount(dailyBillFeeDetails, 3);
                state.orderShippingAmount = getFeeAmount(dailyBillFeeDetails, 4);
                state.orderDonationAmount = getFeeAmount(dailyBillFeeDetails, 7);
            } else {
                const { dailyBillFeeDetails, billChargeCount, billChargeAmountSum } = data[i];

                state.refundBillChargeCount = billChargeCount;
                state.refundBillChargeAmountSum = billChargeAmountSum;

                state.refundFoodAmount = getFeeAmount(dailyBillFeeDetails, 1);
                state.refundBoxAmount = getFeeAmount(dailyBillFeeDetails, 6);
                state.refundActAmount = getFeeAmount(dailyBillFeeDetails, 2);
                state.refundPlatformCommissionAmount = getFeeAmount(dailyBillFeeDetails, 3);
                state.refundShippingAmount = getFeeAmount(dailyBillFeeDetails, 4);
                state.refundDonationAmount = getFeeAmount(dailyBillFeeDetails, 7);
            }
        }
    },
    resetState(state) {
        state.loading = false;
        state.orderBillChargeCount = 0;
        state.orderBillChargeAmountSum = 0;
        state.orderFoodAmount = 0;
        state.orderBoxAmount = 0;
        state.orderActAmount = 0;
        state.orderPlatformCommissionAmount = 0;
        state.orderShippingAmount = 0;
        state.orderDonationAmount = 0;
        state.refundBillChargeCount = 0;
        state.refundBillChargeAmountSum = 0;
        state.refundFoodAmount = 0;
        state.refundBoxAmount = 0;
        state.refundActAmount = 0;
        state.refundPlatformCommissionAmount = 0;
        state.refundShippingAmount = 0;
        state.refundDonationAmount = 0;
    },
};

let source = null;

const actions = {
    fetchBillChargeSummary({ commit }, params) {
        // 中断正在进行中的请求
        if (source) {
            source.cancel();
            source = null;
        }

        commit('changeLoading', true);
        source = CancelToken.source();
        const cancelToken = source.token;
        return request.get('/finance/pc/api/poiBillCharge/billChargeSummaryDetails', {
            params,
            cancelToken,
        }).then((res) => {
            source = null; // 请求成功清除 cancelToken

            const { code, data, msg } = res.data;
            if (code !== 0 && code !== 2019) {
                // 错误时置空状态
                commit('resetState');
                throw new Error(msg);
            }

            commit('changeLoading', false);
            commit('updateBillChargeSummary', data ? data.billChargeTypeDetails : []);
            return data;
        }, (err) => {
            // 错误时置空状态
            commit('resetState');
            throw err;
        });
    },
};

const getters = {
    allBillChargeCount(state) {
        return state.orderBillChargeCount + state.refundBillChargeCount;
    },
    allBillChargeAmountSum(state) {
        return state.orderBillChargeAmountSum + state.refundBillChargeAmountSum;
    },
    allFoodAmount(state) {
        return state.orderFoodAmount + state.refundFoodAmount;
    },
    allBoxAmount(state) {
        return state.orderBoxAmount + state.refundBoxAmount;
    },
    allActAmount(state) {
        return state.orderActAmount + state.refundActAmount;
    },
    allPlatformCommissionAmount(state) {
        return state.orderPlatformCommissionAmount + state.refundPlatformCommissionAmount;
    },
    allShippingAmount(state) {
        return state.orderShippingAmount + state.refundShippingAmount;
    },
    allDonationAmount(state) {
        return state.orderDonationAmount + state.refundDonationAmount;
    },
    // 汇总成对象
    orderBillChargeSummary(state) {
        return {
            billChargeCount: state.orderBillChargeCount,
            billChargeAmountSum: state.orderBillChargeAmountSum,
            foodAmount: state.orderFoodAmount,
            boxAmount: state.orderBoxAmount,
            actAmount: state.orderActAmount,
            platformCommissionAmount: state.orderPlatformCommissionAmount,
            shippingAmount: state.orderShippingAmount,
            donationAmount: state.orderDonationAmount,
        };
    },
    otherBillChargeSummary(state) {
        return {
            billChargeCount: state.refundBillChargeCount,
            billChargeAmountSum: state.refundBillChargeAmountSum,
            foodAmount: state.refundFoodAmount,
            boxAmount: state.refundBoxAmount,
            actAmount: state.refundActAmount,
            platformCommissionAmount: state.refundPlatformCommissionAmount,
            shippingAmount: state.refundShippingAmount,
            donationAmount: state.refundDonationAmount,
        };
    },
    allBillChargeSummary(state, geters) {
        return {
            billChargeCount: geters.allBillChargeCount,
            billChargeAmountSum: geters.allBillChargeAmountSum,
            foodAmount: geters.allFoodAmount,
            boxAmount: geters.allBoxAmount,
            actAmount: geters.allActAmount,
            platformCommissionAmount: geters.allPlatformCommissionAmount,
            shippingAmount: geters.allShippingAmount,
            donationAmount: geters.allDonationAmount,
        };
    },
};

export default {
    namespaced: true,
    state: initialState,
    mutations,
    actions,
    getters,
};
