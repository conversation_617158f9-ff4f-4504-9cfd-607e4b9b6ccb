/**
 * 今日预计收入接口子状态
 */

import dayjs from 'dayjs';
import axios from 'axios';
/* eslint-disable import/no-unresolved, import/extensions */
import request from '$src/utils/request';

const { CancelToken } = axios;

function formatDate(dateObj = new Date()) {
    return dayjs(dateObj).format('YYYY-MM-DD');
}

const initialState = {
    // 接口请求状态
    loading: false,

    // settleState  1已结算 0未结算
    settled: false,
    // 总金额
    totalAmount: '0.00',
    // 结算金额
    settleDate: formatDate(),
    // 周期账单开始时间
    settleBillStartDate: formatDate(),
    // 周期账单结束时间
    settleBillEndDate: formatDate(),
    // 订单类
    orderBillChargeAmount: '0.00',
    // 其他类
    otherBillChargeAmount: '0.00',
    // 服务费返还
    returnBillChargeAmount: '0.00',
};

const mutations = {
    changeLoadingState(state, loading) {
        state.loading = !!loading;
    },
    updateExpectedIncome(state, data) {
        state.settled = data.settleState === 1;
        state.totalAmount = data.totalAmount;
        state.settleDate = data.settleDate;
        if (data.settleBillStartDate) {
            state.settleBillStartDate = data.settleBillStartDate;
        }
        if (data.settleBillEndDate) {
            state.settleBillEndDate = data.settleBillEndDate;
        }

        // todo: check
        state.orderBillChargeAmount = data.billChargeTypeDetails[0].billChargeAmountSum;
        state.otherBillChargeAmount = data.billChargeTypeDetails[1].billChargeAmountSum;
        // eslint-disable-next-line no-mixed-operators
        state.returnBillChargeAmount = data.billChargeTypeDetails[2] && data.billChargeTypeDetails[2].billChargeAmountSum || '0.00';
    },
    // 错误时 重置为空状态
    resetState(state) {
        state.loading = false;
        state.settled = false;
        state.totalAmount = '0.00';
        state.settleDate = formatDate();
        state.settleBillStartDate = formatDate();
        state.settleBillEndDate = formatDate();
        state.orderBillChargeAmount = '0.00';
        state.otherBillChargeAmount = '0.00';
        state.returnBillChargeAmount = '0.00';
    },
};

// cancel token source
let source = null;

const actions = {
    fetchExpectedIncome({ commit }, params) { // { dailyBillDate, wmPoiId }
        if (source) {
            source.cancel();
            source = null;
        }

        commit('changeLoadingState', true);
        source = CancelToken.source();
        return request.get('/finance/pc/api/poiSettleBill/expectedIncome', {
            params,
            cancelToken: source.token,
        }).then((res) => {
            // 成功时清除 token
            source = null;

            const { code, data, msg } = res.data;
            if (code !== 0) {
                // 错误时置空状态
                commit('resetState');
                throw new Error(msg);
            }

            commit('changeLoadingState', false);
            commit('updateExpectedIncome', data);
            return data;
        }, (err) => {
            // 重置状态
            commit('resetState');
            throw err;
        }); // todo: cancel will throw an exception. use `axios.isCancel()` ?
    },
};

export default {
    namespaced: true,
    state: initialState,
    mutations,
    actions,
};
