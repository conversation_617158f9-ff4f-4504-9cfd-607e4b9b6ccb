
/* eslint-disable import/no-unresolved, import/extensions */
import request from '$src/utils/request';

import accountInfo from './accountInfo';
import recentFlows from './recentFlows';
import settleBillList from './settleBillList';

const initialState = {
    displayMessage: false,
    message: '',
};

const mutations = {
    changeDisplay(state, display) {
        state.displayMessage = !!display;
    },
    changeMessage(state, msg) {
        state.message = msg || '';
    },
};

const actions = {
    fetchDisplayMsg({ commit }) {
        return request.get('/finance/v3/h5/api/withdraw/withdrawDisplayMsg')
            .then((res) => {
                const { code, msg, data } = res.data;

                if (code !== 0) {
                    throw new Error(msg);
                }

                commit('changeDisplay', data.display);
                commit('changeMessage', data.message);
            });
    },
};

export default {
    namespaced: true,
    modules: {
        accountInfo,
        recentFlows,
        settleBillList,
    },
    state: initialState,
    mutations,
    actions,
};
