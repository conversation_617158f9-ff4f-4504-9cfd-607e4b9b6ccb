
/* eslint-disable import/no-unresolved, import/extensions */
import request from '$src/utils/request';

const initialState = {
    loading: false,
    settleBillList: [],
    unSettleBillList: [],
};

const mutations = {
    changeLoading(state, loading) {
        state.loading = loading;
    },
    changeSettleBillList(state, list) {
        state.settleBillList = list || [];
    },
    changeUnSettleBillList(state, list) {
        state.unSettleBillList = list || [];
    },
};

const actions = {
    fetchSettleBillList({ commit }) {
        commit('changeLoading', true);
        return request.get('/finance/pc/api/poiSettleBill/settleBillList')
            .then((res) => {
                commit('changeLoading', false);
                const { code, msg, data } = res.data;

                if (code !== 0) {
                    throw new Error(msg);
                }

                commit('changeSettleBillList', data.settleBillList || []);
                commit('changeUnSettleBillList', data.unSettleBillList || []);
            }, (err) => {
                commit('changeLoading', false);
                throw err;
            });
    },
};

export default {
    namespaced: true,
    state: initialState,
    mutations,
    actions,
};
