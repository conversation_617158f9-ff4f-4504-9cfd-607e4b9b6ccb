
/* eslint-disable import/no-unresolved, import/extensions */
import request from '$src/utils/request';

const initialState = {
    loading: false,
    flowList: [],
};

const mutations = {
    changeLoading(state, loading) {
        state.loading = loading;
    },
    changeFlowList(state, list) {
        state.flowList = list || [];
    },
};

const actions = {
    fetchRecentFlows({ commit }) {
        commit('changeLoading', true);
        return request.get('/finance/pc/api/accountFlow/poiAccountFlows?pageSize=4&pageNo=1')
            .then((res) => {
                commit('changeLoading', false);
                const { code, msg, data } = res.data;

                if (code !== 0) {
                    throw new Error(msg);
                }

                commit('changeFlowList', data.flowList);
            }, (err) => {
                commit('changeLoading', false);
                throw err;
            });
    },
};

export default {
    namespaced: true,
    state: initialState,
    mutations,
    actions,
};
