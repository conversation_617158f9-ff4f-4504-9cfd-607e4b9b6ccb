/* eslint-disable import/no-unresolved, import/extensions */
import request from '$src/utils/request';

const DEFAULT_AVATAR = 'http://s3plus.meituan.net/v1/mss_c4375b35f5cb4e678b5b55a48c40cf9d/waimai-fem-service-market/waimai.png';

const initialState = {
    loading: false,
    avatar: DEFAULT_AVATAR,
    accountName: '',
    balance: '0.00',
    frozen: false,
    showWithdraw: true,
};

const mutations = {
    changeLoading(state, loading) {
        state.loading = loading;
    },
    changeAvatar(state, url) {
        state.avatar = url || DEFAULT_AVATAR;
    },
    changeAccountName(state, name) {
        state.accountName = name || '';
    },
    changeBalance(state, balance) {
        state.balance = balance || '0.00';
    },
    changeFrozen(state, frozen) {
        state.frozen = !!frozen;
    },
    changeShowWithdraw(state, show) {
        state.showWithdraw = show;
    },
};
const actions = {
    fetchAccountBaseInfo({ commit }, params) {
        commit('changeLoading', true);
        return request.get('/finance/pc/api/account/poiAccountBaseInfo').then(
            (res) => {
                commit('changeLoading', false);
                const { code, msg, data } = res.data;

                if (code !== 0) {
                    throw new Error(msg);
                }

                commit('changeAvatar', data.picUrl);
                commit('changeAccountName', data.bankAccountName);
                commit('changeBalance', data.mainBalance);
                commit('changeFrozen', data.isFrozenFlag);
                commit('changeShowWithdraw', !!data.supportMasterAccountSelfWithdraw);

                // 增加合规方法
                const { mainBalanceCent } = data;
                const appType = 3;
                const { token, acctId, wmPoiId } = params;
                const url = `/gw/api/mock/condition?wmPoiId=${wmPoiId}&appType=${appType}&acctId=${acctId}&token=${token}`;
                request.get(url).then((conditionRes) => {
                    const conditionData = conditionRes.data;
                    if (conditionData && conditionData.data && conditionData.data.hidden === true && mainBalanceCent < 0) {
                        commit('changeBalance', '0.00');
                    }
                });
            },
            (err) => {
                commit('changeLoading', false);
                throw err;
            },
        );
    },
};

export default {
    namespaced: true,
    state: initialState,
    mutations,
    actions,
};
