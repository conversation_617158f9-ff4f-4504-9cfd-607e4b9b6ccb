import axios from 'axios';
/* eslint-disable import/no-unresolved, import/extensions */
import Request from '$src/utils/request';

const { CancelToken } = axios;

// 测试账户是否满足对应的关键字
function match(account, keyword) {
    const {
        cardNo, accountName, settleSettingId, wmPoiInfoVoList, wmOfflinePoiInfoList, wmUnBindPoiInfoList, wwFreezePoiInfoList,
    } = account;
    return cardNo.indexOf(keyword) > -1
        || accountName.indexOf(keyword) > -1
        || settleSettingId.toString().indexOf(keyword) > -1
        || wmPoiInfoVoList.some(poiInfo => poiInfo.wmPoiName.indexOf(keyword) > -1)
        || wmPoiInfoVoList.some(poiInfo => (`${poiInfo.wmPoiId}`).indexOf(keyword) > -1)
        || wmOfflinePoiInfoList.some(poiInfo => poiInfo.wmPoiName.indexOf(keyword) > -1)
        || wmOfflinePoiInfoList.some(poiInfo => (`${poiInfo.wmPoiId}`).indexOf(keyword) > -1)
        || wmUnBindPoiInfoList.some(poiInfo => poiInfo.wmPoiName.indexOf(keyword) > -1)
        || wmUnBindPoiInfoList.some(poiInfo => (`${poiInfo.wmPoiId}`).indexOf(keyword) > -1)
        || wwFreezePoiInfoList.some(poiInfo => poiInfo.wmPoiName.indexOf(keyword) > -1)
        || wwFreezePoiInfoList.some(poiInfo => (`${poiInfo.wmPoiId}`).indexOf(keyword) > -1);
}

const initialState = {
    loading: true,
    failed: false,
    query: '',
    list: [],
};

const mutations = {
    changeLoading(state, loading) {
        state.loading = loading;
    },
    changeFailedFlag(state, failed) {
        state.failed = failed;
    },
    changeQuery(state, query) {
        state.query = query;
    },
    changeList(state, list) {
        state.list = list;
    },
};

let source = null;
let request = null;

const actions = {
    fetchAccountList({ commit }) {
        if (source) {
            source.cancel();
            source = null;
            request = null;
        }
        commit('changeLoading', true);
        commit('changeList', []);
        source = CancelToken.source();
        const cancelToken = source.token;
        request = Request.get('/finance/pc/api/account/allAccountSettleSettingInfo/v2', { cancelToken })
            .then((res) => {
                source = null;
                request = null;

                commit('changeLoading', false);

                const { code, data, msg } = res.data;

                if (code !== 0) {
                    commit('changeFailedFlag', true);
                    throw new Error(msg);
                }

                commit('changeFailedFlag', false);
                commit('changeList', data);
                return data.wmSettleAccountAndPoiInfoVoList;
            }, (thrown) => {
                commit('changeLoading', false);
                commit('changeFailedFlag', true);
                throw thrown;
            });
        return request;
    },
    // 允许请求失败后通过查询按钮重试
    // !!! promise 值不为 showList ，请使用 getter 获取 !!!
    search({ commit, dispatch, state }, query) {
        commit('changeQuery', query);
        // 失败的情况重发请求
        return (state.failed && !state.loading)
            ? dispatch('fetchAccountList')
            : (request || Promise.resolve(state.showList));
    },
};

const getters = {
    // 只显示 getters.accountList 中的账户
    showList({ list, query }) {
        return query
            ? list.wmSettleAccountAndPoiInfoVoList.filter(account => match(account, query))
            : list.wmSettleAccountAndPoiInfoVoList;
    },
};

export default {
    namespaced: true,
    state: initialState,
    mutations,
    actions,
    getters,
};
