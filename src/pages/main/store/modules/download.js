import axios from 'axios';
/* eslint-disable import/no-unresolved, import/extensions */
import request from '$src/utils/request';
import dayjs from 'dayjs';

const { CancelToken } = axios;

const initialState = {
    checkedSettleId: null,
    settleSettingList: [],
    settleSettingLoading: false,

    checkedPoiId: null,
    poiList: [],
    poiLoading: false,

    loading: false,
    total: 0,
    pageNo: 1,
    pageSize: 10,
    taskList: [],
};

const mutations = {
    changeCheckedSettleId(state, id) {
        state.checkedSettleId = id;
    },
    changeSettleSettingList(state, list) {
        state.settleSettingList = list || [];
    },
    changeSettleSettingLoading(state, loading) {
        state.settleSettingLoading = loading;
    },
    changeCheckedPoiId(state, id) {
        state.checkedPoiId = id;
    },
    changePoiList(state, list) {
        state.poiList = list || [];
    },
    changePoiLoading(state, loading) {
        state.poiLoading = loading;
    },
    changeLoading(state, loading) {
        state.loading = loading;
    },
    changeTotal(state, total) {
        state.total = total;
    },
    changePageNo(state, pageNo) {
        state.pageNo = pageNo;
    },
    changeTaskList(state, list) {
        state.taskList = list || [];
    },
};

let source1 = null;
function getPoiList(params) {
    if (source1) {
        source1.cancel();
        source1 = null;
    }

    source1 = CancelToken.source();

    return request.get('/finance/pc/api/billDownload/getPoiList', {
        params,
        cancelToken: source1.token,
    });
}

let source2 = null;
function getTaskList(params) {
    if (source2) {
        source2.cancel();
        source2 = null;
    }

    source2 = CancelToken.source();

    return request.get('/finance/pc/api/billDownload/getBillDownloadList', {
        params,
        cancelToken: source2.token,
    });
}

const actions = {
    fetchSettleSettingList({ commit }, sku) {
        commit('changeSettleSettingLoading', true);
        return request.get('/finance/pc/api/billDownload/getSettleSettingList', {
            params: { sku },
        })
            .then((res) => {
                commit('changeSettleSettingLoading', false);

                const { code, msg, data } = res.data;

                if (code !== 0) {
                    throw new Error(msg);
                }

                commit('changeCheckedSettleId', data.checkedId);
                // settleSettingList
                commit('changeSettleSettingList', data.settleSettingList || []);
            }, (err) => {
                commit('changeSettleSettingLoading', false);
                throw err;
            });
    },
    fetchPoiList({ commit }, params) {
        commit('changePoiLoading', true);
        commit('changeCheckedPoiId', null);

        return getPoiList(params).then((res) => {
            commit('changePoiLoading', false);

            const { code, msg, data } = res.data;

            if (code !== 0) {
                throw new Error(msg);
            }

            commit('changeCheckedPoiId', data.checkedId || -1);
            commit('changePoiList', data.poiList || []);
        }, (err) => {
            commit('changePoiLoading', false);
            throw err;
        });
    },
    fetchTaskList({ state, commit }, {
        pageNo, bizType, beginDate, endDate, schoolId,
    }) {
        const { pageSize } = state;

        const params = {
            pageNo,
            pageSize,
            bizType,
            beginDate: beginDate ? dayjs(beginDate).format('YYYY-MM-DD') : '',
            endDate: endDate ? dayjs(endDate).format('YYYY-MM-DD') : '',
            schoolId,
        };

        commit('changeLoading', true);
        return getTaskList(params).then((res) => {
            commit('changeLoading', false);

            const { code, msg, data } = res.data;

            if (code !== 0) {
                commit('changeTotal', 0);
                commit('changePageNo', 0);
                commit('changeTaskList', []);
                // throw new Error(msg);
                // 返回一个带有错误信息的对象，而不是抛出错误
                return {
                    success: false,
                    message: msg || '获取账单导出列表失败',
                };
            }
            commit('changeTotal', data.totalSize || 0);
            commit('changePageNo', pageNo);
            commit('changeTaskList', data.taskList || []);
        }, (err) => {
            commit('changeLoading', false);
            throw err;
        });
    },
};

export default {
    namespaced: true,
    state: initialState,
    mutations,
    actions,
};
