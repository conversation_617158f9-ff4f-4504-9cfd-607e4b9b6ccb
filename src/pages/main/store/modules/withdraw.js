/* eslint-disable import/no-unresolved, import/extensions */
import request from '$src/utils/request';

const initState = {
    loading: false,
    withdrawDetail: {
        payBindType: 1,
        minPayMount: '0.00',
        walletId: '',
        balance: '0.00',
        balanceOutStatus: false,
        balanceOutMsg: '',
        cardTailNo: '',
        walletAccountName: '',
        bankName: '',
        accountMobile: '',
        bankAccountName: '',
    },
};

const mutations = {
    changeLoading(st, loading) {
        st.loading = loading;
    },
    changeWithdrawDetail(st, data) {
        st.withdrawDetail = data;
    },
};

const actions = {
    fetchWithdrawDetail({ commit }, params) {
        commit('changeLoading', true);

        return request.get('/finance/pc/api/account/withdraw/withdrawInfo', {
            params,
        }).then((res) => {
            commit('changeLoading', false);

            const { code, msg, data } = res.data;
            if (code !== 0) {
                throw new Error(msg);
            }

            commit('changeWithdrawDetail', data);
        }, (err) => {
            commit('changeLoading', false);
            throw err;
        });
    },
};

export default {
    namespaced: true,
    state: initState,
    mutations,
    actions,
};
