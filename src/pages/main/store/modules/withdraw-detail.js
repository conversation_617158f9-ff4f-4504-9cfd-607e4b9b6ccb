/* eslint-disable import/no-unresolved, import/extensions */
import request from '$src/utils/request';

const initState = {
    loading: false,
    withdrawDetail: {
        moneyStr: '',
        comment: '',
        bankAccountName: '',
        bankName: '',
        cardNumber: '',
        cTime: '',
        flowNo: null,
        payBindType: 1,
        walletId: 0,
        walletAccountName: '',
        walletUrl: '',
        withdrawFlowStatusList: [],
    },
};

const mutations = {
    changeLoading(st, loading) {
        st.loading = loading;
    },
    changeWithdrawDetail(st, data) {
        st.withdrawDetail = data;
    },
};

const actions = {
    fetchWithdrawDetail({ commit }, params) {
        commit('changeLoading', true);

        const url = params.wmPoiId
            ? '/finance/pc/api/accountFlow/withDrawDetail' // 单店带 wmPoiId
            : '/finance/pc/api/multiAccount/withDrawDetail'; // 多店不带

        return request.get(url, {
            params,
        }).then((res) => {
            commit('changeLoading', false);

            const { code, msg, data } = res.data;
            if (code !== 0) {
                throw new Error(msg);
            }

            commit('changeWithdrawDetail', data);
        }, (err) => {
            commit('changeLoading', false);
            throw err;
        });
    },
};

export default {
    namespaced: true,
    state: initState,
    mutations,
    actions,
};
