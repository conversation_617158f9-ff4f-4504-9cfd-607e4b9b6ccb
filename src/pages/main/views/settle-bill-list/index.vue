<template>
    <div :class="$style.bills">
        <roo-tabs
            :class="$style.tabs"
            type="border"
        >
            <roo-tab-pane label="已结算账单">
                <div :class="$style.tabContent">
                    <div :class="$style.filter">
                        <div>
                            <label>时间</label>
                            <div :class="$style.dateBox">
                                <roo-date-time-picker
                                    :max-date="new Date()"
                                    v-model="settledStartDate"
                                    format="yyyy-MM-dd"
                                    value-type="string"
                                    @change="handleDateChange"
                                />
                            </div>
                            <span>-</span>
                            <div :class="[$style.dateBox,$style.dateRight]">
                                <roo-date-time-picker
                                    :max-date="new Date()"
                                    v-model="settledEndDate"
                                    format="yyyy-MM-dd"
                                    value-type="string"
                                    @change="handleDateChange"
                                />
                            </div>
                            <roo-button
                                :class="$style.btn"
                                @click="handleSearch"
                            >查询</roo-button>
                        </div>
                        <div :class="$style.allBills">
                            <a
                                v-lx-mc="{
                                    bid: 'b_waimai_e_7r8w068x_mc',
                                    options: {
                                        cid,
                                    },
                                }"
                                href="#noop"
                                @click.prevent="handleGetAllSettledBillList"
                            >
                                全部账单
                            </a>
                        </div>
                    </div>
                    <div :class="$style.section">
                        <settle-bill-table
                            :cid="cid"
                            :view-bid="'b_waimai_e_jarcpfg6_mc'"
                            :settled-bid="'b_waimai_e_evwb8n2t_mc'"
                            :download-bid="'b_waimai_e_igtpc2yr_mc'"
                            :wm-poi-id="wmPoiId"
                            :list="settledBillList.settleBillList"
                        />
                    </div>
                    <div
                        v-if="settledBillList.pageCount !==0"
                        :class="$style.footer"
                    >
                        <roo-pagination
                            :page-size="settledPageInfo.pageSize"
                            :total="settledBillList.pageCount"
                            :current-page="settledPageInfo.currentPage"
                            @current-change="handlePageChange"
                        />
                    </div>
                </div>
            </roo-tab-pane>

            <roo-tab-pane label="未结算账单">
                <div :class="$style.tabContent">
                    <div :class="$style.section">
                        <settle-bill-table
                            :cid="cid"
                            :view-bid="'b_waimai_e_yc7ekt0g_mc'"
                            :download-bid="'b_waimai_e_005r1j4z_mc'"
                            :wm-poi-id="wmPoiId"
                            :list="unSettledBillList"
                            :settle-type="'unSettleType'"
                            :is-delay="isDelay"
                        />
                    </div>
                </div>
            </roo-tab-pane>
        </roo-tabs>


        <roo-loading
            :show="loading"
            fullscreen
            backdrop
            lock
        />
    </div>
</template>

<script>
import dayjs from 'dayjs';
import { mapState, mapActions } from 'vuex';

/* eslint-disable import/extensions, import/no-unresolved */
import request from '$src/utils/request';
import toastError from '$lib/toast-error';
import SettleBillTable from '$components/settle-bill-table';
import * as utils from '$lib/utils';
/* eslint-enable import/extensions, import/no-unresolved */

const timeDiff = 90 * 24 * 3600 * 1000;

export default {
    name: 'SettleBillList',
    components: {
        SettleBillTable,
    },
    data() {
        return {
            settledStartDate: dayjs(new Date().getTime() - timeDiff).format('YYYY-MM-DD'),
            settledEndDate: dayjs(new Date()).format('YYYY-MM-DD'),
            settledPageInfo: {
                pageSize: 10,
                currentPage: 1,
            },
            isDelay: 0,
        };
    },
    computed: {
        cid() {
            const { meta } = this.$route;
            return meta && meta.cid;
        },
        // 首页只有选中单店时可进入，wmPoiId 取 cookie 值
        wmPoiId() {
            const cookies = utils.getCookies();
            const wmPoiIdFromUrl = this.$route.query && this.$route.query.wmPoiId;
            return wmPoiIdFromUrl || (cookies.wmPoiId === -1 ? '' : cookies.wmPoiId);
        },
        ...mapState('settleBillList', [
            'loading',
            'settledBillList',
            'unSettledBillList',
        ]),
    },
    mounted() {
        this.getSettledBillList();
        this.fetchUnSettledBillList().catch(toastError);
        this.getIsDelay();
    },
    methods: {
        getIsDelay() {
            const cookies = utils.getCookies();
            const acctId = parseInt(cookies.acctId, 10) || null;
            const wmPoiId = parseInt(cookies.wmPoiId, 10) || null;
            const params = {
                acctId,
                wmPoiId: wmPoiId || -1,
            };
            request.get('/finance/v4/h5/api/account/accountInfo', { params }).then((res) => {
                const { code, msg, data } = res.data;

                if (code !== 0) {
                    throw new Error(msg);
                }
                this.isDelay = data.isDelay;
            })
                .catch(toastError);
        },
        handleSearch() {
            this.settledPageInfo.currentPage = 1;
            this.getSettledBillList();
        },
        handlePageChange(page) {
            this.settledPageInfo.currentPage = page;
            this.getSettledBillList();
        },
        getSettledBillList() {
            this.fetchSettledBillList({
                settleBillStartDate: this.settledStartDate,
                settleBillEndDate: this.settledEndDate,
                pageSize: this.settledPageInfo.pageSize,
                pageNo: this.settledPageInfo.currentPage,
            }).catch(toastError);
        },
        handleGetAllSettledBillList() {
            this.settledStartDate = dayjs(new Date().getTime() - timeDiff).format('YYYY-MM-DD');
            this.settledEndDate = dayjs(new Date()).format('YYYY-MM-DD');
            this.settledPageInfo.currentPage = 1;
            this.getSettledBillList();
        },
        ...mapActions('settleBillList', [
            'fetchSettledBillList',
            'fetchUnSettledBillList',
        ]),
        handleDateChange() {
            const cookies = utils.getCookies();
            const acctId = parseInt(cookies.acctId, 10) || null;
            const wmPoiId = parseInt(cookies.wmPoiId, 10) || null;
            LXAnalytics('moduleEdit', 'b_waimai_e_vhjusx9v_me', {
                poi_id: wmPoiId,
                custom: {
                    acctId,
                    wmPoiId: wmPoiId || -1,
                },
            }, { cid: this.cid });
        },
    },
};
</script>

<style lang="scss" module>
.bills {
    height: 100%;
    padding: 0 10px;
}
.tabs {
    display: flex;
    height: 100%;
    flex-direction: column;
    border-radius: 2px;
    background: #fff;
    box-shadow: 0 0 6px 0 #F3F3F4;

    :global(.roo-tab-content) {
        flex: 1;
        overflow-y: auto;
    }

    &:global(.roo-tabs-line .roo-tabs-container) {
        padding: 0 20px;
    }

    :global(.tab-item.active) {
        font-weight: bold;
    }
}
.tab-content {
    padding: 20px 30px;
    background: #fff;
}
.filter {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;
    div {
        display: flex;
        align-items: center;
        label {
            margin-right: 10px;
            font-size: 14px;
            color: #858692;
        }
        span {
            display: inline-block;
            width: 10px;
        }
    }
}
.date-box {
    display: inline-block;
    width: 200px;
}
.date-right {
    margin-left: 6px;
}
.btn {
    margin-left: 20px;
}
.all-bills {
    display: inline-block;
    font-size: 14px;
    text-align: right;
    vertical-align: middle;
}
.footer {
    text-align: right;
    margin-top: 30px;
}
</style>
