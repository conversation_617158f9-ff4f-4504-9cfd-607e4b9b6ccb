<template>
    <div :class="$style.container">
        <roo-breadcrumb :items="breadcrumb" />

        <div :class="$style.panel">
            <div :class="$style.header">
                <h3>
                    {{ acctTypeName }}账户充值
                </h3>
            </div>
            <roo-form
                ref="form"
                :model="formData"
                :class="$style.form"
                :label-width="2"
            >
                <roo-form-item label="当前余额">
                    <money
                        :class="$style.money"
                        :amount="balance"
                    />
                </roo-form-item>
                <roo-form-item
                    :rules="rules"
                    prop="amountText"
                    label="充值金额"
                >
                    <roo-input
                        v-if="acctType !== 1009"
                        v-model="amountText"
                        :placeholder="cantRecharge ? '不能充值' : '请输入充值金额'"
                        @change="handleAmountChange"
                    />
                    <money
                        v-else
                        :class="$style.money"
                        :amount="amountText"
                    />
                    <roo-radio-group
                        v-if="!(acctType == 1009)"
                        v-model="checkedAmount"
                        inline
                    >
                        <roo-radio
                            v-for="item in radioList"
                            :key="item.value"
                            :value="item.value"
                        >
                            {{ item.label }}
                        </roo-radio>
                    </roo-radio-group>
                    <div
                        v-if="showAdRecommendTip"
                        :class="$style.warningTip"
                    >
                        为保证您的顺利推广，建议您最少充值{{ bizadRecommendMoney / 100 }}元
                    </div>
                    <p
                        v-if="showHelpTip"
                        :class="$style.tip"
                    >
                        可充值余额 {{ balanceStr | formatNumberThousand }} ，
                        <a
                            href="#"
                            @click.prevent="amountText = balanceStr"
                        >
                            全部充值
                        </a>
                    </p>
                </roo-form-item>
                <roo-form-item
                    v-if="activityComment"
                    label="优惠活动"
                >
                    <div :class="$style.activity">
                        {{ activityComment }}
                    </div>
                </roo-form-item>
                <roo-form-item label=" ">
                    <roo-button
                        :class="$style.mr20"
                        :loading="submitting"
                        :disabled="!amount"
                        @click="handleConfirmClick"
                    >
                        立即充值
                    </roo-button>
                    <roo-button
                        :disabled="submitting"
                        type="hollow"
                        @click="handleCancelClick"
                    >
                        取消
                    </roo-button>
                </roo-form-item>
            </roo-form>
        </div>
    </div>
</template>

<script>
/* eslint-disable import/no-unresolved, import/extensions */
import request from '$src/utils/request';
import { toast } from '@roo/roo-vue';

/* eslint-disable import/extensions, import/no-unresolved */
import Money from '$components/money';

import toastError from '$lib/toast-error';
import { openCashierBySDK } from '@mtfe/pay-cashier-sdk-npm';
import SDK_SOURCE from '$src/pages/main/index.js';
import { formatNumberThousand, getCookieValue } from '$lib/utils';
/* eslint-enable import/extensions, import/no-unresolved */

const ACCT_TYPE_NAME = {
    999: '配送费',
    1008: '余额',
    1028: '推广费',
    1009: '保证金',
};

const BALANCE = 1008;
// const MARGIN = 1009;

function getQueryString(params) {
    const pairs = [];
    Object.keys(params).forEach((key) => {
        const k = encodeURIComponent(key);
        const v = encodeURIComponent(params[key]);
        pairs.push(`${k}=${v}`);
    });
    return pairs.join('&');
}

const env = {
    production: 'prod',
    stage: 'staging',
    test: 'test',
    dev: 'dev',
}[process.env.DEPLOY_ENV];

export default {
    name: 'Recharge',
    components: {
        Money,
    },
    filter: {
        formatNumberThousand,
    },
    data() {
        return {
            loading: true,

            amountText: '',
            checkedAmount: '0',

            wmPoiName: '',
            balance: '0.00',
            activityComment: '',

            submitting: false,
            minAmount: 0,
        };
    },
    computed: {
    // 获取来源标示和推荐金额(推荐金额单位:分)
        bizadSource() {
            return this.$route.query.bizad_source || null;
        },
        bizadRecommendMoney() {
            return parseInt(this.$route.query.bizad_recommend_money, 10) || null;
        },
        activityId() {
            return parseInt(this.$route.query.activityId, 10) || null;
        },
        wmPoiId() {
            return parseInt(this.$route.query.wmPoiId, 10) || null;
        },
        acctType() {
            return parseInt(this.$route.query.acctType, 10);
        },
        // todo: 充值跳转需重点测试
        linkSource() {
            return this.$route.query.linkSource;
        },

        acctTypeName() {
            return ACCT_TYPE_NAME[this.acctType];
        },

        breadcrumb() {
            return [{
                text: '我的账户',
            }, {
                text: `${this.acctTypeName}账户充值`,
            }];
        },

        radioList() {
            if (this.linkSource === 'adSys') {
                return [{
                    value: '0',
                    label: '其他',
                }, {
                    value: '50000',
                    label: '50,000元',
                }, {
                    value: '100000',
                    label: '100,000元',
                }, {
                    value: '200000',
                    label: '200,000元',
                }];
            }
            return [{
                value: '0',
                label: '其他',
            }, {
                value: '500',
                label: '500元',
            }, {
                value: '1000',
                label: '1,000元',
            }, {
                value: '2000',
                label: '2,000元',
            }, {
                value: '5000',
                label: '5,000元',
            }];
        },

        // 余额账户余额大于 0 时不能充值
        cantRecharge() {
            return this.acctType === BALANCE && this.balance >= 0;
        },

        formData() {
            return {
                amountText: this.amountText,
            };
        },
        rules() {
            return [
                { required: true, type: 'string', message: '请输入充值金额' },
                { type: 'string', pattern: /(^[1-9]([0-9]+)?(\.[0-9]{1,2})?$)|(^(0)$)|(^[0-9]\.[0-9]([0-9])?$)/, message: '请输入正确的金额格式' },
                {
                    validator: (rule, value, callback) => {
                        if (this.amount <= 0) {
                            callback(new Error('充值金额需要大于0'));
                            return;
                        }
                        if (this.amount > 21474836.47) {
                            callback(new Error('金额过大'));
                            return;
                        }
                        // 余额充值需在一定范围内
                        if (this.acctType === BALANCE && this.amount > -this.balance) {
                            callback(new Error(`充值金额不得大于${this.balanceStr}元`));
                            return;
                        }
                        // 保证金充值不得低于 100 元
                        // if (this.acctType === MARGIN && this.amount < 100) {
                        //     callback(new Error('充值金额不得低于100元'));
                        //     return;
                        // }
                        // 针对推广费账户，当推荐金额大于最小金额，且输入金额小于推荐金额时
                        if (this.bizadRecommendMoney) {
                            if ((this.amount * 100) < this.minAmount) {
                                callback(new Error(`推广费最少充值${this.minAmount / 100}元`));
                                return;
                            }
                        }
                        callback();
                    },
                },
            ];
        },

        amount() {
            return parseFloat(this.amountText);
        },

        // show help tip
        showHelpTip() {
            return this.acctType === BALANCE && this.balance < 0 && !this.amountText;
        },

        showAdRecommendTip() {
            const {
                acctType,
                bizadRecommendMoney,
                amount,
                minAmount,
            } = this;
            return acctType === 1028
                && bizadRecommendMoney
                && (amount * 100) < bizadRecommendMoney
                && (amount * 100) >= minAmount;
        },

        balanceStr() {
            const { balance } = this;
            return balance[0] === '-' ? balance.slice(1) : balance;
        },
    },
    watch: {
        checkedAmount(val) {
            if (val !== '0') {
                this.amountText = val;
            }
        },
    },
    mounted() {
        if (this.acctType) {
            this.fetchRechargeInfo();
        } else {
            toast.error('缺少参数');
        }
    },
    methods: {
        fetchRechargeInfo() {
            this.loading = true;
            return request.get('/finance/pc/api/accountRecharge/rechargeInfo', {
                params: {
                    wmPoiId: this.wmPoiId,
                    acctType: this.acctType,
                    isAdKA: this.linkSource === 'adSys',
                    bizad_source: this.bizadSource,
                    activityId: this.activityId,
                },
            }).then((res) => {
                const { code, msg, data } = res.data;

                if (code !== 0) {
                    throw new Error(msg);
                }

                this.loading = false;
                this.wmPoiName = data.wmPoiName || '';
                this.balance = data.accountBalance || '';
                this.activityComment = data.activityComment || '';
                this.minAmount = data.minAmount || 0;
                // 履约账户 采用depositRechargeAmount字段填充
                if (data.acctType === 1009) {
                    this.amountText = data.depositRechargeAmount || '';
                }
                if (this.bizadRecommendMoney) {
                    // 推广费账户采用 推荐金额和最小金额进行判断
                    // 推荐金额大于最小金额,则默认填充推荐金额
                    if (this.bizadRecommendMoney >= this.minAmount) {
                        this.amountText = `${this.bizadRecommendMoney / 100}`;
                    } else {
                        // 推荐金额小于最小金额,则默认填充最小金额
                        this.amountText = `${this.minAmount / 100}`;
                    }
                }
            }).catch(toastError);
        },

        handleAmountChange(val) {
            const target = this.radioList.find(item => item.value === val);
            if (target) {
                this.checkedAmount = target.value;
            } else {
                this.checkedAmount = '0';
            }
        },

        handleConfirmClick() {
            if (this.submitting) {
                return;
            }

            this.submitting = true;

            let url = '/finance/pc/api/accountRecharge/genPayOrder';
            const params = {
                wmPoiId: this.wmPoiId,
                acctType: this.acctType,
                amount: this.amountText,
                bizad_source: this.bizadSource,
                isAdKA: false,
                giftAmount: 0,
            };

            if (this.linkSource === 'adSys') {
                params.acctId = this.$route.query.acctId || getCookieValue('acctId');
                params.accountType = 20;
                params.isAdKA = true;
                params.source = 'pc_ad_main';
                url = '/finance/pc/api/adMain/genPreRechargeOrder';
            }

            request.get(url, { params })
                .then((res) => {
                    const { code, msg, data } = res.data;

                    if (code !== 0) {
                        throw new Error(msg);
                    }

                    this.navigateToCashier(data.url, data.data, this.acctType);
                })
                .catch((err) => {
                    this.submitting = false;
                    toastError(err);
                });
        },

        handleCancelClick() {
            this.$router.go(-1);
        },

        navigateToCashier(url, data, acctType) {
            const {
                tradeNo,
                payToken,
                returnUrl,
                redrUrl,
                bizadSource,
                loginType,
                epassportToken,
                jumpNew,
            } = data;
            if (jumpNew === 0) {
                const query = {
                    tradeno: tradeNo,
                    pay_token: payToken,
                    pay_success_url: returnUrl,
                    redr_url: redrUrl,
                    bizad_source: bizadSource,
                    login_type: loginType,
                    biz_token: epassportToken,
                };
                if (acctType === 1028) {
                    query.limit = 0;
                }
                const qs = getQueryString(query);
                window.location = `${url}/m/cashier/show/index?${qs}`;
            } else {
                const requestCashierParam = {
                    env,
                    // 业务线唯一标识
                    pay_cashier_sdk_source: SDK_SOURCE,
                    // 默认'1'：SDK内部通过window.location.href跳转到收银台首页。传'0'：SDK返回跳转收银台首页的url，业务方可自行选择跳转方式。
                    auto_jump: '1',
                    tradeno: tradeNo,
                    pay_token: payToken,
                    pay_success_url: returnUrl,
                    redr_url: redrUrl,
                    nb_platform: 'www',
                    nb_source: 'pc_biz_cashier',
                    biz_token: epassportToken,
                    login_type: loginType,
                    // 调用JSSDK版本，默认为'1'
                    js_sdk_version: '1',
                };
                // 调用openCashierBySDK
                // auto_jump传'0'时，业务方可拿到jumpUrl自行选择跳转方式，如:打开新webViewv跳转
                openCashierBySDK(requestCashierParam).catch((error) => {
                    console.log('NPM-SDK-catch', error);
                    if (error && error.code !== 0) {
                        this.submitting = false;
                        toast.error(error.message);
                    }
                });
            }
        },
    },
};
</script>

<style lang="scss" module>
.container {
    padding: 10px 20px;

    :global(.form-group:last-child) {
        margin-bottom: 0;
    }
}

.panel {
    padding: 20px;
    background: #FFF;

    h3 {
        margin: 0;
        color: #3F4156;
        font-size: 20px;
        font-weight: bold;
    }
}

.form {
    margin-top: 20px;
    width: 520px;

    :global(.roo-radio-group) {
        margin-top: 10px;
    }

    :global(.custom-radio) {
        margin-right: 5px;
    }

    :global(.roo-radio + .roo-radio:not(.block-label)) {
        margin-left: 15px;
        margin-right: 0;
    }
}

.warning-tip {
    margin-top: 4px;
    color: #f8c833;
    font-size: 14px;
}

.activity {
    color: #f8c833;
    font-size: 20px;
    line-height: 36px;
}

.money {
    font-size: 20px;
    line-height: 36px;
}

.mr20 {
    margin-right: 20px;
}
.tip {
    margin-top: 4px;
    margin-bottom: 0;
}
.defaultMoney {
    line-height: 35px;
    font-size: 20px;
}
</style>
