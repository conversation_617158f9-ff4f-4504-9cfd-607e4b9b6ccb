<template>
    <div :class="$style.module">
        <roo-tabs
            v-model="active"
            type="border"
        >
            <roo-tab-pane
                :label="orderTabLabel"
                name="order"
            >
                <div :class="$style.pane">
                    <div :class="$style.filters">
                        <radio-button-group
                            v-model="orderBillChargeTypeCode"
                            size="small"
                            @change="handleOrderFilterChange"
                        >
                            <radio-button val="1,2">
                                全部
                            </radio-button>
                            <radio-button val="1">
                                外卖订单
                            </radio-button>
                            <radio-button val="2">
                                退款订单
                            </radio-button>
                        </radio-button-group>
                    </div>
                    <old-order-table
                        :class="$style.mb20"
                        :wm-poi-id="wmPoiId"
                        :loading="orderBillLoading"
                        :list="orderBillList"
                        :summary="orderSummary"
                    />
                    <div :class="$style.pagination">
                        <roo-pagination
                            :total="orderBillTotal"
                            :page-size="orderBillPageSize"
                            :current-page="orderBillPageNo"
                            @current-change="handleOrderPageChange"
                        />
                    </div>
                </div>
            </roo-tab-pane>
            <roo-tab-pane
                :label="otherTabLabel"
                name="other"
            >
                <div :class="$style.pane">
                    <div :class="$style.filters">
                        <radio-button-group
                            v-model="otherBillChargeTypeCode"
                            size="small"
                            @change="handleOtherFilterChange"
                        >
                            <radio-button val="3,4,5,6,7,9,10,11">全部</radio-button>
                            <radio-button val="3">配送费用</radio-button>
                            <radio-button val="4">餐损赔付</radio-button>
                            <radio-button val="5">服务费返还</radio-button>
                            <radio-button val="6">订单部分退款</radio-button>
                            <radio-button val="7">部分退款冲抵</radio-button>
                            <radio-button val="9">商服赔付</radio-button>
                            <radio-button val="10">保险费用</radio-button>
                            <radio-button val="11">推广花费</radio-button>
                        </radio-button-group>
                    </div>
                    <old-other-table
                        :class="$style.mb20"
                        :wm-poi-id="wmPoiId"
                        :loading="otherBillLoading"
                        :list="otherBillList"
                    />
                    <div :class="$style.pagination">
                        <roo-pagination
                            :total="otherBillTotal"
                            :page-size="otherBillPageSize"
                            :current-page="otherBillPageNo"
                            @current-change="handleOtherPageChange"
                        />
                    </div>
                </div>
            </roo-tab-pane>
        </roo-tabs>
    </div>
</template>

<script>
import { mapState, mapGetters, mapActions } from 'vuex';

/* eslint-disable import/extensions, import/no-unresolved */
import RadioButton from '$components/radio-button';
import RadioButtonGroup from '$components/radio-button-group';

import toastError from '$lib/toast-error';
import { formatNumberThousand } from '$lib/utils';
/* eslint-enable import/extensions, import/no-unresolved */

import OldOrderTable from './components/old-order-table';
import OldOtherTable from './components/old-other-table';

const defaultOrderFilter = '1,2';
const defaultOtherFilter = '3,4,5,6,7,9,10,11';

export default {
    name: 'OldModule',
    components: {
        RadioButton,
        RadioButtonGroup,

        OldOrderTable,
        OldOtherTable,
    },
    props: {
        wmPoiId: {
            type: [String, Number],
            required: true,
        },
        dailyBillDate: {
            type: String,
            required: true,
        },
    },
    data() {
        return {
            active: 'order',
            orderBillChargeTypeCode: defaultOrderFilter,
            otherBillChargeTypeCode: defaultOtherFilter,
        };
    },
    computed: {
        ...mapState('dailyBill/expectedIncome', [
            'orderBillChargeAmount',
            'otherBillChargeAmount',
        ]),

        ...mapState('dailyBill/billChargeList', [
            'orderBillLoading',
            'orderBillList',
            'orderBillTotal',
            'orderBillPageNo',
            'orderBillPageSize',

            'otherBillLoading',
            'otherBillList',
            'otherBillTotal',
            'otherBillPageNo',
            'otherBillPageSize',
        ]),

        ...mapGetters('dailyBill/billChargeSummary', [
            'orderBillChargeSummary',
            'otherBillChargeSummary',
            'allBillChargeSummary',
        ]),

        baseParams() {
            return {
                wmPoiId: this.wmPoiId,
                dailyBillDate: this.dailyBillDate,
            };
        },

        orderTabLabel() {
            return `订单类 ${formatNumberThousand(this.orderBillChargeAmount)}`;
        },
        otherTabLabel() {
            return `其它类 ${formatNumberThousand(this.otherBillChargeAmount)}`;
        },

        orderSummary() {
            switch (this.orderBillChargeTypeCode) {
                case '1':
                    return this.orderBillChargeSummary;
                case '2':
                    return this.otherBillChargeSummary;
                case '1,2':
                default:
                    return this.allBillChargeSummary;
            }
        },
    },
    watch: {
        baseParams() {
            this.reload();
        },
    },
    mounted() {
        this.reload();
    },
    methods: {
        ...mapActions('dailyBill/billChargeList', [
            'fetchOrderBillChargeList',
            'fetchOtherBillChargeList',
        ]),
        ...mapActions('dailyBill/billChargeSummary', [
            'fetchBillChargeSummary',
        ]),

        // 初始化 & 日期更新时需重新加载
        reload() {
            this.active = 'order';
            this.orderBillChargeTypeCode = defaultOrderFilter;
            this.otherBillChargeTypeCode = defaultOtherFilter;

            this.fetchBillChargeSummary(this.baseParams).catch(toastError);
            this.fetchOrderBillChargeList(Object.assign({
                billChargeTypeCode: this.orderBillChargeTypeCode,
                pageNo: 1,
            }, this.baseParams)).catch(toastError);
            this.fetchOtherBillChargeList(Object.assign({
                billChargeTypeCode: this.otherBillChargeTypeCode,
                pageNo: 1,
            }, this.baseParams)).catch(toastError);
        },

        handleOrderPageChange(pageNo) {
            this.fetchOrderBillChargeList(Object.assign({
                billChargeTypeCode: this.orderBillChargeTypeCode,
                pageNo,
            }, this.baseParams)).catch(toastError);
        },
        handleOtherPageChange(pageNo) {
            this.fetchOtherBillChargeList(Object.assign({
                billChargeTypeCode: this.otherBillChargeTypeCode,
                pageNo,
            }, this.baseParams)).catch(toastError);
        },

        handleOrderFilterChange(val) {
            const params = Object.assign({
                billChargeTypeCode: val,
                pageNo: 1,
            }, this.baseParams);

            this.fetchOrderBillChargeList(params)
                .catch(toastError);
        },
        handleOtherFilterChange(val) {
            const params = Object.assign({
                billChargeTypeCode: val,
                pageNo: 1,
            }, this.baseParams);

            this.fetchOtherBillChargeList(params)
                .catch(toastError);
        },
    },
};
</script>

<style lang="scss" module>
.module {
    background: #fff;
    box-shadow: 0 0 6px 0 #F3F3F4;

    :global(.roo-tabs-nav > li > a) {
        max-width: none;
    }
}

.pane {
    padding: 20px;
}

.filters {
    margin-bottom: 20px;
    text-align: left;
}

.mb20 {
    margin-bottom: 20px;
}

.pagination {
    text-align: right;

    :global(.pagination) {
        margin: 0;
    }
}
</style>
