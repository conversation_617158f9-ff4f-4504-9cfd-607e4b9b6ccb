<template>
    <div :class="$style.module">
        <roo-tabs
            v-model="active"
            type="border"
        >
            <div>
                <roo-alert
                    v-show="unFinishTip && unFinishTip.showTip"
                    :class="$style.alert"
                    type="warning"
                    icon="exclamation-circle"
                >
                    当日未完成订单将延时自动完成并做结算，想了解订单详情可点击<a style=" cursor: pointer" @click="looks">查看</a>
                </roo-alert>
            </div>
            <roo-tab-pane
                :label="orderTabLabel"
                name="order"
            >
                <div :class="$style.pane">
                    <div :class="$style.filters">
                        <radio-button-group
                            v-model="orderBillChargeTypeCode"
                            size="small"
                            @change="handleOrderFilterChange"
                        >
                            <radio-button
                                v-if="orderFilters.length !== 1"
                                key="all"
                                :val="orderAllFilter"
                            >
                                全部
                            </radio-button>
                            <radio-button
                                v-for="item in orderFilters"
                                :key="item.code"
                                :val="item.code"
                            >
                                {{ item.name }}
                            </radio-button>
                        </radio-button-group>
                        <span :class="$style.floatRight">
                            单位：元
                        </span>
                    </div>
                    <new-order-table
                        :class="$style.mb20"
                        :wm-poi-id="wmPoiId"
                        :loading="orderBillLoading"
                        :fee-type-list="feeTypeList"
                        :list="orderBillList"
                        :summary="orderSummary"
                        @show-detail="handleShowDetail"
                    />
                    <div :class="$style.pagination">
                        <roo-pagination
                            :total="orderBillTotal"
                            :page-size="orderBillPageSize"
                            :current-page="orderBillPageNo"
                            @current-change="handleOrderPageChange"
                        />
                    </div>
                </div>
            </roo-tab-pane>
            <roo-tab-pane
                :label="otherTabLabel"
                name="other"
            >
                <div :class="$style.pane">
                    <div :class="$style.filters">
                        <radio-button-group
                            v-model="otherBillChargeTypeCode"
                            size="small"
                            @change="handleOtherFilterChange"
                        >
                            <radio-button
                                v-if="otherFilters.length !== 1"
                                key="all"
                                :val="otherAllFilter"
                            >
                                全部
                            </radio-button>
                            <radio-button
                                v-for="item in otherFilters"
                                :key="item.code"
                                :val="item.code"
                            >
                                {{ item.name }}
                            </radio-button>
                        </radio-button-group>
                        <span :class="$style.floatRight">
                            单位：元
                        </span>
                    </div>
                    <new-other-table
                        :class="$style.mb20"
                        :wm-poi-id="wmPoiId"
                        :loading="otherBillLoading"
                        :list="otherBillList"
                        @show-detail="handleShowDetail"
                    />
                    <div :class="$style.pagination">
                        <roo-pagination
                            :total="otherBillTotal"
                            :page-size="otherBillPageSize"
                            :current-page="otherBillPageNo"
                            @current-change="handleOtherPageChange"
                        />
                    </div>
                </div>
            </roo-tab-pane>
            <roo-tab-pane
                :label="returnTabLabel"
                name="return"
            >
                <div :class="$style.pane">
                    <div :class="$style.filters">
                        <radio-button-group
                            v-model="returnBillChargeTypeCode"
                            size="small"
                            @change="handleReturnFilterChange"
                        >
                            <radio-button
                                v-if="returnFilters.length !== 1"
                                key="all"
                                :val="returnAllFilter"
                            >
                                全部
                            </radio-button>
                            <radio-button
                                v-for="item in returnFilters"
                                :key="item.code"
                                :val="item.code"
                            >
                                {{ item.name }}
                            </radio-button>
                        </radio-button-group>
                        <span :class="$style.floatRight">
                            单位：元
                        </span>
                    </div>
                    <new-return-table
                        :class="$style.mb20"
                        :wm-poi-id="wmPoiId"
                        :loading="returnBillLoading"
                        :list="returnBillList"
                        @show-detail="handleShowDetail"
                    />
                    <div :class="$style.pagination">
                        <roo-pagination
                            :total="returnBillTotal"
                            :page-size="returnBillPageSize"
                            :current-page="returnBillPageNo"
                            @current-change="handleReturnPageChange"
                        />
                    </div>
                </div>
            </roo-tab-pane>
        </roo-tabs>

        <!-- <detail-modal
            v-model="displayDetailModal"
            :type="detail && detail.billChargeType"
            :title="detail && detail.title"
            :template="detail && detail.webTemplate"
            :query="getQuery(detail)"
            @hidden="detail = null"
        /> -->
        <roo-modal
            v-model="displayDetailModal"
            :size="'large'"
            :title="detail && detail.title"
            @hidden="detail = null"
        >
            <iframe
                v-if="detail"
                :src="getOrderDetailPageUrl(detail)"
                :height="iframeHeight"
                width="590"
                frameborder="0"
            >
            </iframe>
            <div v-else></div>
        </roo-modal>
    </div>
</template>

<script>
// 动态化后的表格
import dayjs from 'dayjs';
import { mapState, mapGetters, mapActions } from 'vuex';

/* eslint-disable import/extensions, import/no-unresolved */
import RadioButton from '$components/radio-button';
import RadioButtonGroup from '$components/radio-button-group';

import toastError from '$lib/toast-error';
import { formatNumberThousand, getQueryString, navigateTo } from '$lib/utils';
/* eslint-enable import/extensions, import/no-unresolved */

import moment from 'moment';
import DetailModal from './detail-modal';
import NewOrderTable from './components/new-order-table';
import NewOtherTable from './components/new-other-table';
import NewReturnTable from './components/new-return-table';
import { jumpTo } from '../../../../lib/bridge';

export default {
    name: 'NewModule',
    components: {
        RadioButton,
        RadioButtonGroup,

        NewOrderTable,
        NewOtherTable,
        NewReturnTable,
        DetailModal,
    },
    props: {
        wmPoiId: {
            type: [String, Number],
            required: true,
        },
        dailyBillDate: {
            type: String,
            required: true,
        },
        acctId: {
            type: [String, Number],
            required: true,
        }
    },
    data() {
        return {
            active: 'order',
            orderBillChargeTypeCode: null,
            otherBillChargeTypeCode: null,
            returnBillChargeTypeCode: null,

            displayDetailModal: false,
            detail: null,
            iframeHeight: 500,
        };
    },
    computed: {
        ...mapState('dailyBill/expectedIncome', [
            'orderBillChargeAmount',
            'otherBillChargeAmount',
            'returnBillChargeAmount',
        ]),

        ...mapState('dailyBill/dynamicBillChargeList', [
            'feeTypeList',

            'orderBillLoading',
            'orderBillList',
            'orderBillTotal',
            'orderBillPageNo',
            'orderBillPageSize',

            'otherBillLoading',
            'otherBillList',
            'otherBillTotal',
            'otherBillPageNo',
            'otherBillPageSize',

            'returnBillLoading',
            'returnBillList',
            'returnBillTotal',
            'returnBillPageNo',
            'returnBillPageSize',
            'unFinishTip'
        ]),

        ...mapState('dailyBill/dynamicBillChargeSummary', [
            'billChargeTypeDetails',
        ]),

        ...mapGetters('dailyBill/dynamicBillChargeSummary', [
            'orderAllFilter',
            'otherAllFilter',
            'returnAllFilter',
            'orderFilters',
            'otherFilters',
            'returnFilters',
            'orderBillChargeSummary',
            'refundBillChargeSummary',
            'refundPartBillChargeSummary',
            'refundOffsetBillChargeSummary',
            'allBillChargeSummary',
        ]),

        baseParams() {
            return {
                wmPoiId: this.wmPoiId,
                dailyBillDate: this.dailyBillDate,
            };
        },

        orderTabLabel() {
            return `订单类 ${formatNumberThousand(this.orderBillChargeAmount)}`;
        },
        otherTabLabel() {
            return `其它类 ${formatNumberThousand(this.otherBillChargeAmount)}`;
        },
        returnTabLabel() {
            return `服务费返还激励 ${formatNumberThousand(this.returnBillChargeAmount)}`;
        },
        // 金额合计
        orderSummary() {
            switch (this.orderBillChargeTypeCode) {
                case '1':
                    return this.orderBillChargeSummary;
                case '2':
                    return this.refundBillChargeSummary;
                case '16':
                    return this.refundPartBillChargeSummary;
                case '17':
                    return this.refundOffsetBillChargeSummary;
                case '1,2,16,17':
                default:
                    return this.allBillChargeSummary;
            }
        },
    },
    watch: {
        baseParams() {
            this.reload();
        },
    },
    mounted() {
        this.reload();
        // iframe 高度
        this.iframeHeight = (window.innerHeight - 80) * 0.9;
    },
    methods: {
        ...mapActions('dailyBill/dynamicBillChargeList', [
            'fetchOrderBillChargeList',
            'fetchOtherBillChargeList',
            'fetchReturnBillChargeList',
            'fetchUnFinishTip'
        ]),
        ...mapActions('dailyBill/dynamicBillChargeSummary', [
            'fetchBillChargeSummary',
        ]),
        reload() {
            this.active = 'order';

            this.fetchBillChargeSummary(this.baseParams)
                .then(() => {
                    this.orderBillChargeTypeCode = this.orderAllFilter;
                    this.otherBillChargeTypeCode = this.otherAllFilter;
                    this.returnBillChargeTypeCode = this.returnAllFilter;

                    this.fetchOrderBillChargeList(Object.assign({
                        billChargeTypeCode: this.orderBillChargeTypeCode,
                        pageNo: 1,
                    }, this.baseParams)).catch(toastError);
                    this.fetchOtherBillChargeList(Object.assign({
                        billChargeTypeCode: this.otherBillChargeTypeCode,
                        pageNo: 1,
                    }, this.baseParams)).catch(toastError);
                    this.fetchReturnBillChargeList(Object.assign({
                        billChargeTypeCode: this.returnBillChargeTypeCode, // 费用返还激励
                        pageNo: 1,
                    }, this.baseParams)).catch(toastError);
                    this.fetchUnFinishTip({
                        businessLine: 1,
                        acctId: this.acctId,
                        wmPoiId: this.wmPoiId,
                        dailyBillDate: moment(this.dailyBillDate).format('yyyy/MM/DD') || '',
                    }).catch(toastError);
                })
                .catch(toastError);
        },

        handleOrderPageChange(pageNo) {
            this.fetchOrderBillChargeList(Object.assign({
                billChargeTypeCode: this.orderBillChargeTypeCode,
                pageNo,
            }, this.baseParams)).catch(toastError);
        },
        handleOtherPageChange(pageNo) {
            this.fetchOtherBillChargeList(Object.assign({
                billChargeTypeCode: this.otherBillChargeTypeCode,
                pageNo,
            }, this.baseParams)).catch(toastError);
        },
        handleReturnPageChange(pageNo) {
            this.fetchReturnBillChargeList(Object.assign({
                billChargeTypeCode: this.returnBillChargeTypeCode, // 费用返还激励
                pageNo,
            }, this.baseParams)).catch(toastError);
        },

        handleOrderFilterChange(val) {
            const params = Object.assign({
                billChargeTypeCode: val,
                pageNo: 1,
            }, this.baseParams);

            this.fetchOrderBillChargeList(params)
                .catch(toastError);
        },
        handleOtherFilterChange(val) {
            const params = Object.assign({
                billChargeTypeCode: val,
                pageNo: 1,
            }, this.baseParams);

            this.fetchOtherBillChargeList(params)
                .catch(toastError);
        },
        handleReturnFilterChange(val) {
            const params = Object.assign({
                billChargeTypeCode: val,
                pageNo: 1,
            }, this.baseParams);

            this.fetchReturnBillChargeList(params)
                .catch(toastError);
        },

        handleShowDetail(detail) {
            this.displayDetailModal = true;
            this.detail = detail;
        },
        getOrderDetailPage(item) {
            navigateTo(this.getOrderDetailPageUrl(item));
        },
        getOrderDetailPageUrl(item) {
            // 违约金类型，直接跳转到规则中心
            if (item.chargeTypeCode === 6174 && item.webUrl) {
                return item.webUrl;
            } else {
                const query = this.getQuery(item);
                const queryString = getQueryString(query);
                return `/finance/newfe#/finance/orderDetail?${queryString}`;
            }
        },
        getQuery(item) {
            if (item) {
                return {
                    chargeTypeCode: item.chargeTypeCode,
                    billChargeId: item.billChargeId,
                    wmOrderViewId: item.wmOrderViewId,
                    dailyBillDate: dayjs(item.dailyBillDate).format('YYYY/MM/DD'),
                    wmPoiId: this.wmPoiId || null,
                };
            }
            return {};
        },

        looks() {
            //  console.log('90909',this.unFinishTip)
            if (this.unFinishTip != null) {
                jumpTo(this.unFinishTip.webUrl);
            }
        },
    },
};
</script>

<style lang="scss" module>
.module {
    background: #fff;
    box-shadow: 0 0 6px 0 #F3F3F4;

    :global(.roo-tabs-nav > li > a) {
        max-width: none;
    }
}

.pane {
    padding: 20px;
}

.filters {
    margin-bottom: 20px;
    text-align: left;
}

.float-right {
    line-height: 32px;
    float: right;
}

.mb20 {
    margin-bottom: 20px;
}

.pagination {
    text-align: right;

    :global(.pagination) {
        margin: 0;
    }
}
.alert{
    margin-left: 28px;
    margin-right: 28px;
    margin-top: 18px;
    margin-bottom: -16px;
    text-align: left;
}
</style>