<template>
    <roo-modal
        :class="$style.modal"
        :value="value"
        :title="title"
        :size="template === 'order' ? 'large' : 'normal'"
        no-body-padding
        @input="$emit('input', $event)"
        @hidden="handleHidden"
    >
        <div :class="$style.body">
            <div
                v-if="loading"
                key="loading"
                :class="$style.loading"
            >
                <i class="roo-icon roo-icon-loading"></i>
                正在加载
            </div>
            <template v-else>
                <order-template
                    v-if="template === 'order'"
                    key="order"
                    :type="type"
                    :list="dataList"
                    :meta="metaData"
                    :charge-model="chargeModel"
                />
                <kv-template
                    v-else-if="template === 'kv'"
                    key="kv"
                    :list="kvList"
                />
            </template>
        </div>
    </roo-modal>
</template>

<script>
// 详情弹窗
// 接口请求逻辑封装在单独模块中

import axios from 'axios';
/* eslint-disable import/no-unresolved, import/extensions */
import request from '$src/utils/request';

/* eslint-disable import/extensions, import/no-unresolved */
import { pick } from '$lib/utils';
import toastError from '$lib/toast-error';
/* eslint-enable import/extensions, import/no-unresolved */

import KvTemplate from './components/modal-template/kv-template';
import OrderTemplate from './components/modal-template/order-template';

const { CancelToken } = axios;

export default {
    name: 'DetailModal',
    components: {
        KvTemplate,
        OrderTemplate,
    },
    props: {
        value: {
            type: Boolean,
            default: false,
        },
        type: {
            type: Number,
            default: 1, // enum [1, 2, 16, 17]
        },
        title: {
            type: String,
            default: '',
        },
        // 使用模板
        template: {
            type: String,
            default: 'order',
        },
        // 动态化接口请求 query 参数
        query: {
            type: Object,
            required: true,
        },
    },
    data() {
        return {
            loading: true,
            dataList: [],
            // ... 其他元信息
            wmOrderViewId: '',
            wmOrderDaySeq: 0,
            wmOrderSubmittedDate: '',
            wmOrderCompletedDate: '',
            wmOrderRefundedDate: '',
            wmOrderCanceledDate: '',
            wmOrderCompensatedDate: '',
            wmOrderShippingType: '',
            dailyBillDate: '',
            amount: 0,
            estimateAmount: 0,
            other: '',
            ctime: '',
            chargeAlias: '',
            chargeModel: 0,
            associateOrderSeq: null,
        };
    },
    computed: {
    // 将 dataList 适配成键值对形式的 list
        kvList() {
            return this.dataList.reduce((acc, cur) => acc.concat(cur.subDataList), []);
        },
        metaData() {
            return pick(this, [
                'wmOrderViewId',
                'wmOrderDaySeq',
                'wmOrderSubmittedDate',
                'wmOrderCompletedDate',
                'wmOrderRefundedDate',
                'wmOrderCanceledDate',
                'wmOrderCompensatedDate',
                'wmOrderShippingType',
                'amount',
                'dailyBillDate',
                'estimateAmount',
                'other',
                'ctime',
                'chargeAlias',
                'associateOrderSeq',
            ]);
        },
    },
    watch: {
        value(val) {
            if (val) {
                this.reload();
            } else {
                if (this.source) {
                    this.source.cancel();
                    this.source = null;
                }
            }
        },
    },
    created() {
        this.source = null;
    },
    mounted() {
        if (this.value) {
            this.reload();
        }
    },
    methods: {
        reload() {
            if (this.source) {
                this.source.cancel();
                this.source = null;
            }
            this.loading = true;
            this.source = CancelToken.source();
            const cancelToken = this.source.token;
            return request.get('/finance/pc/api/poiBillCharge/billChargeDetailDynamic', {
                params: this.query,
                cancelToken,
            })
                .then((res) => {
                    this.source = null;
                    this.loading = false;
                    const { code, msg, data } = res.data;
                    if (code !== 0) {
                        throw new Error(msg);
                    }

                    this.dataList = data.dataList || [];
                    this.chargeModel = data.chargeModel || 0;
                    this.wmOrderViewId = data.wmOrderViewId || '';
                    this.wmOrderDaySeq = data.wmOrderDaySeq || 0;
                    this.wmOrderSubmittedDate = data.wmOrderSubmittedDate || '';
                    this.wmOrderCompletedDate = data.wmOrderCompletedDate || '';
                    this.wmOrderRefundedDate = data.wmOrderRefundedDate || '';
                    this.wmOrderCanceledDate = data.wmOrderCanceledDate || '';
                    this.wmOrderCompensatedDate = data.wmOrderCompensatedDate || '';
                    this.wmOrderShippingType = data.wmOrderShippingType || '';
                    this.dailyBillDate = data.dailyBillDate || '';
                    this.amount = data.amount || 0;
                    this.estimateAmount = data.estimateAmount || 0;
                    this.other = data.other || '';
                    this.ctime = data.ctime || '';
                    this.chargeAlias = data.chargeAlias || '';
                    this.associateOrderSeq = data.associateOrderSeq || null;
                })
                .catch((err) => {
                    this.source = null;
                    this.loading = false;
                    toastError(err);
                });
        },
        clear() {
            this.dataList = [];
            this.wmOrderViewId = '';
            this.wmOrderDaySeq = 0;
            this.wmOrderSubmittedDate = '';
            this.wmOrderCompletedDate = '';
            this.wmOrderRefundedDate = '';
            this.wmOrderCanceledDate = '';
            this.wmOrderCompensatedDate = '';
            this.wmOrderShippingType = '';
            this.ctime = '';
        },
        handleHidden(event) {
            this.$emit('hidden', event);
            this.clear();
        },
    },
};
</script>

<style lang="scss" module>
.modal {
    &:global(.roo-modal .roo-modal-dialog .roo-modal-content .roo-modal-body) {
        max-height: 450px;
        overflow-y: auto;
    }
    :global(.roo-icon-loading) {
        animation: spin 1s linear infinite;
    }
}

.body {
    padding: 16px 20px;
}

.loading {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 200px;
}

@keyframes spin {
    from {
        transform: rotate(0);
    }
    to {
        transform: rotate(360deg);
    }
}
</style>
