<template>
    <div :class="$style.container">
        <roo-breadcrumb :items="breadcrumb" />
        <div :class="$style.head">
            <div>
                <roo-date-time-picker
                    v-model="date"
                    v-lx-mc="{
                        bid: 'b_waimai_e_9j2s9ps7_mc',
                        options: { cid },
                    }"
                    :class="$style.dateInput"
                    :min-date="minDate"
                    :max-date="maxDate"
                    format="yyyy-MM-dd 日账单"
                    @change="handleDailyBillDateChange"
                />
            </div>
            <div>
                <p>{{ labelText }}</p>
                <money
                    :class="$style.amount"
                    :amount="totalAmount"
                />
            </div>
            <div :class="$style.right">
                <div>
                    微信扫描二维码，关注美团外卖商户通公众号
                </div>
                <div>
                    绑定账号，第一时间获取账单信息
                </div>

                <img
                    :class="$style.img"
                    src="//s3plus.meituan.net/v1/mss_c4375b35f5cb4e678b5b55a48c40cf9d/staticfile/waimaie-qrcode.jpg"
                    alt="QRCode"
                />
            </div>
        </div>

        <new-module
            v-if="useNew"
            key="new"
            :wm-poi-id="wmPoiId"
            :daily-bill-date="date | yyyymmdd"
        />
        <old-module
            v-else
            key="old"
            :wm-poi-id="wmPoiId"
            :daily-bill-date="date | yyyymmdd"
        />

        <roo-loading
            :show="loading"
            fullscreen
            backdrop
            lock
        />
    </div>
</template>

<script>
import dayjs from 'dayjs';
import { mapState, mapActions } from 'vuex';

/* eslint-disable import/extensions, import/no-unresolved */
import { getCookies, getCookieValue } from '$lib/utils';
import toastError from '$lib/toast-error';

import Money from '$components/money';
/* eslint-enable import/extensions, import/no-unresolved */

// 引入多个组件复用的 table 样式
import './components/table.scss';

import OldModule from './old-module';
import NewModule from './new-module';

function YYYYMMDD(date) {
    return dayjs(date).format('YYYY-MM-DD');
}

export default {
    name: 'DailyBill',
    components: {
        Money,
        OldModule,
        NewModule,
    },
    filters: {
        yyyymmdd(date) {
            return YYYYMMDD(date);
        },
    },
    data() {
        return {
            date: this.$route.query.dailyBillDate
                ? new Date(this.$route.query.dailyBillDate)
                : new Date(),
        };
    },
    computed: {
        // 灵犀 cid
        cid() {
            const { meta } = this.$route;
            return meta && meta.cid;
        },

        wmPoiId() {
            return this.$route.query.wmPoiId || getCookieValue('wmPoiId');
        },

        breadcrumb() {
            return [{
                text: '首页',
                link: { name: 'home' },
            }, {
                text: '账单明细',
            }];
        },

        minDate() {
            return new Date(Date.now() - (90 * 24 * 3600 * 1000));
        },

        maxDate() {
            return new Date();
        },

        ...mapState('dailyBill/expectedIncome', [
            'loading',
            'settled',
            'settleDate',
            'totalAmount',
            'settleBillStartDate',
            'settleBillEndDate',
            'orderBillChargeAmount',
            'otherBillChargeAmount',
        ]),

        labelText() {
            const {
                settled,
                settleBillStartDate,
                settleBillEndDate,
                settleDate,
            } = this;
            if (settled) {
                return ` ${settleBillStartDate}~${settleBillEndDate} 已汇入余额`;
            }
            return ` 预计 ${settleDate} 结算`;
        },

        useNew() {
            return dayjs(this.date).isAfter('2018-09-01');
        },
    },
    watch: {
        date() {
            const cookies = getCookies();
            const acctId = parseInt(cookies.acctId, 10) || null;
            const wmPoiId = parseInt(cookies.wmPoiId, 10) || null;

            LXAnalytics('moduleEdit', 'b_waimai_e_9j2s9ps7_me', {
                poi_id: wmPoiId,
                custom: {
                    acctId,
                    wmPoiId,
                    date: YYYYMMDD(this.date),
                },
            });
        },
    },
    mounted() {
        this.handleDailyBillDateChange(this.date);
    },
    methods: {
        ...mapActions('dailyBill/expectedIncome', [
            'fetchExpectedIncome',
        ]),

        handleDailyBillDateChange(date) {
            this.fetchExpectedIncome({
                wmPoiId: this.wmPoiId,
                dailyBillDate: YYYYMMDD(date),
            }).catch(toastError);
        },
    },
};
</script>

<style style="scss" module>
.container {
    padding: 0 10px;
}

.head {
    position: relative;
    padding: 30px;
    margin-bottom: 20px;
    background: #FFF;
    box-shadow: 0 0 6px 0 #F3F3F4;
}

.date-input {
    display: inline-block;
}

.amount {
    font-size: 38px;
    line-height: 1;
}

.right {
    position: absolute;
    top: 50%;
    right: 20px;
    margin-top: 5px;
    text-align: center;
    transform: translate3d(0, -50%, 0);
}

.img {
    display: block;
    width: 120px;
    height: auto;
    margin: auto;
}
</style>
