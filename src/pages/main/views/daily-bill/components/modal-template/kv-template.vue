<template>
    <ul :class="$style.list">
        <li
            v-for="(item, idx) in list"
            :key="idx"
        >
            <span :class="$style.label">
                {{ item.name }}
            </span>
            <span :class="$style.text">
                {{ item.info }}
            </span>
        </li>
    </ul>
</template>

<script>
export default {
    props: {
        list: {
            type: Array,
            required: true,
        },
    },
};
</script>

<style lang="scss" module>
.list {
    list-style: none;

    li {
        display: flex;
        align-items: flex-start;
    }
}

.label {
    display: inline-block;
    width: 100px;
    color: #999;
    // flex-shrink: 0;
}

.text {
    display: inline-block;
    flex: 1;
    color: #333;
}
</style>
