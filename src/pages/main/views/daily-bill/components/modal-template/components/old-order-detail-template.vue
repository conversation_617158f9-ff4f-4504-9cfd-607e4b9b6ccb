<template>
    <div>
        <table :class="$style.table">
            <col style="width: 70%;" />
            <col style="width: 10%;" />
            <col style="width: 20%;" />

            <tbody
                v-for="(item, idx1) in list"
                :key="idx1"
            >
                <tr key="sum">
                    <th
                        :class="$style.sumTitle"
                        colspan="2"
                    >
                        {{ item.title }}
                    </th>
                    <th :class="getAmountClass(item.totalAmount)">
                        {{ item.totalAmount }}
                    </th>
                </tr>

                <tr
                    v-for="(subItem, idx2) in item.subDataList"
                    :key="idx2"
                >
                    <td
                        key="name"
                        :colspan="calNameColspan(subItem)"
                    >
                        <div :class="$style.subItemTitle">
                            <span>{{ subItem.name }}</span>
                            <span
                                v-if="subItem.commissionDataList && subItem.info"
                            >
                                {{ subItem.info }}
                            </span>
                        </div>
                        <div
                            v-if="subItem.remark"
                            key="remark"
                            :class="$style.remark"
                        >
                            {{ subItem.remark }}
                        </div>
                        <div
                            v-for="commission in subItem.commissionDataList"
                            :key="commission.title"
                            :class="$style.subItemWrap"
                        >
                            <div :class="$style.subItem">
                                <div> • {{ commission.title }}</div>
                                <div :class="$style.subItemMoney">
                                    {{ commission.amount }}
                                </div>
                            </div>
                            <div :class="$style.subItemContainer">
                                <div
                                    v-if="commission.info"
                                    :class="$style.stepItemTitle"
                                >
                                    <div><span :class="$style.littlePoint">•</span> {{ commission.info }}</div>
                                    <div :class="$style.stepComment">
                                        {{ commission.stepPriceComment }}
                                    </div>
                                </div>
                                <div
                                    v-if="commission.startPrice"
                                    :class="$style.stepItemTitle"
                                >
                                    <div><span :class="$style.littlePoint">•</span> {{ commission.startPrice }}</div>
                                    <div :class="$style.stepComment">
                                        {{ commission.startPriceComment }}
                                    </div>
                                </div>
                                <div
                                    v-if="commission.stepPrice"
                                    :class="$style.stepItemTitle"
                                >
                                    <div><span :class="$style.littlePoint">•</span> {{ commission.stepPrice }}</div>
                                </div>
                                <div
                                    v-for="step in commission.stepPriceDetailList"
                                    :key="step.title"
                                    :class="$style.stepContainer"
                                >
                                    <div :class="$style.stepItem">
                                        <div>{{ step.title }}</div>
                                        <div>{{ step.amount }}</div>
                                    </div>
                                    <div :class="$style.stepItemComment">
                                        {{ step.comment }}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </td>
                    <td
                        v-if="subItem.count > -1"
                        key="count"
                        :class="$style.info"
                    >
                        ×{{ subItem.count }}
                        <i
                            v-if="subItem.skuDetail"
                            :class="$style.icon"
                            class="roo-icon roo-icon-info-circle"
                        >
                            <roo-tooltip
                                :class="$style.tooltip"
                                placement="top"
                            >
                                <table :class="$style.tipContent">
                                    <col style="width: 30px;" />
                                    <col style="width: 120px;" />
                                    <col style="width: 120px;" />
                                    <caption>共退差价 {{ subItem.skuDetail.totalAmount }} 元</caption>
                                    <tbody>
                                        <tr
                                            v-for="(sku, idx) in subItem.skuDetail.skuGramList"
                                            :key="idx"
                                        >
                                            <td>
                                                <span :class="$style.no">
                                                    {{ idx + 1 }}
                                                </span>
                                            </td>
                                            <td>需退重量 {{ sku.weight }}</td>
                                            <td>需退差价 {{ sku.amount }}</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </roo-tooltip>
                        </i>
                    </td>
                    <td
                        v-if="subItem.info && !subItem.commissionDataList"
                        key="info"
                        :class="$style.info"
                    >
                        {{ subItem.info }}
                    </td>
                </tr>
            </tbody>

            <tfoot key="footer">
                <tr>
                    <th
                        :class="meta.amount < 0 ? $style.outgo : $style.income"
                        colspan="2"
                    >
                        已结算金额
                    </th>
                    <th :class="[$style.amount, meta.amount < 0 ? $style.outgo : $style.income]">
                        {{ meta.amount >= 0 ? '+' : '-' }}￥{{ meta.amount | abs | formatCent }}
                    </th>
                </tr>
                <tr v-if="meta.other">
                    <td
                        :class="$style.fs14"
                        colspan="3"
                    >
                        {{ meta.other }}
                    </td>
                </tr>
            </tfoot>
        </table>
        <div :class="$style.footerText">
            <div>
                {{ footerText1 }}
            </div>
            <div>
                订单编号 {{ meta.wmOrderViewId }}
            </div>
        </div>
    </div>
</template>

<script>
import orderMixin from './orderMixins';

export default {
    name: 'OldOrderDetailTemplate',
    mixins: [orderMixin],
};
</script>

<style lang="scss" module>
.table {
    width: 100%;
    margin: 0;
    table-layout: fixed;
    font-size: 16px;
    color: #A2A4B3;
    border-top: 1px solid #efefef;
    border-bottom: 1px solid #efefef;

    th, td {
        line-height: 20px;
        text-align: right;

        &:first-child {
        text-align: left;
        }
    }

    td {
        padding: 8px 0;
    }

    th {
        padding-top: 16px;
        padding-bottom: 8px;
        color: #3F4156;
    }

    tbody, tfoot {
        border-bottom: 1px solid #efefef;

        tr:last-child {
        th, td {
            padding-bottom: 16px;
        }
        }
    }

    .sumTitle {
        color: #3F4156;
        font-size: 16px;
        font-weight: 600;
    }

    .fs14 {
        font-size: 14px;
    }

    .amount {
        font-size: 20px;
    }

    .remark {
        font-size: 12px;
    }

    .remarkTwo {
        display: flex;
        justify-content: space-between;
        font-size: 12px;
    }

    .commissionList {
        padding: 20px;
        background: #F7F8FA;
    }


    .income {
        color: #01C0AC;
        font-weight: 400;
    }

    .outgo {
        color: #F15757;
        font-weight: 400;
    }

    .subItem {
        color: #36394D;
        font-size: 13px;
        display: flex;
        justify-content: space-between;
        font-weight: 600;
    }

    .subItemTitleRight {
        .originalInfo {
        font-size: 12px;
        font-weight: 400;
        color: #858692;
        margin-right: 10px;
        text-decoration: line-through;
        }
    }

    .subItemTwo {
        color: #36394D;
        font-size: 13px;
        display: flex;
        justify-content: space-between;
        font-weight: 600;

        span {
        vertical-align: middle;
        }
    }

    .subItemContainer {
        padding: 5px 0 5px 10px;
        font-size: 13px;
        color: #36394D;
        line-height: 20px;

    }

    .stepComment {
        padding-left: 8px;
        color: #A2A4B3;
    }

    .subItemMoney {
        margin-left: 5px;
        .originalInfo {
        font-size: 12px;
        font-weight: 400;
        color: #858692;
        margin-right: 10px;
        text-decoration: line-through;
        }
    }

    .stepContainer {
        padding-left: 10px;
        padding-bottom: 5px;
    }

    .stepItem {
        display: flex;
        justify-content: space-between;
    }

    .stepItemComment {
        font-size: 12px;
        color: #A2A4B3;
    }

    .info {
        font-size: 13px;
        color: #3F4156;
        font-weight: 600;
    }

    .littlePoint {
        font-size: 4px;
        color: #A2A4B3;
    }

    .subItemTitle {
        color: #3F4156;
        font-size: 14px;
        font-weight: 600;
        display: flex;
        justify-content: space-between;
    }

    .subItemWrap {
        margin-top: 10px;
    }

    .stepItemTitle {
        margin-bottom: 5px;
    }
}

.footer-text {
    margin-top: 16px;
    font-size: 12px;
    color: #A2A4B3;
    text-align: center;
}

.tooltip .roo-tooltip-inner {
    max-width: none;
}

.icon:global(.roo-icon) {
    height: auto;
    vertical-align: bottom;
    cursor: pointer;
}

.tip-content {
    margin: 0;
    table-layout: fixed;
    border-collapse: collapse;
    color: #FAFAFA;
    text-align: left;
    // white-space: nowrap;

    caption {
        text-align: center;
        color: #FFF;
    }

    .no {
        display: inline-block;
        width: 18px;
        height: 18px;
        line-height: 18px;
        border-radius: 3px;
        background: #FFF;
        color: #3F4156;
        text-align: center;
    }

    td {
        padding: 5px;
    }
}
</style>
