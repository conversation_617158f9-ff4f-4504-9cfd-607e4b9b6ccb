/* eslint-disable import/extensions, import/no-unresolved */
import { formatCent } from '$lib/filters';
/* eslint-enable import/extensions, import/no-unresolved */
export default {
    filters: {
        formatCent,
        abs(num) {
            return Math.abs(num);
        },
    },
    data() {
        return {
            commissionShow: new Set(),
        };
    },
    props: {
        type: {
            type: Number,
            default: 1, // enum [1, 2, 16, 17]
        },
        list: {
            type: Array,
            required: true,
        },
        meta: {
            type: Object,
            required: true,
        },
        chargeModel: {
            type: Number,
            required: true,
        },
    },
    computed: {
        footerText1() {
            // 如果有部分退业务，需要优先展示部分退的时间ctime（默认展示订单完成时间）
            const { meta, type } = this;
            const isPartReturn = [16, 17].indexOf(type) > -1;
            let partOrCompletStr = '';

            if (isPartReturn) {
                partOrCompletStr = meta.ctime ? `| ${meta.ctime} ${meta.chargeAlias || '部分退款'}` : '| --';
            } else {
                partOrCompletStr = meta.wmOrderCompletedDate ? `| ${meta.wmOrderCompletedDate} ${meta.chargeAlias || '订单完成'}` : '| --';
            }
            return `${meta.wmOrderSubmittedDate} 下单 `
                + `${partOrCompletStr} `
                + `| ${meta.wmOrderShippingType} `;
        },
        // footerText2() {}
    },
    methods: {
        getAmountClass(str) {
            if (str) {
                if (str[0] === '+') {
                    return this.$style.income;
                } else if (str[0] === '-') {
                    return this.$style.outgo;
                }
            }
            return null;
        },
        calNameColspan(row) {
            let span = 1;
            if (row.count === -1) {
                span += 1;
            }
            if (!row.info) {
                span += 1;
            }
            if (row.commissionDataList) {
                span += 1;
            }
            return span;
        },
        handelOpenClick(name) {
            const newSet = this.commissionShow;
            if (newSet.has(name)) {
                newSet.delete(name);
            } else {
                newSet.add(name);
            }
            const list = Array.from(newSet);
            this.commissionShow = new Set(list);
        },
    },
};
