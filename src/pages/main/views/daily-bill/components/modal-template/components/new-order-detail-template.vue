<template>
    <div>
        <table :class="$style.table">
            <col style="width: 70%;" />
            <col style="width: 10%;" />
            <col style="width: 20%;" />
            <tbody
                v-for="(item, idx1) in list"
                :key="idx1"
            >
                <tr key="sum">
                    <th
                        :class="$style.sumTitle"
                        colspan="2"
                    >
                        {{ item.title }}
                    </th>
                    <th :class="getAmountClass(item.totalAmount)">
                        {{ item.totalAmount }}
                    </th>
                </tr>

                <tr
                    v-for="(subItem, idx2) in item.subDataList"
                    :key="idx2"
                >
                    <td
                        key="name"
                        :colspan="calNameColspan(subItem)"
                    >
                        <div :class="$style.subItemTitle">
                            <span>{{ subItem.name }}</span>
                            <div :class="$style.subItemTitleRight">
                                <span
                                    v-if="subItem.originalInfo"
                                    :class="$style.originalInfo"
                                > {{ subItem.originalInfo }}</span>
                                <span
                                    v-if="subItem.info"
                                >
                                    {{ subItem.info }}
                                </span>
                            </div>
                        </div>
                        <div
                            v-if="subItem.remark"
                            key="remark"
                            :class="$style.remarkTwo"
                        >
                            <span>{{ subItem.remark }}</span>
                            <span
                                v-if="subItem.commissionDataList"
                                @click="handelOpenClick(subItem.name)"
                            >
                                {{ commissionShow.has(subItem.name) ? '收起' : '详情' }}
                                <roo-icon
                                    :name="commissionShow.has(subItem.name) ? 'chevron-up' : 'chevron-down'"
                                    size="16px"
                                />
                            </span>
                        </div>
                        <div
                            v-if="subItem.originalRemark"
                            :class="$style.remark"
                        >
                            {{ subItem.originalRemark }}
                        </div>
                        <div
                            v-if="subItem.commissionDataList && commissionShow.has(subItem.name)"
                            :class="$style.commissionList"
                        >
                            <div
                                v-for="(commission, index) in subItem.commissionDataList"
                                :key="index"
                                :class="$style.subItemWrap"
                            >
                                <div :class="$style.subItemTwo">
                                    <div>
                                        <span>{{ commission.title }}</span>
                                        <!-- 拼好饭收费模式判断方式 -->
                                        <tooltip v-if="chargeModel === 102 && commission.isShowDetail" :commission="commission" />
                                        <!-- 新收费模式判断方式 -->
                                        <tooltip v-if="chargeModel === 4 && commission.title !== '时段收费' && commission.title !== '固定配送费'" :commission="commission" />
                                    </div>
                                    <div :class="$style.subItemMoney">
                                        <span
                                            v-if="commission.originalAmount"
                                            :class="$style.originalInfo"
                                        > {{ commission.originalAmount }}</span>
                                        <span>
                                            {{ commission.amount }}
                                        </span>
                                    </div>
                                </div>
                                <div :class="$style.subItemContainer">
                                    <div
                                        v-if="commission.info"
                                        :class="$style.stepItemTitle"
                                    >
                                        <!-- 拼好饭收费模式判断方式 -->
                                        <ul v-if="chargeModel === 102" :class="$style.infoUl">
                                            <li
                                                v-for="(info, j) in commission.info.split('#')"
                                                :key="j"
                                            >
                                                {{ info }}
                                            </li>
                                        </ul>
                                        <div v-else>
                                            {{ commission.info }}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </td>
                    <td
                        v-if="subItem.count > -1"
                        key="count"
                        :class="$style.info"
                    >
                        ×{{ subItem.count }}
                        <i
                            v-if="subItem.skuDetail"
                            :class="$style.icon"
                            class="roo-icon roo-icon-info-circle"
                        >
                            <roo-tooltip
                                :class="$style.tooltip"
                                placement="top"
                            >
                                <table :class="$style.tipContent">
                                    <col style="width: 30px;" />
                                    <col style="width: 120px;" />
                                    <col style="width: 120px;" />
                                    <caption>共退差价 {{ subItem.skuDetail.totalAmount }} 元</caption>
                                    <tbody>
                                        <tr
                                            v-for="(sku, idx) in subItem.skuDetail.skuGramList"
                                            :key="idx"
                                        >
                                            <td>
                                                <span :class="$style.no">
                                                    {{ idx + 1 }}
                                                </span>
                                            </td>
                                            <td>需退重量 {{ sku.weight }}</td>
                                            <td>需退差价 {{ sku.amount }}</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </roo-tooltip>
                        </i>
                    </td>
                </tr>
            </tbody>

            <tfoot key="footer">
                <tr>
                    <th
                        :class="meta.amount < 0 ? $style.outgo : $style.income"
                        colspan="2"
                    >
                        已结算金额
                    </th>
                    <th :class="[$style.amount, meta.amount < 0 ? $style.outgo : $style.income]">
                        {{ meta.amount >= 0 ? '+' : '-' }}￥{{ meta.amount | abs | formatCent }}
                    </th>
                </tr>
                <tr v-if="meta.other">
                    <td
                        :class="$style.fs14"
                        colspan="3"
                    >
                        {{ meta.other }}
                    </td>
                </tr>
            </tfoot>
        </table>
        <div :class="$style.footerText">
            <div>
                {{ footerText1 }}
            </div>
            <div>
                订单编号 {{ meta.wmOrderViewId }}
                <span v-if="meta.associateOrderSeq">,关联拼好饭拼单一起送订单：{{ meta.associateOrderSeq }}</span>
            </div>
        </div>
    </div>
</template>

<script>
import orderMixin from './orderMixins';
import Tooltip from './tooltip';

export default {
    name: 'NewOrderDetailTemplate',
    mixins: [orderMixin],
    components: {
        Tooltip,
    },
};
</script>

<style lang="scss" module>
.table {
    width: 100%;
    margin: 0;
    table-layout: fixed;
    font-size: 16px;
    color: #A2A4B3;
    border-top: 1px solid #efefef;
    border-bottom: 1px solid #efefef;

    th, td {
        line-height: 20px;
        text-align: right;

        &:first-child {
        text-align: left;
        }
    }

    td {
        padding: 8px 0;
    }

    th {
        padding-top: 16px;
        padding-bottom: 8px;
        color: #3F4156;
    }

    tbody, tfoot {
        border-bottom: 1px solid #efefef;

        tr:last-child {
        th, td {
            padding-bottom: 16px;
        }
        }
    }

    .sumTitle {
        color: #3F4156;
        font-size: 16px;
        font-weight: 600;
    }

    .fs14 {
        font-size: 14px;
    }

    .amount {
        font-size: 20px;
    }

    .remark {
        font-size: 12px;
    }

    .remarkTwo {
        display: flex;
        justify-content: space-between;
        font-size: 12px;
    }

    .commissionList {
        padding: 20px;
        background: #F7F8FA;
    }


    .income {
        color: #01C0AC;
        font-weight: 400;
    }

    .outgo {
        color: #F15757;
        font-weight: 400;
    }

    .subItem {
        color: #36394D;
        font-size: 13px;
        display: flex;
        justify-content: space-between;
        font-weight: 600;
    }

    .subItemTitleRight {
        .originalInfo {
        font-size: 12px;
        font-weight: 400;
        color: #858692;
        margin-right: 10px;
        text-decoration: line-through;
        }
    }

    .subItemTwo {
        color: #36394D;
        font-size: 13px;
        display: flex;
        justify-content: space-between;
        font-weight: 600;

        span {
        vertical-align: middle;
        }
    }

    .subItemContainer {
        padding: 5px 0 5px 0px;
        font-size: 13px;
        color: #36394D;
        line-height: 20px;

    }

    .stepComment {
        padding-left: 8px;
        color: #A2A4B3;
    }

    .subItemMoney {
        margin-left: 5px;
        .originalInfo {
        font-size: 12px;
        font-weight: 400;
        color: #858692;
        margin-right: 10px;
        text-decoration: line-through;
        }
    }

    .stepContainer {
        padding-left: 10px;
        padding-bottom: 5px;
    }

    .stepItem {
        display: flex;
        justify-content: space-between;
    }

    .stepItemComment {
        font-size: 12px;
        color: #A2A4B3;
    }

    .info {
        font-size: 13px;
        color: #3F4156;
        font-weight: 600;
    }

    .littlePoint {
        font-size: 4px;
        color: #A2A4B3;
    }

    .subItemTitle {
        color: #3F4156;
        font-size: 14px;
        font-weight: 600;
        display: flex;
        justify-content: space-between;
    }

    .subItemWrap {
        margin-top: 10px;
    }

    .stepItemTitle {
        margin-bottom: 5px;
        .infoUl {
            margin-left: 20px;
            li {
                list-style:square;
            }
        }
    }
}

.footer-text {
    margin-top: 16px;
    font-size: 12px;
    color: #A2A4B3;
    text-align: center;
}

.tooltip .roo-tooltip-inner {
    max-width: none;
}

.icon:global(.roo-icon) {
    height: auto;
    vertical-align: bottom;
    cursor: pointer;
}

.tip-content {
    margin: 0;
    table-layout: fixed;
    border-collapse: collapse;
    color: #FAFAFA;
    text-align: left;
    // white-space: nowrap;

    caption {
        text-align: center;
        color: #FFF;
    }

    .no {
        display: inline-block;
        width: 18px;
        height: 18px;
        line-height: 18px;
        border-radius: 3px;
        background: #FFF;
        color: #3F4156;
        text-align: center;
    }

    td {
        padding: 5px;
    }
}
</style>
