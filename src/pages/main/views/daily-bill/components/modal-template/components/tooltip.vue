<template>
    <roo-icon
        name="question2"
    >
        <roo-tooltip
            style="width: 300px"
            placement="top"
        >
            <div :class="$style.tooltipContent">
                <div
                    v-if="commission.startPrice || commission.startPrice"
                    :class="$style.stepItemTitle"
                >
                    <div
                        v-if="commission.startPrice"
                    >
                        <span>起步价</span>
                        <span>{{ commission.startPrice }}</span>
                    </div>
                    <div
                        v-if="commission.stepPrice"
                    >
                        <span>距离阶梯加价</span>
                        <span>{{ commission.stepPrice }}</span>
                    </div>
                </div>
                <template v-if="commission.stepPriceDetailList && commission.stepPriceDetailList.length">
                    <div
                        v-if="commission.startPrice || commission.startPrice"
                        :class="$style.line"
                    ></div>
                    <p v-if="commission.title !== '封顶比例' && commission.stepPriceDetailList.length > 0">
                        计价规则
                    </p>
                    <div
                        v-for="step in commission.stepPriceDetailList"
                        :key="step.title"
                        :class="$style.stepContainer"
                    >
                        <div :class="$style.stepItem">
                            <div>{{ step.title }}</div>
                            <!--                                <div>{{ step.amount }}</div>-->
                        </div>
                        <div :class="$style.stepItemComment">
                            {{ step.comment }}
                        </div>
                    </div>
                </template>
                <!-- 技术服务费补贴明细 -->
                <div
                    v-if="commission.techAllowanceDetailList && commission.techAllowanceDetailList.length"
                    :class="$style.modalStepRow"
                >
                    <div v-for="step in commission.techAllowanceDetailList" :key="step.title">
                        <span>{{ step.title }}</span>
                        <span>{{ step.amount }}</span>
                    </div>
                </div>
                <!-- 履约服务费补贴明细 -->
                <div
                    v-if="commission.agreeAllowanceDetailList && commission.agreeAllowanceDetailList.length"
                    :class="$style.modalStepRow"
                >
                    <div v-for="step in commission.agreeAllowanceDetailList" :key="step.title" class="">
                        <span>{{ step.title }}</span>
                        <span>{{ step.amount }}</span>
                    </div>
                </div>
            </div>
        </roo-tooltip>
    </roo-icon>
</template>

<script>
export default {
    name: 'Tooltip',
    props: {
        commission: {
            type: Object,
            required: true,
        },
    },
};
</script>

<style lang="scss" module>
.tooltipContent {
    padding: 10px;

    p {
        font-weight: 600;
        font-family: PingFangSC-Semibold;
        font-size: 14px;
        color: #FFFFFF;
        margin: 10px 0;
    }


    .line {
        height: 1px;
        background-color: #585A6E;
        margin: 20px 0;
    }

    .stepContainer {
        margin-bottom: 10px;
    }

    .stepItemTitle {
        div {
        display: flex;
        justify-content: space-between;
        }
        div:nth-child(1) {
        margin-bottom: 10px;
        }
    }

    .modalStepRow {
        div {
        display: flex;
        justify-content: space-between;
        }
    }
}
</style>
