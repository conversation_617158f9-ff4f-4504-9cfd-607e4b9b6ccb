<template>
    <div>
        <!-- 旧收费模式 -->
        <old-order-detail-template
            v-if="chargeModel !== 4 && chargeModel !== 102"
            :type="type"
            :list="list"
            :meta="meta"
            :charge-model="chargeModel"
        />
        <!-- 新收费模式 -->
        <new-order-detail-template
            v-if="chargeModel === 4"
            :type="type"
            :list="list"
            :meta="meta"
            :charge-model="chargeModel"
        />
        <!-- 拼好返收费模式 -->
        <new-order-detail-template
            v-if="chargeModel === 102"
            :type="type"
            :list="list"
            :meta="meta"
            :charge-model="chargeModel"
        />
    </div>
</template>

<script>
import NewOrderDetailTemplate from './components/new-order-detail-template';
import OldOrderDetailTemplate from './components/old-order-detail-template';

export default {
    name: 'OrderTemplate',
    props: {
        type: {
            type: Number,
            default: 1, // enum [1, 2, 16, 17]
        },
        list: {
            type: Array,
            required: true,
        },
        meta: {
            type: Object,
            required: true,
        },
        chargeModel: {
            type: Number,
            required: true,
        },
    },
    components: {
        NewOrderDetailTemplate,
        OldOrderDetailTemplate,
    },
};
</script>

<style lang="scss" module>
.table {
  width: 100%;
  margin: 0;
  table-layout: fixed;
  font-size: 16px;
  color: #A2A4B3;
  border-top: 1px solid #efefef;
  border-bottom: 1px solid #efefef;

  th, td {
    line-height: 20px;
    text-align: right;

    &:first-child {
      text-align: left;
    }
  }

  td {
    padding: 8px 0;
  }

  th {
    padding-top: 16px;
    padding-bottom: 8px;
    color: #3F4156;
  }

  tbody, tfoot {
    border-bottom: 1px solid #efefef;

    tr:last-child {
      th, td {
        padding-bottom: 16px;
      }
    }
  }

  .sumTitle {
    color: #3F4156;
    font-size: 16px;
    font-weight: 600;
  }

  .fs14 {
    font-size: 14px;
  }

  .amount {
    font-size: 20px;
  }

  .remark {
    font-size: 12px;
  }

  .remarkTwo {
    display: flex;
    justify-content: space-between;
    font-size: 12px;
  }

  .commissionList {
    padding: 20px;
    background: #F7F8FA;
  }


  .income {
    color: #01C0AC;
    font-weight: 400;
  }

  .outgo {
    color: #F15757;
    font-weight: 400;
  }

  .subItem {
    color: #36394D;
    font-size: 13px;
    display: flex;
    justify-content: space-between;
    font-weight: 600;
  }

  .subItemTitleRight {
    .originalInfo {
      font-size: 12px;
      font-weight: 400;
      color: #858692;
      margin-right: 10px;
      text-decoration: line-through;
    }
  }

  .subItemTwo {
    color: #36394D;
    font-size: 13px;
    display: flex;
    justify-content: space-between;
    font-weight: 600;

    span {
      vertical-align: middle;
    }
  }

  .subItemContainer {
    padding: 5px 0 5px 10px;
    font-size: 13px;
    color: #36394D;
    line-height: 20px;

  }

  .stepComment {
    padding-left: 8px;
    color: #A2A4B3;
  }

  .subItemMoney {
    margin-left: 5px;
    .originalInfo {
      font-size: 12px;
      font-weight: 400;
      color: #858692;
      margin-right: 10px;
      text-decoration: line-through;
    }
  }

  .stepContainer {
    padding-left: 10px;
    padding-bottom: 5px;
  }

  .stepItem {
    display: flex;
    justify-content: space-between;
  }

  .stepItemComment {
    font-size: 12px;
    color: #A2A4B3;
  }

  .info {
    font-size: 13px;
    color: #3F4156;
    font-weight: 600;
  }

  .littlePoint {
    font-size: 4px;
    color: #A2A4B3;
  }

  .subItemTitle {
    color: #3F4156;
    font-size: 14px;
    font-weight: 600;
    display: flex;
    justify-content: space-between;
  }

  .subItemWrap {
    margin-top: 10px;
  }

  .stepItemTitle {
    margin-bottom: 5px;
  }
}

.footer-text {
  margin-top: 16px;
  font-size: 12px;
  color: #A2A4B3;
  text-align: center;
}

.tooltip .roo-tooltip-inner {
  max-width: none;
}

.icon:global(.roo-icon) {
  height: auto;
  vertical-align: bottom;
  cursor: pointer;
}

.tip-content {
  margin: 0;
  table-layout: fixed;
  border-collapse: collapse;
  color: #FAFAFA;
  text-align: left;
  // white-space: nowrap;

  caption {
    text-align: center;
    color: #FFF;
  }

  .no {
    display: inline-block;
    width: 18px;
    height: 18px;
    line-height: 18px;
    border-radius: 3px;
    background: #FFF;
    color: #3F4156;
    text-align: center;
  }

  td {
    padding: 5px;
  }
}

.tooltipContent {
  padding: 10px;

  p {
    font-weight: 600;
    font-family: PingFangSC-Semibold;
    font-size: 14px;
    color: #FFFFFF;
    margin: 10px 0;
  }


  .line {
    height: 1px;
    background-color: #585A6E;
    margin: 20px 0;
  }

  .stepContainer {
    margin-bottom: 10px;
  }

  .stepItemTitle {
    div {
      display: flex;
      justify-content: space-between;
    }
    div:nth-child(1) {
      margin-bottom: 10px;
    }
  }

  .modalStepRow {
    div {
      display: flex;
      justify-content: space-between;
    }
  }
}
</style>
