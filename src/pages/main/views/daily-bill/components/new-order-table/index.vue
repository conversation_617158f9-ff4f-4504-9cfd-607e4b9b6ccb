<template>
<div class="wrapper_table">
    <div class="show"></div>
    <div class="show_right"></div>
 <div class="table-wrap">
    <table class="bill-charge-table">
        <col style="width: 150px;" />
        <col style="width: 100px;" />
        <colgroup class="details">
            <col
                v-for="item in feeTypeList"
                :key="item.typeCode"
                :style="{ width: getColumnWidth(item) }"
            />
        </colgroup>
        <col style="width: 135px;" />
        <col style="width: 75px;" />

        <thead>
            <tr>
                <th key="date">
                    日期&订单编号
                    <i class="roo-icon roo-icon-info-circle">
                        <roo-tooltip
                            styles="light"
                            placement="top"
                        >
                            显示的日期为下单日期及当天的订单序号
                        </roo-tooltip>
                    </i>
                </th>
               
                <th key="type">
                    类型
                </th>

                <!-- 各费用项 -->
                <th
                    v-for="item in feeTypeList"
                    :key="item.typeCode"
                >
                    {{ item.title }}
                    <i
                        v-if="item.tips"
                        key="tip"
                        class="roo-icon roo-icon-info-circle"
                    >
                        <roo-tooltip
                            styles="light"
                            placement="top"
                        >
                            {{ item.tips }}
                        </roo-tooltip>
                    </i>
                </th>

                <th key="settleAmount">
                    结算金额
                </th>
                <th key="action">
                    操作
                </th>
            </tr>
        </thead>

        <tbody>
            <tr
                v-if="loading"
                key="loading"
            > 
                <td
                    :colspan="columnCount"
                    :style="{ height: `${(list.length || 1) * 40}px` }"
                >
                    正在加载
                </td>
            </tr>
            <tr
                v-else-if="list.length === 0"
                key="empty"
            >
                <td :colspan="columnCount">
                    暂无数据
                </td>
            </tr>
            <template v-else>
                <tr
                    v-for="item in list"
                    :key="item.billChargeId"
                >
                    <!-- 日期&订单编号 -->
                    <td>
                        {{ item.outCreateDate }}
                        {{ item.poiOrderPushDayseq && item.poiOrderPushDayseq !== -1 ? `#${item.poiOrderPushDayseq}` : null }}
                    </td>
                    <!-- 类型 -->
                    <td key="type">
                        {{ item.billChargeTypeName }}
                    </td>

                    <td
                        v-for="fee in item.wmPoiBillChargeFeeDynamicVoList"
                        :key="fee.billFeeTypeCode"
                        :class="fee.tip ? 'relative' : null"
                    >
                        {{ fee.feeAmount | formatAmount | display }}

                        <span
                            v-if="fee.tip"
                            class="tip"
                        >
                            {{ fee.tip }}
                        </span>
                    </td>

                    <!-- 结算金额 -->
                    <td
                        key="settleAmount"
                        class="amount"
                    >
                        {{ item.chargeAmount | formatAmount }}
                    </td>

                    <!-- 操作 -->
                    <td key="action">
                        <a
                            v-lx-mc="{
                                bid: 'b_waimai_e_5h5m85uz_mc',
                                options: { cid: 'c_waimai_e_2vuu20sc' },
                            }"
                            href="#noop"
                            @click.prevent="handleDetailClick(item)"
                        >
                            详情
                        </a>
                    </td>
                </tr>
            </template>
        </tbody>

        <tfoot v-if="summary">
            <tr>
                <td
                    key="count"
                    colspan="2"
                >
                    总计（共{{ summary.billChargeCount }}笔）
                </td>

                <td
                    v-for="fee in summary.dailyBillFeeDetails"
                    :key="fee.billFeeTypeCode"
                >
                    {{ fee.feeAmountSum | formatAmount }}
                </td>

                <td
                    key="settleAmount"
                    class="amount"
                    colspan="2"
                >
                    &nbsp;= {{ summary.billChargeAmountSum | formatAmount }}
                </td>
            </tr>
        </tfoot>
    </table>
</div>
</div>
</template>

<script>
import { toast } from '@roo/roo-vue';

/* eslint-disable import/extensions, import/no-unresolved */
import { emptyArray, formatNumberThousand, getQueryString, navigateTo } from '$lib/utils';
import dayjs from 'dayjs';
/* eslint-enable import/extensions, import/no-unresolved */

export default {
    name: 'NewOrderTable',
    filters: {
        formatAmount(num) {
            return formatNumberThousand((num / 100).toFixed(2));
        },

        display(str) {
            return str === '0.00' ? '--' : str;
        },
    },
    props: {
        wmPoiId: {
            type: [String, Number],
            required: true,
        },
        loading: {
            type: Boolean,
            default: false,
        },
        feeTypeList: {
            type: Array,
            default: emptyArray,
        },
        list: {
            type: Array,
            default: emptyArray,
        },
        summary: {
            type: Object,
            default() {
                return {};
            },
        },
    },
    computed: {
        columnCount() {
            return this.feeTypeList.length + 4;
        },
    },
    methods: {
        getColumnWidth(item) {
          if (item.title && item.title.length > 5) {
                const width = Math.max(item.title.length * 15 + 40, 100);
                return `${width}px`;
            }
            return '100px'; // 最小宽度设为100px
        },
        handleDetailClick(item) {
            if (item) {
                /**
                 * 判断web明细类型：webDetailType
                 * -1：无操作
                 * 1: 自链接
                 * 2: 外链接
                 * 3: 弹窗
                 */
                if (item.webDetailType === 1) { // 内部链接... 实际上只有 order-detail
                    this.goToOrderDetailPage(item);
                    // this.$router.push({
                    //     name: 'order-detail',
                    //     query: {
                    //         chargeTypeCode: item.chargeTypeCode,
                    //         billChargeId: item.billChargeId,
                    //         wmOrderViewId: item.wmOrderViewId,
                    //         dailyBillDate: item.dailyBillDate,
                    //         wmPoiId: this.wmPoiId || null,
                    //     },
                    // });
                } else if (item.webDetailType === 2) { // 外部链接
                    // KA 提示逻辑，实际上订单类只会显示订单卡片，此处保持一致
                    if (item.specialType === 1) {
                        if (item.isKa) {
                            toast.warn('活动由总部提报，查询明细请联系总部');
                        } else {
                            // 跳转
                            const path = item.webUrl;
                            navigateTo(path);
                        }
                    } else if (item.webUrl) {
                        // 通用外部链接
                        navigateTo(item.webUrl);
                    } else { // 内部链接兜底
                        this.goToOrderDetailPage(item);
                    }
                } else {
                    this.$emit('show-detail', item);
                }
            }
        },
        goToOrderDetailPage(item) {
            const queryString = getQueryString({
                chargeTypeCode: item.chargeTypeCode,
                billChargeId: item.billChargeId,
                wmOrderViewId: item.wmOrderViewId,
                dailyBillDate: dayjs(item.dailyBillDate).format('YYYY/MM/DD'),
                wmPoiId: this.wmPoiId || null,
            });
            navigateTo(`/finance/newfe#/finance/orderDetail?${queryString}`);
        },
    },
};
</script>