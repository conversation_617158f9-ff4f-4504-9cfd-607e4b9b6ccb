<template>
    <table class="bill-charge-table">
        <col>
        <col>
        <col>
        <col>
        <col>

        <thead>
            <tr>
                <th>日期</th>
                <th>类型</th>
                <th>详细类型</th>
                <th>结算金额</th>
                <th>操作</th>
            </tr>
        </thead>

        <tbody>
            <tr
                v-if="loading"
                key="loading"
            >
                <td
                    :style="{ height: `${(list.length || 1) * 40}px` }"
                    colspan="5"
                >
                    正在加载
                </td>
            </tr>
            <tr
                v-else-if="list.length === 0"
                key="empty"
            >
                <td colspan="5">
                    暂无数据
                </td>
            </tr>
            <template v-else>
                <tr
                    v-for="item in list"
                    :key="item.billChargeId"
                >
                    <td>
                        {{ item.outCreateDate }}
                        {{ item.poiOrderPushDayseq && item.poiOrderPushDayseq !== -1 ? `#${item.poiOrderPushDayseq}` : null }}
                    </td>
                    <td>{{ item.billChargeTypeName }}</td>
                    <td>
                        {{ item.chargeTypeName }}
                        <i
                            v-if="item.tips"
                            key="tip"
                            class="roo-icon roo-icon-info-circle"
                        >
                            <roo-tooltip
                                styles="light"
                                placement="top"
                            >
                                {{ item.tips }}
                            </roo-tooltip>
                        </i>
                    </td>
                    <td>{{ item.chargeAmount | formatAmount }}</td>
                    <td>
                        <template v-if="item.webDetailType === -1">
                            --
                        </template>
                        <template v-else>
                            <a
                                v-lx-mc="{
                                    bid: 'b_waimai_e_5h5m85uz_mc',
                                    options: { cid: 'c_waimai_e_2vuu20sc' },
                                }"
                                href="#noop"
                                @click.prevent="handleDetailClick(item)"
                            >
                                详情
                            </a>
                        </template>
                    </td>
                </tr>
            </template>
        </tbody>
    </table>
</template>

<script>
import { toast } from '@roo/roo-vue';

/* eslint-disable import/extensions, import/no-unresolved */
import { formatNumberThousand, getQueryString, navigateTo } from '$lib/utils';
import dayjs from 'dayjs';
/* eslint-enable import/extensions, import/no-unresolved */

export default {
    name: 'NewOtherTable',
    filters: {
        formatAmount(num) {
            if (num === 0) {
                return '--';
            }

            return formatNumberThousand((num / 100).toFixed(2));
        },

        // 费用项金额为 0 展示为 --
        display(str) {
            return str === '0.00' ? '--' : str;
        },
    },
    props: {
        wmPoiId: {
            type: [String, Number],
            required: true,
        },
        loading: {
            type: Boolean,
            default: false,
        },
        list: {
            type: Array,
            default() {
                return [];
            },
        },
    },
    methods: {
        handleDetailClick(item) {
            if (item) {
                if (item.webDetailType === 1) { // 内部链接... 实际上只有 order-detail
                    this.goToOrderDetailPage(item);
                } else if (item.webDetailType === 2) { // 外部链接
                    if (item.specialType === 1) { // KA 提示逻辑
                        if (item.isKa) {
                            toast.warn('活动由总部提报，查询明细请联系总部');
                        } else {
                            // 跳转
                            const path = item.webUrl;
                            navigateTo(path);
                        }
                    } else if (item.webUrl) { // 通用外部链接
                        navigateTo(item.webUrl);
                    } else { // 内部链接兜底
                        this.goToOrderDetailPage(item);
                    }
                } else {
                    this.$emit('show-detail', item);
                }
            }
        },
        goToOrderDetailPage(item) {
            const queryString = getQueryString({
                chargeTypeCode: item.chargeTypeCode,
                billChargeId: item.billChargeId,
                wmOrderViewId: item.wmOrderViewId,
                dailyBillDate: dayjs(item.dailyBillDate).format('YYYY/MM/DD'),
                wmPoiId: this.wmPoiId || null,
            });
            navigateTo(`/finance/newfe#/finance/orderDetail?${queryString}`);
        },
    },
};
</script>
