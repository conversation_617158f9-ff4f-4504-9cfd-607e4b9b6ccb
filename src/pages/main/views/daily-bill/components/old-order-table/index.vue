<template>
    <table class="bill-charge-table">
        <col style="width: 150px;">
        <col style="width: 100px;">
        <colgroup class="details">
            <col>
            <col>
            <col>
            <col>
            <col>
            <col>
        </colgroup>
        <col style="width: 135px;">
        <col style="width: 75px;">

        <thead>
            <tr>
                <th>
                    日期&订单编号
                    <i class="roo-icon roo-icon-info-circle">
                        <roo-tooltip
                            styles="light"
                            placement="top"
                        >
                            “外卖订单”显示的日期为下单时间。<br>“退款订单”显示的日期为退款完成时间。
                        </roo-tooltip>
                    </i>
                </th>
                <th>类型</th>
                <th>
                    菜品原价
                    <i class="roo-icon roo-icon-info-circle">
                        <roo-tooltip
                            styles="light"
                            placement="top"
                        >
                            订单中菜品金额总计
                        </roo-tooltip>
                    </i>
                </th>
                <th>
                    包装费用
                    <i class="roo-icon roo-icon-info-circle">
                        <roo-tooltip
                            styles="light"
                            placement="top"
                        >
                            餐盒和打包袋费用
                        </roo-tooltip>
                    </i>
                </th>
                <th>
                    商家活动支出
                    <i class="roo-icon roo-icon-info-circle">
                        <roo-tooltip
                            styles="light"
                            placement="top"
                        >
                            订单中商家补贴的活动款金额
                        </roo-tooltip>
                    </i>
                </th>
                <th>
                    平台服务费
                    <i class="roo-icon roo-icon-info-circle">
                        <roo-tooltip
                            styles="light"
                            placement="top"
                        >
                            订单抽佣费用
                        </roo-tooltip>
                    </i>
                </th>
                <th>配送费</th>
                <th>公益捐款</th>
                <th>
                    结算金额
                    <i class="roo-icon roo-icon-info-circle">
                        <roo-tooltip
                            styles="light"
                            placement="top"
                        >
                            商家实际收入，结算金额=菜品原价+包装费-商家活动支出-平台服务费
                        </roo-tooltip>
                    </i>
                </th>
                <th>操作</th>
            </tr>
        </thead>

        <tbody>
            <tr
                v-if="loading"
                key="loading"
            >
                <td
                    :style="{ height: `${(list.length || 1) * 40}px` }"
                    colspan="10"
                >
                    加载中
                </td>
            </tr>
            <tr
                v-else-if="list.length === 0"
                key="empty"
            >
                <td
                    colspan="10"
                >
                    暂无数据
                </td>
            </tr>
            <template v-else>
                <tr
                    v-for="item in list"
                    :key="item.billChargeId"
                >
                    <!-- 日期&订单编号 -->
                    <td>
                        {{ item.outCreateDate }}
                        {{ item.poiOrderPushDayseq && item.poiOrderPushDayseq !== -1 ? `#${item.poiOrderPushDayseq}` : null }}
                    </td>
                    <!-- 日期 -->
                    <td>{{ item.billChargeTypeName }}</td>
                    <!-- billChargeType 1=外卖订单,2=退款订单 -->
                    <!-- 菜品原价 -->
                    <td>{{ item | formatFoodAmount }}</td>
                    <!-- 餐盒费 -->
                    <td>{{ item | formatBoxAmount }}</td>
                    <!-- 商家活动支出 -->
                    <td>{{ item | formatActAmount }}</td>
                    <!-- 平台服务费 -->
                    <td>{{ item | formatPlatformCommissionAmount }}</td>
                    <!-- 用户支付配送费 -->
                    <td :class="item.quickShippingTipAmount ? 'relative' : null">
                        {{ item | formatShippingAmount }}
                        <span
                            v-if="item.quickShippingTipAmount"
                            class="tip"
                        >
                            配送小费
                        </span>
                    </td>
                    <td>{{ item | formatDonationAmount }}</td>
                    <!-- 结算金额 -->
                    <td class="amount">{{ item.chargeAmount | formatNumberThousand }}</td>
                    <!-- 操作 -->
                    <td>
                        <router-link :to="getBillDetailRoute(item)">详情</router-link>
                    </td>
                </tr>
            </template>
        </tbody>

        <tfoot v-if="summary">
            <tr>
                <td colspan="2">总计（共{{ summary.billChargeCount }}笔）</td>
                <td>{{ summary.foodAmount | formatCents | formatNumberThousand }}</td>
                <td>{{ summary.boxAmount | formatCents | formatNumberThousand }}</td>
                <td>{{ summary.actAmount | formatCents | formatNumberThousand }}</td>
                <td>{{ summary.platformCommissionAmount | formatCents | formatNumberThousand }}</td>
                <td>{{ summary.shippingAmount | formatCents | formatNumberThousand }}</td>
                <td>{{ summary.donationAmount | formatCents | formatNumberThousand }}</td>
                <td
                    colspan="2"
                    class="amount"
                >
                    &nbsp;= {{ summary.billChargeAmountSum | formatCents | formatNumberThousand }}
                </td>
            </tr>
        </tfoot>
    </table>
</template>

<script>
/* eslint-disable import/extensions, import/no-unresolved */
import { formatNumberThousand, getQueryString, navigateTo } from '$lib/utils';
import dayjs from 'dayjs';
/* eslint-enable import/extensions, import/no-unresolved */

export default {
    name: 'OldOrderTable',
    filters: {
        formatNumberThousand,
        formatFoodAmount({ foodAmount, billChargeType }) {
            const amount = formatNumberThousand(foodAmount); // 添加千分位
            if (parseFloat(foodAmount) === 0) return amount;
            return billChargeType === 1 ? amount : `-${amount}`;
        },
        formatBoxAmount({ boxAmount, billChargeType }) {
            if (!boxAmount || boxAmount === '--') return '--';

            const amount = formatNumberThousand(boxAmount);
            if (parseFloat(boxAmount) === 0) return amount;
            return billChargeType === 1 ? amount : `-${amount}`;
        },
        formatActAmount({ actAmount, billChargeType }) {
            const amount = formatNumberThousand(actAmount);
            if (parseFloat(actAmount) === 0) return amount;
            return billChargeType === 1 ? `-${amount}` : amount;
        },
        formatPlatformCommissionAmount({ platformCommissionAmount, billChargeType }) {
            const amount = formatNumberThousand(platformCommissionAmount);
            if (parseFloat(platformCommissionAmount) === 0) return amount;
            return billChargeType === 1 ? `-${amount}` : amount;
        },
        // 格式化配送费
        formatShippingAmount({ quickShippingTipAmount, shippingAmount, billChargeType }) {
            // 快送小费 对于 外卖订单 来说是负值
            if (quickShippingTipAmount) {
                if (parseFloat(quickShippingTipAmount) === 0) {
                    return '--';
                }
                const amount = formatNumberThousand(quickShippingTipAmount);
                return billChargeType === 1 ? `-${amount}` : amount;
            }

            // 配送费
            const amount = formatNumberThousand(shippingAmount);
            if (billChargeType === 1) { // 外卖订单 正向数据
                return parseFloat(shippingAmount) === 0 ? '--' : `${amount}`;
            }
            // 2 退款订单
            return parseFloat(shippingAmount) === 0 ? '--' : `-${amount}`;
        },
        formatDonationAmount({ donationAmount, billChargeType }) {
            // 无捐款后端返回 '--'
            if (!donationAmount || donationAmount === '--') {
                return '--';
            }
            const amount = formatNumberThousand(donationAmount);
            return billChargeType === 1 ? `-${amount}` : amount;
        },
        formatCents(num) {
            return (num / 100).toFixed(2);
        },
    },
    props: {
        wmPoiId: {
            type: [String, Number],
            required: true,
        },
        loading: {
            type: Boolean,
            default: false,
        },
        list: {
            type: Array,
            default() {
                return [];
            },
        },
        summary: {
            type: Object,
            default: null,
        },
    },
    methods: {
        getBillDetailRoute(bill) {
            const query = {
                chargeTypeCode: bill.chargeTypeCode,
                billChargeId: bill.billChargeId,
                wmOrderViewId: bill.wmOrderViewId,
                dailyBillDate: dayjs(bill.dailyBillDate).format('YYYY/MM/DD'),
                wmPoiId: this.wmPoiId || null,
            };
            const queryString = getQueryString(query);
            navigateTo(`/finance/newfe#/finance/orderDetail?${queryString}`);
        },
    },
};
</script>
