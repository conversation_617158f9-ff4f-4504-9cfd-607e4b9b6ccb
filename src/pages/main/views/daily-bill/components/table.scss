.wrapper_table {
    position: relative;
    .show{
        width: 1px;
        box-shadow: 10px 0px 10px #bbbdbd;
        position: absolute;
        left: 240px;
        top: 0;
        bottom: 0px;
        z-index: 999;
    }
    .show_right{
        width: 1px;
        box-shadow: 10px 0px 10px #bbbdbd;
        position: absolute;
        right: 216px;
        top: 0;
        bottom: 0px;
        z-index: 999;
    }
}
.table-wrap{
    width: 100%;
    overflow: auto;
}
.bill-charge-table {
    width: 100%;
    max-width: 100%;
    table-layout: fixed;
    border: 1px solid #E9EAF2;
    border-collapse: collapse;
    background: #FCFCFC;
    color: #3F4156;
    text-align: center;

    thead {
        background: #F7F8FA;
        color: #858692;
        width: 100px;
        th {
            text-align: center;
            border-left-style: hidden;
            border-right-style: hidden;
            width: 150px;
            &:first-child,
            &:last-child {
                border-left-style: none;
                border-right-style: none;
            }
        }
        th:first-child,
        th:last-child,
        td:first-child,
        td:last-child {
            position: sticky;
            left: 0;
            background: #F7F8FA;
            text-align: center;
            right: 0px;
            border-left: 1px solid #DDDDDD ;
            width: 100px;
        }
        th:nth-child(2),
        td:nth-child(2){
           position: sticky;
           left: 149px;
           background: #F7F8FA;
           text-align: center;
           right: 0px;
           border-left: 1px solid #DDDDDD ;
           width: 100px;
        }
        th:nth-last-child(2),
        td:nth-last-child(2){
           position: sticky;
           left: 150px;
           background: #F7F8FA;
           text-align: center;
           right: 74px;
           border-left: 1px solid #DDDDDD ;
           width: 100px;
        }
    }

     /* 首列固定/最后一列固定*/
    //  th:first-child,
    //  th:last-child,
    //  td:first-child,
    //  td:last-child {
    //      position: sticky;
    //      left: 0;
    //      background: #F7F8FA;
    //      text-align: center;
    //      right: 0px;
    //      border-left: 1px solid #DDDDDD ;
    //      width: 100px;
    //  }

    tbody{
        th:first-child,
        th:last-child,
        td:first-child,
        td:last-child {
            position: sticky;
            left: 0;
            background: #F7F8FA;
            text-align: center;
            right: 0px;
            border-left: 1px solid #DDDDDD ;
            width: 100px;
        }
        th:nth-child(2),
        td:nth-child(2){
           position: sticky;
           left: 149px;
           background: #F7F8FA;
           text-align: center;
           right: 0px;
           border-left: 1px solid #DDDDDD ;
           width: 100px;
        }
        th:nth-last-child(2),
        td:nth-last-child(2){
           position: sticky;
           left: 150px;
           background: #F7F8FA;
           text-align: center;
           right: 74px;
           border-left: 1px solid #DDDDDD ;
           width: 100px;
        } 
    }
    tfoot{
        th:first-child,
        th:last-child,
        td:first-child,
        td:last-child {
            position: sticky;
            left: 0;
            background: #F7F8FA;
            text-align: center;
            right: 0px;
            border-left: 1px solid #DDDDDD ;
            width: 100px;
        }
    }

     /* 表头首列和最后一列强制最顶层 */
     th:last-child,
     th:nth-child(2),
     td:nth-last-child(2),
     th:first-child {
         z-index: 3;
         /*左上角单元格z-index，切记要设置，不然表格纵向横向滚动时会被该单元格右方或者下方的单元格遮挡*/
        //  background: #858692;
     }

    i.roo-icon {
        height: auto; // 为了居中
        margin-right: -1em;
        cursor: pointer;
    }

    tr {
        height: 40px;
    }

    th, td {
        border-top: 1px solid #E9EAF2;
        border-bottom: 1px solid #E9EAF2;
    }

    .details {
        border-left: 1px dashed #E9EAF2;
        border-right: 1px dashed #E9EAF2;
        background-color: #FCFCFC;
        
    }

    .relative {
        position: relative;
    }
    .tip {
        position: absolute;
        left: 0;
        right: 0;
        bottom: 0;
        line-height: 14px;
        color: #999;
    }
}
