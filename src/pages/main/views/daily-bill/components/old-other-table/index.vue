<template>
    <table class="bill-charge-table">
        <col>
        <col>
        <col>
        <col>
        <col>

        <thead>
            <tr>
                <th>日期</th>
                <th>类型</th>
                <th>详细类型</th>
                <th>结算金额</th>
                <th>操作</th>
            </tr>
        </thead>

        <tbody>
            <tr
                v-if="loading"
                key="loading"
            >
                <td
                    :style="{ heigth: `${(list.length || 1) * 40}px` }"
                    colspan="5"
                >
                    加载中
                </td>
            </tr>
            <tr
                v-else-if="list.length === 0"
                key="empty"
            >
                <td colspan="5">
                    暂无数据
                </td>
            </tr>
            <template v-else>
                <tr
                    v-for="item in list"
                    :key="item.billChargeId"
                >
                    <!-- 日期&订单编号 -->
                    <td>
                        {{ item.outCreateDate }}
                        {{ item.poiOrderPushDayseq && item.poiOrderPushDayseq !== -1 ? `#${item.poiOrderPushDayseq}` : null }}
                    </td>
                    <!-- 类型 -->
                    <td>{{ item.billChargeTypeName }}</td>
                    <!-- 详细 -->
                    <td>{{ item.chargeTypeName }}</td>
                    <!-- 结算金额 -->
                    <td class="amount">{{ item.chargeAmount | formatNumberThousand }}</td>
                    <!-- 操作 -->
                    <td>
                        <template v-if="item.chargeTypeCode === 2800">
                            --
                        </template>
                        <template v-else>
                            <a
                                href="#noop"
                                @click.prevent="handleDetailClick(item)"
                            >
                                详情
                            </a>
                        </template>
                    </td>
                </tr>
            </template>
        </tbody>
    </table>
</template>

<script>
import { toast } from '@roo/roo-vue';

/* eslint-disable import/extensions, import/no-unresolved */
import { formatNumberThousand, getQueryString, navigateTo } from '$lib/utils';
import dayjs from 'dayjs';
/* eslint-enable import/extensions, import/no-unresolved */

export default {
    name: 'OldOtherTable',
    filters: {
        formatNumberThousand,
    },
    props: {
        wmPoiId: {
            type: [String, Number],
            required: true,
        },
        loading: {
            type: Boolean,
            default: false,
        },
        list: {
            type: Array,
            default() {
                return [];
            },
        },
    },
    methods: {
        handleDetailClick(bill) {
            if (bill) {
                if (bill.chargeTypeCode === 6000 || bill.chargeTypeCode === 6001) {
                    if (bill.isKA) {
                        toast.warn('活动由总部提报，查询明细请联系总部');
                    } else {
                        // 跳转
                        const href = `${window.location.origin}/ad/v1/pc#/sales/detail?date=${bill.outCreateDate.replace(/-/g, '')}`;
                        const message = JSON.stringify({
                            method: 'jumpTo',
                            args: [{ href }],
                        });

                        window.parent.postMessage(message, '*');
                    }
                    return;
                }
                const query = {
                    chargeTypeCode: bill.chargeTypeCode,
                    billChargeId: bill.billChargeId,
                    wmOrderViewId: bill.wmOrderViewId,
                    dailyBillDate: dayjs(bill.dailyBillDate).format('YYYY/MM/DD'),
                    wmPoiId: this.wmPoiId || null,
                };
                const queryString = getQueryString(query);
                navigateTo(`/finance/newfe#/finance/orderDetail?${queryString}`);
            }
        },
    },
};
</script>
