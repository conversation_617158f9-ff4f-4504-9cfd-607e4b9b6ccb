<template>
    <div :class="$style.panel">
        <div :class="$style.header">
            <h3 :class="$style.title">
                结算ID：{{ settleSettingId }}&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; 卡号：{{ cardNum }} &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; 户名：{{ accountName }}
            </h3>
            <!-- <roo-button
                v-lx-mc="{
                    bid: 'b_waimai_e_k70u0s1m_mc',
                    options: { cid: 'c_waimai_e_aine8qhs' },
                }"
                v-if="unbindCount === 0"
                size="small"
                @click="toAccountFlow"
            >
                进入账户
            </roo-button> -->
            <p
                v-if="unPoiList.length === 0 && showSettleSettingButton"
                v-lx-mc="{
                    bid: 'b_waimai_e_q7y85ghv_mc',
                    options: {
                        cid: 'c_waimai_e_5o2lxsdv',
                    },
                }"
                :class="$style.edit_message"
                @click="toEditAccount"
            >
                修改结算信息
                <roo-tooltip
                    trigger="hover"
                    placement="bottom"
                >
                    1、如果需要修改开户行、银行卡号、开户人手机号、结算账户绑定门店，可点击“修改”按钮；
                    <br />

                    2、如果需要更换A门店绑定的结算ID，可以在已有的结算ID的关联门店中添加A门店，或新建一个结算ID绑定A门店。
                </roo-tooltip>
            </p>
            <p
                v-if="unPoiList.length === 0"
                v-lx-mc="{
                    bid: 'b_waimai_e_k70u0s1m_mc',
                    options: { cid: 'c_waimai_e_aine8qhs' },
                }"
                :class="$style.go_account"
                @click="toAccountFlow"
            >
                进入账户  > 
            </p>
            <span
                v-lx-mc="{
                    bid: 'b_waimai_e_k70u0s1m_mc',
                    options: { cid: 'c_waimai_e_aine8qhs' },
                }"
                v-else
                :class="$style.redTip"
                @click="$emit('alert', unPoiList)"
            >
                {{ unPoiList.length }}个门店未绑定到该登录账号，请联系业务经理 查看详情>
            </span>
        </div>
        <div :class="$style.body">
            <p style="color: #222222;font-weight: 500;font-size: 14px;">
                提现方式 : 自动提现
            </p>
            <p style="color: #222222;font-weight: 500;font-size: 14px;">
                {{ isDelay == 1 ? '结算账期' : '结算周期' }} : {{ settlePeriod }}
            </p>
            <div :class="$style.separator" ></div>
            <p style="font-size: 14px;font-weight: 500;color: #222222;">
                结算门店 : 共 {{ poiCounts }} 个
                <!-- <span v-if="unbindCount > 0 || offlinePoiCount > 0">
                    （ <span v-if="unbindCount > 0"><span :class="$style.red">{{ unbindCount }}</span>个门店未绑定</span>
                    <span v-if="offlinePoiCount > 0"> <span style="color: darkgrey;">{{ offlinePoiCount }}</span>个门店已下线</span> ）
                </span> -->
            </p>
            <div :class="$style.poiStatus">
                <p v-if="poiCount > 0" style="height: 0px;font-size: 12px;color: #222222;font-weight: 500;">
                    正常 : {{ poiCount }}个门店
                </p>
                <div :class="poiCount > 0 ? $style.poiLists : $style.newPoiLists">
                    <p :class="collapsed ? $style.poiList : null" style="width: 95%;">
                        <span
                            v-for="(poi, idx) in poiList || []"
                            :key="poi.wmPoiId"
                            :class="poi.bindStatus ? $style.poi : [$style.poi, $style.red]"
                        >
                            {{ `${idx > 0 ? '、' : ''}${poi.wmPoiId}-${poi.wmPoiName}` }}
                        </span>
                    </p>
                    <p
                        v-if="showToggle(poiList)"
                        :class="$style.link"
                        style="margin-left: 10px;"
                        @click="collapsed = !collapsed"
                    >
                        {{ collapsed ? '展开' : '收起' }}
                        <i
                            :class="{ 'roo-icon-chevron-down': collapsed, 'roo-icon-chevron-up': !collapsed }"
                            class="roo-icon"
                        ></i>
                    </p>
                </div>
                <p v-if="unPoiList.length > 0" style="color: #FF192D;height: 0px;font-size: 12px;font-weight: 500;">
                    未绑定 : {{ unPoiList.length }}个门店
                </p>
                <div :class="unPoiList.length > 0 ? $style.poiLists : $style.newPoiLists">
                    <p :class="unPoiCollasped ? $style.poiList : null">
                        <span
                            v-for="(poi, idx) in unPoiList"
                            :key="poi.wmPoiId"
                            :class="poi.bindStatus ? $style.poi : [$style.poi, $style.red]"
                        >
                            {{ `${idx > 0 ? '、' : ''}${poi.wmPoiId}-${poi.wmPoiName}` }}
                        </span>
                    </p>
                    <p
                        v-if="showToggle(unPoiList)"
                        :class="$style.link"
                        style="margin-left: 10px;"
                        @click="unPoiCollasped = !unPoiCollasped"
                    >
                        {{ unPoiCollasped ? '展开' : '收起' }}
                        <i
                            :class="{ 'roo-icon-chevron-down': unPoiCollasped, 'roo-icon-chevron-up': !unPoiCollasped }"
                            class="roo-icon"
                        ></i>
                    </p>
                </div>
                <p v-if="freezePoiList.length > 0" style="color: #FF192D;height: 0px;font-size: 12px;font-weight: 500;">
                    已冻结 : {{ freezePoiList.length }}个门店
                </p>
                <div :class="freezePoiList.length > 0 ? $style.poiLists : $style.newPoiLists">
                    <p :class="freezePoiCollasped ? $style.poiList : null" style="width: 95%;">
                        <span
                            v-for="(poi, idx) in freezePoiList"
                            :key="poi.wmPoiId"
                            :class="poi.bindStatus ? $style.poi : [$style.poi, $style.red]"
                        >
                            {{ `${idx > 0 ? '、' : ''}${poi.wmPoiId}-${poi.wmPoiName}` }}
                            <span style="color:#FF6A00 ;cursor: pointer;font-size: 12px;" @click="checkFreeze(poi)">查看冻结原因</span>
                        </span>
                        <roo-modal
                            v-model="freezeModal"
                            size="small"
                        >
                            <h3>门店被冻结</h3>
                            <div v-if="freezeReason" style="margin-top: 10px;">
                                {{ freezeReason }}
                            </div>
                            <div v-else>
                                {{ '很抱歉，您当前门店的账户已冻结，无法提现。如有问题请联系业务经理处理，相关款项会在账户解冻后的下个账期发起提现。谢谢' }}
                            </div>
                            <template slot="footer">
                                <roo-button
                                    v-if="frozenReasonType == 5"
                                    type="hollow"
                                    @click="freezeModal = false"
                                >
                                    取消
                                </roo-button>
                                <roo-button v-if="frozenReasonType == 5" @click="handlefreeze">
                                    修改资质信息
                                </roo-button>
                                <roo-button v-else @click="freezeModal = false">
                                    我知道了
                                </roo-button>
                            </template>
                        </roo-modal>
                    </p>
                    <p
                        v-if="showToggle(freezePoiList)"
                        :class="$style.link"
                        style="margin-left: 10px;"
                        @click="freezePoiCollasped = !freezePoiCollasped"
                    >
                        {{ freezePoiCollasped ? '展开' : '收起' }}
                        <i
                            :class="{ 'roo-icon-chevron-down': freezePoiCollasped, 'roo-icon-chevron-up': !freezePoiCollasped }"
                            class="roo-icon"
                        ></i>
                    </p>
                </div>
                <p v-if="offlinePoiList.length > 0" style="height: 0px;font-size: 12px;color: #222222;font-weight: 500;">
                    已下线 : {{ offlinePoiList.length }}个门店
                </p>
                <div :class="offlinePoiList.length > 0 ? $style.poiLists : $style.newPoiLists">
                    <p :class="offlinePoiCollasped ? $style.poiList : null" style="width: 95%;">
                        <span
                            v-for="(poi, idx) in offlinePoiList"
                            :key="poi.wmPoiId"
                            :class="poi.bindStatus ? $style.poi : [$style.poi, $style.red]"
                        >
                            {{ `${idx > 0 ? '、' : ''}${poi.wmPoiId}-${poi.wmPoiName}` }}
                        </span>
                    </p>
                    <p
                        v-if="showToggle(offlinePoiList)"
                        :class="$style.link"
                        style="margin-left: 10px;"
                        @click="offlinePoiCollasped = !offlinePoiCollasped"
                    >
                        {{ offlinePoiCollasped ? '展开' : '收起' }}
                        <i
                            :class="{ 'roo-icon-chevron-down': offlinePoiCollasped, 'roo-icon-chevron-up': !offlinePoiCollasped }"
                            class="roo-icon"
                        ></i>
                    </p>
                </div>
            </div>
            <!-- <div
                :class="$style.toggle"
            >
                <span
                    :class="$style.link"
                    @click="collapsed = !collapsed"
                >
                    {{ collapsed ? '展开全部' : '收起' }}
                    <i
                        :class="{ 'roo-icon-chevron-down': collapsed, 'roo-icon-chevron-up': !collapsed }"
                        class="roo-icon"
                    />
                </span>
            </div> -->
        </div>
    </div>
</template>

<script>
import axios from 'axios';
/* eslint-disable import/extensions, import/no-unresolved */
import { selectPoi } from '$lib/bridge';
import { navigateTo, getQueryString, getHost, getCookies, getHosts } from '$lib/utils';
/* eslint-enable import/extensions, import/no-unresolved */
import { toast } from '@roo/roo-vue';


function isBoolean(val) {
    return typeof val === 'boolean' || val instanceof Boolean;
}
const { CancelToken } = axios;

export default {
    name: 'AccountCard',
    props: {
        accountId: { // 返回的 accountId 实际上是 wmPoiId
            type: String,
            required: true,
        },
        cardNum: {
            type: String,
            required: true,
        },
        settleSettingId: {
            type: String,
            required: true,
        },
        showSettleSettingButton: {
            type: Boolean,
            required: true,
        },
        accountName: {
            type: String,
            required: true,
        },
        settlePeriod: {
            type: String,
            required: true,
        },
        poi: {
            type: String,
            required: true,
        },
        isDelay: {
            type: Number,
            required: true,
        },
        // 正常门店
        poiList: {
            type: Array,
            required: true,
            validator(list) {
                return list.length > 0 && list.every(poi => poi.wmPoiId
                    && poi.wmPoiName
                    && poi.accountId
                    && isBoolean(poi.bindStatus));
            },
        },
        // 未绑定门店
        unPoiList: {
            type: Array,
            required: true,
            validator(list) {
                return list.length > 0 && list.every(poi => poi.wmPoiId
                    && poi.wmPoiName
                    && poi.accountId
                    && isBoolean(poi.bindStatus));
            },
        },
        // 下线门店
        offlinePoiList: {
            type: Array,
            required: true,
            validator(list) {
                return list.length > 0 && list.every(poi => poi.wmPoiId
                    && poi.wmPoiName
                    && poi.accountId
                    && isBoolean(poi.bindStatus));
            },
        },
        // 冻结门店
        freezePoiList: {
            type: Array,
            required: true,
            validator(list) {
                return list.length > 0 && list.every(poi => poi.wmPoiId
                    && poi.wmPoiName
                    && poi.accountId
                    && isBoolean(poi.bindStatus));
            },
        },
        offlinePoiCount: {
            type: Number,
            default: 0,
        },
        poiCounts: {
            type: Number,
            default: 0,
        },
    },
    data() {
        return {
            collapsed: true,
            freezeModal: false,
            unPoiCollasped: true,
            offlinePoiCollasped: true,
            freezePoiCollasped: true,
            freezeReason: '',
            frozenReasonType: 0,
        };
    },
    computed: {
        // 结算门店数
        poiCount() {
            return this.poiList.length;
        },
    },
    methods: {
        handlefreeze() {
            navigateTo(`${getHosts()}/v2/shop/manage/shopInfo`);
        },
        // 是否显示 展开全部/收起 按钮
        showToggle(poiLists) {
            const { length } = poiLists.length > 0 && poiLists.map(poi => poi.wmPoiName).join('、');// 1640;
            return length >= 50;
        },
        // 查看冻结原因
        checkFreeze(poi) {
            this.freezeModal = !this.freezeModal;
            if (poi.freezeReason !== '') {
                this.freezeReason = poi.freezeReason;
            }
            if (poi.frozenReasonType !== 0) {
                this.frozenReasonType = poi.frozenReasonType;
            }
        },
        // 进入账户
        toAccountFlow() {
            const query = {};

            if (this.poiList.length > 1) {
                query.accountId = this.accountId;
                query.poiCount = this.poiList.length;
                const queryString = getQueryString(query);
                navigateTo(`/gw/static_resource/finance#/finance/accountInfo?${queryString}`);
            } else {
                const poi = this.poiList[0];
                // 门店下线之后wmPoiInfoVoList字段返回空数组，继续执行会导致前端报错
                if (!poi) {
                    toast.warn('门店信息缺失');
                    return;
                }
                const jumpUrl = '/gw/static_resource/finance#/finance/accountInfo';
                // 外壳切换成单店
                selectPoi(poi.wmPoiId, poi.name, jumpUrl);
            }
        },
        // 修改结算信息
        toEditAccount() {
            const cookies = getCookies();
            const acctId = parseInt(cookies.acctId, 10) || null;
            const token = cookies.token || null;
            const query = {
                BSID: token,
                source: 16,
                page: 'daojiaSelfSettleEdit',
                type: 'PC',
                verifyStrategy: 'waimaiSelfSettleEdit',
                entryFlowId: this.settleSettingId,
                iphPayMerchantNo: getHost().indexOf('test') >= 0 ? ************** : **************,
                bizType: 12001,
                acctId,
                poiList: '',
                poi: this.poi,
                cbType: 5,
                sourceName: 'waimai',
            };
            const queryString = getQueryString(query);
            navigateTo(`${getHost()}/merchant/front/common/merchant-entry-login-verify?${queryString}`);
        },
    },
};
</script>

<style lang="scss" module>
$red: #FF5A5A;
$green: #06C1AE;
$grey: #6A6A6A;

.panel {
    border: 1px solid #E9EAF2;
    background: #FFF;

    margin-bottom: 20px;

    &:last-child {
        margin-bottom: 0;
    }
}

.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 50px;
    padding: 0 20px;
    background: #F7F8FA;
    position: relative;

    h3 {
        margin: 0;
    }
}

.red-tip {
    color: #FF5A5A;
    cursor: pointer;
}

.body {
    display: block;
    padding: 10px 20px;

    p {
        margin: 10px 0;
    }
}

.separator {
    display: block;
    height: 0;
    margin: 20px 0;
    border-top: 2px dashed #e5e5e5;
}
.poi-list {
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
}
.toggle {
    margin-top: 10px;
    text-align: center;
}
.red {
    color: $red;
}
.green {
    color: $green;
}

.poi {
    font-size: 12px;
    color: $grey;
}
.poi.red {
    color: $red;
}

.link {
    color: #3F4156;
    cursor: pointer;
    user-select: none;
    font-size: 12px;
    // margin-left: 30px;
}
.go_account{
  font-weight: 500;
  font-family: PingFangSC-Medium;
  font-size: 14px;
  color: #FF6A00;
  text-align: right;
  cursor: pointer;
}
.edit_message{
  font-weight: 500;
  font-family: PingFangSC-Medium;
  font-size: 14px;
  color: #FF6A00;
  cursor: pointer;
  position: absolute;
  right: 116px;
}
.poiLists{
   display: flex;
   align-items: center;
}
.newPoiLists{
  display: flex;
  align-items: center;
  height: 0px;
}
.poiStatus{
  border-radius: 2px;
  background: #F5F6FA;
  padding: 12px;
}
</style>
