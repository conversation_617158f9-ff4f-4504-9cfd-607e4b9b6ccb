<template>
    <div :class="$style.container">
        <div :class="$style.card">
            <div :class="$style.selectMessage">
                <roo-input
                    v-model="keyword"
                    :class="$style.search"
                    placeholder="请输入结算ID/门店ID/卡号/户名/门店名称"
                />
                <roo-button
                    slot="append"
                    :class="$style.messageBtn"
                    @click="handleSearchClick"
                >
                    查询
                </roo-button>
            </div>
            <div :class="$style.messageTop">
                <p :class="$style.allMessage">
                    全部结算信息
                </p>
                <span
                    v-if="list.showSettleSettingAddButton"
                    v-lx-mc="{
                        bid: 'b_waimai_e_9kfaw4w3_mc',
                        options: {
                            cid: 'c_waimai_e_5o2lxsdv',
                        },
                    }"
                    :class="$style.addMessage"
                    @click="toAddAccount"
                >
                    新增结算信息
                    <roo-tooltip
                        trigger="hover"
                        placement="bottom"
                    >
                        1、如果需要修改结算账户类型（个人/对公账户）、开户人姓名&证件号或公司名称&统一社会信用代码，可点击“新增结算”按钮；
                        <br />
                        
                        2、如果需要更换A门店绑定的结算ID，可以在已有的结算ID的关联门店中添加A门店，或新建一个结算ID绑定A门店；
                    </roo-tooltip>
                </span>
            </div>
            <!-- 账户卡片列表 -->
            <div :class="$style.accountList">
                <!-- accountId 都是 0？ -->
                <account-card
                    v-for="(account, idx) in showList"
                    :key="idx"
                    :account-id="account.accountId"
                    :card-num="account.cardNo"
                    :settle-setting-id="account.settleSettingId"
                    :show-settle-setting-button="account.showSettleSettingButton"
                    :account-name="account.accountName"
                    :poi-list="account.wmPoiInfoVoList || []"
                    :un-poi-list="account.wmUnBindPoiInfoList || []"
                    :offline-poi-list="account.wmOfflinePoiInfoList || []"
                    :freeze-poi-list="account.wwFreezePoiInfoList || []"
                    :offline-poi-count="account.offlinePoiCount"
                    :settle-period="account.settlePeriod"
                    :is-delay="account.isDelay"
                    :poi="list.wmPoiId"
                    :poi-counts="account.poiCount"
                    @alert="handleAlert"
                />
            </div>
        </div>

        <roo-loading
            :show="loading"
            fullscreen
            backdrop
            lock
        />

        <!-- 页面中只使用一个弹窗 -->
        <roo-modal
            v-model="displayModal"
            :class="$style.modal"
            title="门店信息提示"
            size="normal"
            effect="zoom"
            backdrop
            @hidden="unbindPoiList = []"
        >
            <p :class="$style.gray">未绑定门店 ID :</p>
            <p>
                <span
                    v-for="(poi, idx) in unbindPoiList"
                    :key="poi.wmPoiId"
                >
                    {{ `${idx > 0 ? '；' : ''}${poi.wmPoiId}-${poi.wmPoiName}` }}
                </span>
            </p>
            <p :class="$style.gray">
                以上共计{{ unbindPoiList.length }}家门店未绑定到当前登录账号，请将以上信息提供给业务经理核实后查看
            </p>
            <roo-button
                slot="footer"
                @click="displayModal = false"
            >
                我知道了
            </roo-button>
        </roo-modal>
    </div>
</template>

<script>
import { mapState, mapGetters, mapActions } from 'vuex';

/* eslint-disable import/extensions, import/no-unresolved */
import toastError from '$lib/toast-error';
import { getCookieValue, navigateTo, getQueryString, getHost, getCookies } from '$lib/utils';
/* eslint-enable import/extensions, import/no-unresolved */

import AccountCard from './components/account-card';


export default {
    name: 'AccountList',
    components: {
        AccountCard,
    },
    data() {
        return {
            keyword: '',
            displayModal: false,
            unbindPoiList: [],
        };
    },
    computed: {
        ...mapState('accountList', ['loading', 'failed', 'list']),
        ...mapGetters('accountList', ['showList']),
    },
    mounted() {
        this.fetchAccountList().catch(toastError);
    },
    methods: {
        ...mapActions('accountList', ['fetchAccountList', 'search']),
        handleSearchClick() {
            return this.search(this.keyword).catch(toastError);
        },
        handleAlert(poiList) {
            if (poiList.length === 0) {
                return;
            }
            this.displayModal = true;
            this.unbindPoiList = poiList;
        },
        toAddAccount() {
            const cookies = getCookies();
            const acctId = parseInt(cookies.acctId, 10) || null;
            const token = cookies.token || null;
            const query = {};
            query.BSID = token;
            query.source = 16;
            query.page = 'daojiaSelfSettleEdit';
            query.type = 'PC';
            query.verifyStrategy = 'waimaiSelfSettleEdit';
            query.entryFlowId = '';
            query.iphPayMerchantNo = getHost().indexOf('test') >= 0 ? ************** : **************;
            query.bizType = 12001;
            query.acctId = acctId;
            query.poiList = '';
            query.poi = this.list.wmPoiId;
            query.cbType = 5;
            query.sourceName = 'waimai';
            const queryString = getQueryString(query);
            navigateTo(`${getHost()}/merchant/front/common/merchant-entry-login-verify?${queryString}`);
        },
    },

    beforeRouteEnter(to, from, next) {
        const cookieWmPoiId = parseInt(getCookieValue('wmPoiId'), 10);
        if (cookieWmPoiId && cookieWmPoiId !== -1) { // 已是单店
            navigateTo('/gw/static_resource/finance#/finance/accountInfo');
            next();
        } else {
            next();
        }
    },
};
</script>

<style lang="scss" module>
.container {
    padding: 0;
}

.card {
    padding: 20px;
    background: #FFF;
    box-shadow: 0 0 6px 0 #F3F3F4;
}

.search {
    width: 300px;
    margin-bottom: 20px;
}

.modal {
    :global(.modal-dialog) {
        width: 460px;
    }
    :global(.modal-body) {
        min-height: 0;
        padding-bottom: 0;

        max-height: 300px;
        overflow: auto;
    }
    :global(.modal-footer) {
        padding: 20px;
        border-top: 0;

        &:before {
            display: none;
        }
    }
}

.gray {
    color: #6A6A6A;
}

.messageTop{
  width: 100%;
  display: flex;
  align-items: center;
}
.allMessage{
  font-weight: 500;
  font-family: PingFangSC-Medium;
  font-size: 18px;
  color: #222222;
}
.addMessage{
  background: #FFFFFF;
  border: 1px solid #666666;
  border-radius: 2px;
  font-family: Helvetica;
  font-size: 12px;
  color: #222222;
  text-align: center;
  line-height: 32px;
  width: 109px;
  height: 32px;
  display: inline-block;
  margin-left: 81%;
}
.selectMessage{
  display: flex;
}
.messageBtn{
  margin-left: 15px;
  width: 75px;
  height: 36px;
}
</style>
