<template>
    <div :class="$style.withdrawDetailContainer">
        <roo-breadcrumb
            :class="$style.breadcrumb"
        >
            <roo-breadcrumb-item>我的账户</roo-breadcrumb-item>
            <roo-breadcrumb-item>账户余额转出详情</roo-breadcrumb-item>
        </roo-breadcrumb>
        <div v-if="withdrawDetail">
            <div
                :class="$style.container"
            >
                <div :class="$style.balanceInfo">
                    <div :class="$style.infoTitle">
                        余额转出详情
                    </div>
                    <ul :class="$style.infoContent">
                        <li :class="$style.infoItem">
                            <div :class="$style.infoItemTitle">
                                金额
                            </div>
                            <span :class="$style.infoItemText">{{ withdrawDetail.moneyStr | formatNumberThousand }} 元</span>
                        </li>
                        <li :class="$style.infoItem">
                            <div :class="$style.infoItemTitle">
                                {{ accountType }}
                            </div>
                            <span :class="$style.infoItemText">{{ accountName }}</span>
                        </li>
                        <li :class="$style.infoItem">
                            <div :class="$style.infoItemTitle">
                                流水单号
                            </div>
                            <span :class="$style.infoItemText">{{ withdrawDetail.flowNo }}</span>
                        </li>
                        <li :class="$style.infoItem">
                            <div :class="$style.infoItemTitle">
                                备注
                            </div>
                            <span :class="$style.infoItemText">{{ withdrawDetail.comment }}
                                <a
                                    v-if="withdrawDetail.payBindType === 2"
                                    v-lx-mc="{
                                        bid: 'b_waimai_e_vhqwx0qr_mc',
                                        options: { cid: 'c_waimai_e_3s27n8yw' },
                                    }"
                                    :href="withdrawDetail.walletUrl"
                                >查看近30天钱包流水</a>
                            </span>
                        </li>
                    </ul>
                </div>
            </div>
            <div :class="$style.progress">
                <div :class="$style.progressTitle">
                    余额转出进度
                </div>
                <div :class="$style.progressDetail">
                    <div :class="$style.progressSteps">
                        <roo-steps
                            :active="active"
                        >
                            <roo-step
                                v-for="(step, index) in withdrawDetail.withdrawFlowStatusList"
                                :key="index"
                                :step="index + 1"
                                :class="step.withdrawStatus === 3 ? $style.failed : null"
                            >
                                <span>{{ step.displayMsg }}</span>
                                <br />
                                <span :class="$style.withdrawTime">{{ step.withdrawTime || '' }}</span>
                            </roo-step>
                        </roo-steps>
                    </div>
                    <div
                        v-if="status === 3"
                        :class="$style.failedInfo"
                    >
                        <div>收款账户信息错误，请联系业务经理协助处理，谢谢。</div>
                        <div>此笔提现金额已退回到账户余额中。</div>
                        <div>
                            <a
                                href="#noop"
                                @click.prevent="showHelp = true;"
                            >
                                变更银行卡信息
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            <!-- 方案一：隐藏余额流水详情中近期钱包流水展示 -->
            <!-- <div
          v-if="withdrawDetail.payBindType === 2 && walletFlow.length > 0"
          :class="$style.panel"
      >
          <div :class="$style.panelTitle">
              钱包近期提现流水
              <a
                  v-lx-mc="{
                      bid: 'b_waimai_e_nrtad7ly_mc',
                      options: { cid: 'c_waimai_e_3s27n8yw' },
                  }"
                  v-if="withdrawDetail.walletUrl"
                  :class="$style.link"
                  :href="withdrawDetail.walletUrl"
              >
                  查看近30天钱包流水 &gt;
              </a>
          </div>
          <table :class="$style.walletFlow">
              <col>
              <col>
              <col>
              <col>
              <col>

              <thead>
                  <tr>
                      <th>类型</th>
                      <th>发起日期</th>
                      <th>金额</th>
                      <th>银行卡</th>
                      <th>状态</th>
                  </tr>
              </thead>

              <tbody>
                  <tr
                      v-for="row in walletFlow"
                      :key="row.accountFlowId"
                  >
                      <td>提现</td>
                      <td>{{ row.eventTimeStr }}</td>
                      <td>{{ row.amountStr }} 元</td>
                      <td>尾号{{ row.bankCardTailNum }} &nbsp; {{ row.accountName }}</td>
                      <td>{{ row.withdrawStatusName }}</td>
                  </tr>
              </tbody>
          </table>
      </div>-->
        </div>
        <roo-modal
            v-model="showHelp"
            :class="$style.modal"
            :backdrop="true"
            title="变更银行账户帮助"
            size="normal"
            effect="zoom"
        >
            <ul :class="$style.list">
                <li>联系业务经理，递交申请材料并签订变更合同，申请材料包括：身份证复印件、银行卡复印件</li>
                <li>业务经理在系统提交变更资料，您将收到系统下发的信息审核提示短信</li>
                <li>审核通过后，您将收到系统下发的银行账户信息变更成功提示短信；</li>
                <li>我的业务经理：{{ withdrawDetail.name }} {{ withdrawDetail.mobile }}</li>
            </ul>
            <roo-button
                slot="footer"
                @click="showHelp = false"
            >
                我知道了
            </roo-button>
        </roo-modal>

        <roo-loading
            :show="loading"
            fullscreen
            backdrop
            lock
        />
    </div>
</template>

<script>
/* eslint-disable import/no-unresolved, import/extensions */
import request from '$src/utils/request';
import dayjs from 'dayjs';

/* eslint-disable import/extensions, import/no-unresolved */
import * as utils from '$lib/utils';
import toastError from '$lib/toast-error';
import { mapState, mapActions } from 'vuex';
/* eslint-enable import/extensions, import/no-unresolved */

export default {
    name: 'WithdrawDetail',
    filters: {
        formatNumberThousand: utils.formatNumberThousand,
    },
    data() {
        return {
            showHelp: false,

            walletFlow: [],
        };
    },
    computed: {
        ...mapState('withdrawDetail', ['loading', 'withdrawDetail']),

        wmPoiId() {
            return this.$route.query && this.$route.query.wmPoiId;
        },
        accountType() {
            return this.withdrawDetail.payBindType === 1 ? '汇入账户' : '钱包ID';
        },
        accountName() {
            const { payBindType, walletId } = this.withdrawDetail;
            if (payBindType === 1) {
                const { bankAccountName, bankName, cardNumber } = this.withdrawDetail;
                return cardNumber ? `${bankAccountName} ${bankName} ${cardNumber}` : '';
            }
            return `${walletId}`;
        },
        active() {
            const { withdrawFlowStatusList } = this.withdrawDetail;
            return withdrawFlowStatusList.filter(list => list.withdrawStatus !== -1).length;
        },
        lastItem() {
            const { withdrawFlowStatusList } = this.withdrawDetail;
            if (withdrawFlowStatusList.length > 0) {
                return withdrawFlowStatusList[withdrawFlowStatusList.length - 1];
            }
            return null;
        },
        status() {
            const { lastItem } = this;
            if (lastItem) {
                return lastItem.withdrawStatus;
            }
            return 0;
        },
    },
    mounted() {
        const params = {
            wmPoiId: this.wmPoiId,
            outId: this.$route.query.outId,
        };
        // 如果多店ID为Null，不传承
        if (this.$route.query.partnerId !== null) {
            params.partnerId = this.$route.query.partnerId;
        }
        this.fetchWithdrawDetail(params)
            .then(this.fetchWalletFlow)
            .catch(toastError);
    },
    methods: {
        ...mapActions('withdrawDetail', ['fetchWithdrawDetail']),

        fetchWalletFlow() {
            const { wmPoiId, status } = this;
            const { payBindType, walletId, withdrawFlowStatusList } = this.withdrawDetail;

            // 钱包流水且已提现成功
            if (payBindType === 2
                && status === 2
                && walletId
                && withdrawFlowStatusList[1]
                && withdrawFlowStatusList[1].status !== -1
                && withdrawFlowStatusList[1].withdrawTime) {
                const mid = dayjs(withdrawFlowStatusList[1].withdrawTime);
                const startDate = mid.subtract(1, 'd').format('YYYY-MM-DD');
                const endDate = mid.add(1, 'd').format('YYYY-MM-DD');

                return request.get('/finance/pc/api/multiAccount/accountWalletRunning', {
                    params: {
                        wmPoiId,
                        startDate,
                        endDate,
                        walletId,
                        pageNo: 1,
                        pageSize: 20,
                    },
                }).then((res) => {
                    const { code, msg, data } = res.data;

                    if (code !== 0) {
                        throw new Error(msg);
                    }

                    this.walletFlow = data || [];
                });
            }

            return Promise.resolve();
        },
    },
};
</script>

<style lang="scss" module>
.withdraw-detail-container {
    padding: 20px;
}
.balance-info,.progress-detail {
    background: #fff;
}
.info-title,.progress-title {
    height: 40px;
    line-height: 40px;
    padding: 0 20px;
    color: #2a2a2a;
    font-weight: bold;
    background: #fdfdfd;
    border-bottom: 1px solid #fafafa;
}
.info-content {
    padding: 10px 20px;
    font-size: 12px;
    color: #a0a0a0;
}
.info-item {
    padding: 10px 0;
}
.info-item-title {
    display: inline-block;
    width: 48px;
    text-align: right;
}
.info-item-text {
    margin-left: 30px;
    font-size: 12px;
    color: #000;
}
.progress {
    margin-top: 30px;
}
.progress-detail {
    display: flex;
    align-items: center;
    padding: 20px;

    .progress-steps {
        flex-shrink: 0;
    }

    :global(.roo-steps li .caption) {
        max-width: 100%;
    }
}
.failed-info {
    margin-left: 30px;
    // color: #ff5a5a;
}
.withdraw-time {
    color: #aaa;
}
:global{
    .steps.vertical li {
        width: 150px;
    }
    .steps.vertical li::before {
        width: 45px;
        left: 0;
    }
}
:global {
    .steps li .caption {
        line-height: inherit;
        margin-top: 10px;
    }
}
:global(.roo-step).failed {
    color: #ff5a5a !important;
}
.failed :global(.step) {
    background:  #ff5a5a !important;
}


.panel {
    margin-top: 30px;
    background: #FFF;
}
.panel-title {
    height: 40px;
    line-height: 40px;
    padding: 0 20px;
    color: #2a2a2a;
    font-weight: bold;
    background: #fdfdfd;
    border-bottom: 1px solid #fafafa;

    .link {
        float: right;
        font-weight: normal;
    }
}
.wallet-flow {
    display: table;
    table-layout: fixed;
    border-collapse: collapse;
    width: 100%;
    text-align: center;
    color: #2A2A2A;

    tr {
        height: 40px;
        padding: 0 20px;
    }

    thead th {
        text-align: center;
        color: #858692;
    }
}
</style>
