<template>
    <div :class="$style.mutilContainer">
        <roo-breadcrumb
            :class="$style.breadcrumb"
        >
            <roo-breadcrumb-item>我的账户</roo-breadcrumb-item>
            <roo-breadcrumb-item>账单详情</roo-breadcrumb-item>
        </roo-breadcrumb>

        <div v-if="shopDetails">
            <div :class="$style.billInfo">
                <div :class="$style.infoTitle">
                    <span>账单信息</span>
                    <span :class="$style.titleRight">合并账单周期: {{ shopDetails.settleBillStarteDate }} 至 {{ shopDetails.settleBillEndDate }}</span>
                </div>
                <ul :class="$style.infoContent">
                    <li :class="$style.infoItem">
                        <div :class="$style.infoItemTitle">
                            收入金额
                        </div>
                        <span :class="$style.infoItemText">{{ shopDetails.settleBillAmount | formatNumberThousand }}元</span>
                    </li>
                    <li :class="$style.infoItem">
                        <div :class="$style.infoItemTitle">
                            结算门店数量
                        </div>
                        <span :class="$style.infoItemText">{{ shopDetails.settlePoiCount }}个</span>
                    </li>
                </ul>
            </div>
            <div :class="$style.download">
                <roo-button
                    v-lx-mc="{
                        bid: 'b_waimai_e_25ufk2d7_mc',
                        options: { cid },
                    }"
                    :loading="downloading"
                    @click="handleDownload"
                >
                    下载全部账单
                </roo-button>
            </div>
            <div :class="$style.shopDetail">
                <div :class="$style.shopTitle">
                    <span :class="$style.shop">门店</span>
                    <span :class="$style.billAmount">门店账单金额</span>
                    <span :class="$style.operate">操作</span>
                </div>
                <div :class="$style.shopItems">
                    <div
                        v-for="(item, index) in shopDetails.poiSettleBillList"
                        :key="index"
                        :class="itemClass(index)"
                    >
                        <span :class="$style.shop">
                            {{ item.wmPoiName }}
                        </span>
                        <span :class="$style.billAmount">
                            {{ amount(item.settleBillAmount) }}
                        </span>
                        <span :class="$style.operate">
                            <a
                                v-lx-mc="{
                                    bid: 'b_waimai_e_6s5p8h12_mc',
                                    options: { cid },
                                }"
                                href="#noop"
                                @click.prevent="handleView(item)"
                            >
                                查看
                            </a>
                        </span>
                    </div>
                </div>
                <div
                    v-if="shopDetails.pageCount > 0"
                    :class="$style.textRight"
                >
                    <roo-pagination
                        :page-size="pageSize"
                        :total="shopDetails.pageCount"
                        :current-page="currentPage"
                        @current-change="handlePageChange"
                    />
                </div>
            </div>
        </div>

        <roo-loading
            :show="loading"
            fullscreen
            backdrop
            lock
        />
    </div>
</template>

<script>
/* eslint-disable import/no-unresolved, import/extensions */
import request from '$src/utils/request';
import { mapState, mapActions } from 'vuex';
import { toast } from '@roo/roo-vue';

/* eslint-disable import/extensions, import/no-unresolved */
import * as utils from '$lib/utils.js';
import toastError from '$lib/toast-error';
/* eslint-enable import/extensions, import/no-unresolved */

export default {
    name: 'MutilBillDetail',
    filters: {
        formatNumberThousand: utils.formatNumberThousand,
    },
    data() {
        return {
            pageSize: 10,
            currentPage: 1,
            downloading: false,
        };
    },
    computed: {
        cid() {
            const { meta } = this.$route;
            return meta && meta.cid;
        },
        outId() {
            return this.$route.query.outId;
        },
        ...mapState('mutilBillDetail', ['loading', 'shopDetails']),
    },
    mounted() {
        this.getShopDetail();
    },
    methods: {
        itemClass(index) {
            return [this.$style.shopItem,
                index % 2 === 1 ? this.$style.even : null,
                index === this.shopDetails.pageCount - 1 ? this.$style.lastItem : null,
            ];
        },
        amount(billAmount) {
            return parseFloat(billAmount) > 0 ? `+${billAmount}` : billAmount;
        },
        getShopDetail() {
            const params = {
                out_id: this.outId, // 后端参数out_id下划线
                pageSize: this.pageSize,
                pageNo: this.currentPage,
            };
            this.fetchShopDetail(params).catch(toastError);
        },
        handlePageChange(page) {
            this.currentPage = page;
            this.getShopDetail();
        },
        handleView(item) {
            this.$router.push({
                name: 'bill-detail',
                query: {
                    settleBillId: item.settleBillId,
                    wmPoiId: item.wmPoiId,
                    outId: this.outId,
                },
            });
        },
        handleDownload() {
            this.downloading = true;
            const { settleBillStarteDate, settleBillEndDate } = this.shopDetails;
            request.get('/finance/pc/api/billDownload/createBillExportTask', {
                params: {
                    wmPoiId: -1,
                    beginDate: settleBillStarteDate,
                    endDate: settleBillEndDate,
                },
            }).then((res) => {
                this.downloading = false;

                const { code, msg } = res.data;
                if (code !== 0) {
                    throw new Error(msg);
                }

                toast.success('下载成功!');
            }, (err) => {
                this.downloading = false;
                throw err;
            }).catch(toastError);
        },

        ...mapActions('mutilBillDetail', ['fetchShopDetail']),
    },
};
</script>

<style lang="scss" module>
.mutil-container {
    padding: 20px;
}
.bill-info,.shop-detail {
    background: #fff;
}
.info-title,.shop-title {
    height: 40px;
    line-height: 40px;
    padding: 0 20px;
    color: #2a2a2a;
    font-weight: bold;
    background: #fdfdfd;
    border-bottom: 1px solid #fafafa;
}
.info-title, .shop-title {
    display: flex;
    justify-content: space-between;
}
.title-right {
    color: #6a6a6a;
    font-size: 12px;
    font-weight: normal;
}
.shop {
    flex: 7;
}
.billAmount, .operate {
    text-align: right;
    flex: 1
}
.info-content {
    padding: 10px 20px;
    font-size: 12px;
    color: #a0a0a0;
}
.info-item {
    padding:10px 0;
}
.info-item-title {
    display: inline-block;
    width: 80px;
    text-align: right;
}
.info-item-text {
    margin-left: 30px;
}
.download {
    margin: 20px 0;
    text-align: right;
}
.shop-item {
    height:60px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    line-height: 40px;
    padding: 0 20px;
    font-size: 12px;
}
.even {
    background: #fdfdfd;
    border-bottom: 1px solid #fafafa;
}
.text-right {
    text-align: right;
    padding: 30px 0px 20px;
}
.last-item {
    border-bottom: 1px solid #fafafa;
}
</style>
