<template>
    <div :class="$style.container">
        <roo-breadcrumb
            :items="breadcrumb"
        />

        <div :class="$style.card">
            <h3>{{ title }}</h3>
            <table :class="$style.table">
                <col style="width: 20%;" />
                <col style="width: 10%;" />
                <col style="width: 70%;" />

                <template v-if="showDetail">
                    <tbody key="commodities">
                        <tr>
                            <th>商品</th>
                            <th></th>
                            <th></th>
                        </tr>
                        <tr
                            v-for="(commodity, index) in commodities"
                            :key="index"
                        >
                            <td>
                                {{ commodity.commodityName }}
                            </td>
                            <td>
                                ×{{ commodity.commodityCount }}
                            </td>
                            <td>
                                + ￥{{ commodity.commodityAmount | formatNumberThousand }}
                            </td>
                        </tr>
                    </tbody>

                    <tbody
                        v-if="activityInfo"
                        key="activityInfo"
                    >
                        <tr>
                            <th>活动支出</th>
                            <th></th>
                            <th>- ¥{{ activityInfo.activityTotalAmount | formatNumberThousand }}</th>
                        </tr>
                        <tr
                            v-for="(activity, index) in activityInfo.activityList"
                            :key="index"
                        >
                            <td>{{ activity.activityName }}</td>
                            <td></td>
                            <td>- ¥{{ activity.activityAmount | formatNumberThousand }}</td>
                        </tr>
                    </tbody>

                    <tbody
                        v-if="commissionInfo"
                        key="commissionInfo"
                    >
                        <tr>
                            <th>平台抽佣</th>
                            <th></th>
                            <th>- ¥{{ commissionInfo.commissionTotalAmount | formatNumberThousand }}</th>
                        </tr>
                        <tr
                            v-for="(commission, index) in commissionInfo.commissionList"
                            :key="index"
                        >
                            <td>{{ commission.commissionName }}</td>
                            <td></td>
                            <td>- ¥{{ commission.commissionAmount | formatNumberThousand }}</td>
                        </tr>
                    </tbody>

                    <tbody
                        v-if="showUserPay"
                        key="userPayShippingAmount"
                    >
                        <tr>
                            <th>用户支付配送费金额</th>
                            <th></th>
                            <th>+ ¥{{ userPayShippingAmount | formatNumberThousand }}</th>
                        </tr>
                    </tbody>

                    <tbody
                        key="donation"
                    >
                        <tr>
                            <th>公益捐款</th>
                            <th></th>
                            <th>- ¥{{ (donationAmount || '0.00') | formatNumberThousand }}</th>
                        </tr>
                    </tbody>
                </template>

                <tbody
                    v-if="showInsuranceInfo"
                    key="insuranceInfo"
                >
                    <tr>
                        <th>保险</th>
                        <th></th>
                        <th>{{ insuranceFlag }} ￥{{ insuranceAmount | formatNumberThousand }}</th>
                    </tr>
                    <tr>
                        <td>{{ insuranceItemText }}</td>
                        <td></td>
                        <td>{{ insuranceFlag }} ￥{{ insuranceAmount | formatNumberThousand }}</td>
                    </tr>
                </tbody>

                <tfoot key="summary">
                    <tr>
                        <th>
                            已结算金额
                        </th>
                        <th></th>
                        <th>
                            ¥{{ (chargeAmount || 0) | formatNumberThousand }}
                        </th>
                    </tr>
                </tfoot>
            </table>

            <div :class="$style.footerText">
                {{ footerText }}
            </div>
        </div>

        <roo-loading
            :show="loading"
            fullscreen
            backdrop
            lock
        />
    </div>
</template>

<script>
/* eslint-disable import/no-unresolved, import/extensions */
import request from '$src/utils/request';

/* eslint-disable import/extensions, import/no-unresolved */
import toastError from '$lib/toast-error';
import { formatNumberThousand } from '$lib/utils';
/* eslint-enable import/extensions, import/no-unresolved */

export default {
    name: 'OrderDetail',
    filters: {
        formatNumberThousand,
    },
    data() {
        return {
            loading: false,

            settleState: 0,

            chargeTypeCode: 2000,

            orderSubmitedDate: null,
            orderRefundedDate: null,
            orderCanceledDate: null,
            orderCompletedDate: null,
            orderCompensatedDate: null,

            orderViewId: '',
            orderDaySeq: 0,
            orderPayType: '',
            orderShippingType: '',

            orderTotal: '0.00',
            tipsAmount: '0.00',
            chargeAmount: '0.00',
            donationAmount: '0.00',
            userOfflinePayAmount: '0.00',
            userPayShippingAmount: '0.00',

            activityInfo: null,
            insuranceInfo: null,
            commissionInfo: null,
            commodities: [],
        };
    },
    computed: {
        title() {
            return `#${this.orderDaySeq} 交易详情`;
        },
        showDetail() {
            return parseInt(this.$route.query.chargeTypeCode, 10) === 2000;
        },
        showUserPay() {
            return ['商家自配', '美团众包', '美团跑腿'].indexOf(this.orderShippingType) > -1;
        },
        breadcrumb() {
            return [{
                text: '首页',
                link: { name: 'home' },
            }, {
                text: '账单明细',
                link: { name: 'daily-bill' },
                query: ['dailyBillDate', 'wmPoiId'],
            }, {
                text: '交易详情',
            }];
        },

        // 此次 UI 改版升级添加保险类型展示
        showInsuranceInfo() {
            return (this.chargeTypeCode === 4000 || this.chargeTypeCode === 4100) && this.insuranceInfo;
        },
        insuranceFlag() {
            const { chargeAmount } = this;
            if (chargeAmount[0] === '-') {
                return '-';
            }
            return '+';
        },
        insuranceAmount() {
            if (this.chargeTypeCode === 4000 || this.chargeTypeCode === 4100) {
                const { chargeAmount } = this;
                if (chargeAmount[0] === '-') {
                    return chargeAmount.slice(1);
                }
                return chargeAmount;
            }
            return '0.00';
        },
        insuranceItemText() {
            if (this.chargeTypeCode === 4000) {
                return `${this.insuranceInfo}-投保扣款`;
            } else if (this.chargeTypeCode === 4100) {
                return `${this.insuranceInfo}-投保退款`;
            }
            return '';
        },

        footerText() {
            return `${this.orderSubmitedDate}下单 `
                + `| ${this.orderCompletedDate ? `${this.orderCompletedDate}订单完成` : '--'} `
                + `| ${this.orderShippingType} `
                + `| 订单编号 ${this.orderViewId}`;
        },
    },
    watch: {
        '$route.query': function () {
            this.fetchBillChargeDetail(this.$route.query);
        },
    },
    mounted() {
        this.fetchBillChargeDetail(this.$route.query);
    },
    methods: {
        // 接口请求参数取页面 query
        // { wmPoiId, chargeTypeCode, billChargeId, wmOrderViewId, dailyBillDate }
        fetchBillChargeDetail(params) {
            this.loading = true;
            return request.get('/finance/pc/api/poiBillCharge/billChargeDetail', { params })
                .then((res) => {
                    this.loading = false;

                    const { code, msg, data } = res.data;
                    if (code !== 0) {
                        throw new Error(msg);
                    }

                    this.settleState = data.settleState;
                    this.chargeTypeCode = data.chargeTypeCode;

                    this.orderSubmitedDate = data.wmOrderSubmitedDate || null;
                    this.orderRefundedDate = data.wmOrderRefundedDate || null;
                    this.orderCanceledDate = data.wmOrderCanceledDate || null;
                    this.orderCompletedDate = data.wmOrderCompletedDate || null;
                    this.orderCompensatedDate = data.wmOrderCompensatedDate || null;

                    this.orderViewId = data.wmOrderViewId || '';
                    this.orderDaySeq = data.wmOrderDaySeq || 0;
                    this.orderPayType = data.wmOrderPayType || '';
                    this.orderShippingType = data.wmOrderShippingType || '';

                    this.orderTotal = data.wmOrderTotal || '0.00';
                    this.tipsAmount = data.tipsAmount || '0.00';
                    this.chargeAmount = data.chargeAmount || '0.00';
                    this.donationAmount = data.wmDonationAmount || '0.00';
                    this.userOfflinePayAmount = data.userOfflinePayAmount || '0.00';
                    this.userPayShippingAmount = data.userPayShippingAmount || '0.00';

                    this.commodities = data.commodities || []; // 商品
                    this.activityInfo = data.activityInfo || null;
                    this.insuranceInfo = data.insuranceInfo || null;
                    this.commissionInfo = data.commissionInfo || null;
                }, (err) => {
                    this.loading = false;
                    throw err;
                })
                .catch(toastError);
        },
    },
};
</script>

<style lang="scss" module>
.container {
    padding: 20px;
}

.card {
    padding: 20px;
    background: #FFF;

    h3 {
        margin: 0;
    }
}

.table {
    width: 100%;
    margin: 20px 0;
    table-layout: fixed;
    color: #858692;

    th, td {
        line-height: 20px;

        &:first-child {
            text-align: right;
        }
    }

    td {
        padding: 6px 20px;
    }

    th {
        padding: 10px 20px;
        color: #3F4156;
    }

    tbody {
        border-bottom: 1px dashed #e5e5e5;

        tr:last-child {
            td {
                padding-bottom: 15px;
            }
        }
    }
}

.footer-text {
    font-size: 12px;
    color: #858692;
}
</style>
