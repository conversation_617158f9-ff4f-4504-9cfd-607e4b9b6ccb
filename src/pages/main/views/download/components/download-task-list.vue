<template>
    <table :class="$style.table">
        <col style="width: 50px;">
        <col style="width: 200px;">
        <col style="width: 100px;">
        <col style="width: 50px;">

        <thead>
            <tr>
                <th>序号</th>
                <th>账单内容</th>
                <th>操作时间</th>
                <th>操作</th>
            </tr>
        </thead>
        <tbody>
            <tr
                v-if="loading"
                key="loading"
            >
                <td
                    :class="$style.loading"
                    :style="{ height: `${40 * (list.length || 1)}px` }"
                    colspan="4"
                >
                    加载中
                </td>
            </tr>
            <tr
                v-else-if="list.length === 0"
                key="empty"
            >
                <td
                    :class="$style.empty"
                    colspan="4"
                >
                    暂无数据
                </td>
            </tr>
            <template v-else>
                <tr
                    v-for="task in list"
                    :key="task.taskNo"
                >
                    <td>{{ task.taskNo }}</td>
                    <td>{{ task.taskInfo }}</td>
                    <td>{{ task.taskTime }}</td>
                    <td>
                        <span
                            v-if="task.taskStatus === 20"
                            key="error"
                            :class="$style.error"
                        >
                            导出失败
                        </span>
                        <span
                            v-else-if="task.taskStatus === 30 || task.taskStatus === 40"
                            :class="$style.pending"
                        >
                           
                            导出中
                            <a style="margin-left: 8px;cursor: pointer;" @click="cancelBillExportTask(task.taskNo)">取消</a>
                        </span>
                        <span v-else-if="task.taskStatus === 10">
                            <a>已取消</a>
                        </span>
                        <a
                            key="download"
                            v-lx-mc="{
                                bid: 'b_waimai_e_gbv3jysx_mc',
                                options: { cid },
                            }"
                            v-else
                            :href="`/finance/v2/finance/orderChecking/export/download${task.taskUrl}`"
                            target="_blank"
                        >
                            下载
                        </a>
                    </td>
                </tr>
            </template>
        </tbody>
    </table>
</template>

<script>
// eslint-disable-next-line import/no-unresolved, import/extensions
import request from '$src/utils/request';
// eslint-disable-next-line import/no-duplicates
import { confirm } from '@roo/roo-vue';
// eslint-disable-next-line import/no-unresolved, import/extensions
import { getCookies } from '$lib/utils';
// eslint-disable-next-line import/no-duplicates
import { toast } from '@roo/roo-vue';


export default {
    name: 'DownloadTaskList',
    props: {
        cid: {
            type: String,
            default: '',
        },
        loading: {
            type: Boolean,
            default: false,
        },
        list: {
            type: Array,
            default() {
                return [];
            },
        },
        fetchTaskList: {
            type: Function,
            default: () => {},
        },
        bizType: {
            type: Number,
            default: 0,
        },
    },
    data() {
        return {
        };
    },
    methods: {
        cancelBillExportTask(taskNo) {
            const cookies = getCookies();

            confirm('是否确定要暂停当前的下载任务', (yes) => {
                if (yes) {
                    request.post(`/finance/pc/api/billDownload/cancelBillExportTask?taskId=${taskNo}`, {
                        acctId: parseInt(cookies.acctId, 10) || null,
                        // taskId: taskNo,
                    }).then((res) => {
                        const { code, msg } = res.data;
                        if (code == 0) {
                            this.fetchTaskList({ pageNo: 1, bizType: this.bizType });
                        }
                        if (code !== 0) {
                            toast.error(msg);
                            throw new Error(msg);
                        }
                    });
                }
            });
        },
    },
};
</script>

<style lang="scss" module>
.table {
    width: 100%;
    border: 1px solid #E9EAF2;
    border-radius: 3px;
    table-layout: fixed;
    border-collapse: separate;
    border-spacing: 0;
    font-size: 14px;
    color: #3F4156;

    thead {
        color: #858692;

        th {
            background: #F7F8FA;
        }
    }

    tr {
        &:nth-of-type(odd) {
            background: #FFF;
        }
        &:nth-of-type(even) {
            background: #F7F8FA;
        }

        // &.unsettle {
        //     background: #FFFBEF;
        // }
    }

    th, td {
        height: 40px;
        padding: 0 20px;
        font-weight: normal;
    }

    .empty {
        height: 260px;
    }
}

.loading,
.empty {
    text-align: center;
}

.pending {
    color: #63D29D;
}

.error {
    color: #FF4646;
}
</style>
