<template>
    <div :class="$style.container">
        <div :class="$style.card">
            <h3 v-show="!isSchoolAccounts">{{ sku === '1' ? '商品对账' : '下载专区' }}</h3>

            <!-- 添加学校信息展示 -->
            <div v-if="isSchoolAccounts" :class="$style.schoolInfo">
                <span>{{ schoolId || '-' }} · {{ schoolName || '-' }}</span>
            </div>

            <roo-alert :class="$style.alert" style="background: rgba(255, 106, 0, 0.1);border-color: rgba(255, 106, 0, 0.3);border-radius: 8px;">
                <p style="margin: 0 0 10px 0;">
                    下载账单，请注意以下问题：
                </p>
                <p style="margin: 0 0 0 0;">
                    1、门店数量或时间跨度较大会增加账单生成时间，请耐心等待。月初、月末为下载高峰时段，平均等待时长为 90 分钟，建议避开高峰使用。
                </p>
                <p style="margin: 0 0 0 0;">
                    2、如果账单的订单数目过多，我们将分多个Excel文件来展示。
                </p>
                <p v-if="!isSchoolAccounts" style="margin: 0 0 0 0;">
                    3、当前提供2023年度账单的直接下载服务。若需获取往年账单，可按月份分次下载。
                </p>
                <p v-if="isSchoolAccount" style="margin: 0 0 0 0;">
                    {{ !isSchoolAccounts ? '4、当结算账户与结算门店选择“全部”时，针对与账号解绑的门店，其历史账单可被一同下载。' : '' }}
                </p>
            </roo-alert>

            <div :class="$style.section">
                <h4>选择账单</h4>
                <div :class="$style.row" style="display: flex;margin-bottom: 10px;">
                    <span :class="$style.label" style="line-height: 36px">
                        账单类型
                    </span>
                    <roo-tabs
                        v-model="active"
                        type="fill"
                    >
                        <roo-tab-pane
                            v-if="!isSchoolAccounts"
                            name="store-bill"
                            label="门店账单"
                        />
                        <roo-tab-pane
                            v-if="!isSchoolAccounts"
                            name="account-running"
                            label="账户流水"
                        />
                        <roo-tab-pane
                            v-if="isSchoolAccounts"
                            name="school-bill"
                            label="校园账单"
                        />
                    </roo-tabs>
                </div>
                <div :class="$style.row" style="display: flex;margin-bottom: 10px;">
                    <span :class="$style.label" style="line-height: 36px">
                        账单日期
                    </span>
                    <roo-tabs v-model="dateActive" type="fill">
                        <roo-tab-pane name="day" label="日">
                            <roo-date-time-picker
                                v-model="startDate"
                                :class="$style.select"
                                :min-date="minStartDate"
                                :max-date="maxStartDate"
                                style="margin-left: -12px;"
                                clearable
                            />
                            -
                            <roo-date-time-picker
                                v-model="endDate"
                                :class="$style.select"
                                :min-date="minEndDate"
                                :max-date="maxEndDate"
                                clearable
                            />
                        </roo-tab-pane>
                        <roo-tab-pane name="month" label="月">
                            <roo-date-time-picker
                                v-model="monthDate"
                                :class="$style.select"
                                :max-date="new Date().setDate(0)"
                                format="yyyy-MM"
                                style="margin-left: -12px;"
                                clearable
                            />
                        </roo-tab-pane>
                        <roo-tab-pane name="years" label="年">
                            <roo-date-time-picker
                                v-model="yearDate"
                                :class="$style.select"
                                :max-date="new Date(2023, 0, 1)"
                                :min-date="new Date(2023, 0, 1)"
                                format="yyyy"
                                style="margin-left: -12px;"
                                clearable
                            />
                        </roo-tab-pane>
                        <span v-if="dateActive === 'years'" style="color:#999">年度账单下载时间较长，建议分月下载</span>
                    </roo-tabs>
                </div>
                <div v-if="active == 'store-bill' || active == 'school-bill'" :class="$style.row" style="display: flex;margin-bottom: 10px;">
                    <span :class="$style.label" style="line-height: 36px;">
                        明细类型
                    </span>
                    <roo-tabs
                        v-for="item in billType"
                        :key="item.key"
                        v-model="billTypeActive"
                        type="fill"
                    >
                        <roo-tab-pane
                            :name="item.key"
                            :label="item.value"
                        />
                    </roo-tabs>
                    <roo-icon name="question" style="margin-left: 4px;margin-top: 12px;">
                        <roo-tooltip
                            trigger="hover"
                            placement="right"
                        >
                            <ol style="margin-left: -40px;font-size: 12px;">
                                <li>账单明细：导出所选日期范围内日账单明细</li>
                                <li>订单明细：导出所选日期范围内的每笔订单明细</li>
                                <li v-if="!isSchoolAccounts">汇总信息：导出所选日期范围内汇总信息</li>
                            </ol>
                        </roo-tooltip>
                    </roo-icon>
                </div>
                <div v-if="billTypeActive == '54' && fileType.length > 0" :class="$style.row" style="display: flex;">
                    <span :class="$style.label">
                        下载格式
                    </span>
                    <span style="display: flex;">
                        <roo-radio-group
                            v-for="item in fileType"
                            :key="item.key"
                            v-model="downMode"
                            style="margin-left: 20px;"
                        >
                            <roo-radio :key="item.key" :value="item.value">{{ item.value }}</roo-radio>
                        </roo-radio-group>
                    </span>
                </div>
                <div v-if="!isSchoolAccounts" :class="$style.row">
                    <span :class="$style.label">
                        结算账户
                    </span>
                    <roo-select
                        v-model="checkedSettleId"
                        :class="$style.select"
                        @change="handleSettleIdChange"
                    >
                        <roo-option
                            :key="-1"
                            :value="-1"
                            label="全部"
                        />
                        <roo-option
                            v-for="item in settleSettingList"
                            :key="item.settleId"
                            :value="item.settleId"
                            :label="getLabel(item)"
                        >
                            <div
                                :class="$style.option"
                                :title="`${item.acctName}-${item.lastCardNo}`"
                            >
                                <div :class="$style.settleName">
                                    {{ item.acctName }}
                                </div>
                                <div :class="$style.cardNo">
                                    -{{ item.lastCardNo }}
                                </div>
                            </div>
                        </roo-option>
                    </roo-select>
                </div>
                <div v-if="!isSchoolAccounts" :class="$style.row">
                    <span :class="$style.label">
                        结算门店
                    </span>
                    <roo-select
                        v-model="checkedPoiId"
                        :class="$style.select"
                        filterable
                        @change="handlePoiIdChange"
                    >
                        <roo-option
                            v-for="opt in poiOptions"
                            :key="opt.value"
                            :value="opt.value"
                            :label="opt.label"
                            :disabled="opt.disabled"
                        >
                            <!-- 过长文本会被截断，悬停时展示所有文案 -->
                            <div :title="opt.label">
                                {{ opt.label }}
                            </div>
                        </roo-option>
                    </roo-select>
                </div>
                <div :class="$style.row">
                    <span :class="$style.label"></span>
                    <roo-button
                        v-lx-mc="{
                            bid: 'b_waimai_e_22a33nxn_mc',
                            options: { cid },
                        }"
                        :loading="exporting"
                        @click="createTask"
                    >
                        下载账单
                    </roo-button>
                </div>

                <div :class="$style.section">
                    <h4>
                        导出记录
                        <div style="margin-top: 23px;margin-bottom: -10px;margin-left: -12px;position: relative;">
                            <roo-tabs
                                v-model="timeActive"
                                type="fill"
                                @active-change="handleDateTypeChange"
                            >
                                <roo-tab-pane
                                    name="all"
                                    label="全部"
                                />
                                <roo-tab-pane
                                    name="week"
                                    label="最近一周"
                                />
                                <roo-tab-pane
                                    name="months"
                                    label="最近一月"
                                />
                                <roo-tab-pane
                                    name="flee"
                                    label="自定义"
                                />
                            </roo-tabs>
                            <span
                                :class="$style.reload"
                                @click="handleReloadClick"
                            >
                                刷新
                            </span>
                        </div>
                    </h4>
                    <div v-if="timeActive === 'flee'" style="margin-top: -24px;margin-bottom: 20px;">
                        <roo-date-time-picker
                            v-model="startTime"
                            :class="$style.select"
                            :min-date="minStartTime"
                            :max-date="maxStartTime"
                            clearable
                            @change="customizeDateChange"
                        />
                        -
                        <roo-date-time-picker
                            v-model="endTime"
                            :class="$style.select"
                            :min-date="minEndTime"
                            :max-date="maxEndTime"
                            clearable
                            @change="customizeDateChange"
                        />
                    </div>
                    <download-task-list
                        :cid="cid"
                        :loading="loading"
                        :list="taskList"
                        :fetch-task-list="fetchTaskList"
                        :biz-type="bizType"
                    />

                    <div :class="$style.pagination">
                        <roo-pagination
                            :total="total"
                            :page-size="pageSize"
                            :current-page="pageNo"
                            @current-change="handlePageChange"
                        />
                    </div>
                </div>
            </div>
        </div>

        <roo-loading
            :show="settleSettingLoading"
            backdrop
            lock
        />
    </div>
</template>

<script>
import dayjs from 'dayjs';
import axios from 'axios';
/* eslint-disable import/no-unresolved, import/extensions */
import request from '$src/utils/request';
import { mapState, mapActions } from 'vuex';
import { toast } from '@roo/roo-vue';

/* eslint-disable import/extensions, import/no-unresolved */
import { getCookies } from '$lib/utils';
/* eslint-enable import/extensions, import/no-unresolved */

import DownloadTaskList from './components/download-task-list';

function toastError(err) {
    if (axios.isCancel(err)) {
        return;
    }
    if (err.response) {
        toast.error(`请求失败#${err.response.status}`);
    } else if (err.response) {
        toast.error('网络请求失败，请检查网络连接或稍后重试');
    } else {
        // toast.error(err.message || '未知错误');
        console.log(err.message || '未知错误');
    }
}

export default {
    name: 'Download',
    components: {
        DownloadTaskList,
    },
    data() {
        return {
            startDate: null,
            endDate: null,

            exporting: false,
            bizType: 0,
            active: '',
            dateActive: 'day',
            billTypeActive: '1',
            downMode: 'EXCEL',
            monthDate: '',
            yearDate: '',
            billType: [],
            fileType: [],
            timeActive: 'months',
            startTime: '',
            endTime: '',
            isSchoolAccount: false,
            isSchoolAccounts: false,
            schoolId: '',
            schoolName: '',
        };
    },
    created() {
        this.isSchoolAccounts = this.$route.query.isSchoolAccounts === '1';
        this.schoolId = this.$route.query.schoolId;
        this.schoolName = this.$route.query.schoolName;
        this.getBillType();
        this.getFileType();
        this.handleSchoolAccount();
    },
    computed: {
        // 灵犀 cid
        cid() {
            const { meta } = this.$route;
            return meta && meta.cid;
        },

        sku() {
            return this.$route.query.sku || '0';
        },

        ...mapState('download', [
            'settleSettingList',
            'settleSettingLoading',

            'poiList',
            'poiLoading',

            'loading',
            'total',
            'pageNo',
            'pageSize',
            'taskList',
        ]),

        checkedSettleId: {
            get() {
                return this.$store.state.download.checkedSettleId;
            },
            set(val) {
                this.$store.commit('download/changeCheckedSettleId', val);
            },
        },

        checkedPoiId: {
            get() {
                return this.$store.state.download.checkedPoiId;
            },
            set(val) {
                this.$store.commit('download/changeCheckedPoiId', val);
            },
        },

        poiOptions() {
            if (this.poiLoading) {
                return [{
                    value: null,
                    label: '加载中',
                    disabled: true,
                }];
            }

            const arr = [{
                value: -1,
                label: '全部',
            }];

            this.poiList.forEach((item) => {
                arr.push({
                    value: item.wmPoiId,
                    label: item.wmPoiName,
                    disabled: !item.bindStatus,
                });
            });

            return arr;
        },

        // 账单区间最大为 1 个月， SKU 不可导出 3 月前数据
        minStartDate() {
            if (this.sku === '1') {
                const d1 = new Date(Math.max(Date.now() - (90 * 24 * 3600 * 1000), new Date(2019, 0, 1).getTime()));

                if (this.endDate) {
                    const d2 = new Date(this.endDate.getTime() - (30 * 24 * 3600 * 1000));

                    if (d2.getTime() > d1.getTime()) {
                        return d2;
                    }
                }

                return d1;
            }

            if (this.endDate) {
                return new Date(this.endDate.getTime() - (30 * 24 * 3600 * 1000));
            }
            return null;
        },
        maxStartDate() {
            if (this.endDate) {
                return this.endDate;
            }
            return new Date();
        },

        minEndDate() {
            if (this.sku === '1') {
                const d1 = new Date(2019, 0, 1);
                if (this.startDate && this.startDate.getTime() > d1.getTime()) {
                    return this.startDate;
                }
                return d1;
            }
            if (this.startDate) {
                return this.startDate;
            }
            return null;
        },
        maxEndDate() {
            const now = new Date();
            if (this.startDate) {
                return new Date(Math.min(now.getTime(), this.startDate.getTime() + (30 * 24 * 3600 * 1000)));
            }
            return now;
        },


        minStartTime() {
            if (this.sku === '1') {
                const d1 = new Date(Math.max(Date.now() - (90 * 24 * 3600 * 1000), new Date(2019, 0, 1).getTime()));

                if (this.endTime) {
                    const d2 = new Date(this.endTime.getTime() - (30 * 24 * 3600 * 1000));

                    if (d2.getTime() > d1.getTime()) {
                        return d2;
                    }
                }

                return d1;
            }

            if (this.endTime) {
                return new Date(this.endTime.getTime() - (30 * 24 * 3600 * 1000));
            }
            return null;
        },
        maxStartTime() {
            if (this.endTime) {
                return this.endTime;
            }
            return new Date();
        },

        minEndTime() {
            if (this.sku === '1') {
                const d1 = new Date(2019, 0, 1);
                if (this.startTime && this.startTime.getTime() > d1.getTime()) {
                    return this.startTime;
                }
                return d1;
            }
            if (this.startTime) {
                return this.startTime;
            }
            return null;
        },
        maxEndTime() {
            const now = new Date();
            if (this.startTime) {
                return new Date(Math.min(now.getTime(), this.startTime.getTime() + (30 * 24 * 3600 * 1000)));
            }
            return now;
        },
    },
    watch: {
        checkedSettleId(settleId) {
            if (settleId) {
                this.handleSettleSettingChange(settleId);
            }
        },
        active() {
            switch (this.active) {
                case 'store-bill':
                    this.fetchTaskList({
                        pageNo: 1,
                        bizType: this.billTypeActive === '1' ? 0 : this.billTypeActive,
                        beginDate: this.startTime,
                        endDate: this.endTime,
                    }).catch(toastError);
                    this.bizType = 0;
                    break;
                case 'account-running':
                    this.fetchTaskList({
                        pageNo: 1,
                        bizType: 34,
                        beginDate: '',
                        endDate: '',
                    }).catch(toastError);
                    this.bizType = 34;
                    break;
                case 'school-bill':
                    this.fetchTaskList({
                        pageNo: 1,
                        bizType: 104, // Assuming 55 is the bizType for school-bill
                        beginDate: this.startTime,
                        endDate: this.endTime,
                        schoolId: this.schoolId,
                    }).catch(toastError);
                    this.bizType = 104; // Assuming 55 is the bizType for school-bill
                    break;
                default:
                    break;
            }
            return '';
        },
        // 明细类型
        billTypeActive() {
            this.fetchTaskList({
                pageNo: 1,
                bizType: this.billTypeActive === '1' ? 0 : this.billTypeActive,
                beginDate: this.startTime,
                endDate: this.endTime,
                schoolId: this.schoolId,
            }).catch(toastError);
            this.bizType = this.billTypeActive === '1' ? 0 : this.billTypeActive;
        },
        // 文件类型
        // downMode() {
        //     this.fetchTaskList({
        //         pageNo: 1,
        //         bizType: this.billTypeActive === '1' ? 0 : this.billTypeActive,
        //         beginDate: this.startTime,
        //         endDate: this.endTime,
        //     }).catch(toastError);
        // },
    },
    mounted() {
        // 约束下拉选框最大宽度
        document.body.classList.add(this.$style.maxlen);

        this.fetchSettleSettingList(this.bizType).catch(toastError);
        // this.fetchTaskList(1).catch(toastError);
        this.handleReloadClick();
        // this.activeHeadle();
        // 根据URL参数和查询条件设置active
        if (this.isSchoolAccounts) {
            // 如果是校园账户，优先设置为school-bill
            this.active = 'school-bill';
        } else if (this.$route.query.type === 'account-running') {
            this.active = 'account-running';
        } else if (this.$route.query.type === 'school-running') {
            this.active = 'school-bill';
        } else {
            this.active = 'store-bill';
        }
        if (this.timeActive === 'months') {
            this.lastMonth();
        }
    },
    destroyed() {
        // 取消下拉选框最大宽度
        document.body.classList.remove(this.$style.maxlen);
    },
    methods: {
        ...mapActions('download', [
            'fetchSettleSettingList',
            'fetchPoiList',
            'fetchTaskList',
        ]),
        // 最近7天
        last7Days() {
            const end = new Date();
            const start = new Date(end.getTime() - 7 * 24 * 3600 * 1000);
            this.startTime = start;
            this.endTime = end;
        },
        // 最近一个月
        lastMonth() {
            const end = new Date();
            const start = new Date(end.getTime() - 29 * 24 * 3600 * 1000);
            this.startTime = start;
            this.endTime = end;
        },
        // 不同时间账单列表数据
        handleDateTypeChange(val) {
            this.timeActive = val;
            switch (val) {
                case 'all':
                    this.startTime = '';
                    this.endTime = '';
                    this.fetchTaskList({
                        pageNo: 1,
                        bizType: this.bizType,
                    }).catch(toastError);
                    break;
                case 'week':
                    this.last7Days();
                    this.fetchTaskList({
                        pageNo: 1,
                        bizType: this.bizType,
                        beginDate: this.startTime,
                        endDate: this.endTime,
                    }).catch(toastError);
                    break;
                case 'months':
                    this.lastMonth();
                    this.fetchTaskList({
                        pageNo: 1,
                        bizType: this.bizType,
                        beginDate: this.startTime,
                        endDate: this.endTime,
                        schoolId: this.schoolId,
                    }).catch(toastError);
                    break;
                case 'flee': this.startTime = '';
                    this.endTime = '';
                    break;
                default:
                    break;
            }
        },
        // 自定义
        customizeDateChange() {
            if (this.startTime && this.endTime) {
                this.fetchTaskList({
                    pageNo: 1,
                    bizType: this.bizType,
                    beginDate: this.startTime,
                    endDate: this.endTime,
                    schoolId: this.schoolId,
                }).catch(toastError);
            }
        },
        // 账单类型
        getBillType() {
            // 构建请求参数
            const params = {};
            let path = '';

            // 如果是校园账户，使用不同的接口路径
            if (this.isSchoolAccounts) {
                path = '/finance/waimai/money/channel/getBillType';
                params.type = 1;
            } else {
                path = '/finance/pc/api/billDownload/getBillType';
            }

            request.get(path, { params }).then((res) => {
                const { code, msg, data } = res.data;
                if (code !== 0) {
                    throw new Error(msg);
                }
                this.billType = data.billTypeList;
            }).catch((err) => {
                toastError(err);
            });
        },
        // 文件类型
        getFileType() {
            request.get('/finance/pc/api/billDownload/getFileType').then((res) => {
                const { code, msg, data } = res.data;
                if (code !== 0) {
                    throw new Error(msg);
                }
                this.fileType = data.fileTypeList;
            }).catch((err) => {
                toastError(err);
            });
        },
        // 标签文本截断
        getLabel(item) {
            if (item.acctName.length < 8) {
                return `${item.acctName}-${item.lastCardNo}`;
            }
            // eslint-disable-next-line
            return `${item.acctName.slice(0, 7)}...-${item.lastCardNo}`;
        },

        handleSettleSettingChange(val) {
            this.fetchPoiList({
                wmSettleId: val,
                sku: this.sku,
            }).catch(toastError);
        },

        createTask() {
            const {
                sku, checkedSettleId, checkedPoiId, startDate, endDate, bizType, yearDate, monthDate, dateActive, downMode, billTypeActive, active,
            } = this;
            if (!this.checkedSettleId && !this.isSchoolAccounts) {
                toast.warn('请选择结算账户');
                return;
            }
            if (!this.checkedPoiId && !this.isSchoolAccounts) {
                toast.warn('请选择结算门店');
                return;
            }
            if (!this.startDate && !this.yearDate && !monthDate) {
                toast.warn('请选择账单开始日期');
                return;
            }
            if (!this.endDate && !this.yearDate && !monthDate) {
                toast.warn('请选择账单结束日期');
                return;
            }

            this.exporting = true;
            const params = {
                wmPoiId: checkedPoiId,
                wmSettleId: checkedSettleId,
                bizType,
            };
            if (bizType == '54') {
                params.fileType = downMode == 'EXCEL' ? 1 : 2;
            }
            if (dateActive == 'month') {
                params.beginDate = dayjs(monthDate.setDate(1)).format('YYYY-MM-DD');
                params.endDate = dayjs(new Date(monthDate.getFullYear(), monthDate.getMonth() + 1, 0)).format('YYYY-MM-DD');
            }
            if (dateActive == 'day') {
                params.beginDate = dayjs(startDate).format('YYYY-MM-DD');
                params.endDate = dayjs(endDate).format('YYYY-MM-DD');
            }
            if (dateActive == 'years') {
                params.yearTime = dayjs(yearDate).format('YYYY');
            }
            if (this.isSchoolAccounts) {
                params.schoolId = this.schoolId;
            }
            request.get('/finance/pc/api/billDownload/createBillExportTask', { params })
                .then((res) => {
                    this.exporting = false;

                    const { code, msg } = res.data;

                    if (code !== 0) {
                        toast.error(msg);
                        throw new Error(msg);
                    }

                    // 刷新任务列表
                    this.handlePageChange(1);
                })
                .catch((err) => {
                    this.exporting = false;
                    toastError(err);
                });
        },

        handlePageChange(pageNo) {
            const { bizType } = this;
            this.fetchTaskList({
                pageNo,
                bizType,
                beginDate: this.startTime,
                endDate: this.endTime,
                schoolId: this.schoolId,
            }).catch(toastError);
        },

        handleReloadClick() {
            if (this.loading) {
                return;
            }
            const { bizType } = this;
            this.fetchTaskList({
                pageNo: 1,
                bizType,
                beginDate: this.startTime,
                endDate: this.endTime,
                schoolId: this.schoolId,
            }).catch(toastError);
        },

        handleSettleIdChange(val) {
            const cookies = getCookies();
            const acctId = parseInt(cookies.acctId, 10) || null;
            const wmPoiId = parseInt(cookies.wmPoiId, 10) || null;

            LXAnalytics('moduleEdit', 'b_waimai_e_n9iaviij_me', {
                poi_id: wmPoiId,
                custom: {
                    acctId,
                    wmPoiId: wmPoiId || -1,
                    download_value: val,
                },
            }, { cid: this.cid });
        },

        handlePoiIdChange(val) {
            const cookies = getCookies();
            const acctId = parseInt(cookies.acctId, 10) || null;
            const wmPoiId = parseInt(cookies.wmPoiId, 10) || null;

            LXAnalytics('moduleEdit', 'b_waimai_e_cjetcajj_me', {
                poi_id: wmPoiId,
                custom: {
                    acctId,
                    wmPoiId: wmPoiId || -1,
                    download_value: val,
                },
            }, { cid: this.cid });
        },
        handleSchoolAccount() {
            request.get('/finance/pc/api/billDownload/checkSchoolAccount').then((res) => {
                const { code, msg, data } = res.data;
                if (code !== 0) {
                    throw new Error(msg);
                }
                this.isSchoolAccount = data;
            }).catch((err) => {
                toastError(err);
            });
        },
    },
};
</script>

<style lang="scss" module>
.container {
    height: 100%;

    h3 {
        margin: 0;
        font-size: 20px;
        line-height: 1;
        color: #3F4156;
    }
}

.alert {
    margin: 30px 0;
}
.card {
    min-height: 100%;
    border-radius: 2px;
    padding: 40px;
    padding-bottom: 10px;
    background: #FFF;
    box-shadow: 0 0 6px 0 #F3F3F4;
}

.label {
    display: inline-block;
    width: 65px;
    margin-right: 10px;
    text-align: right;
}

.select {
    display: inline-block;
    width: 200px;
}

:global {
      .roo-datepicker-body table tr {
          height: 0px !important;
      }
    }

.row {
    margin-bottom: 20px;
    &:last-child {
        margin-bottom: 0;
    }
    :global(.roo-tabs-container) {
          border: none;
    }
}
.mr10 {
    margin-right: 10px;
}

.section {
    margin: 30px 0;

    :global(.roo-tabs-container) {
      border: none;
    }
    h4 {
        position: relative;
        padding-left: 14px;
        margin-top: 0;
        margin-bottom: 20px;
        // border-left: 4px solid #A2A4B3;
        font-size: 14px;
        line-height: 1;
        color: #3F4156;

        &:before {
            content: '';
            position: absolute;
            left: 0;
            top: 1px;
            display: block;
            width: 4px;
            height: 12px;
            border-radius: 1px;
            background: #A2A4B3;
        }
    }
}

.reload {
    float: right;
    font-weight: normal;
    cursor: pointer;
    position: absolute;
    right: 0;
    width: 72px;
    height: 36px;
    border: 1px solid #999;
    border-radius: 18px;
    text-align: center;
    line-height: 36px;
    color: #222222;
    font-family: PingFang SC;
    font-weight: 500;
    font-size: 14px;
    // margin-bottom: -10px;
    bottom: 22px;
}

.pagination {
    text-align: right;
    margin-top: 40px;

    :global(.pagination) {
        margin: 0;
    }
}

// ---------------- 下拉选框 ----------------

// 约束下拉选框宽度
.maxlen :global(.roo-select-dropdown) {
    max-width: 200px;
}
.maxlen :global(.dropdown-menu a) {
    padding: 0 10px;
}

.option {
    display: flex;
    // max-width: 180px;
}
.settle-name {
    flex: 1;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}
.card-no {
    flex-grow: 0;
    flex-shrink: 0;
}
.schoolInfo{
    color: #3F4156;
  font-family: PingFang SC;
  font-weight: 500;
  font-size: 18px;
  line-height: 24px;
}
</style>
