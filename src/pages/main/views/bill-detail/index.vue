<template>
    <div :class="$style.billContainer">
        <roo-breadcrumb
            :class="$style.breadcrumb"
            :items="breadcrumb"
            :props="props"
        />

        <div
            v-if="billDetails"
            :class="$style.container"
        >
            <div :class="$style.billInfo">
                <div :class="$style.infoTitle">
                    账单信息
                </div>
                <ul :class="$style.infoContent">
                    <li :class="$style.infoItem">
                        <div :class="$style.infoItemTitle">
                            收入金额
                        </div>
                        <span :class="$style.infoItemText">{{ billDetails.billAmount | formatNumberThousand }}</span>
                    </li>
                    <li :class="$style.infoItem">
                        <div :class="$style.infoItemTitle">
                            汇入账户
                        </div>
                        <span :class="$style.infoItemText">{{ billDetails.accountName }}</span>
                    </li>
                    <li :class="$style.infoItem">
                        <div :class="$style.infoItemTitle">
                            类型
                        </div>
                        <span :class="$style.infoItemText">{{ billDetails.settleBillType }}</span>
                    </li>
                    <li :class="$style.infoItem">
                        <div :class="$style.infoItemTitle">
                            账期
                        </div>
                        <span :class="$style.infoItemText">{{ billDetails.settleBillStartDate }}~{{ billDetails.settleBillEndDate }}</span>
                    </li>
                    <li :class="$style.infoItem">
                        <div :class="$style.infoItemTitle">
                            交易时间
                        </div>
                        <span :class="$style.infoItemText">{{ billDetails.createTime }}</span>
                    </li>
                    <li :class="$style.infoItem">
                        <div :class="$style.infoItemTitle">
                            流水账号
                        </div>
                        <span :class="$style.infoItemText">{{ billDetails.settleBillId }}</span>
                    </li>
                </ul>
            </div>

            <div :class="$style.billDetail">
                <div :class="$style.detailTitle">
                    账单明细
                </div>
                <div :class="$style.detailContent">
                    <div :class="$style.detailSummary">
                        <span :class="$style.summaryDate">
                            {{ billDetails.settleBillStartDate }}~{{ billDetails.settleBillEndDate }}账单
                        </span>
                        <div :class="$style.summaryMsg">
                            <div>￥{{ billDetails.billAmount }}</div>
                            <div :class="$style.msg">
                                已汇入余额流水
                            </div>
                        </div>
                    </div>
                    <div :class="$style.detailItemsContainer">
                        <div
                            v-for="(bill, index) in billDetails.dailyBills"
                            :key="index"
                            :class="$style.detailItems"
                        >
                            <div
                                :class="$style.detailItem"
                            >
                                <div :class="$style.date">
                                    {{ bill.dailyBillDate }}
                                </div>
                                <div :class="$style.rightContent">
                                    <div :class="$style.billAmount">
                                        {{ bill.dailyBillAmount }}
                                    </div>
                                    <div :class="$style.operate">
                                        <div
                                            :class="$style.moreDetail"
                                        >
                                            <a
                                                v-lx-mc="{
                                                    bid: 'b_waimai_e_52jw98ii_mc',
                                                    options: { cid: 'c_waimai_e_t0yst1ta' },
                                                }"
                                                href="#noop"
                                                @click.prevent="handleView(bill)"
                                            >
                                                查看
                                            </a>
                                        </div>
                                        <div :class="$style.line"></div>
                                        <a
                                            v-lx-mc="{
                                                bid: 'b_waimai_e_ifafwur2_mc',
                                                options: { cid: 'c_waimai_e_t0yst1ta' },
                                            }"
                                            :class="$style.download"
                                            href="#noop"
                                            @click="handleDownload(bill.dailyBillDate)"
                                        >
                                            下载
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <roo-loading
            :show="loading"
            fullscreen
            backdrop
            lock
        />
    </div>
</template>

<script>
import { mapState, mapActions, mapMutations } from 'vuex';
/* eslint-disable import/no-unresolved, import/extensions */
import request from '$src/utils/request';
import { toast } from '@roo/roo-vue';

/* eslint-disable import/extensions, import/no-unresolved */
import * as utils from '$lib/utils.js';
import toastError from '$lib/toast-error';
/* eslint-enable import/extensions, import/no-unresolved */

export default {
    name: 'BillDetail',
    filters: {
        formatNumberThousand: utils.formatNumberThousand,
    },
    data() {
        return {
            props: {
                text: 'name',
            },
            downloading: false,
        };
    },
    computed: {
        wmPoiId() {
            const cookies = utils.getCookies();
            const wmPoiIdFromUrl = this.$route.query && this.$route.query.wmPoiId;
            return wmPoiIdFromUrl || cookies.wmPoiId;
        },
        breadcrumb() {
            if (this.$route.query.outId) {
                return [{
                    name: '我的账户',
                }, {
                    name: '账单详情',
                    link: {
                        name: 'mutil-bill-detail',
                    },
                    query: ['outId'],
                }, {
                    name: '账单明细',
                }];
            }
            return [{
                name: '我的账户',
            }, {
                name: '账单明细',
            }];
        },
        ...mapState('billDetail', ['billDetails', 'loading']),
    },
    mounted() {
        this.fetchBillDetail({
            wmPoiId: this.wmPoiId,
            settleBillId: this.$route.query.settleBillId,
        }).catch(toastError);
    },
    methods: {
        handleView(bill) {
            this.$router.push({
                name: 'daily-bill',
                query: {
                    wmPoiId: this.wmPoiId || '',
                    dailyBillDate: bill.dailyBillDate,
                    settleState: bill.settleState,
                },
            });
        },
        handleDownload(dailyBillDate) {
            this.changeLoading(true);

            const params = {
                beginDate: dailyBillDate,
                endDate: dailyBillDate,
                wmPoiId: this.wmPoiId,
            };
            request.get('/finance/pc/api/billDownload/createBillExportTask', {
                params,
            }).then((res) => {
                this.changeLoading(false);

                const { data, msg, code } = res.data;
                if (code !== 0 || data.taskNo === 0) {
                    throw new Error(msg);
                }
                toast.success('下载成功，请到下载专区下载');
            }, (err) => {
                this.changeLoading(false);

                throw err;
            }).catch(toastError);
        },
        ...mapMutations('billDetail', ['changeLoading']),
        ...mapActions('billDetail', ['fetchBillDetail']),
    },
};
</script>

<style lang="scss" module>
.bill-container {
    padding: 20px;
}
.bill-info,.bill-detail {
    background: #fff;
}
.info-title,.detail-title {
    height: 40px;
    line-height: 40px;
    padding: 0 20px;
    color: #2a2a2a;
    font-weight: bold;
    background: #fdfdfd;
    border-bottom: 1px solid #fafafa;
}
.info-content {
    padding: 10px 20px;
    font-size: 12px;
    color: #a0a0a0;
}
.info-item {
    padding:10px 0;
}
.info-item-title {
    display: inline-block;
    width: 48px;
    text-align: right;
}
.info-item-text {
    margin-left: 30px;
    font-size: 12px;
    color: #000;
}
.bill-detail {
    margin-top: 20px;
}
.detail-summary {
    height: 60px;
    padding: 0 20px;
    border-left: 4px solid #A2A4B3;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-weight: bold;
    border-bottom: 1px solid #fafafa;
}
.msg {
    font-weight: normal;
    color: #858692;
    font-size: 12px;
}
.detail-items-container {
    padding: 0 20px;
}
.detail-items {
    border-bottom: 1px dotted  #e5e5e5;
}
.detail-items:nth-last-child(1) {
    border-bottom: none;
}
.detail-item {
    width: 100%;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.date,.rightContent {
    display: inline-block;
    font-size: 12px;
    font-weight: normal;
}
.bill-amount,.operate,.more-detail {
   display: inline-block;
}
.bill-amount {
    margin-right: 40px;
}
.line {
    display: inline-block;
    width: 1px;
    height: 14px;
    margin: 0 8px -2px 8px;
    background: #F89800;
}
</style>
