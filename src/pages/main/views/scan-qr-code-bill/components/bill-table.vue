<template>
    <table class="bill-charge-table">
        <col>
        <col>
        <colgroup class="details">
            <col>
            <col>
            <col>
        </colgroup>
        <col>

        <thead>
            <tr>
                <th>日期&订单编号</th>
                <th>类型</th>
                <th>订单总价</th>
                <th>用户支付</th>
                <th>服务费</th>
                <th>结算金额</th>
            </tr>
        </thead>

        <tbody>
            <tr
                v-if="loading"
                key="loading"
            >
                <td
                    :style="{ height: `${(list.length || 1) * 40}px` }"
                    colspan="6"
                >
                    正在加载
                </td>
            </tr>
            <tr
                v-else-if="list.length === 0"
                key="empty"
            >
                <td colspan="6">
                    暂无数据
                </td>
            </tr>
            <template v-else>
                <tr
                    v-for="(item, idx) in list"
                    :key="idx"
                >
                    <td>{{ item.dailyBillDate }} #{{ item.wmOrderViewId }}</td>
                    <td>{{ item.billChargeTypeName }}</td>
                    <td>{{ item.wmOrderTotalPrice | formatAmount }}</td>
                    <td>{{ item.userPayAmount | formatAmount }}</td>
                    <td>{{ item.platformCommissionAmount | formatAmount }}</td>
                    <td>
                        <span :class="$style.amount">
                            {{ item.chargeAmount | formatAmount }}
                        </span>
                    </td>
                </tr>
            </template>
        </tbody>
    </table>
</template>

<script>
// 使用和日账单（订单明细）页面相同的样式

/* eslint-disable import/extensions, import/no-unresolved */
import { formatNumberThousand } from '$lib/utils';
/* eslint-enable import/extensions, import/no-unresolved */

import '../../daily-bill/components/table.scss';

export default {
    name: 'BillTable',
    filters: {
        formatAmount(num) {
            return formatNumberThousand((num / 100).toFixed(2));
        },
    },
    props: {
        loading: {
            type: Boolean,
            default: false,
        },
        list: {
            type: Array,
            default() {
                return [];
            },
        },
    },
};
</script>

<style lang="sass" module>

</style>
