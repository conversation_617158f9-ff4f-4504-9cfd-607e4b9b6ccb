<template>
    <div :class="$style.container">
        <div :class="$style.card">
            <h3 :class="$style.h3">
                {{ settleDate }} 扫码购账单
            </h3>
            <div :class="$style.mb20">
                <money
                    :class="$style.money"
                    :amount="totalAmount"
                />
                &nbsp;&nbsp;&nbsp;{{ settleStateText }}
            </div>
            <div :class="$style.mb20">
                <radio-button-group
                    v-model="billChargeTypeCode"
                    size="small"
                    @change="handleFilterChange"
                >
                    <radio-button
                        v-for="opt in options"
                        :key="opt.value"
                        :val="opt.value"
                    >
                        {{ opt.label }}
                    </radio-button>
                </radio-button-group>
            </div>

            <bill-table
                :class="$style.mb20"
                :loading="loading2"
                :list="billList"
            />

            <div :class="$style.pagination">
                <roo-pagination
                    :total="total"
                    :page-size="pageSize"
                    :current-page="pageNo"
                    @current-change="handlePageChange"
                />
            </div>
        </div>

        <roo-loading
            :show="loading1"
            fullscreen
            backdrop
            lock
        />
    </div>
</template>

<script>
import axios from 'axios';
/* eslint-disable import/no-unresolved, import/extensions */
import request from '$src/utils/request';
import dayjs from 'dayjs';
import { toast } from '@roo/roo-vue';

/* eslint-disable import/extensions, import/no-unresolved */
import RadioButton from '$components/radio-button';
import RadioButtonGroup from '$components/radio-button-group';

import Money from '$components/money';

import toastError from '$lib/toast-error';
// import { formatNumberThousand } from '$lib/utils';
/* eslint-enable import/extensions, import/no-unresolved */

import BillTable from './components/bill-table';

const { CancelToken } = axios;

export default {
    name: 'ScanQRCodeBill',
    components: {
        Money,
        BillTable,
        RadioButton,
        RadioButtonGroup,
    },
    data() {
        return {
            loading1: false,
            loading2: false,

            billChargeTypeCode: -1,

            settleState: 0,
            totalAmount: 0,
            settleDate: dayjs().format('YYYY-MM-DD'),

            total: 0,
            pageNo: 1,
            pageSize: 10,
            billList: [],
        };
    },
    computed: {
        dailyBillId() {
            return this.$route.query.dailyBillId;
        },
        settled() {
            return this.settleState === 1;
        },
        settleStateText() {
            return this.settleState === 1 ? '已结算' : '未结算';
        },

        options() {
            return [{
                value: -1,
                label: '全部',
            }, {
                value: 2003,
                label: '扫码购订单',
            }, {
                value: 2004,
                label: '扫码购订单退款',
            }, {
                value: 2048,
                label: '扫码购订单部分退款',
            }, {
                value: 3003,
                label: '自助收银订单',
            }, {
                value: 3004,
                label: '自助收银订单退款',
            }, {
                value: 3048,
                label: '自助收银订单部分退款',
            }];
        },
    },
    watch: {
        dailyBillId() {
            this.reload();
        },
    },
    created() {
        this.source = null;
    },
    mounted() {
        this.reload();
    },
    methods: {
        fetchBillSummary() {
            this.loading1 = true;
            const { dailyBillId } = this;
            return request.get('/finance/v3/h5/api/scanCode/queryDailyBill', {
                params: {
                    dailyBillId,
                },
            }).then((res) => {
                this.loading1 = false;

                const { code, msg, data } = res.data;

                if (code !== 0) {
                    throw new Error(msg);
                }

                this.settleState = data.settleState;
                this.totalAmount = data.totalAmount;
                this.settleDate = data.settleDate;
            }, (err) => {
                this.loading1 = false;
                throw err;
            }).catch(toastError);
        },
        fetchBillList(pageNo) {
            if (this.source) {
                this.source.cancel();
                this.source = null;
            }

            this.source = CancelToken.source();
            const { token } = this.source;

            this.loading2 = true;
            const { dailyBillId, pageSize, billChargeTypeCode } = this;
            return request.get('/finance/v3/h5/api/scanCode/queryFlow4Bill', {
                params: {
                    dailyBillId,
                    pageNo,
                    pageSize,
                    billChargeTypeCode,
                },
                cancelToken: token,
            }).then((res) => {
                this.loading2 = false;

                const { code, msg, data } = res.data;

                if (code === 2009) {
                    this.pageNo = pageNo;
                    this.total = 0;
                    this.billList = [];
                    return;
                }

                if (code !== 0) {
                    throw new Error(msg);
                }

                this.pageNo = pageNo;
                this.total = data.count;
                this.billList = data.billList || [];
            }, (err) => {
                this.loading2 = false;
                throw err;
            }).catch(toastError);
        },

        reload() {
            if (!this.dailyBillId) {
                toast.error('缺少参数 dailyBillId');
                return;
            }
            this.billChargeTypeCode = -1;
            this.fetchBillSummary();
            this.fetchBillList(1);
        },

        handleFilterChange() {
            this.fetchBillList(1);
        },
        handlePageChange(pageNo) {
            this.fetchBillList(pageNo);
        },
    },
};
</script>

<style lang="scss" module>
.container {
    padding: 20px;
}

.card {
    padding: 20px;
    background: #FFF;
}

.money {
    font-size: 32px;
}

.mb20 {
    margin-bottom: 20px;
}

.h3 {
    margin-top: 0;
    margin-bottom: 20px;
}

.pagination {
    text-align: right;

    :global(.pagination) {
        margin: 0;
    }
}
</style>
