<template>
    <div :class="$style.accountInfoContainer" >
        <roo-breadcrumb>
            <roo-breadcrumb-item
                :link="{ name: 'account-list' }"
            >
                我的账户
            </roo-breadcrumb-item>
            <roo-breadcrumb-item>账户信息</roo-breadcrumb-item>
        </roo-breadcrumb>
        <div>
            <div :class="$style.accountInfo">
                <div :class="$style.infoTitle">结算信息</div>
                <ul :class="$style.infoContent">
                    <li :class="$style.infoItem">
                        <div :class="$style.infoItemTitle">结算周期</div>
                        <span :class="$style.infoItemText">{{ accountInfo.settlePeriod }}</span>
                    </li>
                    <li :class="$style.infoItem">
                        <div :class="$style.infoItemTitle">结算方式</div>
                        <span :class="$style.infoItemText">{{ accountInfo.settleTypeName }}</span>
                    </li>
                    <li :class="$style.infoItem">
                        <div :class="$style.infoItemTitle">最低结算金额</div>
                        <span :class="$style.infoItemText">{{ accountInfo.minWithdrawAmount }}元</span>
                    </li>
                    <li :class="$style.infoItem">
                        <div :class="$style.infoItemTitle">商家财务联系人</div>
                        <span :class="$style.infoItemText">{{ contact }}</span>
                    </li>
                </ul>
            </div>

            <div
                v-if="accountInfo.payBindType === 1"
                :class="$style.cardInfo"
            >
                <div :class="$style.infoTitle">银行卡信息</div>
                <ul :class="$style.infoContent">
                    <li :class="$style.infoItem">
                        <div :class="$style.infoItemTitle">开户行</div>
                        <span :class="$style.infoItemText">{{ accountInfo.bank }}</span>
                    </li>
                    <li :class="$style.infoItem">
                        <div :class="$style.infoItemTitle">银行卡号</div>
                        <span :class="$style.infoItemText">{{ accountInfo.cardNo }}</span>
                    </li>
                    <li :class="$style.infoItem">
                        <div :class="$style.infoItemTitle">户名</div>
                        <span :class="$style.infoItemText">{{ accountInfo.bankAccountName }}</span>
                    </li>
                </ul>
            </div>

            <div
                v-else
                :class="$style.cardInfo"
            >
                <div :class="$style.infoTitle">美团商家钱包</div>
                <ul :class="$style.infoContent">
                    <li :class="$style.infoItem">
                        <div :class="$style.infoItemTitle">户名</div>
                        <span :class="$style.infoItemText">{{ accountInfo.bankAccountName }}</span>
                    </li>
                    <li :class="$style.infoItem">
                        <div :class="$style.infoItemTitle">美团商家钱包</div>
                        <span :class="$style.infoItemText">
                            {{ accountInfo.walletId }}
                            <a
                                v-lx-mc="{
                                    bid: 'b_waimai_e_fqw4hr5p_mc',
                                    options: { cid },
                                }"
                                :href="accountInfo.walletFlowUrl"
                            >
                                查看钱包流水
                            </a>
                        </span>
                    </li>
                </ul>
            </div>
        </div>
        <div :class="$style.footer">
            <span
                v-lx-mc="{
                    bid: 'b_waimai_e_rne1c0y4_mc',
                    options: { cid },
                }"
                :class="$style.link"
                @click="showHelp = true"
            >变更银行账户信息 ></span>
        </div>
        <roo-modal
            v-model="showHelp"
            :class="$style.modal"
            :backdrop="true"
            title="变更银行账户帮助"
            size="normal"
            effect="zoom"
        >
            <ul :class="$style.list">
                <li>联系业务经理，递交申请材料并签订变更合同，申请材料包括：身份证复印件、银行卡复印件</li>
                <li>业务经理在系统提交变更资料，您将收到系统下发的信息审核提示短信</li>
                <li>审核通过后，您将收到系统下发的银行账户信息变更成功提示短信；</li>
                <li>我的业务经理：{{ contact }}</li>
            </ul>
            <roo-button
                slot="footer"
                @click="showHelp = false"
            >我知道了</roo-button>
        </roo-modal>
        <roo-loading
            :show="loading"
            fullscreen
            backdrop
            lock
        />
    </div>
</template>

<script>
import { mapState, mapActions } from 'vuex';

/* eslint-disable import/extensions, import/no-unresolved */
import toastError from '$lib/toast-error';
/* eslint-enable import/extensions, import/no-unresolved */


export default {
    name: 'AccountInfo',
    data() {
        return {
            showHelp: false,
        };
    },
    computed: {
        // 灵犀 cid
        cid() {
            const { meta } = this.$route;
            return meta && meta.cid;
        },

        contact() {
            return `${this.accountInfo.serviceManagerName} ${this.accountInfo.serviceManagerMobile}`;
        },
        ...mapState('accountInfo', ['loading', 'accountInfo']),
    },
    mounted() {
        const params = {
            wmPoiId: this.$route.query.wmPoiId,
        };
        this.fetchAccountInfo(params).catch(toastError);
    },
    methods: {
        ...mapActions('accountInfo', ['fetchAccountInfo']),
    },
};
</script>

<style lang="scss" module>
.account-info-container {
    padding: 10px;
}
.account-info, .card-info{
    background: #fff;
}
.card-info {
    margin-top: 20px;
}
.info-title{
    height: 40px;
    line-height: 40px;
    padding: 0 20px;
    color: #2a2a2a;
    font-weight: bold;
    background: #fdfdfd;
    border-bottom: 1px solid #fafafa;
}
.info-content {
    padding: 10px 20px;
    font-size: 12px;
    color: #a0a0a0;
}
.info-item {
    padding:10px 0;
}
.info-item-title {
    display: inline-block;
    width: 90px;
    text-align: right;
}
.info-item-text {
    margin-left: 30px;
    font-size: 12px;
    color: #000;
}
.footer {
    text-align: center;
    margin-top: 20px;
}
.link {
    color: #2A2A2A;
    cursor: pointer;
}
.list {
    padding-left: 30px;
    line-height: 22px;
    color: #2A2A2A;
}
.list li {
    list-style: disc;
}

.modal:global(.roo-modal) {
    :global(.roo-modal-dialog .roo-modal-content .roo-modal-footer) {
        padding-top: 50px;
    }
}
</style>
