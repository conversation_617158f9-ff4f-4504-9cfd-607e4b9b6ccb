<template>
    <div :class="$style.card">
        <div :class="$style.headerRow">
            <img
                :class="$style.avatar"
                :src="avatar"
                alt="avatar"
            />
            <h4
                v-if="name"
                :class="$style.h4"
            >
                {{ name }}
            </h4>
            <span
                v-if="poiCount > 1"
                :class="$style.grayMark"
            >
                此账户关联门店 {{ poiCount }} 个
            </span>
            <roo-button
                v-if="poiCount <= 1"
                v-lx-mc="{
                    bid: 'b_waimai_e_6gbo1loh_mc',
                    options: { cid },
                }"
                type="hollow"
                size="mini"
                @click="navigateToAccountInfo"
            >
                账户信息
            </roo-button>
        </div>
        <!-- 各类资金账户 -->
        <div :class="$style.accounts">
            <!-- 主账户余额 -->
            <div :class="$style.account">
                <div :class="$style.accountTitle">
                    可提现余额
                    <roo-icon name="question">
                        <roo-tooltip
                            trigger="hover"
                            placement="right"
                        >
                            以下情况无法发起手动提现操作：
                            <ol :class="$style.ol">
                                <li>1. 当前可提现余额小于最低提现金额</li>
                                <li>2. 当前门店属于多门店合并打款</li>
                                <li>3. 可提现余额小于等于0</li>
                            </ol>
                        </roo-tooltip>
                    </roo-icon>
                </div>
                <div :class="$style.accountBalance">
                    <money :amount="mainBalance" />
                </div>
                <div>
                    <roo-button
                        v-if="showMainAccountWithdraw && !frozen"
                        key="recharge"
                        v-lx-mc="{
                            bid: 'b_waimai_e_ntggpuvw_mc',
                            options: { cid },
                        }"
                        :class="$style.mr20"
                        size="mini"
                        @click="navigateToWithdraw(0)"
                    >
                        提现
                    </roo-button>
                    <roo-button
                        v-if="showMainAccountRecharge"
                        key="withdraw"
                        v-lx-mc="{
                            bid: 'b_waimai_e_ypzhc2wi_mc',
                            options: { cid },
                        }"
                        type="hollow"
                        size="mini"
                        @click="navigateToRecharge(1008)"
                    >
                        充值
                    </roo-button>
                    <roo-button
                        v-if="frozen"
                        key="frozen"
                        type="dashed"
                        size="mini"
                    >
                        账户已冻结
                        <roo-icon name="question" />
                        <roo-tooltip trigger="hover">
                            <div>很抱歉，您当前门店的账户已冻结，无法提现。</div>
                            <div>如有问题请联系业务经理处理，相关款项会在账户解冻后的下个账期发起提现。</div>
                            <div>谢谢!</div>
                        </roo-tooltip>
                    </roo-button>
                </div>
            </div>

            <!-- 推广费余额 -->
            <div
                v-if="showAdAccount"
                :class="$style.account"
            >
                <div :class="$style.accountTitle">
                    推广费账户
                </div>
                <div :class="$style.accountBalance">
                    <money :amount="adBalance" />
                </div>
                <div>
                    <roo-button
                        v-show="showAdAccountRecharge"
                        key="recharge"
                        v-lx-mc="{
                            bid: 'b_waimai_e_kxqyc9v5_mc',
                            options: { cid },
                        }"
                        :class="$style.mr20"
                        type="hollow"
                        size="mini"
                        @click="navigateToRecharge(1028)"
                    >
                        充值
                    </roo-button>
                    <roo-button
                        v-show="showAutoTransfer && showSpecialItem"
                        key="withdraw"
                        v-lx-mc="{
                            bid: 'b_waimai_e_yiknsfl5_mc',
                            options: { cid },
                        }"
                        type="hollow"
                        size="mini"
                        @click="navigateToADSys"
                    >
                        自动充值
                    </roo-button>
                </div>
            </div>
            <!-- 售后红包账户 -->
            <div
                v-if="showFoodSafetyRedPacketAccount"
                :class="$style.account"
            >
                <div :class="$style.accountTitle">
                    售后红包账户
                </div>
                <div :class="$style.accountBalance">
                    <money :amount="foodSafetyRedPacketBalance" />
                </div>
            </div>

            <!-- 推广红包账户 -->
            <div
                v-if="showRedPacketAccount && showSpecialItem"
                :class="$style.account"
            >
                <div :class="$style.accountTitle">
                    推广红包账户
                </div>
                <div :class="$style.redPacketAccountText">
                    <div>
                        高佣返红包:
                        <money
                            :amount="highCommissionReturnBalance"
                            :class="$style.redPacketAccountBalance"
                        />
                    </div>
                    <div>
                        交易额转推广费:
                        <money
                            :amount="tradeToPromotionBalance"
                            :class="$style.redPacketAccountBalance"
                        />
                    </div>
                    <div>
                        智能账户:
                        <money
                            :amount="intelligentActivityBalance"
                            :class="$style.redPacketAccountBalance"
                        />
                    </div>
                </div>
            </div>

            <!-- 履约保证金账户 -->
            <div
                v-if="showDepositAccount && showSpecialItem"
                :class="$style.account"
            >
                <div :class="$style.accountTitle">
                    保证金账户
                    <!-- <roo-icon name="question">
                        <roo-tooltip
                            trigger="hover"
                            placement="right"
                        >
                            <div>如商家在优惠申请书期限内无违约情形且到期20天内未续签的，系统将在第21天自动退还保证金。</div>
                            <div>其他特殊情形，请联系您的业务经理处理。</div>
                        </roo-tooltip>
                    </roo-icon> -->
                </div>
                <div :class="$style.accountBalance">
                    <money :amount="depositBalance" />
                </div>
                <div>
                    <!-- 保证金账户不在展示提现按钮 -->
                    <!-- <roo-button
                        v-if="showDepositAccountRechargeFlag"
                        key="recharge"
                        v-lx-mc="{
                            bid: 'b_waimai_e_un2su2d3_mc',
                            options: { cid },
                        }"
                        :class="$style.mr20"
                        type="hollow"
                        size="mini"
                        @click="navigateToRecharge(1009)"
                    >
                        充值
                    </roo-button> -->
                    <roo-button
                        v-if="supportDepositAccountSelfWithdraw"
                        key="recharge"
                        :class="$style.mr20"
                        size="mini"
                        @click="navigateToWithdraw(1009)"
                    >
                        提现
                    </roo-button>
                </div>
            </div>

            <div v-if="bussinessCount">
                <div>您当前有{{ bussinessCount }}笔{{ bussinessName }}正在审核中，金额 {{ bussinessAmount | formatCent }} 元。</div>
                <div>整体流程预计7～15个工作日内完成，请你耐心等待。</div>
                <div>更多详细信息请查看相关提现流水。</div>
            </div>

            <!-- 配送费账户 -->
            <!-- <div
                v-if="showLogisticsAccount"
                :class="$style.account"
            >
                <div :class="$style.accountTitle">
                    配送费账户
                </div>
                <div :class="$style.accountBalance">
                    <money :amount="logisticsBalance" />
                </div>
            </div> -->
        </div>
    </div>
</template>

<script>
import { mapState } from 'vuex';

/* eslint-disable import/extensions, import/no-unresolved */
import Money from '$components/money';
import { formatCent } from '$lib/filters';
import { getQueryString, getCookieValue } from '$lib/utils';
/* eslint-enable import/extensions, import/no-unresolved */

export default {
    name: 'AccountInfoCard',
    filters: {
        formatCent,
    },
    components: {
        Money,
    },
    computed: {
        // 灵犀 cid
        cid() {
            const { meta } = this.$route;
            return meta && meta.cid;
        },

        ...mapState('accountFlow/accountInfo', [
            'loading',
            // 基础信息
            'name',
            'avatar',

            // 主账户
            'mainBalance',
            'showMainAccountRecharge',
            'showMainAccountWithdraw',
            'frozen',

            // 推广费账户
            'showAdAccount',
            'adBalance',
            'showAdAccountRecharge',
            'showAutoTransfer',

            // 保证金账户
            'showDepositAccount',
            'depositBalance',
            'showDepositAccountRechargeFlag',
            'supportDepositAccountSelfWithdraw',

            // 推广红包账户
            'showRedPacketAccount',
            'redPacketBalance',
            'highCommissionReturnBalance', // 高佣返余额
            'tradeToPromotionBalance', // 交易费转推广费余额
            'intelligentActivityBalance', // 智能活动金额

            // 配送费账户
            'showLogisticsAccount',
            'logisticsBalance',

            'bussinessName',
            'bussinessCount',
            'bussinessAmount',

            // 是否展示售后红包账户
            'showFoodSafetyRedPacketAccount',
            // 售后红包账户金额
            'foodSafetyRedPacketBalance',
            // 合规检查
            'showSpecialItem',
        ]),
        poiCount() {
            return parseInt(this.$route.query.poiCount, 10) || 1;
        },
    },
    methods: {
        navigateToAccountInfo() {
            const { wmPoiId } = this.$route.query;
            const query = wmPoiId ? { wmPoiId } : { wmPoiId: getCookieValue('wmPoiId') };
            const queryString = getQueryString(query);
            window.location = `/finance/newfe#/finance/accountDetails?${queryString}`;
        },
        navigateToWithdraw(acctType) {
            const { wmPoiId } = this.$route.query;
            const query = {
                wmPoiId: wmPoiId || null,
                acctType: acctType || 0,
            };
            window.location = `/finance/newfe#/finance/balanceWithdraw?${getQueryString(query)}`;
        },
        navigateToRecharge(acctType) {
            const query = { acctType };
            const { wmPoiId, linkSource } = this.$route.query;
            if (wmPoiId) {
                query.wmPoiId = wmPoiId;
            }
            if (linkSource) {
                query.linkSource = linkSource;
            }
            // 充值页面，先修改内部项目跳转
            const queryString = getQueryString(query);
            window.location = `/finance/newfe#/finance/balanceRecharge?${queryString}`;
        },
        navigateToADSys() {
            window.location = '/ad/v1/pc?skipWaimaie#/account';
        },
    },
};
</script>

<style lang="scss" module>
.card {
    position: relative;
    flex-shrink: 0;
    padding: 30px 0;
    margin-bottom: 10px;
    border-radius: 2px;
    background: #fff;
    box-shadow: 0 0 6px 0 #f3f3f4;

    h4 {
        margin: 0;
        color: #2a2a2a;
        font-weight: bold;
        font-size: 14px;
    }

    .h4 {
        display: inline-block;
        margin: 0 10px;
    }

    :global(.roo-icon) {
        height: auto;
    }
}

.gray-mark {
    margin: 0 10px;
    color: #858692;
}

.header-row {
    position: absolute;
    padding: 0 40px;
    top: -10px;
}

.avatar {
    display: inline-block;
    width: 50px;
    height: 50px;
    border-radius: 3px;
    vertical-align: bottom;
}

.accounts {
    display: flex;
    padding-top: 30px;
}

.account {
    width: 25%;
    padding: 0 40px;
}

.account-title {
    font-size: 14px;
    line-height: 20px;
    color: #3f4156;
}

.account-balance {
    margin: 10px 0;
    font-size: 38px;
    line-height: 40px;
    color: #001d26;
}

.redPacketAccountText {
    margin-top: 20px;
    font-size: 14px;
    line-height: 20px;
    color: #858692;
}

.redPacketAccountBalance {
    color: #3f4156;
    font-size: 20px;
    line-height: 40px;
    font-weight: 500;
}

.account + .account {
    border-left: 1px solid #e9eaf2;
}

.mr20 {
    margin-right: 10px;
}

.ol {
    list-style: none;
    padding: 0;
    font-size: 14px;
    color: #cbccd1;
    line-height: 14px;

    li {
        margin: 10px 0;

        &:last-child {
            margin-bottom: 0;
        }
    }
}
</style>
