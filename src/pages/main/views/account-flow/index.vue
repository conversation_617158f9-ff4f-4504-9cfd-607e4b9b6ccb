<template>
    <div :class="$style.container">
        <account-info-card />

        <div :class="$style.flowPanel">
            <roo-tabs
                v-model="acctType"
                :class="$style.tabs"
                type="border"
                @active-change="handleParamsChange"
            >
                <roo-tab-pane
                    v-for="item in accountTypes"
                    :key="item.type"
                    :name="item.type"
                    :label="`${item.name}流水记录`"
                />
            </roo-tabs>
            <div :class="$style.panelContent">
                <div v-if="acctType !== 1009" :class="$style.filterRow">
                    <radio-button-group
                        v-model="dateType"
                        :class="$style.radioGroup"
                        @change="handleDateTypeChange"
                    >
                        <radio-button
                            v-lx-mc="{
                                bid: 'b_waimai_e_61kuzi1u_mc',
                                options: { cid },
                            }"
                            :val="1"
                        >
                            今天
                        </radio-button>
                        <radio-button
                            v-lx-mc="{
                                bid: 'b_waimai_e_bzr792z5_mc',
                                options: { cid },
                            }"
                            :val="2"
                        >
                            最近 7 日
                        </radio-button>
                        <radio-button
                            v-lx-mc="{
                                bid: 'b_waimai_e_urri4k3m_mc',
                                options: { cid },
                            }"
                            :val="3"
                        >
                            最近一月
                        </radio-button>
                        <radio-button
                            v-lx-mc="{
                                bid: 'b_waimai_e_43f7kp7v_mc',
                                options: { cid },
                            }"
                            :val="4"
                        >
                            自定义
                        </radio-button>
                    </radio-button-group>

                    <span :class="$style.label"> 时间 </span>
                    <roo-date-time-picker
                        v-model="startDate"
                        :class="$style.calendar"
                        :min-date="minStartDate"
                        :max-date="maxStartDate"
                        :disabled="dateType !== 4"
                        clearable
                        @change="customizeDateChange"
                    />
                    -
                    <roo-date-time-picker
                        v-model="endDate"
                        :class="$style.calendar"
                        :min-date="minEndDate"
                        :max-date="maxEndDate"
                        :disabled="dateType !== 4"
                        clearable
                        @change="customizeDateChange"
                    />
                    <a @click="goDown">下载账户流水</a>
                </div>
                <flow-table
                    :loading="listLoading"
                    :is-main-account-flow="acctType === 1008"
                    :show-comment="acctType === 1039 || acctType === 1063"
                    :list="list"
                    :show-flow-modal="showFlowModal"
                />

                <div
                    v-if="total > 0 && acctType !== 1009"
                    :class="$style.pagination"
                >
                    <roo-pagination
                        :total="total"
                        :page-size="pageSize"
                        :current-page="pageNo"
                        @current-change="handlePageChange"
                    />
                </div>
            </div>
        </div>

        <FlowModal v-if="show" :comment="comment" :flow-time="flowTime" :hidden1="hidden" :flow-no="flowNo" />

        <roo-loading :show="loading" fullscreen backdrop lock />
    </div>
</template>

<script>
import dayjs from 'dayjs';
import { mapState, mapMutations, mapActions } from 'vuex';
import { toast } from '@roo/roo-vue';

/* eslint-disable import/extensions, import/no-unresolved */
import toastError from '$lib/toast-error';

// import { getCookies } from '$lib/utils';

import RadioButton from '$components/radio-button';
import RadioButtonGroup from '$components/radio-button-group';
/* eslint-enable import/extensions, import/no-unresolved */

import AccountInfoCard from './account-info-card';

import FlowTable from './components/flow-table';
import FlowModal from './flow-modal';
/* eslint-disable import/extensions */
import { getCommonParams,navigateTo  } from '$lib/utils';

export default {
    name: 'AccountFlow',
    components: {
        FlowTable,
        AccountInfoCard,
        RadioButton,
        RadioButtonGroup,
        FlowModal,
    },
    data() {
        const today = new Date();

        return {
            acctType: 1008, // 总是有余额
            dateType: 2,

            startDate: today,
            endDate: today,
            show: false,
            flowNo: '',
            comment: '',
            flowTime: '',
        };
    },
    computed: {
        // 灵犀 cid
        cid() {
            const { meta } = this.$route;
            return meta && meta.cid;
        },

        // 从账户列表页自动跳转过来，必携带 wmPoiId / accountId 参数
        wmPoiId() {
            return this.$route.query.wmPoiId || null;
        },
        accountId() {
            return this.$route.query.accountId || null;
        },

        ...mapState('accountFlow/accountInfo', ['loading', 'showSpecialItem']),

        ...mapState('accountFlow/accountFlow', [
            // 'loading',
            'total',
            'pageNo',
            'pageSize',
            'list',
        ]),
        ...mapState('accountFlow/accountFlow', {
            listLoading: 'loading',
        }),

        accountTypes() {
            const state = this.$store.state.accountFlow.accountInfo;
            const arr = [
                {
                    type: 1008,
                    name: '余额',
                },
            ];
            if (state.showAdAccount) {
                arr.push({
                    type: 1028,
                    name: '推广费',
                });
            }
            if (state.showFoodSafetyRedPacketAccount) {
                arr.push({
                    type: 1063,
                    name: '售后红包',
                });
            }
            if (state.showRedPacketAccount && state.showSpecialItem) {
                arr.push({
                    type: 1039,
                    name: '推广红包',
                });
            }
            if (state.showDepositAccount && state.showSpecialItem) {
                arr.push({
                    type: 1009,
                    name: '保证金',
                });
            }
            if (state.showLogisticsAccount) {
                arr.push({
                    type: 999,
                    name: '配送费',
                });
            }
            return arr;
        },

        minStartDate() {
            if (this.endDate) {
                return new Date(this.endDate.getTime() - 29 * 24 * 3600 * 1000);
            }
            return null;
        },
        maxStartDate() {
            if (this.endDate) {
                return this.endDate;
            }
            return new Date();
        },

        minEndDate() {
            if (this.startDate) {
                return this.startDate;
            }
            return null;
        },
        maxEndDate() {
            if (this.startDate) {
                return new Date(
                    Math.min(
                        Date.now(),
                        this.startDate.getTime() + 29 * 24 * 3600 * 1000,
                    ),
                );
            }
            return new Date();
        },
    },
    mounted() {
        if (this.wmPoiId) {
            this.fetchAccountInfoByWmPoiId(this.wmPoiId).catch(toastError);
        } else if (this.accountId) {
            this.fetchAccountInfoByAccountId(this.accountId).catch(toastError);
        } else {
            toast.error('缺少参数');
            return;
        }
        this.last7Days();
        // 单店 or 多店 总是有主账户流水
        this.updateParams();
        this.handlePageChange(1);
        // 合规检查
        this.fetchShowSpecialItem(getCommonParams());
    },
    methods: {
        ...mapMutations('accountFlow/accountFlow', ['changeParams']),
        ...mapMutations('accountFlow/accountInfo', ['changeFlowModal']),
        ...mapActions('accountFlow/accountInfo', [
            'fetchAccountInfoByWmPoiId',
            'fetchAccountInfoByAccountId',
            'fetchShowSpecialItem',
        ]),
        ...mapActions('accountFlow/accountFlow', ['fetchAccountFlows']),
        showFlowModal(flow) {
            this.show = true;
            this.flowNo = flow.flowNo;
            this.comment = flow.comment;
            this.flowTime = flow.flowTime;
        },
        hidden() {
            this.show = false;
        },
        // 将参数同步到 store 中
        updateParams() {
            const params = {
                beginDate: dayjs(this.startDate).format('YYYY-MM-DD'),
                endDate: dayjs(this.endDate).format('YYYY-MM-DD'),
                acctType: this.acctType,
            };
            if (this.wmPoiId) {
                params.wmPoiId = this.wmPoiId;
            }
            if (this.accountId) {
                params.financeAccountId = this.accountId;
            }
            this.changeParams(params);
        },

        handleDateTypeChange(val) {
            switch (val) {
                case 1:
                    this.today();
                    this.handleParamsChange();
                    break;
                case 2:
                    this.last7Days();
                    this.handleParamsChange();
                    break;
                case 3:
                    this.lastMonth();
                    this.handleParamsChange();
                    break;
                default:
                    break;
            }
        },

        handlePageChange(pageNo) {
            this.fetchAccountFlows(pageNo).catch(toastError);
        },

        handleParamsChange() {
            this.updateParams();
            this.handlePageChange(1);
        },

        today() {
            this.startDate = new Date();
            this.endDate = new Date();
        },
        last7Days() {
            const end = new Date();
            const start = new Date(end.getTime() - 7 * 24 * 3600 * 1000);
            this.startDate = start;
            this.endDate = end;
        },
        lastMonth() {
            const end = new Date();
            const start = new Date(end.getTime() - 29 * 24 * 3600 * 1000);
            this.startDate = start;
            this.endDate = end;
        },
        // 自定义选择时间时，起始时间和结束时间都有值，才发送请求，并设埋点
        customizeDateChange() {
            if (this.startDate && this.endDate) {
                this.handleParamsChange();
            }
        },
        goDown(){
            navigateTo('/finance/pc/download?type=account-running', true);
            this.$router.push({
                name:'download',
                query: {
                    type: 'account-running'
                }
            })
        }
    },
};
</script>

<style lang="scss" module>
.container {
    display: flex;
    flex-direction: column;
    height: 100%;
    padding: 10px 0 0 0;

    :global(.roo-tabs-line .roo-tabs-container) {
        padding: 0 20px;
    }
    :global(.tab-item.active) {
        font-weight: bold;
    }
}

.flow-panel {
    flex: 1;
    border-radius: 2px;
    background: #fff;
    box-shadow: 0 0 6px 0 #f3f3f4;
    overflow: auto;

    :global(.roo-tabs-nav > li > a) {
        max-width: none;
    }
}

.tabs {
    height: 50px;

    :global(.nav > li > a) {
        padding: 15px 20px;
    }
}

.panel-content {
    padding: 20px 40px;
}

.filter-row {
    margin-bottom: 20px;
}

.radio-group {
    margin-right: 40px;
}

.label {
    font-size: 14px;
    line-height: 38px;
}

.calendar {
    display: inline-block;
    margin: 0 5px;
    line-height: 1;
    vertical-align: middle;
}

.pagination {
    margin-top: 20px;
    text-align: right;

    :global(.roo-pagination) {
        margin: 0;
    }
}
</style>
