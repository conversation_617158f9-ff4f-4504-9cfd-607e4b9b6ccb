<template>
    <div class="my-modal" @click="hiden">
        <div id="myContent" class="content">
            <div class="header">
                <h1>交易详情</h1>
                <roo-icon
                    name="close"
                    size="24px"
                    style="cursor:pointer"
                    @click="hidden1"
                />
            </div>
            <div class="divide-line"></div>
            <h1 class="title">{{ comment }}</h1>
            <moneyTable />
            <div class="divide-line"></div>
            <orderTable />
            <div class="divide-line"></div>
            <div>
                <div>结算账期：{{ settleBillComment }}</div>
                <div>创建时间：{{ flowTime }}</div>
                <div>流水单号：{{ flowNo1 }}</div>
            </div>
        </div>
    </div>
</template>

<script>
import { mapState, mapActions } from 'vuex';
import moneyTable from './components/money-table';
import orderTable from './components/order-table';

export default {
    components: {
        moneyTable,
        orderTable,
    },
    props: {
        // eslint-disable-next-line vue/require-default-prop
        hidden1: Function,
        flowNo: {
            type: String,
            default: '',
        },
        comment: {
            type: String,
            default: '',
        },
        flowTime: {
            type: String,
            default: '',
        },
    },
    computed: {
        ...mapState('accountFlow/accountInfo', ['pageCount', 'poiIncomeTotalAmount', 'platformPayTotalAmount', 'poiRealIncomeTotalAmount', 'orders', 'settleBillComment', 'flowNo1']),
        wmPoiId() {
            return this.$route.query.wmPoiId || null;
        },
    },
    mounted() {
        if (this.flowNo !== '') {
            this.fetchSmartInfo({
                flowNo: this.flowNo,
                wmPoiId: this.wmPoiId,
                pageNo: 1,
            });
        }
    },
    methods: {
        ...mapActions('accountFlow/accountInfo', ['fetchSmartInfo']),
        hiden(e) {
            const myDiv = document.getElementById('myContent');
            if (myDiv.contains(e.target)) {
                return;
            }
            this.hidden1();
        },
    },
};
</script>

<style lang="scss">
.my-modal {
    position: fixed;
    z-index: 9999;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: rgba($color: #000000, $alpha: 0.2);
    .header{
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    .divide-line{
        width: 100%;
        margin: 24px 0;
        border-top: 1px solid #E9EAF2;
    }
    .title {
        font-size: 20px;
        margin: 0;
    }
    .content {
        background: #fff;
        width: 650px;
        padding: 24px;
    }
}
</style>
