<template>
    <div class="money-info">
        <span class="real-income">实际转入金额&nbsp; ¥</span>
        <span class="real-income-money">{{ poiRealIncomeTotalAmount | formatCent }}</span>
        <span> &nbsp;= &nbsp;节省金额</span>
        <span>¥{{ poiIncomeTotalAmount | formatCent }}</span>
        <span> &nbsp;- &nbsp;平台代付金额</span>
        <span>¥{{ Math.abs(platformPayTotalAmount) | formatCent }}</span>
    </div>
</template>

<script>
import { mapState } from 'vuex';
// eslint-disable-next-line import/extensions
import { formatCent } from '$lib/filters';

export default {
    computed: {
        ...mapState('accountFlow/accountInfo', ['poiIncomeTotalAmount', 'platformPayTotalAmount', 'poiRealIncomeTotalAmount']),
    },
    filters: {
        formatCent,
    },
};
</script>

<style lang="scss">
.money-info{
    font-weight: 500;
    font-family: PingFangSC-Medium;
    font-size: 14px;
    color: #666666;
    margin-top: 16px;
    .real-income{
        font-family: PingFangSC-Medium;
        color: #333333;
    }
    .real-income-money{
        font-weight: 900;
        font-family: Arial-Black;
        font-size: 20px;
        color: #222222;
        line-height: 20px;
    }
}
</style>
