<template>
    <table :class="$style.table">
        <col style="width: 185px" />
        <col v-if="showComment" style="width: 160px" />
        <col v-else style="width: 270px" />
        <col style="width: 120px" />
        <col style="width: 120px" />
        <col style="width: 100px" />
        <col v-if="showComment" style="width: 240px" />
        <col v-if="isMainAccountFlow" style="width: 80px" />
        <col v-else-if="showComment" style="width: 180px" />
        <col v-else style="width: 200px" />
        <col v-if="showComment" style="width: 80px" />

        <thead>
            <tr>
                <th key="date">日期</th>
                <th key="type">类型</th>
                <th key="amount">金额 (元)</th>
                <th key="balance">现有余额</th>
                <th key="status">状态</th>
                <th v-if="showComment" key="comment">备注</th>
                <th v-if="isMainAccountFlow" key="action">操作</th>
                <th v-else key="no">交易号</th>
                <th v-if="showComment" key="action">操作</th>
            </tr>
        </thead>

        <tbody>
            <tr v-if="loading" key="loading">
                <td
                    :class="$style.loading"
                    :style="{ height: `${list.length * 40 || 150}px` }"
                    :colspan="columns"
                >
                    加载中
                </td>
            </tr>
            <tr v-else-if="list.length === 0" key="empty">
                <td :class="$style.empty" :colspan="columns">暂无数据</td>
            </tr>
            <template v-else>
                <!-- 流水数据可能来源于多个数据表后端不能提供全局唯一的 id，且没有排序操作，默认的以索引为 key 就地复用策略实际是高效的 -->
                <tr v-for="(flow, idx) in list" :key="idx">
                    <td>{{ flow.flowTime }}</td>
                    <td>
                        {{ flow.flowTypeComment }}
                        {{ flow.flowTypeName }}
                    </td>
                    <td :class="getAmountClass(flow.flowAmount)">
                        <money :amount="flow.flowAmount" :show-unit="false" />
                    </td>
                    <td>
                        <money :amount="flow.balanceAmount" />
                    </td>
                    <td>
                        {{ flow.flowStatusName || flow.flowStateName }}
                    </td>
                    <td v-if="showComment">
                        {{ flow.comment }}
                    </td>
                    <td v-if="isMainAccountFlow">
                        <!-- 合并账单或者扫码购走此逻辑 -->
                        <a
                            v-if="hasDetail(flow) && (flow.flowType == 2)"
                            :href="getDetailHref(flow)"
                        >
                            详情
                        </a>
                        <router-link
                            v-lx-mc="getLXConfig(flow)"
                            v-else-if="hasDetail(flow) && (flow.flowType == 6 || flow.flowType == -10 || flow.flowType == 11)"
                            :to="getDetailLink(flow)"
                        >
                            详情
                        </router-link>
                        <span v-else> -- </span>
                    </td>
                    <td v-else>
                        {{ flow.flowNo }}
                    </td>
                    <td v-if="showComment">
                        <a v-if="showComment && flow.subFlowType === 1" href="#" @click="showModal(flow)">详情</a>
                    </td>
                </tr>
            </template>
        </tbody>
    </table>
</template>

<script>
/* eslint-disable import/extensions, import/no-unresolved */
import Money from '$components/money';
import { getQueryString } from '$lib/utils';
/* eslint-enable import/extensions, import/no-unresolved */

export default {
    name: 'FlowTable',
    components: {
        Money,
    },
    props: {
        cid: {
            type: String,
            default: '',
        },
        loading: {
            type: Boolean,
            default: false,
        },
        isMainAccountFlow: {
            type: Boolean,
            default: false,
        },
        showComment: {
            type: Boolean,
            default: false,
        },
        list: {
            type: Array,
            default() {
                return [];
            },
        },
        showFlowModal: {
            type: Function,
            default() {
                return () => {};
            },
        },
    },
    computed: {
        columns() {
            let cols = 6;
            if (this.showComment) {
                cols += 1;
            }
            return cols;
        },
    },
    methods: {
        showModal(flow) {
            this.showFlowModal(flow);
        },
        getAmountClass(str) {
            const { expend, income } = this.$style;
            return parseFloat(str) < 0 ? expend : income;
        },
        // 有详情页
        hasDetail(flow) {
            return [2, 6, -10, 11].indexOf(flow.flowType) > -1;
        },

        getLXConfig(flow) {
            // 余额提现
            if (flow.flowType === 2) {
                return {
                    bid: 'b_waimai_e_fl428fkm_mc',
                    options: { cid: this.cid },
                };
            }
            // 合并账单
            if (flow.flowType === 6 || flow.flowType === -10) {
                return {
                    bid: 'b_waimai_e_z6pe13vo_mc',
                    options: { cid: this.cid },
                };
            }
            return null;
        },
        getDetailHref(flow) {
            const { wmPoiId } = this.$route.query;
            const { outId, wmPoiId: partnerId } = flow;
            // 多店提现详情不需要 wmPoiId
            const queryString = getQueryString({ wmPoiId, outId, partnerId });
            // 余额提现 withdrawDetail
            return `/finance/newfe#/finance/exportDetail?${queryString}`;
        },
        getDetailLink(flow) {
            const { wmPoiId } = this.$route.query;
            const { settleBillId, wmPoiId: partnerId } = flow;
            // 账单 billDetail
            if (flow.flowType === 6) {
                return {
                    name: 'bill-detail',
                    query: {
                        wmPoiId: wmPoiId || partnerId, // 多店
                        settleBillId,
                    },
                };
            }
            // 合并入账
            if (flow.flowType === -10) {
                return {
                    name: 'mutil-bill-detail',
                    query: {
                        outId: flow.outId,
                    },
                };
            }
            // 扫码购账单
            if (flow.flowType === 11) {
                return {
                    name: 'scan-qr-code-bill',
                    query: {
                        wmPoiId,
                        dailyBillId: settleBillId,
                    },
                };
            }
            return null;
        },
    },
};
</script>

<style lang="scss" module>
.table {
    width: 100%;
    table-layout: fixed;
    border-collapse: collapse;
    border: 1px solid #e9eaf2;
    font-size: 14px;
    line-height: 19px;
    color: #3f4156;

    thead {
        background: #f7f8fa;
    }

    tr {
        height: 40px;
        border: 1px solid #e9eaf2;
    }

    th {
        font-weight: normal;
        color: #858692;
    }

    th,
    td {
        padding: 0 20px;

        &:nth-child(3),
        &:nth-child(4) {
            text-align: right;
        }

        &:nth-child(4) {
            padding-right: 30px;
        }
    }

    tbody tr:hover {
        background: #e9eaf2;
    }

    .empty {
        height: 150px;
        &:hover {
            background: #fff;
        }
    }
}

.empty,
.loading {
    text-align: center;
}

.income {
    color: #63d29d;
}
.expend {
    color: #f76c6c;
}
</style>
