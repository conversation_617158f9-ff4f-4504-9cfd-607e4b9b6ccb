<template>
    <div class="order-table">
        <table>
            <colgroup>
                <col style="width: '20%'" />
                <col style="width: '20%'" />
                <col style="width: '20%'" />
                <col style="width: '20%'" />
                <col style="width: '20%'" />
            </colgroup>
            <thead>
                <tr>
                    <th class="font14 color333">日期&订单编号</th>
                    <th class="font14 color333">交易类型</th>
                    <th class="font14 color666">预算金额</th>
                    <th class="font14 color666">实际优惠</th>
                    <th class="font14 color222">商家节省金额</th>
                    <th class="font14 color222">平台代付金额</th>
                </tr>
            </thead>
            <tbody>
                <tr v-for="item in orders" :key="item.bizId">
                    <td class="font12 color333">{{ dateStr(item.date * 1000) }}&nbsp;#{{ item.bizId }}</td>
                    <td class="font12 color333">{{ item.type === 1 ? '外卖订单' : '退款订单' }}</td>
                    <td class="font12 color666">
                        <span v-if="item.configActivityAmount !== 0 && item.configActivityAmount != null">
                            {{ item.configActivityAmount | formatMoney(false) }}
                        </span>
                    </td>
                    <td class="font12 color666">
                        <span v-if="item.realActivityAmount !== 0 && item.realActivityAmount != null">
                            {{ item.realActivityAmount | formatMoney(false) }}
                        </span>
                    </td>
                    <td :class="(+item.poiIncomeAmount>0) ? 'plus' : 'minus'">
                        <span v-if="item.poiIncomeAmount !== 0 && item.poiIncomeAmount != null">
                            {{ item.poiIncomeAmount | formatMoney(true) }}
                        </span>
                    </td>
                    <td :class="(+item.platformPayAmount>0) ? 'plus' : 'minus'">
                        <span v-if="item.platformPayAmount !== 0 && item.platformPayAmount != null">
                            {{ item.platformPayAmount | formatMoney(true) }}
                        </span>
                    </td>
                </tr>
                <tr>
                    <td colspan="6">
                        <roo-pagination
                            :total="total"
                            :page-size="smartPageSize"
                            :current-page="pageNo"
                            @current-change="handlePageChange"
                        />
                    </td>
                </tr>
                <tr>
                    <td class="font14 color333" style="text-align: left; padding-left: 20px;">合计</td>
                    <td colspan="3"></td>
                    <td class="font14 color333">
                        <span v-if="poiIncomeTotalAmount !== 0 && poiIncomeTotalAmount != null">
                            {{ poiIncomeTotalAmount | formatMoney(true) }}
                        </span>
                    </td>
                    <td class="font14 color333">
                        <span v-if="platformPayTotalAmount !== 0 && platformPayTotalAmount != null">
                            {{ platformPayTotalAmount | formatMoney(true) }}
                        </span>
                    </td>
                </tr>
            </tbody>
        </table>
    </div>
</template>

<script>
import { mapState, mapMutations, mapActions } from 'vuex';
// eslint-disable-next-line import/extensions
import { formatCent, formatMoney } from '$lib/filters';
import dayjs from 'dayjs';

export default {
    filters: {
        formatCent,
        formatMoney,
    },
    computed: {
        ...mapState('accountFlow/accountInfo', ['total', 'smartPageSize', 'pageCount', 'orders', 'flowNo', 'wmPoiId', 'pageNo', 'poiIncomeTotalAmount', 'platformPayTotalAmount']),
    },
    methods: {
        ...mapMutations('accountFlow/accountInfo', ['changeParams']),
        ...mapActions('accountFlow/accountInfo', ['fetchSmartInfo']),
        nextPage() {
            // flowNo, wmPoiId, pageNo, pageSize
            this.fetchSmartInfo({
                flowNo: this.flowNo,
                wmPoiId: this.wmPoiId,
                pageNo: this.pageNo + 1,
            });
        },
        handlePageChange(pageNo) {
            this.fetchSmartInfo({
                flowNo: this.flowNo,
                wmPoiId: this.wmPoiId,
                pageNo,
            });
        },
        dateStr(date) {
            return dayjs(date).format('YYYY-MM-DD');
        },
    },
};
</script>

<style lang="scss">
table {
    width: 100%;
    tr {
        height: 40px;
    }
    th,
    td {
        font-weight: 500;
        text-align: center;
    }
}
.plus {
    color: #00bf7f;
}

.minus {
    color: #ff192d;
}
.font14 {
    font-size: 14px;
}
.font12 {
    font-size: 12px;
}
.color666 {
    color: #666666;
}
.color333 {
    color: #333333;
}
.color222 {
    color: #222222;
}
</style>
