<template>
    <div :class="$style.container">
        <roo-alert
            v-if="displayMessage"
            key="message"
            type="warning"
            dismissable
        >
            {{ message }}
        </roo-alert>
        <div :class="$style.header">
            <div :class="$style.account">
                <div :class="[$style.row, $style.accountHeader]">
                    <div>
                        <img
                            :class="$style.avatar"
                            :src="avatar"
                            alt="avatar"
                        />
                        <h4 :class="$style.accountName">
                            {{ accountName }}
                        </h4>
                    </div>
                    <!-- <router-link
                        v-lx-mc="{
                            bid: 'b_waimai_e_ja4d1ot9_mc',
                            options: {
                                cid,
                            },
                        }"
                        :class="[$style.accountLink, $style.link]"
                        :to="{ name: 'account-info' }"
                    >
                        账户信息
                    </router-link> -->
                    <roo-button
                        v-lx-mc="{
                            bid: 'b_waimai_e_ja4d1ot9_mc',
                            options: {
                                cid,
                            },
                        }"
                        type="hollow"
                        size="mini"
                        @click="navigateToAccountInfo"
                    >
                        账户信息
                    </roo-button>
                </div>
                <div :class="$style.row">
                    <div>
                        <div :class="$style.grayText">
                            可提现余额
                            <roo-icon name="question">
                                <roo-tooltip
                                    trigger="hover"
                                    placement="right"
                                >
                                    以下情况无法发起手动提现操作：
                                    <ol :class="$style.ol">
                                        <li>1. 当前可提现余额小于最低提现金额</li>
                                        <li>2. 当前门店属于多门店合并打款</li>
                                        <li>3. 可提现余额小于等于0</li>
                                    </ol>
                                </roo-tooltip>
                            </roo-icon>
                        </div>
                        <div :class="$style.money">
                            <money :amount="balance" />
                        </div>
                    </div>
                    <roo-button
                        v-if="frozen"
                        :class="$style.withdrawButton"
                        type="dashed"
                        size="small"
                    >
                        账户已冻结
                        <roo-icon name="question" />
                        <roo-tooltip trigger="hover">
                            <div>很抱歉，您当前门店的账户已冻结，无法提现。</div>
                            <div>如有问题请联系业务经理处理，相关款项会在账户解冻后的下个账期发起提现。</div>
                            <div>谢谢!</div>
                        </roo-tooltip>
                    </roo-button>
                    <roo-button
                        v-else-if="showWithdraw"
                        :class="$style.withdrawButton"
                        size="small"
                        @click="navigateToWithdraw"
                    >
                        提现
                    </roo-button>
                </div>
                <hr :class="$style.separator" />
                <div :class="$style.row">
                    <div>
                        <span v-if="showSpecialItem">
                            推广费 / 保证金账户
                        </span>
                        <span v-else>
                            推广费
                        </span>
                        <span :class="$style.tag">充值</span>
                    </div>
                    <roo-button
                        v-lx-mc="{
                            bid: 'b_waimai_e_ak364lzj_mc',
                            options: {
                                cid,
                            },
                        }"
                        size="small"
                        type="hollow"
                        @click="navigateToAccountFlow"
                    >
                        进入账户
                    </roo-button>
                </div>
            </div>
            <div :class="$style.recentFlow">
                <div :class="[$style.row, $style.flowTitle]">
                    <h4>最近余额流水动态</h4>
                    <router-link
                        v-lx-mc="{
                            bid: 'b_waimai_e_xirr2idz_mc',
                            options: {
                                cid,
                            },
                        }"
                        :class="$style.link"
                        :to="{ name: 'account-list' }"
                    >
                        查看余额流水
                    </router-link>
                </div>
                <div :class="$style.flowContainer">
                    <table :class="$style.flowTable">
                        <col style="width: 190px;" />
                        <col style="width: 80px;" />
                        <col />
                        <tbody>
                            <tr
                                v-for="flow in flowList"
                                :key="`${flow.flowType}-${flow.outId}`"
                            >
                                <td>{{ flow.flowTime }}</td>
                                <td>{{ flow.flowTypeName }}</td>
                                <td>
                                    {{ flow.flowAmount | formatNumberThousand }} 元
                                    <span :class="$style.grayText">
                                        / 余额 {{ flow.balanceAmount | formatNumberThousand }} 元
                                    </span>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <div :class="$style.card">
            <div :class="$style.row">
                <h3>账单信息</h3>
                <router-link
                    v-lx-mc="{
                        bid: 'b_waimai_e_b9mdm9ca_mc',
                        options: {
                            cid,
                        },
                    }"
                    :class="$style.link"
                    :to="{ name: 'settle-bill-list' }"
                >
                    全部账单
                </router-link>
            </div>
            <div :class="$style.section">
                <div :class="$style.sectionHeader">
                    待结算账单
                </div>
                <settle-bill-table
                    :cid="cid"
                    :view-bid="'b_waimai_e_khv46m6q_mc'"
                    :settled-bid="'b_waimai_e_tsyizi8l_mc'"
                    :download-bid="'b_waimai_e_ewhhy8yi_mc'"
                    :wm-poi-id="wmPoiId"
                    :list="unSettleBillList"
                />
            </div>
            <div :class="$style.section">
                <div :class="$style.sectionHeader">
                    已结算账单
                </div>
                <settle-bill-table
                    :cid="cid"
                    :view-bid="'b_waimai_e_jh8yfi0d_mc'"
                    :settled-bid="'b_waimai_e_tsyizi8l_mc'"
                    :download-bid="'b_waimai_e_6jfdg1mk_mc'"
                    :wm-poi-id="wmPoiId"
                    :list="settleBillList"
                />
            </div>
        </div>

        <roo-loading
            :show="loading"
            fullscreen
            backdrop
            lock
        />
    </div>
</template>

<script>
// 对账首页
import { mapState, mapActions } from 'vuex';

/* eslint-disable import/extensions, import/no-unresolved */
import Money from '$components/money';
import SettleBillTable from '$components/settle-bill-table';

import toastError from '$lib/toast-error';
import {
    noop, formatNumberThousand, getCookieValue, getQueryString, getCommonParams,
} from '$lib/utils';
/* eslint-enable import/extensions, import/no-unresolved */

export default {
    name: 'Home',
    components: {
        Money,
        SettleBillTable,
    },
    filters: {
        formatNumberThousand,
    },
    computed: {
        // 灵犀 cid
        cid() {
            const { meta } = this.$route;
            return meta && meta.cid;
        },

        // 首页只有选中单店时可进入，wmPoiId 取 cookie 值
        wmPoiId() {
            return getCookieValue('wmPoiId');
        },

        ...mapState('home', [
            'displayMessage',
            'message',
        ]),
        ...mapState('home/accountInfo', [
            'avatar',
            'accountName',
            'balance',
            'frozen',
            'showWithdraw',
        ]),
        ...mapState('home/recentFlows', [
            'flowList',
        ]),
        ...mapState('home/settleBillList', [
            'settleBillList',
            'unSettleBillList',
        ]),
        ...mapState('accountFlow/accountInfo', ['showSpecialItem']),

        loading() {
            const state = this.$store.state.home;
            return state.accountInfo.loading
                || state.recentFlows.loading
                || state.settleBillList.loading;
        },
    },
    mounted() {
        // 首页 关于提现顺延的 tip 信息，请求失败不影响业务，不做提示
        this.fetchDisplayMsg().catch(noop);

        this.fetchAccountBaseInfo(getCommonParams()).catch(toastError);
        this.fetchRecentFlows().catch(toastError);
        this.fetchSettleBillList().catch(toastError);
        // 合规检查
        this.fetchShowSpecialItem(getCommonParams());
    },
    methods: {
        ...mapActions('home', ['fetchDisplayMsg']),
        ...mapActions('home/accountInfo', ['fetchAccountBaseInfo']),
        ...mapActions('home/recentFlows', ['fetchRecentFlows']),
        ...mapActions('home/settleBillList', ['fetchSettleBillList']),
        ...mapActions('accountFlow/accountInfo', ['fetchShowSpecialItem']),
        navigateToWithdraw() {
            const query = {
                wmPoiId: this.wmPoiId,
                acctType: 0,
            };
            window.location = `/finance/newfe#/finance/balanceWithdraw?${getQueryString(query)}`;
        },
        navigateToAccountFlow() {
            this.$router.push({
                name: 'account-list', // 当前为单店，账户列表自动跳转到账户流水
            });
        },
        navigateToAccountInfo() {
            const { wmPoiId } = this.$route.query;
            const query = wmPoiId ? { wmPoiId } : { wmPoiId: getCookieValue('wmPoiId') };
            const queryString = getQueryString(query);
            window.location = `/finance/newfe#/finance/accountDetails?${queryString}`;
        },
    },
};
</script>

<style lang="scss" module>
.container {
    padding-top: 10px;

    h3, h4 {
        margin: 0;
        color: #2A2A2A;
        font-weight: bold;
    }
    h3 {
        font-size: 20px;
    }
    h4 {
        font-size: 14px;
    }

    :global(.roo-alert) {
        margin: 0 0 20px 0;
    }
    :global(.roo-icon) {
        height: auto;
    }
}

.header {
    display: flex;
    margin-bottom: 10px;
    border-radius: 2px;
    background: #FFF;
    box-shadow: 0 0 6px 0 #F3F3F4;
}

.account {
    position: relative;
    flex: 4;
    padding: 20px 40px;
}

.row {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.account-header {
    transform: translate3d(0, -30px, 0);
}

.avatar {
    display: inline-block;
    width: 50px;
    height: 50px;
    border-radius: 3px;
    margin-right: 10px;
    vertical-align: bottom;
}

.account-name {
    display: inline;
}

.money {
    margin-top: 8px;
    font-size: 24px;
}

.account-link, .withdraw-button {
    align-self: flex-end;
}

.separator {
    height: 0;
    border-top: none;
    border-bottom: 1px dashed #E9EAF2;
    margin: 20px 0;
}

.tag {
    margin-left: 10px;
    padding: 2px 4px;
    border-radius: 3px;
    background: #FFE7E7;
    color: #F76C6C;
    font-size: 12px;
}

.recent-flow {
    flex: 6;
    padding: 20px 40px;
    border-left: 1px solid #E9EAF2;
}

.flow-title {
    margin-bottom: 30px;
}

.flow-container {
    display: block;
    // height: 120px;
    overflow: auto;
}

.flow-table {
    width: 100%;
    table-layout: fixed;
    border-collapse: collapse;
    color: #3F4156;

    th, td {
        height: 38px;
        text-align: left;
        vertical-align: center;
    }

    td:last-child {
        text-align: right;
    }
}

.card {
    padding: 40px;
    border-radius: 2px;
    background: #FFF;
    box-shadow: 0 0 6px 0 #F3F3F4;
}

.section {
    margin-top: 30px;
}

.section-header {
    position: relative;
    margin: 20px 0;
    padding-left: 14px;
    // border-left: 4px solid #A2A4B3;
    color: #3F4156;
    line-height: 1;

    &:before {
        content: '';
        position: absolute;
        left: 0;
        top: 1px;
        display: block;
        width: 4px;
        height: 12px;
        border-radius: 1px;
        background: #A2A4B3;
    }
}

.gray-text {
    color: #858692;
}

.link:link,
.link:visited {
    color: #585A6E;
    text-decoration: none;
}
.link:hover,
.link:active {
    color: #3F4156;
    text-decoration: none;
}

.ol {
    list-style: none;
    padding: 0;
    font-size: 14px;
    color: #CBCCD1;
    line-height: 14px;

    li {
        margin: 10px 0;

        &:last-child {
            margin-bottom: 0;
        }
    }
}
</style>
