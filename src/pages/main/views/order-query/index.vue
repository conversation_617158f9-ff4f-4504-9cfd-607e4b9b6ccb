<template>
    <div :class="$style.container">
        <h3 :class="$style.title">订单查询</h3>
        <div :class="$style.query">
            <span :class="$style.label">
                订单展示ID
            </span>
            <div :class="$style.input">
                <roo-input
                    :value="orderViewId"
                    :icon="orderViewId ? 'times-circle' : null"
                    placeholder="搜索内容"
                    @icon-click="handleIconClick"
                    @keyup="handleInputKeyUp"
                >
                    <template slot="append">
                        <roo-button
                            icon="search"
                            @click="handleQueryClick"
                        />
                    </template>
                </roo-input>
            </div>
        </div>
        <div :class="$style.content">
            <span :class="$style.name">{{ poiInfo }}</span>
            <table :class="$style.table">
                <col style="width: 150px;" />
                <col style="width: 180px;" />
                <col />
                <col style="width: 110px;" />
                <col style="width: 120px;" />
                <col style="width: 70px;" />
                <thead>
                    <tr>
                        <th>交易类型</th>
                        <th>交易时间</th>
                        <th>费用明细</th>
                        <th>结算金额(元)</th>
                        <th>结算日期</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody v-if="billList && billList.length">
                    <tr
                        v-for="billItem in billList"
                        :key="billItem.billChargeId"
                    >
                        <td>{{ billItem.billChargeTypeName }}{{ billItem.chargeTypeName? `(${billItem.chargeTypeName})`: '' }}</td>
                        <td>
                            {{ billItem.outCreateTime }}{{ billItem.chargeTypeCode === 2000 ? `#${billItem.poiOrderPushDayseq}` : '' }}
                        </td>
                        <td>
                            <div
                                v-for="(item, index) in billItem.wmPoiBillChargeFeeDynamicVoList"
                                :key="item.billFeeTypeCode"
                                :class="$style.detail"
                            >
                                <span
                                    v-if="index || item.direction === '-'"
                                    :class="$style.operator"
                                >{{ item.direction }}</span>
                                <div>
                                    <span :class="$style.label">{{ item.billFeeTypeName }}</span>
                                    <span :class="$style.money"><span>￥</span>{{ item.feeAmount | formatMoney }}</span>
                                </div>
                            </div>
                        </td>
                        <td>
                            <div :class="$style.settleLabel">{{ billItem.settleStatus }}</div>
                            <div
                                v-if="billItem.chargeAmount > 0"
                                :class="[$style.settleMoney, $style.green]"
                            >
                                <span>+￥</span>{{ billItem.chargeAmount | formatMoney }}
                            </div>
                            <div
                                v-else
                                :class="[$style.settleMoney, $style.red]"
                            >
                                <span>-￥</span>{{ billItem.chargeAmount | formatMoney }}
                            </div>
                        </td>
                        <td :class="$style.link">
                            <router-link
                                v-lx-mc="{
                                    bid: 'b_waimai_e_v5lk4rcb_mc',
                                    options: { cid },
                                }"
                                :to="{
                                    name: 'daily-bill',
                                    query: {
                                        wmPoiId: wmPoiId,
                                        dailyBillDate: billItem.dailyBillDate
                                    }
                                }"
                            >
                                {{ billItem.dailyBillDate }}
                            </router-link>
                        </td>
                        <td>
                            <span
                                v-if="billItem.webDetailType === -1"
                            >
                                --
                            </span>
                            <a
                                v-lx-mc="{
                                    bid: 'b_waimai_e_gby9uhhc_mc',
                                    options: { cid },
                                }"
                                v-else
                                href="#noop"
                                @click.prevent="handleDetailClick(billItem)"
                            >
                                详情
                            </a>
                        </td>
                    </tr>
                </tbody>
            </table>
            <div
                v-if="billList && !billList.length"
                :class="$style.noDate"
            >
                <img src="./image/no-data.png" />
                <div>暂无数据</div>
            </div>
        </div>
        <roo-loading
            :show="loading"
            fullscreen
            backdrop
            lock
        />
        <!-- <detail-modal
            v-model="displayDetailModal"
            :type="detail && detail.billChargeType"
            :title="detail && detail.title"
            :template="detail && detail.webTemplate"
            :query="getQuery(detail)"
            @hidden="detail = null"
        /> -->
        <roo-modal
            v-model="displayDetailModal"
            :size="'large'"
            :title="detail && detail.title"
        >
            <iframe
                v-if="detail"
                :src="getOrderDetailPageUrl(detail)"
                :height="iframeHeight"
                width="590"
                frameborder="0"
            >
            </iframe>
            <div v-else></div>
        </roo-modal>
    </div>
</template>

<script>
import dayjs from 'dayjs';
import { mapState, mapActions } from 'vuex';
import { toast } from '@roo/roo-vue';
import DetailModal from '../daily-bill/detail-modal';

/* eslint-disable import/extensions, import/no-unresolved */
import { formatNumberThousand, getQueryString, navigateTo } from '$lib/utils';
import toastError from '$lib/toast-error';
/* eslint-enable import/extensions, import/no-unresolved */

export default {
    name: 'OrderQuery',
    filters: {
        formatMoney(money) {
            let result = '';
            if (money > 0) {
                result = `${formatNumberThousand((money / 100).toFixed(2))}`;
            } else if (money === 0) {
                result = '0.00';
            } else {
                result = `${formatNumberThousand((-money / 100).toFixed(2))}`;
            }
            return result;
        },
    },
    components: {
        DetailModal,
    },
    data() {
        return {
            displayDetailModal: false,
            detail: null,
            iframeHeight: 500,
        };
    },
    mounted() {
        // iframe 高度
        this.iframeHeight = (window.innerHeight - 80) * 0.9;
    },
    computed: {
        // 灵犀 cid
        cid() {
            const { meta } = this.$route;
            return meta && meta.cid;
        },
        ...mapState('orderQuery', [
            'loading',
            'poiInfo',
            'billList',
            'wmPoiId',
            'orderViewId',
        ]),
    },
    methods: {
        ...mapActions('orderQuery', [
            'fetchOrderInfo',
            'changeOrderViewId',
        ]),

        handleQueryClick() {
            if (!this.orderViewId) {
                toast.error('请输入订单ID');
                return;
            }
            this.fetchOrderInfo({
                orderViewId: this.orderViewId,
            }).catch(toastError);
        },
        handleInputKeyUp(e) {
            const data = e.target.value.trim();
            const matchArr = data.match(/\d*/);
            if (!matchArr) {
                e.target.value = '';
                this.changeOrderViewId('');
            } else {
                // eslint-disable-next-line prefer-destructuring
                e.target.value = matchArr[0];
                this.changeOrderViewId(matchArr[0]);
            }
        },
        handleIconClick() {
            this.changeOrderViewId('');
        },
        handleDetailClick(item) {
            if (item) {
                /**
                 * 判断web明细类型：webDetailType
                 * -1：无操作
                 * 1: 自链接
                 * 2: 外链接
                 * 3: 弹窗
                 */
                if (item.webDetailType === 1) { // 内部链接... 实际上只有 order-detail
                    this.goToOrderDetailPage(item);
                } else if (item.webDetailType === 2) { // 外部链接
                    // KA 提示逻辑，实际上订单类只会显示订单卡片，此处保持一致
                    if (item.specialType === 1) {
                        if (item.isKa) {
                            toast.warn('活动由总部提报，查询明细请联系总部');
                        } else {
                            // 跳转
                            const path = item.webUrl;
                            navigateTo(path);
                        }
                    } else if (item.webUrl) { // 通用外部链接
                        navigateTo(item.webUrl);
                    } else { // 内部链接兜底
                        this.goToOrderDetailPage(item);
                    }
                } else {
                    this.displayDetailModal = true;
                    this.detail = item;
                }
            }
        },
        goToOrderDetailPage(item) {
            navigateTo(this.getOrderDetailPageUrl(item));
        },
        getOrderDetailPageUrl(item) {
            const query = this.getQuery(item);
            const queryString = getQueryString(query);
            return `/finance/newfe#/finance/orderDetail?${queryString}`;
        },
        getQuery(item) {
            if (item) {
                return {
                    chargeTypeCode: item.chargeTypeCode,
                    billChargeId: item.billChargeId,
                    wmOrderViewId: item.wmOrderViewId,
                    dailyBillDate: dayjs(item.dailyBillDate).format('YYYY/MM/DD'),
                    wmPoiId: this.wmPoiId || null,
                };
            }
            return {};
        },
    },
};

</script>

<style lang="scss" module>
.container {
    background: #FFFFFF;
    box-shadow: 0 0 6px 0 #E9EAF2;
    border-radius: 2px;
    padding: 40px;

    .title {
        font-size: 20px;
        color: #3F4156;
        margin-top: 0;
    }

    .query {
        padding-bottom: 29.5px;
        border-bottom: 1px solid #E9EAF2;
        margin-bottom: 29.5px;

        .label {
            font-size: 14px;
            color: #3F4156;
            margin-right: 12px;
        }

        .input {
            display: inline-block;
            width: 260px;
        }
    }

    .content {
        .name {
            display: inline-block;
            margin-bottom: 20px;
            font-size: 14px;
            color: #3F4156;
        }

        .no-date {
            margin-top: 88px;
            text-align: center;

            div {
                font-size: 14px;
                color: #A2A4B3;
                margin-top: 30px;
            }
        }

        .table {
            min-width: 100%;
            table-layout: fixed;
            border-collapse: collapse;
            border: 1px solid #E9EAF2;
            font-size: 14px;
            color: #3F4156;

            thead {
                background: #F7F8FA;
            }

            tr {
                /*height: 40px;*/
                border: 1px solid #E9EAF2;
            }

            th {
                font-weight: normal;
                color: #858692;
                height: 50px;
            }

            td {
                height: 110px;
            }

            th, td {
                padding: 10px;

                &:nth-child(1) {
                    padding-left: 30px;
                }

                &:nth-child(4),
                &:nth-child(5) {
                    padding-right: 20px;
                    text-align: right;
                }
            }

            .link a {
                text-decoration: underline !important;
            }

            .link a:hover {
                text-decoration: underline !important;
            }
        }

        .detail {
            display: inline-block;
            height: 65px;
            padding-top: 10px;

            div {
                display: inline-block;
                margin-right: 12px;
                /*position: relative;*/
                /*top: 14px;*/
            }

            .label {
                font-size: 14px;
                color: #A2A4B3;
            }

            .money {
                font-size: 16px;
                color: #3F4156;
                display: block;
                margin-top: 5px;

                span {
                    font-size: 14px;
                }
            }

            .operator {
                margin-right: 12px;
                position: relative;
                top: -17px;
            }
        }

        .settle-label {
            font-size: 14px;
            color: #3F4156;
        }

        .settle-money {
            font-size: 18px;

            span {
                font-size: 14px;
            }
        }

        .green {
            color: #24C17F;
        }

        .red {
            color: #F76C6C;
        }
    }
}
</style>
