<template>
    <div :class="$style.withdrawContainer">
        <roo-breadcrumb
            :class="$style.breadcrumb"
        >
            <roo-breadcrumb-item>我的账户</roo-breadcrumb-item>
            <roo-breadcrumb-item>余额提现</roo-breadcrumb-item>
        </roo-breadcrumb>
        <div :class="$style.container">
            <div :class="$style.header">
                余额提现到{{ accountType }}
            </div>
            <div :class="$style.content">
                <roo-form
                    :label-width="3"
                    style="width: 600px;"
                >
                    <roo-form-item
                        :label="accountType"
                    >
                        <roo-input
                            :value="accoutName"
                            :disabled="withdrawDetail.balanceOutStatus"
                            readonly
                        />
                    </roo-form-item>
                    <roo-form-item
                        :rules="rules"
                        label="提现金额"
                        prop="amount"
                    >
                        <div :class="$style.amount">
                            ￥{{ withdrawDetail.balance | formatCent | formatNumberThousand }}
                        </div>
                        <div :class="$style.yellowTip">
                            默认提现全部余额，无法修改，敬请谅解
                        </div>
                    </roo-form-item>
                    <roo-form-item
                        :class="$style.labelNoPadding"
                        label="到账时间"
                    >
                        预计3个工作日内到账
                    </roo-form-item>
                    <roo-form-item
                        keep-label-width
                        prop="button"
                    >
                        <roo-button
                            :disabled="!canWithdraw"
                            @click="displayModal = true"
                        >
                            确认提现
                        </roo-button>
                        <roo-button
                            type="hollow"
                            @click="handleCancel"
                        >
                            取消
                        </roo-button>
                    </roo-form-item>
                </roo-form>
            </div>
        </div>
        <roo-modal
            ref="modal"
            v-model="displayModal"
            :backdrop="backdrop"
            size="middle"
            effect="zoom"
        >
            <div slot="header">
                <button
                    :class="$style.close"
                    :disabled="modalDisabled"
                    type="button"
                    @click="displayModal=false"
                >
                    <i class="roo-icon roo-icon-close"></i>
                </button>
                <h5 :class="$style.modalTitle">
                    提现手机验证
                </h5>
            </div>

            <p>为了账户安全，我们将发送验证码至手机 {{ withdrawDetail.accountMobile | formatPhone }}，如需更换绑定手机，请联系业务经理</p>
            <div :class="$style.flexBox">
                <roo-input
                    v-model="verification"
                    :class="$style.verification"
                />
                <roo-button
                    :disabled="pending || tick > 0"
                    :class="$style.getVerify"
                    type="hollow"
                    @click="handleGetVerify"
                >
                    {{ sendButtonText }}
                </roo-button>
            </div>
            <div :class="$style.alignCenter">
                <roo-button
                    :disabled="!verification"
                    :loading="withdrawing"
                    @click="handleConfirmWithdraw"
                >
                    确认提现
                </roo-button>
            </div>
        </roo-modal>
        <roo-loading
            :show="loading"
            fullscreen
            backdrop
            lock
        />
    </div>
</template>
<script>
import { mapState, mapActions } from 'vuex';
/* eslint-disable import/no-unresolved, import/extensions */
import request from '$src/utils/request';
import { alert, toast } from '@roo/roo-vue';

/* eslint-disable import/extensions, import/no-unresolved */
import * as utils from '$lib/utils.js';
import toastError from '$lib/toast-error';
/* eslint-enable import/extensions, import/no-unresolved */

export default {
    name: 'Withdraw',
    filters: {
        formatNumberThousand: utils.formatNumberThousand,
        formatCent: function formatCent(cent) {
            return parseFloat(cent).toFixed(2);
        },
        formatPhone: function formatPhone(phone) {
            const a = phone.slice(0, 3);
            const b = phone.slice(3, 7);
            const c = phone.slice(-4);
            return `${a}-${b}-${c}`;
        },
    },
    data() {
        return {
            displayModal: false,
            verification: '', // 验证码
            first: true, // 第一次发送
            pending: false, // 正在发送
            tick: 0, // 倒计时
            withdrawing: false,
            valid: false,
            backdrop: true,
            modalDisabled: false,
        };
    },
    computed: {
        ...mapState('withdraw', ['loading', 'withdrawDetail']),
        wmPoiId() {
            return (this.$route.query && this.$route.query.wmPoiId) || null;
        },
        acctType() {
            return (this.$route.query && this.$route.query.acctType) || 0;
        },
        accountType() {
            return this.withdrawDetail.payBindType === 1
                ? '银行卡'
                : '钱包';
        },
        accoutName() {
            const { payBindType } = this.withdrawDetail;

            if (payBindType === 1) {
                const { bankName, bankAccountName, cardTailNo } = this.withdrawDetail;
                return `${bankName} ${bankAccountName} ${cardTailNo}`;
            }

            const { walletId, walletAccountName } = this.withdrawDetail;
            return `${walletAccountName} ${walletId}`;
        },
        sendButtonText() {
            if (this.pending) {
                return '发送中';
            }
            if (this.tick > 0) {
                return `已发送 ${this.tick}s`;
            }
            return this.first ? '获取验证码' : '重新发送';
        },
        canWithdraw() {
            return this.withdrawDetail.balanceOutStatus || this.withdrawDetail.balance > 0;
        },
        rules() {
            return [{
                validator: this.validateAmount,
                trigger: 'blur',
            }];
        },
    },
    mounted() {
        const params = {
            wmPoiId: this.wmPoiId,
            acctType: this.acctType,
        };
        this.fetchWithdrawDetail(params).then(() => {
            if (!this.withdrawDetail.accountMobile) {
                alert('需要绑定手机才能进行提现', () => {
                    this.$router.go(-1);
                });
            }
            if (this.withdrawDetail.balanceOutStatus) {
                alert(this.withdrawDetail.balanceOutMsg, () => {
                    this.$router.go(-1);
                });
            }
        }).catch(toastError);
    },
    methods: {
        handleCancel() {
            this.$router.go(-1);
        },
        handleGetVerify() {
            this.pending = true;
            return request.post('/finance/pc/api/account/sendMsg', {
                phone: this.withdrawDetail.accountMobile,
                wmPoiId: this.wmPoiId,
            }).then((res) => {
                this.pending = false;
                this.first = false;

                const { code, msg } = res.data;
                if (code !== 0) {
                    throw new Error(msg);
                }
                this.launchCountdown();
            }).catch((err) => {
                this.pending = false;
                toastError(err);
            });
        },
        launchCountdown() {
            if (this.tick !== 0) {
                return;
            }
            this.tick = 60;
            const countdown = () => {
                this.tick = this.tick - 1;
                if (this.tick === 0) {
                    return;
                }
                setTimeout(countdown, 1000);
            };
            setTimeout(countdown, 1000);
        },
        handleConfirmWithdraw() {
            this.backdrop = 'static';
            this.modalDisabled = true;

            if (this.withdrawing) {
                toast.error('有提现中的任务，禁止再次提交提现任务');
                return;
            }
            this.withdrawing = true;
            const params = {
                wmPoiId: this.wmPoiId,
                validCode: this.verification,
                phone: this.withdrawDetail.accountMobile,
                withdrawAmount: this.withdrawDetail.balance,
                acctType: this.acctType,
            };
            request.post('/finance/pc/api/account/withdraw/verify', params).then((res) => {
                this.withdrawing = false;
                this.backdrop = true;
                this.modalDisabled = false;

                const { code, msg, data } = res.data;

                if (code !== 0) {
                    this.displayModal = false;
                    throw new Error(msg);
                }

                // 提现成功后跳转到提现详情页
                this.$router.push({
                    name: 'withdraw-detail',
                    query: {
                        wmPoiId: this.wmPoiId,
                        outId: data.outId,
                    },
                });
            }).catch((err) => {
                this.withdrawing = false;
                this.displayModal = false;
                this.backdrop = true;
                this.modalDisabled = false;

                toastError(err);
            });
        },
        ...mapActions('withdraw', ['fetchWithdrawDetail']),
    },
};
</script>
<style lang="scss" module>
.withdraw-container {
    padding: 20px;
}
.container {
    background: #fff;
}
.header {
    height: 40px;
    line-height: 40px;
    padding: 0 20px;
    color: #2a2a2a;
    font-weight: bold;
    background: #fdfdfd;
    border-bottom: 1px solid #fafafa;
}
.modal-title {
    margin: 0;
    color: #3F4156;
    font-size: 17.5px;
    font-weight: normal;
}
.close {
    float: right;
    padding: 0;
    color: #A2A4B3;
    font-size: 15.96px;
    background: transparent;
    border: 0;
    cursor: pointer;
    -webkit-appearance: none;
}
.content {
    padding: 20px 0;
}
.withdraw {
    padding: 10px 0 ;
}
.withdraw-all {
     padding-left: 10px;
}
.label-no-padding :global(.control-label) {
    padding-top: 0;
}
.flex-box {
    display: flex;
    align-items: center;
    margin-bottom: 30px;
}
.verification {
    margin-right: 30px;
}
.get-verify {
    width: 150px;
}
.align-center {
    text-align: center;
    padding-bottom: 20px;
}
.amount {
    font-size: 24px;
}
.yellow-tip {
    color: #F8B500;
}
</style>
