<template>
    <div id="app">
        <router-view />
    </div>
</template>

<script>
export default {
    name: 'App',
};
</script>

<style lang="scss">
html {
    height: 100%;
}
body {
    height: 100%;
    padding-right: 20px;
    background: #F7F8FA;
}

#app {
    position: relative;
    display: block;
    width: 100%;
    height: 100%;
    font-family: "Helvetica Neue",
        Arial, // 数字和英文优先使用 Helvetica Neue 和 Arial
        -apple-system,
        BlinkMacSystemFont,
        "PingFang SC",
        STHeiti,
        "Microsoft Yahei",
        sans-serif;

    // iframe 内的 media query 为 iframe 宽度 = 窗口宽度 - 200(边栏宽度)
    @media (max-width: 1240px) {
        min-width: 900px;
        width: 100%;
    }
    @media (min-width: 1241px) {
        width: 1200px;
        margin: auto;
    }

    // .kui-loading .loading-tip {
    //     // kui-loading 中的文字 display 为 inline-block 导致位置有些偏上
    //     display: block;
    // }

    button.close {
        outline: none;
    }

    a {
        text-decoration: none;
    }
}
</style>
