import '@babel/polyfill';
import Vue from 'vue';

import '@roo/roo-vue';
import '@roo/roo-vue/dist/css/roo-vue.css';

import { initCashier } from '@mtfe/pay-cashier-sdk-npm';
import LXMC from '../../directives/lx-mc';

import App from './app';
import store from './store';
import router from './router';

Vue.directive(LXMC.name, LXMC);

/* eslint-disable no-new */
new Vue({
    el: '#app',
    store,
    router,
    render(h) {
        return h(App);
    },
});
/* eslint-enable no-new */
// SDK_SOURCE分配表 https://km.sankuai.com/page/1292960094
const SDK_SOURCE = 4;
const env = {
    production: 'prod',
    stage: 'staging',
    test: 'test',
    dev: 'dev',
}[process.env.DEPLOY_ENV];
const initCashierParam = {
    env,
    pay_cashier_sdk_source: SDK_SOURCE,
    js_sdk_version: '1',
};
initCashier(initCashierParam);
export default SDK_SOURCE;
