import Vue from 'vue';
import Router from 'vue-router';

import Home from './views/home';
import Download from './views/download';
import AccountFlow from './views/account-flow';
import AccountList from './views/account-list';
import SettleBillList from './views/settle-bill-list';
import BillDetail from './views/bill-detail';
import Withdraw from './views/withdraw';
import WithdrawDetail from './views/withdraw-detail';
import MutilBillDetail from './views/mutil-bill-detail';
import Recharge from './views/recharge';
import DailyBill from './views/daily-bill';
import ScanQRCodeBill from './views/scan-qr-code-bill';
import AccountInfo from './views/account-info';
import OrderDetail from './views/order-detail';
import OrderQuery from './views/order-query';

import { getCookies } from '../../lib/utils';

Vue.use(Router);


// 页面内使用 this.$route.meta.cid 获取 cid

const router = new Router({
    routes: [{
        path: '/home',
        alias: '/home/<USER>',
        name: 'home',
        component: Home,
        meta: {
            cid: 'c_waimai_e_v1oofntv',
        },
    }, {
        path: '/download',
        alias: '/downloads/downloadBill',
        name: 'download',
        component: Download,
        meta: {
            cid: 'c_waimai_e_389xteat',
        },
    }, {
        path: '/settle-bill-list',
        alias: '/list/bills',
        name: 'settle-bill-list',
        component: SettleBillList,
        meta: {
            cid: 'c_waimai_e_4doowz4c',
        },
    }, {
        path: '/account-list',
        alias: '/account/accountList',
        name: 'account-list',
        component: AccountList,
        meta: {
            cid: 'c_waimai_e_aine8qhs',
        },
    }, {
        path: '/account-flow',
        name: 'account-flow',
        component: AccountFlow,
        meta: {
            cid: 'c_waimai_e_5o2lxsdv',
        },
    }, {
        path: '/daily-bill',
        name: 'daily-bill',
        component: DailyBill,
        meta: {
            cid: 'c_waimai_e_2vuu20sc',
        },
    }, {
        path: '/order-detail',
        name: 'order-detail',
        component: OrderDetail,
    }, {
        path: '/scan-qr-code-bill',
        name: 'scan-qr-code-bill',
        component: ScanQRCodeBill,
    }, {
        path: '/bill-detail',
        name: 'bill-detail',
        component: BillDetail,
        meta: {
            cid: 'c_waimai_e_t0yst1ta',
        },
    }, {
        path: '/recharge',
        name: 'recharge',
        alias: '/account/recharge',
        component: Recharge,
        meta: {
            cid: 'c_waimai_e_upx1dtko',
        },
    }, {
        path: '/withdraw',
        name: 'withdraw',
        component: Withdraw,
        meta: {
            cid: 'c_waimai_e_dzsdgo6h',
        },
    }, {
        path: '/withdraw-detail',
        name: 'withdraw-detail',
        component: WithdrawDetail,
        meta: {
            cid: 'c_waimai_e_3s27n8yw',
        },
    }, {
        path: '/mutil-bill-detail',
        name: 'mutil-bill-detail',
        component: MutilBillDetail,
        meta: {
            cid: 'c_waimai_e_1kgwe14y',
        },
    }, {
        path: '/account-info',
        name: 'account-info',
        component: AccountInfo,
        meta: {
            cid: 'c_waimai_e_9axtvfk3',
        },
    }, {
        path: '/order-query',
        name: 'order-query',
        component: OrderQuery,
        meta: {
            cid: 'c_waimai_e_slc92b6a',
        },
    }, {
        path: '/',
        redirect: { name: 'home' },
    }],
});

router.afterEach((to) => {
    const { meta } = to;
    if (meta && meta.cid) {
        const cookies = getCookies();
        const acctId = parseInt(cookies.acctId, 10) || null;
        const wmPoiId = parseInt(cookies.wmPoiId, 10) || null;
        const referrer = document.referrer || '';
        LXAnalytics('pageView', {
            poi_id: wmPoiId,
            custom: {
                acctId,
                wmPoiId: wmPoiId || -1,
                referrer,
            },
        }, null, meta.cid);
        console.log('referrer--->', referrer);
    }
});

export default router;
