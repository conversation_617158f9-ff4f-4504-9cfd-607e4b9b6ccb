<template>
    <div>
        <div v-if="isShowData === 1" id="app">
            <div
                v-if="active"
                class="header-nav"
            >
                <div
                    v-if="!isSinglePoi"
                    class="contractHeadWrapper"
                >
                    <div class="contractHeadTitle">
                        {{ contractAName }}
                    </div>
                    <a
                        v-if="contractList.length > 1"
                        class="replaceContractBtn"
                        @click="displayModal = true"
                    >
                        切换合同
                    </a>
                </div>
                <roo-tabs
                    v-model="active"
                    type="border"
                >
                    <roo-tab-pane
                        name="invoice-apply"
                        label="新开发票"
                    />
                    <roo-tab-pane
                        v-if="!isSinglePoi"
                        name="invoice-title"
                        label="抬头管理"
                    />
                    <roo-tab-pane
                        name="invoice-history"
                        label="开票历史"
                    />
                </roo-tabs>

                <span
                    v-if="isSinglePoi && contractId"
                    class="contract-info"
                >
                    合同签约甲方 : {{ contractAName }}
                </span>
            </div>

            <roo-modal
                v-model="displayModal"
                size="small"
            >
                <div class="modalTitle">
                    确认变更合同？
                </div>
                <div style="margin-top: 10px;">
                    变更合同将不会保留当前操作
                </div>

                <template slot="footer">
                    <roo-button
                        type="hollow"
                        @click="displayModal = false"
                    >
                        取消
                    </roo-button>
                    <roo-button @click="handleChangeContract">
                        确认
                    </roo-button>
                </template>
            </roo-modal>

            <router-view />
            <!-- TODO: 直接全量走新流程，无需展示老流程的合同选择 -->
            <!-- <contract-select /> -->
        </div>
        <div v-if="isShowData === 0 || isShowData === 2 || isShowData === 3" class="isshow">
            <div>
                <img src="https://s3plus.meituan.net/v1/mss_0e4451a82e1d428f9fc3b99c0b3ebf15/pic-manager/1c375ee79accb45925cd5eb7c6966aba/default-img.png" alt="" />
            </div>
            <p class="titles">
                当前账号已关闭线上开票功能
            </p>
            <p class="text">
                如需开具发票请联系业务经理
            </p>
        </div>
    </div>
</template>

<script>
import { mapState, mapActions } from 'vuex';
// import ContractSelect from './views/contract-select';
// eslint-disable-next-line import/no-unresolved, import/extensions
import { getCookieValue } from '$lib/utils';
// eslint-disable-next-line import/no-unresolved, import/extensions
import SOURCE from '$lib/sourceEnum';
// eslint-disable-next-line import/no-unresolved, import/extensions
import toastError from '$lib/toast-error';
// import JUMPSSO from './utils/jump_sso';
// eslint-disable-next-line import/no-unresolved, import/extensions
import request from '$invoice/utils/request';
/* eslint-disable import/extensions, import/no-unresolved */
/* eslint-disable import/extensions, import/no-unresolved */

export default {
    name: 'App',
    data() {
        return {
            isShowData: 1, // 0，命中黑名单 （不能开票）1，未命中黑名单 （可以开票）2，参数错误（不能开票）3，系统异常（不能开票）
            displayModal: false,
        };
    },
    created() {
        this.checkGray();
        this.fetchGreyInfo();
        this.fetchShudianGrayInfo();
        this.getNotice();
        this.getNoticeNew();
        const sourceCookieValue = getCookieValue('source');
        // 不为外卖商家中心(source !== 2)时 商家端不做黑白名单校验
        if (sourceCookieValue !== SOURCE.WAIMAI) {
            request.get('/finance/invoice/entrance').then((res) => {
                this.isShowData = res.data.data;
            });
        }

        if (!this.isSinglePoi) {
            this.$router.replace({ name: 'contract-select-update' });
            this.loadFeeTypeList().catch(() => {});
            this.loadContractList().catch(toastError);
        } else {
            this.$router.push({ name: 'invoice-apply' });
            // const sourceCookieValue = getCookieValue('source');
            this.loadFeeTypeList().catch(() => {});
            // 仅为外卖商家中心(source == 2)时，需要加载
            if (sourceCookieValue === SOURCE.WAIMAI) {
                this.loadContractList().catch(toastError);
                // 请求灰度信息
                this.getGreyInfo();
            }
        }
    },
    components: {
        // ContractSelect,
    },

    computed: {
        active: {
            get() {
                const [top] = this.$route.matched;
                if (
                    top && ['invoice-apply', 'invoice-title', 'invoice-history'].indexOf(top.name) > -1
                ) {
                    return top.name;
                }
                return null;
            },
            set(val) {
                if (val === this.active) {
                    return;
                }
                switch (val) {
                    case 'invoice-apply':
                        this.$router.push({ name: !this.isSinglePoi ? 'qualification-select-update' : 'qualification-select' });
                        break;
                    case 'invoice-title':
                        this.$router.push({ name: 'invoice-title' });
                        break;
                    case 'invoice-history':
                        this.$router.push({ name: 'invoice-history' });
                        break;
                    default:
                        break;
                }
            },
        },
        ...mapState('config', ['contractId', 'contractAName', 'contractList', 'isSinglePoi']),
    },
    mounted() {
        // this.fetchGreyInfo();
    },
    methods: {
        ...mapActions('config', ['loadFeeTypeList', 'loadContractList', 'getGreyInfo', 'fetchGreyInfo', 'fetchShudianGrayInfo']),
        ...mapActions('apply', ['checkGray', 'getNotice', 'getNoticeNew']),
        handleChangeContract() {
            this.displayModal = false;
            this.$store.commit('amountSelectNew/reset');
            this.$store.commit('invoiceTitle/reset');
            this.$store.commit('sendInfo/reset');
            this.$store.commit('apply/reset');
            this.$router.replace({ name: 'contract-select-update' });
        },
    },
};
</script>

<style lang="scss">
html {
  height: 100%;
}

body {
  height: 100%;
  background: #f7f8fa;

  .roo-alert {
    padding-left: 40px;

    & > i {
      position: absolute;
      left: 20px;
      top: 50%;
      transform: translateY(-50%);
    }
  }
}

#app {
  position: relative;
  display: block;
  width: 100%;
  height: 100%;
  border-radius: 2px;
  font-family: 'Helvetica Neue', Arial,
    // 数字和英文优先使用 Helvetica Neue 和 Arial
      -apple-system,
    BlinkMacSystemFont, 'PingFang SC', STHeiti, 'Microsoft Yahei', sans-serif;

  // iframe 内的 media query 为 iframe 宽度 = 窗口宽度 - 200(边栏宽度)
  @media (max-width: 1240px) {
    min-width: 900px;
    width: 100%;
  }
  @media (min-width: 1241px) {
    width: 1200px;
    margin: auto;
  }

  .roo-icon {
    height: auto;
  }

  // .kui-loading .loading-tip {
  //     // kui-loading 中的文字 display 为 inline-block 导致位置有些偏上
  //     display: block;
  // }

  button.close {
    outline: none;
  }

  a {
    text-decoration: none;
  }

  .header-nav,
  .footer-nav {
    @media (max-width: 1240px) {
      min-width: 900px;
      width: 100%;
    }
    @media (min-width: 1241px) {
      width: 1200px;
      margin: auto;
    }
  }

  .header-nav {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    margin: auto;
    border-radius: 2px;
    background: #fff;
    z-index: 10;

    .roo-tabs {
      flex: 1;
    }
    .tab-item.active {
      font-weight: bold;
    }
    .roo-tabs-content {
      display: none;
    }
  }

  .footer-nav {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    margin: auto;
    height: 56px;
    padding: 10px 20px;
    border-radius: 2px;
    box-shadow: 0 -4px 5px 0 #f7f8fa;
    background: #fff;
    z-index: 10;
    text-align: right;
  }

  .contract-info {
    position: absolute;
    top: 50%;
    right: 40px;
    color: #858692;
    transform: translate3d(0, -50%, 0);
  }
}
.isshow{
    text-align: center;
    // height: 834px;
    width: 100%;
    background: #fff;
    padding-top: 200px;
    padding-bottom: 200px;
    div {
        width: 100px;
        height: 100px;
        margin: 0px auto;
        img{
            width: 100%;
            height: 100%;
            border-radius: 50%;
        }
    }
}
.titles{
    font-size: 20px;
    color: #3F4156;
}
.text{
    margin-top: -16px;
    color: #858692;
    font-size: 14px;
}
.contractHeadWrapper{
    display: flex;
    flex-direction: row;
    align-items: baseline;
    justify-content: start;
    margin-left: 20px;
    margin-top: 10px;
    margin-bottom: 5px;
}
.contractHeadTitle{
    color: #3F4156;
    font-size: 14px;
}
.replaceContractBtn{
    cursor: pointer;
    color: #FF7700;
    font-size: 14px;
    margin-left: 10px;
}
.modalTitle{
    font-size: 20px;
    color: #222222;
    font-weight: 500;
}
</style>
