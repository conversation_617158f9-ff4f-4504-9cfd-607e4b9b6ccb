import Vue from 'vue';
import Router from 'vue-router';

import InvoiceApply from './views/invoice-apply';
import QualificationSelect from './views/invoice-apply/views/qualification-select';
import QualificationSelectUpdate from './views/invoice-apply/views/qualification-select-update';
import AmountSelect from './views/invoice-apply/views/amount-select';
import AmountSelectNew from './views/invoice-apply/views/amount-select-new';
import AmountSelectNewV2 from './views/invoice-apply/views/amount-select-new-v2';
import AmountSplit from './views/invoice-apply/views/amount-split';
import InvoiceTypeConfirm from './views/invoice-apply/views/invoice-type-confirm';
import InvoiceTypeConfirmUpdate from './views/invoice-apply/views/invoice-type-confirm-update';
import AddressConfirm from './views/invoice-apply/views/address-confirm';
import AddressConfirmNew from './views/invoice-apply/views/address-confirm-new';
import SOURCE from '../../lib/sourceEnum';

import ContractSelectUpdate from './views/contract-select-update';

import TitleManagement from './views/title-management';
import AddTitle from './views/title-management/add-title';
import EditTitle from './views/title-management/edit-title';
import RelateStore from './views/title-management/relate-store';

import InvoiceTitle from './views/invoice-title';

import InvoiceHistory from './views/invoice-history';

import InvoiceDetail from './views/invoice-detail';

import AddQualification from './views/qualification-edit/add';
import EditQualification from './views/qualification-edit/edit';
import { setCookie, getCookieValue } from '../../lib/utils';
import JUMPSSO from './utils/jump_sso';

Vue.use(Router);

const router = new Router({
    routes: [{
        name: 'invoice-apply',
        path: '/invoice-apply',
        component: InvoiceApply,
        children: [{
            name: 'qualification-select',
            path: 'qualification-select',
            component: QualificationSelect,
        }, {
            name: 'qualification-select-update',
            path: 'qualification-select-update',
            component: QualificationSelectUpdate,
        }, {
            name: 'amount-select-new',
            path: 'amount-select-new',
            component: AmountSelectNew,
        }, {
            name: 'amount-select-new-v2',
            path: 'amount-select-new-v2',
            component: AmountSelectNewV2,
        }, {
            name: 'amount-select',
            path: 'amount-select',
            component: AmountSelect,
        }, {
            name: 'amount-split',
            path: 'amount-split',
            component: AmountSplit,
        }, {
            name: 'invoice-type-confirm',
            path: 'invoice-type-confirm',
            component: InvoiceTypeConfirm,
        }, {
            name: 'invoice-type-confirm-update',
            path: 'invoice-type-confirm-update',
            component: InvoiceTypeConfirmUpdate,
        }, {
            name: 'address-confirm',
            path: 'address-confirm',
            component: AddressConfirm,
        }, {
            name: 'address-confirm-new',
            path: 'address-confirm-new',
            component: AddressConfirmNew,
        }, {
            path: '*',
            redirect: { name: 'qualification-select' },
        }],
    }, {
        name: 'invoice-title',
        path: '/invoice-title',
        component: InvoiceTitle,
    }, {
        name: 'invoice-history',
        path: '/invoice-history',
        component: InvoiceHistory,
    }, {
        name: 'contract-select-update',
        path: '/contract-select-update',
        component: ContractSelectUpdate,
    }, {
        name: 'title-management',
        path: '/title-management',
        component: TitleManagement,
        children: [{
            name: 'add-title',
            path: '/add-title',
            component: AddTitle,
        }, {
            name: 'edit-title',
            path: '/edit-title',
            component: EditTitle,
        }, {
            name: 'relate-store',
            path: '/relate-store',
            component: RelateStore,
        }],
    }, {
        name: 'invoice-detail',
        path: '/invoice-detail',
        component: InvoiceDetail,
    }, {
        name: 'add-qualification',
        path: '/add-qualification',
        component: AddQualification,
    }, {
        name: 'edit-qualification',
        path: '/edit-qualification',
        component: EditQualification,
    }, {
        path: '*',
        redirect: { name: getCookieValue('wmPoiId') === '-1' ? 'contract-select-update' : 'invoice-apply' },
    }],
});

router.beforeEach((to, from, next) => {
    // 若页面携带登录信息的参数，则种 Cookie
    const { query } = to;
    const isOther = window.location.origin.indexOf('invoice') > -1;
    const isLocal = window.location.href.indexOf('localhost') > -1;
    const {
        bizAcctId, token, appKey, source,
    } = query;
    const sourceValue = isOther || isLocal ? source : SOURCE.WAIMAI;
    // 历史原因，商家中心业务没有传source,也没有种cookie的，随着业务平台的增多，代码判断逻辑难以维护，故给source传默认值为商家中心
    // 但是兜底值在其他业务路由切换时，也会错误的种到cookie中，因为只有首次进页面的时候业务拼了参数，其余切换都没有，此时便会走默认逻辑
    if (bizAcctId) { setCookie('bizAcctId', bizAcctId); }
    if (token) { setCookie('token', token); }
    if (appKey) { setCookie('appKey', appKey); }
    // 为了避免上述问题，我们加上已有cookie判断，但是就会引发在同一个浏览器中访问各个业务的时候，cookie中的source无法更新
    // if (source && !getCookieValue('source')) { setCookie('source', source); }
    // 下面这种方案可以完美解决
    if (sourceValue) { setCookie('source', sourceValue); }
    // dbdb9ee243_ssoid是线下环境token
    // ec52eea2a6_ssoid是线上环境token
    if (sourceValue === SOURCE.AGENT && !(getCookieValue('dbdb9ee243_ssoid') || getCookieValue('ec52eea2a6_ssoid'))) {
        JUMPSSO.jumpToSSO(next).catch(() => {});
    }
    next();
});

export default router;
