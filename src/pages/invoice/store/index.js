import Vue from 'vue';
import Vuex from 'vuex';

import apply from './modules/apply';
import config from './modules/config';
import region from './modules/region';
import sendInfo from './modules/send-info';
import amountSelect from './modules/amount-select';
import invoiceHistory from './modules/invoice-history';
import qualificationSelect from './modules/qualification-select';
import amountSelectNew from './modules/amount-select-new';
import invoiceTitle from './modules/invoice-title';
import titleManagement from './modules/title-management';


Vue.use(Vuex);

const strict = process.env.NODE_ENV !== 'production';

export default new Vuex.Store({
    modules: {
        apply,
        config,
        region,
        sendInfo,
        amountSelect,
        invoiceHistory,
        qualificationSelect,
        amountSelectNew,
        invoiceTitle,
        titleManagement,
    },
    state: {},
    actions: {
        reset({ commit }) {
            commit('apply/reset');
            commit('sendInfo/reset');
            commit('amountSelect/reset');
            commit('qualificationSelect/reset');
            commit('invoiceTitle/reset');
            commit('titleManagement/reset');
        },
    },
    strict,
});
