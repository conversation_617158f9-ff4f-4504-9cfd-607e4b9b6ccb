/* eslint-disable import/no-unresolved, import/extensions */
import request from '$invoice/utils/request';

const initialState = {
    isEdit: false,
    reasonsList: [],
    relatedStoreList: [],
    relatedStoreIds: [],
    chosenIds: [],
    tempParams: {},
    partnerBName: '',
    partnerTypeT: 0,
    compareQualificationId: {},
};

const mutations = {
    changeIsEdit(state, edit) {
        state.isEdit = edit;
    },
    changeReasonsList(state, reasons) {
        state.reasonsList = reasons;
    },
    changeRelatedStoreList(state, store) {
        state.relatedStoreList = [...store];
    },
    changeRelatedStoreIds(state, ids) {
        state.relatedStoreIds = ids;
    },
    changePartnerBName(state, bName) {
        state.partnerBName = bName;
    },
    changeChosenIds(state, ids) {
        state.chosenIds = [...ids];
    },
    changeTempParams(state, params) {
        state.tempParams = Object.assign(state.tempParams, ...params);
    },
    changeCompareQualificationId(state, item) {
        state.compareQualificationId = item;
    },
    reset(state) {
        state.isRepeat = false;
        state.isEdit = false;
        state.reasonsList = [];
        state.relatedStoreList = [];
        state.relatedStoreIds = [];
        state.tempParams = {};
        state.compareQualificationId = {};
    },
};

function getReasonsList() {
    return request.get('/finance/invoice/api/output/waimai/q/changeReasons', {});
}

function getRelatedStoreList(params) {
    return request.get('/finance/invoice/api/output/waimai/q/getInvoiceTitlePartner', { params });
}

function bindPoiAndTitleRelManage(params) {
    return request.get('/finance/invoice/api/output/waimai/q/poiAndTitleRelManage', { params });
}

const actions = {
    fetchReasonsList({ commit }) {
        return getReasonsList()
            .then((res) => {
                if (res == null) {
                    return [];
                }

                const { code, msg, data } = res.data;
                if (code !== 0) {
                    throw new Error(msg);
                }

                commit('changeReasonsList', data);

                return data;
            }, (err) => {
                throw err;
            });
    },
    fetchRelatedStoreList({ commit, rootState }) {
        const { config } = rootState;
        const { contractId } = config;

        const params = {
            contractId,
            partnerType: 103,
        };

        return getRelatedStoreList(params)
            .then((res) => {
                // if (res == null) {
                //     return [];
                // }

                const { code, msg, data } = res.data;
                if (code !== 0) {
                    throw new Error(msg);
                }

                commit('changeRelatedStoreList', data);

                return data;
            }, (err) => {
                throw err;
            });
    },
    fetchBindPoiAndTitleRelManageRes() {
        const params = {
            qualificationId: 0,
            partnerIdList: [],
        };

        return bindPoiAndTitleRelManage(params)
            .then((res) => {
                if (res == null) {
                    return [];
                }

                const { code, msg, data } = res.data;
                if (code !== 0) {
                    throw new Error(msg);
                }

                return data;
            }, (err) => {
                throw err;
            });
    },
};

export default {
    namespaced: true,
    state: initialState,
    mutations,
    actions,
};
