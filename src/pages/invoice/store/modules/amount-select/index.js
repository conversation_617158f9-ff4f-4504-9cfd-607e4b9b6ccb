import axios from 'axios';
/* eslint-disable import/no-unresolved, import/extensions */
import request from '$invoice/utils/request';

const { CancelToken } = axios;

const initialState = {
    dateFrom: null,
    dateTo: null,

    loading: false,

    total: 0,
    pageNo: 1,
    pageSize: 10,
    settleAmountList: [],
    monthAmountList: [],
    showSellerCompanyList: true, // 和后端shiguoying沟通，showSellerCompanyList永远返回true了
    searching: false,
    hiddenList: [],
    result: null,
};

const mutations = {
    reset(state) {
        state.dateFrom = null;
        state.dateTo = null;

        state.loading = false;

        state.total = 0;
        state.pageNo = 1;
        state.pageSize = 10;
        state.settleAmountList = [];
        state.monthAmountList = [];
        state.showSellerCompanyList = true;

        state.searching = false;
        state.hiddenList = [];
        state.result = null;
    },
    changeDateFrom(state, date) {
        state.dateFrom = date || null;
        // 修改日期时，清空缓存的搜索结果
        state.hiddenList = [];
        state.result = null;
    },
    changeDateTo(state, date) {
        state.dateTo = date || null;
        // 修改日期时，清空缓存的搜索结果
        state.hiddenList = [];
        state.result = null;
    },
    changeShowSellerCompanyList(state) {
        state.showSellerCompanyList = true;
    },

    changeLoading(state, loading) {
        state.loading = loading || false;
    },
    changeTotal(state, total) {
        state.total = total || 0;
    },
    changePageNo(state, no) {
        state.pageNo = no || 1;
    },
    changeSettleAmountList(state, list) {
        const { hiddenList } = state;
        state.settleAmountList = list.map((item) => {
            const idx = hiddenList.findIndex((x) => x.settleId === item.settleId);
            if (idx > -1) {
                return hiddenList.splice(idx, 1)[0];
            } else {
                return item;
            }
        });
    },
    changeMonthAmountList(state, list) {
        state.monthAmountList = list;
    },

    changeSearching(state, loading) {
        state.searching = !!loading;
    },
    changeHiddenList(state, list) {
        state.hiddenList = list || [];
    },
    changeResult(state, result) {
        state.result = result || null;
    },

    // 注意，此处不再从 state 中获取，组件将 store 中的 item 传回 mutation 方法中修改
    checkItem(state, { item, checked }) {
        item.checked = checked;
    },

    checkAllAmount(state, checked) {
        const mutator = (item) => {
            if (item.moneyMilli > 0) {
                // 判断是否存在disabled标记，如果有就不选中
                if (item.isDisabledByTimeCondition) {
                    item.checked = false;
                } else {
                    item.checked = checked;
                }
            } else {
                // 金额小于等于 0 必须选中
                item.checked = true;
            }
        };
        state.monthAmountList.forEach(mutator);
        state.settleAmountList.forEach(mutator);
    },

    setAmount(state, { amount, moneyCent, moneyMilli }) {
        amount.moneyCent = moneyCent;
        amount.moneyMilli = moneyMilli;
        if (moneyMilli <= 0) {
            // 组合门店后金额可能 <= 0
            amount.checked = true;
        }
    },

    setAdDetails(state, { amount, adDetails }) {
        amount.adDetails = adDetails;
    },

    setPoiList(state, { amount, poiList }) {
        amount.poiList = poiList;
    },

    setPoiAmountList(state, { amount, poiAmountList }) {
        amount.poiAmountList = poiAmountList;
    },
};

let source1 = null;
function getAmountList(params) {
    if (source1) {
        source1.cancel();
        source1 = null;
    }

    source1 = CancelToken.source();
    const cancelToken = source1.token;

    return request.get('/finance/invoice/api/common/application/r/amount', {
        params,
        cancelToken,
    });
}

let source2 = null;
function searchAmount(params) {
    if (source2) {
        source2.cancel();
        source2 = null;
    }

    source2 = CancelToken.source();
    const cancelToken = source2.token;

    return request.get('/finance/invoice/api/pc/application/r/serviceAmount', {
        params,
        cancelToken,
    });
}

function settleAmountMapper(item) {
    let totalMoneyCent = 0;
    let totalMoneyMilli = 0;
    if (item.sellerCompanyList && item.sellerCompanyList.length) {
        item.sellerCompanyList.forEach((el) => {
            totalMoneyCent += el.moneyCent;
            totalMoneyMilli += el.moneyMilli;
        });
    }
    return {
        ...item,
        checked: item.moneyMilli <= 0,
        poiList: null,
        poiAmountList: null,
        totalMoneyCent,
        totalMoneyMilli,
    };
}

function monthAmountMapper(item) {
    let totalMoneyCent = 0;
    let totalMoneyMilli = 0;
    if (item.sellerCompanyList && item.sellerCompanyList.length) {
        item.sellerCompanyList.forEach((el) => {
            totalMoneyCent += el.moneyCent;
            totalMoneyMilli += el.moneyMilli;
        });
    }
    return {
        ...item,
        checked: item.moneyMilli <= 0,
        adDetails: null,
        totalMoneyCent,
        totalMoneyMilli,
    };
}

const actions = {
    fetchAmountList({ state, commit, rootState }, pageNo = 1) {
        const { baseFeeTypeItem } = rootState.apply;

        if (!baseFeeTypeItem) {
            throw new TypeError('请选择明细项目');
        }

        const { config, apply } = rootState;

        const { contractId } = config;

        const { pageSize } = state;

        const {
            baseFeeType,
            partnerType,
            amountGatherType,
            qualificationType,
        } = apply.baseFeeTypeItem;

        const params = {
            contractId,
            baseFeeType,
            partnerType,
            amountGatherType,
            qualificationType,

            pageNo,
            pageSize,
        };

        if (state.dateFrom) {
            params.dateFrom = state.dateFrom;
        }
        if (state.dateTo) {
            params.dateTo = state.dateTo;
        }

        commit('changeLoading', true);
        return getAmountList(params).then(
            (res) => {
                commit('changeLoading', false);

                const { code, msg, data } = res.data;
                if (code !== 0) {
                    throw new Error(msg);
                }

                commit('changeTotal', data.totalCount);
                commit('changePageNo', data.pageNo);
                const { settleAmountList, monthAmountList } = data;

                if (data.pageNo === 1) {
                    if (monthAmountList) {
                        commit(
                            'changeMonthAmountList',
                            monthAmountList.map(monthAmountMapper),
                        );
                    }
                    if (settleAmountList) {
                        commit(
                            'changeSettleAmountList',
                            settleAmountList.map(settleAmountMapper),
                        );
                    }
                } else {
                    if (monthAmountList) {
                        commit(
                            'changeMonthAmountList',
                            state.monthAmountList.concat(
                                monthAmountList.map(monthAmountMapper),
                            ),
                        );
                    }
                    if (settleAmountList) {
                        commit(
                            'changeSettleAmountList',
                            state.settleAmountList.concat(
                                settleAmountList.map(settleAmountMapper),
                            ),
                        );
                    }
                }
            },
            (err) => {
                commit('changeLoading', false);
                throw err;
            },
        );
    },
    initAmountList({ state, commit }) {
        const { monthAmountList, settleAmountList } = state;
        if (monthAmountList && monthAmountList.length > 0) {
            commit('changeMonthAmountList', monthAmountList.map(monthAmountMapper));
        }
        if (settleAmountList && settleAmountList.length > 0) {
            commit(
                'changeSettleAmountList',
                settleAmountList.map(settleAmountMapper),
            );
        }
    },
    // 调用接口前，先在已加载列表过滤一遍
    // action 负责
    // 1. 在 hiddenList 中查找，将目标设为 result
    // 2. 请求接口查询，将结果拼到 hiddenList ，并设为 result
    // todo 搜索结算id以及搜索商家id之后会操作这个逻辑,需要重新计算金额分配么  需要确认下
    searchSettleAmount({ state, commit, rootState }, params) {
        const { settleId, poiId } = params;

        if (!(settleId || poiId)) {
            throw new TypeError('缺少 settleId 或 wmPoid');
        }

        // 本地搜索
        const { settleAmountList, hiddenList } = state;

        let target = null;
        let flag = false;
        if (settleId) {
            const func = (x) => x.settleId === settleId;
            target = hiddenList.find(func);
            if (target) {
                flag = true;
            } else {
                target = settleAmountList.find(func);
            }
        } else if (poiId) {
            const func = (x) => x.poiList && x.poiList.some((poi) => poi.id === poiId);
            target = hiddenList.find(func);
            if (target) {
                flag = true;
            } else {
                target = settleAmountList.find(func);
            }
        }

        if (target) {
            if (flag) {
                commit('changeResult', target);
            }
            return target;
        }

        const { dateFrom, dateTo } = state;
        const { config, apply } = rootState;
        const { contractId } = config;
        const { baseFeeTypeItem } = apply;

        // 远程搜索
        commit('changeSearching', true);
        return searchAmount({
            poiId,
            settleId,
            contractId,
            baseFeeType: baseFeeTypeItem && baseFeeTypeItem.baseFeeType,
            partnerType: baseFeeTypeItem && baseFeeTypeItem.partnerType,
            dateFrom,
            dateTo,
        }).then(
            (res) => {
                commit('changeSearching', false);

                const { code, msg, data } = res.data;
                if (code !== 0) {
                    throw new Error(msg);
                }

                // 搜索结果可能已加载 排重
                const finder = (x) => x.settleId === data.settleId;
                let result = state.settleAmountList.find(finder);
                if (result) {
                    return result;
                }
                result = state.hiddenList.find(finder);
                if (result) {
                    commit('changeResult', result);
                    return result;
                }

                const item = {
                    ...data,
                    totalMoneyCent: data.moneyCent,
                    totalMoneyMilli: data.moneyMilli,
                    checked: data.moneyMilli <= 0,
                    poiList: null,
                };

                commit('changeResult', item);
                commit('changeHiddenList', state.hiddenList.concat(item));

                return item; // item 此时已 observed
            },
            (err) => {
                commit('changeSearching', false);
                throw err;
            },
        );
    },
};

const getters = {
    amountList(state, _, rootState) {
        const { baseFeeTypeItem } = rootState.apply;
        if (!baseFeeTypeItem) {
            return [];
        }

        if (baseFeeTypeItem.amountGatherType === 1) {
            // 按结算设置汇总
            return state.settleAmountList;
        } else {
            return state.monthAmountList;
        }
    },
    // 获取发票所有的城市名字
    amountCityNameByMonth(state, _, rootState) {
    // showSellerCompanyList为false的时候,获取当前月结算的城市名称
    // const { monthAmountList, settleAmountList } = state;
        const amountByCity = {};
        // 按照城市来分组
        const checkedList = _.checkedAmountList;
        const { companySelected } = rootState.apply;
        if (companySelected === 0) {
            checkedList.forEach((item) => {
                let processList = [];
                if (item.adDetails) {
                    // 如果是选择明细
                    const list = item.adDetails;
                    processList = list.filter((el) => el.checked);
                } else if (item.poiAmountList) {
                    processList = item.poiAmountList;
                } else {
                    processList = item.sellerCompanyList || [];
                }
                processList.forEach((el) => {
                    amountByCity[el.ownerShip] = el;
                });
            });
            return Object.values(amountByCity)
                .map((el) => el.companyName)
                .join(',');
        }

        // 单一选择某些城市,从sellerCompanyList 中取出对应的城市就行
        checkedList.forEach((item) => {
            let target;
            if (item.adDetails) {
                // 如果是选择明细
                const list = item.adDetails;
                const targetList = list
                    .filter((el) => el.checked)
                    .filter((el) => el.ownerShip === companySelected);
                if (!targetList.length) return;
                target = targetList[0];
            } else if (item.poiAmountList) {
                const list = item.poiAmountList;
                const targetList = list.filter(
                    (el) => el.ownerShip === companySelected,
                );
                if (!targetList.length) return;
                target = targetList[0];
            } else {
                target = item.sellerCompanyList.find(
                    (el) => el.ownerShip === companySelected,
                );
            }
            if (!target) return;
            if (!amountByCity[target.ownerShip]) {
                amountByCity[target.ownerShip] = {
                    moneyCent: 0,
                    moneyMilli: 0,
                };
            }
            amountByCity[target.ownerShip] = target;
        });
        return Object.values(amountByCity)
            .map((el) => el.companyName)
            .join(',');
    },
    amountByCity(state, _, rootState) {
        const { companySelected } = rootState.apply;
        let totalMoneyCent = 0;
        let totalMoneyMilli = 0;
        const amountByCity = {};
        // 按照城市来分组
        const checkedList = _.checkedAmountList;
        if (companySelected === 0) {
            // 全部选择  那么按照城市分组之后累加即可
            checkedList.forEach((item) => {
                let processList = [];
                if (item.adDetails) {
                    // 如果是选择明细
                    processList = item.adDetails.filter((el) => el.checked);
                } else if (item.poiAmountList) {
                    // 如果是选择明细
                    processList = item.poiAmountList;
                } else {
                    processList = item.sellerCompanyList || [];
                }
                processList.forEach((el) => {
                    if (!amountByCity[el.ownerShip]) {
                        amountByCity[el.ownerShip] = {
                            moneyCent: 0,
                            moneyMilli: 0,
                        };
                    }
                    const old = amountByCity[el.ownerShip];
                    const newMoneyCent = el.amountCent || el.moneyCent || 0;
                    const newMoneyMilli = el.amountMilli || el.moneyMilli || 0;
                    amountByCity[el.ownerShip] = {
                        ...el,
                        moneyCent: old.moneyCent + newMoneyCent,
                        moneyMilli: old.moneyMilli + newMoneyMilli,
                    };
                    totalMoneyCent += newMoneyCent;
                    totalMoneyMilli += newMoneyMilli;
                });
            });
            // 返回数组格式
            return {
                totalMoneyCent,
                totalMoneyMilli,
                values: Object.values(amountByCity),
            };
        }

        // 单一选择某些城市,从sellerCompanyList 中取出对应的城市就行
        checkedList.forEach((item) => {
            let target;
            if (item.adDetails) {
                // 如果是选择明细
                const list = item.adDetails;
                const targetList = list
                    .filter((el) => el.checked)
                    .filter((el) => el.ownerShip === companySelected);
                if (!targetList.length) return;
                target = {
                    ...targetList[0],
                    moneyCent: targetList.reduce(
                        (pre, el) => pre + el.amountCent || el.moneyCent || 0,
                        0,
                    ),
                    moneyMilli: targetList.reduce(
                        (pre, el) => pre + el.amountMilli || el.moneyMilli || 0,
                        0,
                    ),
                };
            } else if (item.poiAmountList) {
                const targetList = item.poiAmountList.filter(
                    (el) => el.ownerShip === companySelected,
                );
                if (!targetList.length) return;
                target = {
                    ...targetList[0],
                    moneyCent: targetList.reduce(
                        (pre, el) => pre + el.amountCent || el.moneyCent || 0,
                        0,
                    ),
                    moneyMilli: targetList.reduce(
                        (pre, el) => pre + el.amountMilli || el.moneyMilli || 0,
                        0,
                    ),
                };
            } else {
                target = item.sellerCompanyList.find(
                    (el) => el.ownerShip === companySelected,
                );
            }
            if (!target) return;
            if (!amountByCity[target.ownerShip]) {
                amountByCity[target.ownerShip] = {
                    moneyCent: 0,
                    moneyMilli: 0,
                };
            }
            const old = amountByCity[target.ownerShip];
            const newMoneyCent = target.moneyCent;
            const newMoneyMilli = target.moneyMilli;

            amountByCity[target.ownerShip] = {
                ...target,
                moneyCent: old.moneyCent + newMoneyCent,
                moneyMilli: old.moneyMilli + newMoneyMilli,
            };
            totalMoneyCent += newMoneyCent;
            totalMoneyMilli += newMoneyMilli;
        });
        // 返回数组格式
        return {
            totalMoneyCent,
            totalMoneyMilli,
            values: Object.values(amountByCity),
        };
    },
    checkedAmountList(state, { amountList }) {
        return amountList.concat(state.hiddenList).filter((x) => x.checked);
    },
    checkedAmountCount(state, { checkedAmountList }) {
        return checkedAmountList.length;
    },
    checkedMoneyCent(state, { checkedAmountList }) {
        return checkedAmountList.reduce((acc, cur) => acc + cur.moneyCent, 0);
    },
    checkedMoneyMilli(state, { checkedAmountList }) {
        return checkedAmountList.reduce((acc, cur) => acc + cur.moneyMilli, 0);
    },
};

export default {
    namespaced: true,
    state: initialState,
    mutations,
    actions,
    getters,
};
