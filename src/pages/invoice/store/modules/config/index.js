/**
 * module config
 */

import axios from 'axios';
/* eslint-disable import/no-unresolved, import/extensions */
import request from '$invoice/utils/request';
import { getCookieValue } from '$lib/utils';
import { toast } from '@roo/roo-vue';

const { CancelToken } = axios;

const initialState = {
    feeTypeLoading: false,
    feeTypeList: [],

    contractLoading: false,
    contractList: [],

    // 合同ID
    contractId: null,
    // 合同名称
    contractAName: null,
    // 合同关联门店
    poiInfos: [],

    showTip: true,
    // 2022 - 是否使用新的发票拆分方案
    useNewSplit: false,

    // 2023 - 销项开票优化一期灰度方案
    // 二期灰度逻辑
    isHit: false,

    fuzzyStr: '',
    // 是否单店模式
    isSinglePoi: getCookieValue('wmPoiId') !== '-1',

    /** 是否命中数电发票灰度 */
    isShudianHit: false,
};

const mutations = {
    changeUseNewSplit(state, isGrey) {
        state.useNewSplit = isGrey;
    },
    changeFeeTypeLoading(state, loading) {
        state.feeTypeLoading = !!loading;
    },
    changeFeeTypeList(state, list) {
        state.feeTypeList = list || [];
    },
    changeContractLoading(state, loading) {
        state.contractLoading = !!loading;
    },
    changeContractList(state, list) {
        state.contractList = list || [];
    },
    changeContractId(state, id) {
        state.contractId = id;
    },
    changeContractAName(state, name) {
        state.contractAName = name;
    },
    changeShowTip(state, show) {
        state.showTip = !!show;
    },
    changeIsHit(state, isGrey) {
        state.isHit = isGrey;
    },
    changeIsShudianHit(state, isGray) {
        state.isShudianHit = isGray;
    },
    changePoiInfos(state, poiList) {
        state.poiInfos = poiList;
    },
    changeFuzzyStr(state, str) {
        state.fuzzyStr = str;
    },
    reset(state) {
        state.feeTypeLoading = false;
        state.feeTypeList = [];

        state.contractLoading = false;
        state.contractList = [];

        state.contractId = null;
        state.contractAName = null;
        state.fuzzyStr = '';
    },
};

let source1 = null;
function getFeeTypeList(isSinglePoi) {
    if (source1) {
        source1.cancel();
        source1 = null;
    }

    source1 = CancelToken.source();
    const cancelToken = source1.token;

    const requestUrl = !isSinglePoi ? '/finance/invoice/api/output/waimai/c/getConfigs' : '/finance/invoice/api/common/application/r/queryConfigs';

    return request.get(requestUrl, {
        // cancelToken,
    });
}

let source2 = null;
function getContractList(isSinglePoi) {
    if (source2) {
        source2.cancel();
        source2 = null;
    }

    source2 = CancelToken.source();
    const cancelToken = source2.token;

    const requestUrl = !isSinglePoi ? '/finance/invoice/api/output/waimai/c/queryContractList' : '/finance/invoice/api/common/application/r/queryContractInfo';

    return request.get(requestUrl, {
        // cancelToken,
    });
}

function getPoiInfos(params) {
    return request.get('/finance/invoice/api/output/waimai/c/poiListInfo', { params });
}

// 多店维度下是否使用新的发票拆分方案
function getGreyInfoApi() {
    return request.get('/finance/invoice/api/pc/settle/switch');
}

function getOutputSwitch(params) {
    return request.get('/finance/invoice/api/waimai/output/second/switch', params);
}

function getContractInfo(params) {
    return request.get('/finance/invoice/api/output/waimai/c/poiInfosFuzzily', { params });
}

const actions = {
    // 是否使用新模版，全局请求一次，各个页面可复用
    getGreyInfo({ commit }) {
        return getGreyInfoApi().then((res) => {
            const { code, data } = res.data;
            if (code === 0) {
                commit('changeUseNewSplit', data);
            }
            return data;
        }).catch(() => {});
    },
    loadFeeTypeList({ state, commit }) {
        if (state.feeTypeList.length > 0) {
            return state.feeTypeList;
        }

        commit('changeFeeTypeLoading', true);

        return getFeeTypeList(state.isSinglePoi)
            .then((res) => {
                commit('changeFeeTypeLoading', false);

                const { code, msg, data } = res.data;

                if (code !== 0) {
                    throw new Error(msg);
                }

                commit('changeFeeTypeList', data);

                if (data.length > 0) {
                    commit('apply/changeBaseFeeTypeItem', data[0], { root: true });
                }

                return data;
            });
    },
    loadContractList({ state, commit }) {
        if (state.contractList > 0) {
            return state.contractList;
        }

        commit('changeContractLoading', true);

        return getContractList(state.isSinglePoi)
            .then((res) => {
                commit('changeContractLoading', false);

                const { code, msg, data } = res.data;

                if (code !== 0) {
                    throw new Error(msg);
                }

                commit('changeContractList', data);

                if (data.length === 1) {
                    const { contractId, contractAName } = data[0];
                    commit('config/changeContractId', contractId, { root: true });
                    commit('config/changeContractAName', contractAName, { root: true });
                } else if (data.length === 0) {
                    throw new Error('缺少合同信息');
                }
                return data;
            }, (err) => {
                commit('changeLoading', false);
                throw err;
            });
    },
    fetchGreyInfo({ commit }) {
        return getOutputSwitch().then((res) => {
            const { code, data } = res.data;
            if (code === 0) {
                commit('changeIsHit', data);
            }
            return data;
        }).catch(() => {});
    },
    fetchShudianGrayInfo({ commit }) {
        return request.get('/finance/invoice/api/common/application/r/digitalSwitch').then((res) => {
            const { code, data } = res.data;
            if (code === 0) {
                commit('changeIsShudianHit', data);
            }
            return data;
        }).catch(() => {});
    },
    fetchPoiInfos({ state, commit }) {
        const params = { contractId: state.contractId };

        return getPoiInfos(params).then(
            (res) => {
                const { code, msg, data } = res.data;

                if (code !== 0 || !data) {
                    const errorMsg = (code !== 0 && msg) || '获取门店信息失败';
                    toast(errorMsg, 'error');
                    return;
                }

                commit('changePoiInfos', data.poiList || []);
            },
            (err) => {
                // 请求层做了错误上报，这里不抛出错误
                console.log('err', err);
            },
        );
    },
    fetchContractInfo({ state, commit }) {
        const params = { fuzzy: state.fuzzyStr };
        return getContractInfo(params).then(
            (res) => {
                commit('contractLoading', false);

                const { code, msg, data } = res.data;

                if (code !== 0) {
                    throw new Error(msg);
                }

                commit('changeContractList', data || []);
                // commit('changePageNo', data.pageNo);
                // commit('changeTotal', data.length || 0);

                if (data.length === 1) {
                    const { contractAName, contractId } = data[0];
                    commit('changeContractAName', contractAName);
                    commit('changeContractId', contractId);
                } else if (data.length === 0) {
                    throw new Error('缺少合同信息');
                }
            },
            (err) => {
                commit('contractLoading', false);
                throw err;
            },
        );
    },
};

const getters = {
    partnerType(state) {
        const { feeTypeList } = state;

        if (feeTypeList.length > 0) {
            return feeTypeList[0].partnerType;
        }

        return null;
    },
};

export default {
    namespaced: true,
    state: initialState,
    mutations,
    actions,
    getters,
};
