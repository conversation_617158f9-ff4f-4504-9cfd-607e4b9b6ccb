/* eslint-disable import/no-unresolved, import/extensions */
import request from '$invoice/utils/request';
import { getCookieValue } from '$lib/utils';
import SOURCE from '$lib/sourceEnum';

const initialState = {
    editItemInfo: {},
    isChecking: false,
    isRejected: false,
    promotBar: '',
    titleInfoList: [],
    titleInfos: [],
    titleInfoLoading: false,
    invoiceTitleTotal: 0,
    poiRelatedToTitle: {},
    editInvoiceTitleItem: {},
};

const mutations = {
    changeEditItemInfo(state, info) {
        state.editItemInfo = Object.assign({}, info);
    },
    changeEditInvoiceTitleItem(state, item) {
        state.editInvoiceTitleItem = item;
    },
    changeIsChecking(state, checking) {
        state.isChecking = checking;
    },
    changeIsRejected(state, rejected) {
        state.isRejected = rejected;
    },
    changePromotBar(state, bar) {
        state.promotBar = bar;
    },
    changeTitleInfoList(state, list) {
        state.titleInfoList = list;
    },
    changeTitleInfos(state, infos) {
        state.titleInfos = infos;
    },
    changeTitleInfoLoading(state, loading) {
        state.titleInfoLoading = loading;
    },
    changeInvoiceTitleTotal(state, total) {
        state.invoiceTitleTotal = total;
    },
    changePoiRelatedToTitle(state, poiTitle) {
        state.poiRelatedToTitle = poiTitle;
    },
    reset(state) {
        state.editItemInfo = {};
        state.isChecking = false;
        state.isRejected = false;
        state.pageNo = 1;
        state.promotBar = '';
        state.titleInfoList = [];
        state.titleInfoLoading = false;
        state.invoiceTitleTotal = 0;
        state.titleInfos = [];
        state.poiRelatedToTitle = {};
    },
};

function getTitleInfoList(params) {
    return request.get('/finance/invoice/api/output/waimai/q/getAllInvoiceTitle', { params });
}

function getPromotBar() {
    return request.get('/finance/invoice/api/output/waimai/c/getPromptBar', {});
}

function getPoiRelatedToTitle(params) {
    return request.post('/finance/invoice/api/output/waimai/c/poiRelatedToTitle', params);
}

const actions = {
    fetchTitleInfoList({ commit, rootState }, { pageNo = 1, fuzzy = '' }) {
        // commit('changeTitleInfoLoading', true);

        const { baseFeeTypeItem } = rootState.apply;
        const { contractId } = rootState.config;
        const curSource = getCookieValue('source') || SOURCE.WAIMAI;

        // 一般情况（比如：骑手商城）不需要校验合同ID，当source指向外卖时校验合同ID不能为空
        if (!baseFeeTypeItem || (curSource === SOURCE.WAIMAI && !contractId)) {
            return [];
        }

        if (pageNo === 1) {
            commit('changeTitleInfoList', []);
        }

        const params = {
            contractId,
            partnerType: baseFeeTypeItem.partnerType,
            qualificationType: baseFeeTypeItem.qualificationType,
            qualificationUnion: 1,
            pageNo,
            pageSize: 20,
            fuzzy,
        };
        return getTitleInfoList(params).then((res) => {
            // commit('changeTitleInfoLoading', false);
            if (res == null) {
                return [];
            }

            const { data } = res.data;


            // commit('changePageNo', data.pageNo);
            if (data) {
                commit('changeTitleInfoList', data.titleList);
                commit('changeInvoiceTitleTotal', data.total);
                commit('changeIsChecking', data.status === 1);
                commit('changeIsRejected', data.status === 2);
            }


            const map = Object.create(null);
            (rootState.apply.qualifications || []).forEach((q) => {
                map[q.id] = q;
            });

            return data;
        }, (err) => {
            // commit('changeTitleInfoLoading', false);
            throw err;
        });
    },
    fetchPromotBar({ commit }) {
        return getPromotBar().then(
            (res) => {
                const { data } = res.data;


                if (data) {
                    commit('changePromotBar', data);
                }
            },
            (err) => {
                throw err;
            },
        );
    },
    fetchPoiRelatedToTitle({ commit, state, rootState }) {
        const { isSinglePoi } = rootState.config;
        const { qualifications } = rootState.apply;
        const params = !isSinglePoi ? {
            titleIdList: state.titleInfos,
        } : {
            titleIdList: qualifications.map((q) => q.id),
            requestHitParam: true,
        };
        return getPoiRelatedToTitle(params).then(
            (res) => {
                const { data } = res.data;


                if (data) {
                    commit('changePoiRelatedToTitle', data);
                }
            },
            (err) => { throw err; },
        );
    },
};

export default {
    namespaced: true,
    state: initialState,
    mutations,
    actions,
};
