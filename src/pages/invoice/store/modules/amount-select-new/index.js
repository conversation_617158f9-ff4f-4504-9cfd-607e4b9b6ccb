/* eslint-disable import/no-unresolved, import/extensions */
import request from '$invoice/utils/request';
import owlReport from '$utils/owlReport';
import { copySettleList } from '$utils/util';
import { toast } from '@roo/roo-vue';

// settleAmountList中每一项的数据结构
// {
//     "settleId":1112,
//     "settleName":"abc名称",
//     "moneyCent":10000,
//     "partnerList":[{
//         "partnerId":600149,
//         "partnerName":"pName中文",
//         "partnerMoneyCent":5000,
//         "sellerAmountList":[{
//             "ownerShip":1001,
//             "companyName":"北京三快在线科技有限公司",
//             "sellerMoneyCent":2000,
//         },{
//             "ownerShip":1058,
//             "companyName":"上海三快智送科技有限公司",
//             "sellerMoneyCent":3000,
//         }]
//     },{
//         "partnerId":12324,
//         "partnerName":"pName中文4",
//         "partnerMoneyCent":5000,
//         "sellerAmountList":[{
//             "ownerShip":1001,
//             "sellerMoneyCent":3000,
//             "companyName":"北京三快在线科技有限公司",
//         },{
//             "ownerShip":1058,
//             "companyName":"上海三快智送科技有限公司",
//             "sellerMoneyCent":2000,
//         }]
//     }]
// }

const initialState = {
    dateFrom: null,
    dateTo: null,
    total: 0,
    pageNo: 1,
    pageSize: 20,
    // 基于结算维度的查询结果
    settleAmountList: [],
    // 选中的总金额
    totalSelectedAmount: 0,
    // 第三部分配金额的结构
    checkedAmountList: [],
    // 基于门店维度的查询结果
    partnerAmountList: [],
};

// 存储计算属性
const getters = {
    // 当前已加载条数
    currentCount(state) {
        return state.settleAmountList.length;
    },
    // 当前已选中条数
    checkedAmountCount(state) {
        let checkedCount = 0;
        state.settleAmountList.forEach(ele => {
            if (ele.selected) {
                checkedCount++;
            }
        });
        return checkedCount;
    },
    currentPoiCount(state) {
        return state.partnerAmountList.length;
    },
    // 按门店查询结果中，选中数量
    checkedPoiAmountCount(state) {
        let checkedCount = 0;
        state.partnerAmountList.forEach(ele => {
            if (ele.selected) {
                checkedCount++;
            }
        });
        return checkedCount;
    },

    // 当前已加载的结算ID列表，是否全部选中
    isAllChecked(state, self) {
        return self.currentCount === self.checkedAmountCount;
    },
    // 判断第二步中选择的所有门店在第三步中都已被分配
    allPoiIsSelected(state) {
        const list = state.checkedAmountList;
        for (let i = 0; i < list.length; i++) {
            const sList = list[i].settleList;
            for (let j = 0; j < sList.length; j++) {
                const poiList = sList[j].partnerList;
                // 选中个数
                const checkedCount = poiList.filter(ele => ele.checked.length > 0).length;
                // 总门店个数
                const totalPoi = poiList.length;
                if (checkedCount < totalPoi) {
                    return false;
                }
            }
        }
        return true;
    },
};

// settleItem : 某一个结算ID对象
// ownership : 开销方ID
// 修改开销方信息时，重新初始化列表可选金额，包括（结算ID维度，门店维度）
function changeSettleIDByCompany(settleItem, ownership = -1) {
    // 当前结算ID下可开总金额
    let settleIdtotalCent = 0;
    if (settleItem.partnerList && settleItem.partnerList.length > 0) {
        settleItem.partnerList.forEach((element) => {
            // 默认全部勾选
            element.checked = true;
            // 全部开销方下，某门店可开金额
            if (ownership === -1) {
                element.checkedMoney = element.partnerMoneyCent;
            } else {
                // 某个开销方下，某门店可开金额
                if (element.sellerAmountList && element.sellerAmountList.length > 0) {
                    const companyIndex = element.sellerAmountList.findIndex(ele => ele.ownerShip === ownership);
                    if (companyIndex >= 0) {
                        const money = element.sellerAmountList[companyIndex].sellerMoneyCent;
                        // 某开销方下，某门店可开金额
                        element.checkedMoney = money;
                    } else {
                        element.checkedMoney = 0;
                    }
                } else {
                    element.checkedMoney = 0;
                }
            }
            settleIdtotalCent += element.checkedMoney;
            if (element.checkedMoney < 0) {
                // 是否禁止操作
                element.disabled = true;
            } else {
                element.disabled = false;
            }
        });
    }
    // 结算ID下总可开金额
    if (ownership === -1) {
        settleItem.selectedMoneyCent = settleItem.moneyCent;
    } else {
        settleItem.selectedMoneyCent = settleIdtotalCent;
    }
    // 是否禁止用户勾选
    const isNegativeNum = settleItem.selectedMoneyCent < 0;
    settleItem.disabled = isNegativeNum;
    // 负数金额必须勾选
    settleItem.selected = isNegativeNum;
    return settleIdtotalCent;
}

const mutations = {

    setDateFromTo(state, { dateFrom, dateTo }) {
        state.dateFrom = dateFrom;
        state.dateTo = dateTo;
    },

    reset(state) {
        state.dateFrom = null;
        state.dateTo = null;
        state.total = 0;
        state.pageNo = 1;
        state.settleAmountList = [];
        state.partnerAmountList = [];
        state.totalSelectedAmount = 0;
    },
    resetDataList(state) {
        state.total = 0;
        state.pageNo = 1;
        state.settleAmountList = [];
        state.partnerAmountList = [];
        state.totalSelectedAmount = 0;
    },
    changeDateFrom(state, date) {
        state.dateFrom = date;
        state.total = 0;
        state.pageNo = 1;
        state.settleAmountList = [];
        state.totalSelectedAmount = 0;
    },
    changeDateTo(state, date) {
        state.dateTo = date;
        state.total = 0;
        state.pageNo = 1;
        state.settleAmountList = [];
        state.totalSelectedAmount = 0;
    },
    changeTotal(state, total) {
        state.total = total || 0;
    },
    changePageNo(state, no) {
        state.pageNo = no || 1;
    },
    changeSettleAmountList(state, list) {
        if (state.pageNo === 1) {
            state.settleAmountList = list;
        } else {
            state.settleAmountList = state.settleAmountList.concat(list);
        }
    },
    changePartnerAmountList(state, list) {
        if (state.pageNo === 1) {
            state.partnerAmountList = list;
        } else {
            state.partnerAmountList = state.partnerAmountList.concat(list);
        }
    },
    // ownership: 销方公司ID
    changeAmountListByCompany(state, ownership) {
        let totalSelectedMoneyCent = 0;
        state.settleAmountList.forEach(ele => {
            const selectedMoney = changeSettleIDByCompany(ele, ownership);
            if (selectedMoney < 0) {
                totalSelectedMoneyCent += selectedMoney;
            }
        });
        state.totalSelectedAmount = totalSelectedMoneyCent;
    },
    // 更新选中的总金额
    changeTotalSelectedAmount(state, newTotalMoney) {
        state.totalSelectedAmount = newTotalMoney;
    },

    changePoiListBySelect(state, index) {
        const currentPoi = state.partnerAmountList[index];
        const oldSelectedStatus = currentPoi.selected;
        if (oldSelectedStatus) {
            currentPoi.selected = false;
            state.totalSelectedAmount -= currentPoi.unInvoicedAmount;
        } else {
            currentPoi.selected = true;
            state.totalSelectedAmount += currentPoi.unInvoicedAmount;
        }
    },

    changePoiListBySelectAll(state) {
        const isAllCheck = getters.currentPoiCount(state) === getters.checkedPoiAmountCount(state);
        if (isAllCheck) {
            state.partnerAmountList.forEach(ele => {
                if (ele.unInvoicedAmount >= 0) {
                    ele.selected = false;
                    state.totalSelectedAmount -= ele.unInvoicedAmount;
                }
            });
        } else {
            state.partnerAmountList.forEach(ele => {
                if (!ele.selected) {
                    ele.selected = true;
                    state.totalSelectedAmount += ele.unInvoicedAmount;
                }
            });
        }
    },

    // 更新结算ID选中状态，更新选中总金额
    changeListBySelect(state, index) {
        const currentSettle = state.settleAmountList[index];
        const oldSelectedStatus = currentSettle.selected;
        if (oldSelectedStatus) {
            currentSettle.selected = false;
            state.totalSelectedAmount -= currentSettle.selectedMoneyCent;
        } else {
            currentSettle.selected = true;
            state.totalSelectedAmount += currentSettle.selectedMoneyCent;
        }
    },
    // 通过全选按钮更新结算ID选中状态，更新选中金额
    // 两种状态，1、存在没有选中的（需要全部选中）2、全部都是选中的（除了负数的默认勾选之外全部取消勾选）
    changeListBySelectAll(state) {
        const isAllCheck = getters.currentCount(state) === getters.checkedAmountCount(state);
        if (isAllCheck) {
            state.settleAmountList.forEach(ele => {
                if (ele.selectedMoneyCent >= 0) {
                    ele.selected = false;
                    state.totalSelectedAmount -= ele.selectedMoneyCent;
                }
            });
        } else {
            state.settleAmountList.forEach(ele => {
                if (!ele.selected) {
                    ele.selected = true;
                    state.totalSelectedAmount += ele.selectedMoneyCent;
                }
            });
        }
    },
    // 修改门店选择情况
    // poiTemp：弹窗暂存的门店选择情况
    // index：当前操作的结算ID对应在数组中的下标
    // checkedNum： 选择的门店个数
    changeListCheckPoi(state, { poiTemp, index, checkedNum }) {
        if (index >= 0 && index < state.settleAmountList.length) {
            // 拿到操作的结算ID
            const settleObj = state.settleAmountList[index];
            if (settleObj.selected) {
                state.totalSelectedAmount -= settleObj.selectedMoneyCent;
            }
            settleObj.selectedMoneyCent = 0;
            poiTemp.forEach((ele, indx) => {
                settleObj.partnerList[indx].checked = ele.checked;
                if (ele.checked) {
                    settleObj.selectedMoneyCent += ele.checkedMoney;
                }
            });
            if (settleObj.selected) {
                // 如果一个门店都没选，结算ID勾给去掉
                if (checkedNum === 0) {
                    settleObj.selected = false;
                } else {
                    state.totalSelectedAmount += settleObj.selectedMoneyCent;
                }
            }
        }
    },
    // companyList销方公司列表
    // 存储每个销方公司下的结算ID信息
    // ownership: 销方公司， -1代表全部
    getOwnerCompanyList(state, { ownership, poiRelatedToTitle, queryType = 'bySettleId' }) {
        const companyList = [];
        // key:存储公司信息，value: 存储下标
        const companyHash = {};
        // key: 公司-结算ID, value: 数组下标
        const settleHash = {};

        if (queryType === 'bySettleId') {
            state.settleAmountList.forEach((item) => {
                if (item.selected && item.partnerList && item.partnerList.length > 0) {
                    item.partnerList.forEach(poi => {
                        // 如果是选中的门店
                        if (poi.checked) {
                            // 门店下的销方公司信息
                            poi.sellerAmountList.forEach(company => {
                                // 如果当前已经选择了某一个开销方公司，排除其他ownerShip
                                if (ownership !== -1 && ownership !== company.ownerShip) {
                                    return;
                                }
                                // 门店维度结构体，金额是对应这个开销方下的金额
                                const poiItemByCompany = {
                                    partnerId: poi.partnerId,
                                    partnerName: poi.partnerName,
                                    moneyCent: company.sellerMoneyCent,
                                    // 默认没有分配的门店，已经分配的门店会打上invoiceId
                                    checked: '',
                                };
                                // 获取公司信息是否已经存储
                                if (companyHash[company.ownerShip] === undefined) {
                                    companyHash[company.ownerShip] = companyList.length;
                                    companyList.push({
                                        ownerShip: company.ownerShip,
                                        companyName: company.companyName,
                                        moneyCent: 0,
                                        settleList: [],
                                    });
                                }
                                // 获取公司
                                const com = companyList[companyHash[company.ownerShip]];
                                // 销方公司下的总金额
                                com.moneyCent += company.sellerMoneyCent;
                                // 更新结算ID列表
                                if (settleHash[`${company.ownerShip}-${item.settleId}`] > -1) {
                                    // 已有结算ID内容
                                    const sIndex = settleHash[`${company.ownerShip}-${item.settleId}`];
                                    const settleItem = com.settleList[sIndex];
                                    // 添加门店
                                    settleItem.partnerList.push(poiItemByCompany);
                                    // 更新结算ID下金额
                                    settleItem.moneyCent += company.sellerMoneyCent;
                                } else {
                                    // 新增结算ID
                                    settleHash[`${company.ownerShip}-${item.settleId}`] = com.settleList.length;
                                    // 添加抬头关联门店信息（对第三步门店进行默认勾选）
                                    if (poiRelatedToTitle) {
                                        com.poiRelatedToTitle = poiRelatedToTitle;
                                    }
                                    com.settleList.push({
                                        settleId: item.settleId,
                                        settleName: item.settleName,
                                        moneyCent: company.sellerMoneyCent,
                                        partnerList: [poiItemByCompany],
                                    });
                                }
                            });
                        }
                    });
                }
            });
        } else {
            state.partnerAmountList.forEach((partner) => {
                if (partner.selected && partner.partnerSettleAmountList && partner.partnerSettleAmountList.length > 0) {
                    partner.partnerSettleAmountList.forEach((settle => {
                        if (settle.ownerShipAmountList && settle.ownerShipAmountList.length > 0) {
                            settle.ownerShipAmountList.forEach((company) => {
                                const poiItemByCompany = {
                                    partnerId: partner.partnerId,
                                    partnerName: partner.partnerName,
                                    moneyCent: company.unInvoicedOwnerShipAmount,
                                    // 默认没有分配的门店，已经分配的门店会打上invoiceId
                                    checked: '',
                                };

                                if (companyHash[company.ownerShip] === undefined) {
                                    companyHash[company.ownerShip] = companyList.length;
                                    companyList.push({
                                        ownerShip: company.ownerShip,
                                        companyName: company.companyName,
                                        moneyCent: 0,
                                        settleList: [],
                                    });
                                }

                                const com = companyList[companyHash[company.ownerShip]];

                                com.moneyCent += company.unInvoicedOwnerShipAmount;

                                if (settleHash[`${company.ownerShip}-${settle.settleId}`] > -1) {
                                    // 已有结算ID内容
                                    const sIndex = settleHash[`${company.ownerShip}-${settle.settleId}`];
                                    const settleItem = com.settleList[sIndex];
                                    // 添加门店
                                    settleItem.partnerList.push(poiItemByCompany);
                                    // 更新结算ID下金额
                                    settleItem.moneyCent += company.unInvoicedOwnerShipAmount;
                                } else {
                                    // 新增结算ID
                                    settleHash[`${company.ownerShip}-${settle.settleId}`] = com.settleList.length;
                                    // 添加抬头关联门店信息（对第三步门店进行默认勾选）
                                    if (poiRelatedToTitle) {
                                        com.poiRelatedToTitle = poiRelatedToTitle;
                                    }
                                    com.settleList.push({
                                        settleId: settle.settleId,
                                        settleName: settle.settleName,
                                        moneyCent: company.unInvoicedOwnerShipAmount,
                                        partnerList: [poiItemByCompany],
                                    });
                                }
                            });
                        }
                    }));
                }
            });
        }
        console.log(JSON.stringify(companyList));
        state.checkedAmountList = companyList;
    },
    // 更改checkAmountList内部partnerList默认选中情况
    // !!!仅在灰度命中且splitContent和checkAmountList全部遍历后调用一次
    changeDefaultCheckedAmountList(state, newCheckAmountList) {
        state.checkedAmountList = newCheckAmountList;
    },
    // 更新某个开销方下的settleList
    changeCheckedAmountList(state, { ownership, settleList }) {
        const indx = state.checkedAmountList.findIndex(ele => ele.ownerShip === ownership);
        if (indx > -1) {
            state.checkedAmountList[indx].settleList = copySettleList(settleList);
        }
    },
    // 删除某个开销方下，某张发票的标记
    deletePoiCheckStatus(state, { ownership, flag }) {
        const indx = state.checkedAmountList.findIndex(ele => ele.ownerShip === ownership);
        if (indx > -1) {
            const settleList = state.checkedAmountList[indx].settleList;
            settleList.forEach(settle => {
                settle.partnerList.forEach(poi => {
                    if (poi.checked === flag) {
                        poi.checked = '';
                    }
                });
            });
            state.checkedAmountList[indx].settleList = copySettleList(settleList);
        }
    },
};


const actions = {
    // 新版根据结算维度查询金额
    fetchAmountListBySettleId({ state, commit, rootState },
        {
            pageNum = 1, dateFrom, dateTo, settleIdListStr = '',
        },
    ) {
        const { baseFeeTypeItem } = rootState.apply;
        if (!baseFeeTypeItem) {
            throw new TypeError('请选择明细项目');
        }

        const { config, apply } = rootState;
        const { contractId } = config;
        const { pageSize } = state;
        const {
            baseFeeType,
            // partnerType,
            // amountGatherType,
            // qualificationType,
        } = apply.baseFeeTypeItem;
        const { ownership } = apply; // 保留原逻辑，新交互不支持选择开销方，因此这里事实上只有 0

        // 查询状态下，清空之前的list
        if (pageNum === 1) {
            commit('changeTotalSelectedAmount', 0);
        }

        const params = {
            invoiceSource: 113,
            contractId,
            baseFeeType,
            dateFrom,
            dateTo,
            pageNo: pageNum,
            pageSize,
            settleIds: settleIdListStr,
            ownerShip: ownership,
        };

        // 初始化list结构
        function settleAmountMapper(settleItem) {
            if (settleItem.partnerList && settleItem.partnerList.length > 0) {
                settleItem.partnerList.forEach((element) => {
                    // 是否选中
                    element.checked = true;
                    element.checkedMoney = element.partnerMoneyCent;
                    if (element.partnerMoneyCent < 0) {
                        // 是否禁止操作
                        element.disabled = true;
                    } else {
                        element.disabled = false;
                    }
                });
            }
            const moneyIsNegative = settleItem.moneyCent < 0;
            // 负数金额默认选中
            if (moneyIsNegative) {
                commit('changeTotalSelectedAmount', state.totalSelectedAmount + settleItem.moneyCent);
            }
            // 外层的选择只是选择金额，跟门店选择的状态无关，所以没有半选状态
            return {
                ...settleItem,
                // 可选金额，以分为单位保存
                selectedMoneyCent: settleItem.moneyCent,
                // 是否勾选该结算ID下选中金额到下一步
                selected: moneyIsNegative,
                // 是否禁止操作
                disabled: moneyIsNegative,
            };
        }

        const requestUrl = '/finance/invoice/api/output/waimai/c/settle/amount';

        return request.get(requestUrl, { params }).then(
            (res) => {
                const { code, msg, data } = res.data;
                // 后端会返回异常情况的msg提示
                if (code !== 0) {
                    toast.error(msg, 1000);
                } else {
                    commit('changeTotal', data.totalCount);
                    commit('changePageNo', pageNum);
                    commit('setDateFromTo', { dateFrom, dateTo });
                    const { settleAmountList } = data;
                    if (settleAmountList) {
                        const processList = settleAmountList.map(settleAmountMapper);
                        commit(
                            'changeSettleAmountList',
                            processList,
                        );
                    }
                }
            },
        );
    },

    // 新版根据门店维度查询金额
    fetchAmountListByPoiId({ state, commit, rootState },
        {
            pageNum = 1, dateFrom, dateTo, poiIdListStr = '',
        }) {
        const { baseFeeTypeItem } = rootState.apply;
        if (!baseFeeTypeItem) {
            throw new TypeError('请选择明细项目');
        }

        const { config, apply } = rootState;
        const { contractId } = config;
        const { pageSize } = state;
        const {
            baseFeeType,
            // partnerType,
            // amountGatherType,
            // qualificationType,
        } = apply.baseFeeTypeItem;
        const { ownership } = apply; // 保留原逻辑，新交互不支持选择开销方，因此这里事实上只有 0

        // 查询状态下，清空之前的list
        if (pageNum === 1) {
            commit('changeTotalSelectedAmount', 0);
        }

        const params = {
            invoiceSource: 113,
            contractId,
            baseFeeType,
            dateFrom,
            dateTo,
            pageNo: pageNum,
            pageSize,
            partnerIds: poiIdListStr,
            ownerShip: ownership,
        };

        const requestUrl = '/finance/invoice/api/output/waimai/c/partner/amount';

        return request.get(requestUrl, { params }).then(
            (res) => {
                const { code, msg, data } = res.data;
                // 后端会返回异常情况的msg提示
                if (code !== 0) {
                    toast.error(msg, 1000);
                } else {
                    commit('changeTotal', data.totalCount);
                    commit('changePageNo', pageNum);
                    commit('setDateFromTo', { dateFrom, dateTo });
                    const { partnerAmountList } = data;
                    partnerAmountList.forEach(item => {
                        // 负值默认选中
                        item.selected = item.unInvoicedAmount < 0;
                        item.disabled = item.unInvoicedAmount < 0;

                        // 负数金额默认选中
                        if (item.unInvoicedAmount < 0) {
                            commit('changeTotalSelectedAmount', state.totalSelectedAmount + item.unInvoicedAmount);
                        }
                    });
                    commit(
                        'changePartnerAmountList',
                        partnerAmountList,
                    );
                }
            },
        );
    },

    // 查询全部列表
    // 查询结算ID维度列表
    // 查询门店ID维度列表
    fetchAmountList({ state, commit, rootState }, { pageNum = 1, settleId = -1, partnerId = -1 }) {
        const { baseFeeTypeItem } = rootState.apply;
        if (!baseFeeTypeItem) {
            throw new TypeError('请选择明细项目');
        }
        const { config, apply } = rootState;
        const { contractId } = config;
        const { pageSize } = state;
        const {
            baseFeeType,
            partnerType,
            amountGatherType,
            qualificationType,
        } = apply.baseFeeTypeItem;
        const { ownership } = apply;

        // 查询状态下，清空之前的list
        if (pageNum === 1) {
            commit('changeTotalSelectedAmount', 0);
        }

        const params = {
            contractId,
            baseFeeType,
            partnerType,
            amountGatherType,
            qualificationType,
            pageNo: pageNum,
            pageSize,
            settleId,
            partnerId,
            ownerShip: ownership,
        };

        if (state.dateFrom) {
            params.dateFrom = state.dateFrom;
        }
        if (state.dateTo) {
            params.dateTo = state.dateTo;
        }

        // 初始化list结构
        function settleAmountMapper(settleItem) {
            if (settleItem.partnerList && settleItem.partnerList.length > 0) {
                settleItem.partnerList.forEach((element) => {
                    // 是否选中
                    element.checked = true;
                    element.checkedMoney = element.partnerMoneyCent;
                    if (element.partnerMoneyCent < 0) {
                        // 是否禁止操作
                        element.disabled = true;
                    } else {
                        element.disabled = false;
                    }
                });
            }
            const moneyIsNegative = settleItem.moneyCent < 0;
            // 负数金额默认选中
            if (moneyIsNegative) {
                commit('changeTotalSelectedAmount', state.totalSelectedAmount + settleItem.moneyCent);
            }
            // 外层的选择只是选择金额，跟门店选择的状态无关，所以没有半选状态
            return {
                ...settleItem,
                // 可选金额，以分为单位保存
                selectedMoneyCent: settleItem.moneyCent,
                // 是否勾选该结算ID下选中金额到下一步
                selected: moneyIsNegative,
                // 是否禁止操作
                disabled: moneyIsNegative,
            };
        }

        const requestUrl = '/finance/invoice/api/output/waimai/c/amount';

        return request.get(requestUrl, { params }).then(
            (res) => {
                const { code, msg, data } = res.data;
                // 后端会返回异常情况的msg提示
                if (code !== 0) {
                    toast.error(msg, 1000);
                } else {
                    commit('changeTotal', data.totalCount);
                    commit('changePageNo', pageNum);
                    const { settleAmountList } = data;
                    if (settleAmountList) {
                        const processList = settleAmountList.map(settleAmountMapper);
                        commit(
                            'changeSettleAmountList',
                            processList,
                        );
                    }
                }
            },
        ).catch((err) => {
            // 后端说这个接口可能存在查询超时问题，上线后统计看看
            owlReport('请求结算ID列表接口异常', err);
        });
    },
};

export default {
    namespaced: true,
    state: initialState,
    mutations,
    actions,
    getters,
};
