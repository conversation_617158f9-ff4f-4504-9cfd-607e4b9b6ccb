
import axios from 'axios';
/* eslint-disable import/no-unresolved, import/extensions */
import request from '$invoice/utils/request';

const { CancelToken } = axios;

const initialState = {
    loading: false,

    invoiceSendType: 1, // 可选项

    receiver: '',
    provinceCode: '',
    expressProvince: '',
    cityCode: '',
    expressCity: '',
    districtCode: '',
    expressDistrict: '',
    address: '',

    phoneNo: '',
    telNo: '',

    receiverEmail: '',
};

const mutations = {
    changeLoading(state, loading) {
        state.loading = loading;
    },
    changeInvoiceSendType(state, type) {
        state.invoiceSendType = type || 1;
    },
    changeReceiver(state, receiver) {
        state.receiver = receiver || '';
    },
    changeProvince(state, { code, name }) {
        state.provinceCode = code || '';
        state.expressProvince = name || '';
    },
    changeCity(state, { code, name }) {
        state.cityCode = code || '';
        state.expressCity = name || '';
    },
    changeDistrict(state, { code, name }) {
        state.districtCode = code || '';
        state.expressDistrict = name || '';
    },
    changeAddress(state, address) {
        state.address = address || '';
    },
    changePhoneNo(state, no) {
        state.phoneNo = no;
    },
    changeTelNo(state, no) {
        state.telNo = no;
    },
    changeEmail(state, email) {
        state.receiverEmail = email || '';
    },
    reset(state) {
        state.invoiceSendType = 1;

        state.receiver = '';
        state.provinceCode = '';
        state.expressProvince = '';
        state.cityCode = '';
        state.expressCity = '';
        state.districtCode = '';
        state.expressDistrict = '';
        state.address = '';

        state.phoneNo = '';
        state.telNo = '';

        state.receiverEmail = '';
    },
};

let source = null;
function getAddress(params) {
    if (source) {
        source.cancel();
        source = null;
    }

    source = CancelToken.source();
    const cancelToken = source.token;

    return request.get('/finance/invoice/api/common/application/r/queryAddress', {
        params,
        cancelToken,
    });
}

const actions = {
    fetchAddress({ commit, rootState }) {
        commit('changeLoading', true);

        const { config, apply } = rootState;
        const { contractId } = config;
        const { partnerType } = apply.baseFeeTypeItem;

        const params = {
            contractId,
            partnerType,
        };

        return getAddress(params)
            .then((res) => {
                commit('changeLoading', false);

                const { code, msg, data } = res.data;
                if (code !== 0) {
                    throw new Error(msg);
                }

                commit('changeReceiver', data.receiver);
                commit('changeProvince', {
                    code: data.provinceCode,
                    name: data.expressProvince,
                });
                commit('changeCity', {
                    code: data.cityCode,
                    name: data.expressCity,
                });
                commit('changeDistrict', {
                    code: data.districtCode,
                    name: data.expressDistrict,
                });
                commit('changeAddress', data.addr);
                commit('changePhoneNo', data.phoneNo);
                commit('changeTelNo', data.telNo);
                commit('changeEmail', data.receiverEmail);
            }, (err) => {
                commit('changeLoading', false);
                throw err;
            });
    },
};

export default {
    namespaced: true,
    state: initialState,
    mutations,
    actions,
};
