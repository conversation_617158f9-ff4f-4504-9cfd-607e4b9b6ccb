/**
 * module 保存合同和资质列表以供选择
 */

import axios from 'axios';
/* eslint-disable import/no-unresolved, import/extensions */
import request from '$invoice/utils/request';
import { getCookieValue } from '$lib/utils';
import SOURCE from '$lib/sourceEnum';

const { CancelToken } = axios;

const initialState = {
    loading: false,
    qualificationList: [],
};

const mutations = {
    changeLoading(state, loading) {
        state.loading = loading;
    },
    changeAccountType(state, type) {
        state.accountType = type;
    },
    changeContractList(state, list) {
        state.contractList = list;
    },
    changeQualificationList(state, list) {
        state.qualificationList = list;
    },
    reset(state) {
        state.loading = false;
        state.qualificationList = [];
    },
};

let source = null;
function getQualificationList(params) {
    if (source) {
        source.cancel();
        source = null;
    }

    source = CancelToken.source();
    const cancelToken = source.token;

    return request.get('/finance/invoice/api/common/qualification/r/list', {
        params,
        // cancelToken,
    });
}

const actions = {
    fetchQualificationList({ commit, rootState }) {
        commit('changeLoading', true);

        const { contractId } = rootState.config;
        const { baseFeeTypeItem } = rootState.apply;
        const curSource = getCookieValue('source') || SOURCE.WAIMAI;

        // 一般情况（比如：骑手商城）不需要校验合同ID，当source指向外卖时校验合同ID不能为空
        if (!baseFeeTypeItem || (curSource === SOURCE.WAIMAI && !contractId)) {
            return [];
        }

        const params = {
            contractId,
            partnerType: baseFeeTypeItem.partnerType,
            qualificationType: baseFeeTypeItem.qualificationType,
        };
        return getQualificationList(params)
            .then((res) => {
                commit('changeLoading', false);

                if (res == null) {
                    return [];
                }

                const { code, msg, data } = res.data;
                if (code !== 0) {
                    throw new Error(msg);
                }

                commit('changeQualificationList', data);

                // 进页面默认刷新一次列表，保留已勾选资质
                const map = Object.create(null);
                (rootState.apply.qualifications || []).forEach((q) => {
                    map[q.id] = q;
                });

                const newQualifications = (data || [])
                    .filter(q => q.status === 1 && map[q.id])
                    .map((q, idx, arr) => ({
                        ...q,
                        changed: false,
                        amountList: map[q.id].amountList || (idx < arr.length - 1 ? ['0.00'] : []),
                    }));

                // 刷新资质列表时
                // 清空选中资质
                commit('apply/changeQualifications', newQualifications, { root: true });

                return data;
            }, (err) => {
                commit('changeLoading', false);
                throw err;
            });
    },
};

export default {
    namespaced: true,
    state: initialState,
    mutations,
    actions,
};
