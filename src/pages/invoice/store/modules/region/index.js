
/* eslint-disable import/no-unresolved, import/extensions */
import request from '$invoice/utils/request';

const initialState = {
    regions: [],
};

/* eslint-disable no-param-reassign */
const mutations = {
    changeRegions(state, regions) {
        state.regions = regions;
    },
    appendChildRegions(state, { path, children }) {
        if ((!Array.isArray(path)) || path.length === 0) {
            return;
        }

        let target = { children: state.regions };
        for (let i = 0; i < path.length; ++i) {
            const p = path[i];

            const item = target.children.find(({ code }) => code === p);

            if (item) {
                target = item;
            } else {
                return;
            }
        }

        if (target) {
            target.children = children;
        }
    },
};
/* eslint-enable no-param-reassign */

const actions = {
    loadRootRegions({ state, commit }) {
        if (state.regions.length > 0) {
            return Promise.resolve(state.regions);
        }

        // /finance/invoice/api/application/r/queryRootRegions
        return request.get('/finance/invoice/api/common/application/r/queryRegions?level=1')
            .then((res) => {
                const { code, msg, data } = res.data;

                if (code !== 0) {
                    throw new Error(msg);
                }

                const rootRegions = (data || []).map(item => ({
                    code: item.code,
                    name: item.name,
                    children: [],
                }));

                commit('changeRegions', rootRegions);
                return rootRegions;
            });
    },
    loadChildRegions({ state, commit }, path) {
        if ((!Array.isArray(path)) || path.length === 0) {
            return Promise.resolve(null);
        }

        let target = { children: state.regions };
        for (let i = 0; i < path.length; ++i) {
            const p = path[i];

            const item = target.children.find(({ code }) => code === p);

            if (item) {
                target = item;
            } else {
                return Promise.reject(new Error('invalid path'));
            }
        }

        if (target.children.length > 0) {
            return Promise.resolve(target.children);
        }

        const level = path.length + 1;
        const parentRegionId = path[path.length - 1];

        // /finance/invoice/api/application/r/queryChildRegions
        return request.get('/finance/invoice/api/common/application/r/queryRegions', {
            params: {
                level,
                parentRegionId,
            },
        })
            .then((res) => {
                const { code, msg, data } = res.data;

                if (code !== 0) {
                    throw new Error(msg);
                }

                const children = (data || []).map(item => ({
                    code: item.code,
                    name: item.name,
                    children: [],
                }));

                commit('appendChildRegions', {
                    path,
                    children,
                });

                return children;
            });
    },
};

export default {
    namespaced: true,
    state: initialState,
    mutations,
    actions,
};
