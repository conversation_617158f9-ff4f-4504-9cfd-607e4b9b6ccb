/* eslint-disable import/no-unresolved, import/extensions */
import request from '$invoice/utils/request';
import Vue from 'vue';
import { toast } from '@roo/roo-vue';
/* eslint-disable import/extensions, import/no-unresolved */
import { InvoiceTypeEnum } from '$config/enum';
/* eslint-disable import/extensions, import/no-unresolved */

// 所有选中的资质都可以放到任意开销方下面

// splitInfos中一项内容
// {
//     "qualificationId": 111223, // 抬头id
//     "ownerShip": 1001, // 销方公司id
//     "productionName": "123232", // 明细类型，目前也是统一的
//     "amountCent": 123232, // 总金额
//     "remark": "备注1", // 票面备注，目前是统一的
//     "settlePartners": [
//       {
//         "settleId": 3434343,
//         "partnerList": [
//           1232323,
//           1232324
//         ]
//       },
//       {
//         "settleId": 3434344,
//         "partnerList": [
//           1232323,
//           1232324
//         ]
//       }
//     ]
//   }

const initialState = {
    baseFeeTypeItem: null, // 明细项目

    qualifications: [], // 选中资质

    ownership: -1, // 默认-1为全部1001,1058
    invoiceType: null,

    remark: '',

    submitting: false,

    companySelected: 0, // 推广费选择金额的时候需要选择公司 0为全部公司

    splitAmount: [],
    // 前端页面遍历结构，遍历期间不需要保存结算id和门店信息
    splitContent: [],

    hasOwnershipNegative: false,
    // 是否有金额为0的发票
    hasZeroInvoice: false,
    // 传给后端接口字段
    splitInfos: [],
    // 最终提交的开销方名称
    submitComList: [],
    // 验证码灰度接口
    needVerification: false,
    // 资质新增/修改页面顶部提示文案
    // 老流程展示：验证码功能提示文案
    noticeText: '',
    // 新流程展示：新流程说明文案
    noticeTextNew: '',
};

const mutations = {
    reset(state) {
    // 不清空费用
        state.qualifications = []; // 选中资质
        state.invoiceType = null;
        state.remark = '';
        state.submitting = false;
        state.companySelected = 0;
        state.ownership = -1;
        state.splitAmount = [];
        state.splitContent = [];
        state.hasOwnershipNegative = false;
        state.splitInfos = [];
        state.submitComList = [];
    },
    // 初始化分配金额数据结构
    initSplitContent(state, company) {
        state.splitContent = [];
        let invoiceIncIndex = 1;
        company.forEach((com) => {
            // 定义开销方初始金额
            let activeMoney = com.moneyCent;
            const comInfo = {
                ownerShip: com.ownerShip,
                companyName: com.companyName,
                // 某个销方公司下可分配金额
                moneyCent: com.moneyCent,
                qualifications: [],
                availableCent: com.moneyCent,
            };
            // 定义每个company item下的settleList
            // const sList = com.settleList;
            // 定义单张发票的唯一标识
            const uniqueInvoiceId = new Date().getTime() + invoiceIncIndex;

            state.qualifications.forEach((ele) => {
                // 发票金额
                let qualificationCent = 0;
                // 结算金额
                let settleCent = 0;

                // 在每个qualification下遍历settleList，选出poiRelatedToTitle内对应的的item
                if (com.poiRelatedToTitle) {
                    const relatedPoi = com.poiRelatedToTitle[`${ele.id}`] || [];
                    com.settleList.forEach((sItem) => {
                        const pList = sItem.partnerList;
                        pList.forEach((pItem) => {
                            if (relatedPoi.includes(pItem.partnerId)) {
                                qualificationCent += pItem.moneyCent;
                                settleCent += pItem.moneyCent;
                                activeMoney -= pItem.moneyCent;
                                pItem.checked = `${com.ownerShip}-${ele.id}-${uniqueInvoiceId}`;
                            } else {
                                // pItem.checked = '';
                            }
                        });
                    });
                }

                comInfo.qualifications.push({
                    qualificationId: ele.id,
                    qualificationName: ele.partnerBName,
                    ownerShip: com.ownerShip,
                    moneyCent: qualificationCent,
                    invoiceList: [{
                        moneyCent: settleCent,
                        // 用时间戳作为单张发票的唯一标识
                        invoiceId: uniqueInvoiceId,
                        partnerTitleRelSave: true,
                    }],
                });
                invoiceIncIndex++;
            });

            // 分配已选门店金额后比对开销方初始金额
            if (com.poiRelatedToTitle && activeMoney !== com.moneyCent) {
                comInfo.moneyCent = activeMoney;
            }
            state.splitContent.push(comInfo);
        });
    },
    // 修改某张发票选择金额
    // moneyCent: 该发票选择的金额
    // invoiceId: 每张发票保存的时间戳，唯一标识，因为存在删除操作，所以下标无法使用
    changeSplitContent(
        state,
        {
            companyIndex, qualificationIndex, invoiceId, moneyCent, partnerTitleRelSave,
        },
    ) {
        if (companyIndex < state.splitContent.length) {
            const com = state.splitContent[companyIndex].qualifications;
            if (qualificationIndex < com.length) {
                const q = com[qualificationIndex].invoiceList;
                const invoiceIndex = q.findIndex(ele => ele.invoiceId === invoiceId);
                if (invoiceIndex > -1) {
                    const invoice = q[invoiceIndex];
                    // 金额改变差异值
                    const diff = moneyCent - invoice.moneyCent;
                    // 更新发票选中金额
                    invoice.moneyCent = moneyCent;
                    // 更新资质抬头下已分配金额
                    com[qualificationIndex].moneyCent += diff;
                    // 更新销方公司下可分配金额
                    state.splitContent[companyIndex].moneyCent -= diff;

                    invoice.partnerTitleRelSave = partnerTitleRelSave;
                }
            }
        }
    },
    // 删除某张发票
    deleteSplitContent(state, { companyIndex, qualificationIndex, invoiceId }) {
        if (companyIndex < state.splitContent.length) {
            const com = state.splitContent[companyIndex].qualifications;
            if (qualificationIndex < com.length) {
                const q = com[qualificationIndex].invoiceList;
                const invoiceIndex = q.findIndex(ele => ele.invoiceId === invoiceId);
                if (invoiceIndex > -1) {
                    // 金额改变差异值
                    const diff = q[invoiceIndex].moneyCent;
                    // 更新资质抬头下已分配金额
                    com[qualificationIndex].moneyCent -= diff;
                    // 更新销方公司下可分配金额
                    state.splitContent[companyIndex].moneyCent += diff;
                    // 删除某一张发票
                    q.splice(invoiceIndex, 1);
                }
            }
        }
    },
    // 抬头下，新增发票
    addSplitContent(state, { companyIndex, qualificationIndex }) {
        if (companyIndex < state.splitContent.length) {
            const com = state.splitContent[companyIndex].qualifications;
            if (qualificationIndex < com.length) {
                const q = com[qualificationIndex].invoiceList;
                q.push({
                    moneyCent: 0,
                    invoiceId: new Date().getTime(),
                    partnerTitleRelSave: true,
                });
            }
        }
    },
    // 计算最后传给后端的数据结构splitInfos
    // 计算是否有金额为0的发票hasZeroInvoice，只开金额大于0的发票，小于等于0的需要过滤掉
    changeSplitInfos(state, checkedAmountList) {
        state.splitInfos = [];
        state.hasZeroInvoice = false;
        state.splitContent.forEach(com => {
            com.qualifications.forEach(qua => {
                // 筛选出金额大于0的资质抬头
                if (qua.moneyCent > 0) {
                    // 按照后端接口参数生成的结构
                    qua.invoiceList.forEach(invoice => {
                        if (invoice.moneyCent > 0) {
                            const oneItem = {
                                qualificationId: qua.qualificationId,
                                qualificationName: qua.qualificationName,
                                // 明细名称
                                productionName: state.baseFeeTypeItem.productionName,
                                // 备注
                                remark: '',
                                ownerShip: qua.ownerShip,
                                amountCent: invoice.moneyCent,
                                partnerTitleRelSave: invoice.partnerTitleRelSave,
                                settlePartners: [],
                            };
                            // 发票标记
                            const flag = `${qua.ownerShip}-${qua.qualificationId}-${invoice.invoiceId}`;
                            // 结算ID列表
                            const settleList = checkedAmountList.find(ele => ele.ownerShip === qua.ownerShip).settleList;
                            settleList.forEach(settle => {
                                settle.partnerList.forEach(poi => {
                                    // 遍历所有被打上标记的门店
                                    if (poi.checked === flag) {
                                        const settleIndex = oneItem.settlePartners.findIndex(s => s.settleId === settle.settleId);
                                        // 如果已经存储过该结算ID
                                        if (settleIndex > -1) {
                                            oneItem.settlePartners[settleIndex].partnerList.push(poi.partnerId);
                                        } else {
                                            oneItem.settlePartners.push({
                                                settleId: settle.settleId,
                                                partnerList: [
                                                    poi.partnerId,
                                                ],
                                            });
                                        }
                                    }
                                });
                            });
                            state.splitInfos.push(oneItem);
                        } else {
                            state.hasZeroInvoice = true;
                        }
                    });
                } else {
                    state.hasZeroInvoice = true;
                }
            });
        });
    },
    // 计算是否存在销方公司金额为负数
    changeHasOwnershipNegative(state) {
        if (state.splitContent.some(ele => ele.moneyCent < 0)) {
            state.hasOwnershipNegative = true;
        } else {
            state.hasOwnershipNegative = false;
        }
    },
    // 获取金额大于0的销方公司
    changeSubmitComList(state) {
        const arr = [];
        state.splitContent.forEach(ele => {
            if (ele.moneyCent > 0 || ele.availableCent > 0) {
                arr.push(ele.companyName);
            }
        });
        state.submitComList = arr;
    },
    changeSplitAmount(state, item) {
        state.splitAmount = item;
    },
    changCompanySelected(state, item) {
        state.companySelected = item;
    },
    changeOwnership(state, id) {
        state.ownership = id;
    },
    changeBaseFeeTypeItem(state, item) {
        state.baseFeeTypeItem = item;
    },
    changeQualifications(state, qualifications) {
        state.qualifications = qualifications;
    },
    addQualificationSplitAmount(state, id) {
        const target = state.qualifications.find((q) => q.id === id);
        if (target) {
            if (target.amountList) {
                target.amountList = ['0.00'].concat(target.amountList);
            } else {
                target.amountList = ['0.00'];
            }
        }
    },
    addCitySplitAmount(state, { comIndex, qIndex }) {
        const qualification = state.splitAmount[comIndex].qualifications[qIndex];
        qualification.amountList.unshift('0.00');
    },
    removeCitySplitAmount(state, { comIndex, qIndex, idx }) {
        const qualification = state.splitAmount[comIndex].qualifications[qIndex];
        qualification.amountList.splice(idx, 1);
    },
    changeCitySplitAmount(state, {
        comIndex, qIndex, idx, value,
    }) {
        const qualification = state.splitAmount[comIndex].qualifications[qIndex];
        const newQualifications = [...state.splitAmount[comIndex].qualifications];
        newQualifications[qIndex].amountList[idx] = value;
        state.qualifications = newQualifications;
        Vue.set(qualification.amountList, idx, value); // todo ??? 为何直接设置无效
    },
    removeQualificationSplitAmount(state, { id, index }) {
        const target = state.qualifications.find((q) => q.id === id);
        if (target && target.amountList && index < target.amountList.length) {
            target.amountList = target.amountList.filter((_, idx) => idx !== index);
        }
    },
    cacRest(state) {
    // 计算按城市分类的发票剩余金额
        const { splitAmount } = state;
        splitAmount.forEach((item) => {
            // debugger
            const qTotal = item.qualifications.reduce((pre, el) => {
                const a = el.amountList.reduce((p, e) => p + e * 1, 0);
                return pre + a;
            }, 0);
            const restMilli = item.moneyMilli - qTotal * 10000;
            item.restMilli = (restMilli / 100).toFixed(0) * 100;
        });
    },
    changeQualificationSplitAmount(state, { id, index, value }) {
        const target = state.qualifications.find((q) => q.id === id);
        if (target && target.amountList && index < target.amountList.length) {
            //! set
            Vue.set(target.amountList, index, value);
            // target.amountList = target.amountList.map((str, idx) => {
            //     if (idx !== index) {
            //         return str;
            //     } else {
            //         return value;
            //     }
            // });
        }
    },
    changeInvoiceType(state, invoiceType) {
        state.invoiceType = invoiceType;

        state.qualifications.forEach((q) => {
            if (q.invoiceType !== invoiceType) {
                q.invoiceType = invoiceType;
                q.changed = true;
            }
            q.hasSetDefaultInvoiceType = true;
        });
    },
    changeQualificationField(state, { id, prop, value }) {
    // 传回 qualification 直接修改
        const target = state.qualifications.find((q) => q.id === id);
        if (target[prop] !== value) {
            target[prop] = value;
            target.changed = true;
        }
    },
    // 为了修复：新建的资质，没有税号时，提交时提示税号为空
    // 解决方案：把税号为空的资质取出来，填充上输入的值即可
    resetFields(state) {
        const quas = state.qualifications;
        const splitAmount = state.splitAmount;

        if (splitAmount && splitAmount.length > 0) {
            splitAmount.forEach((item) => {
                const qualifications = item.qualifications;
                if (qualifications && qualifications.length > 0) {
                    qualifications.forEach((netQua) => {
                    // 把税号是空的资质找出来，把空的值替换成输入的值
                    // 更新对应ID的资质
                    // if (netQua.taxpayerIdNo == null || netQua.taxpayerIdNo === '') {
                        // qua.taxpayerIdNo = target.taxpayerIdNo
                        if (quas && quas.length > 0) {
                            quas.forEach((inputQua) => {
                                if (netQua.id === inputQua.id) {
                                    // 注册地址 registerAddress
                                    // 电话 telNo
                                    // 开户行 bankName
                                    // 银行账户 cardNo
                                    ['registerAddress', 'telNo', 'bankName', 'cardNo', 'taxpayerIdNo'].forEach((key) => {
                                        netQua[key] = inputQua[key];
                                    });
                                }
                            });
                        }
                    });
                }
            });
        }
    },
    changeRemark(state, remark) {
        state.remark = remark;
    },
    changeSubmitting(state, loading) {
        state.submitting = loading;
    },
    SET_NEED_VERIFICATION(state, needVerification) {
        state.needVerification = needVerification;
    },
    changeNotice(state, notice) {
        state.noticeText = notice;
    },
    changeNoticeNew(state, notice) {
        state.noticeTextNew = notice;
    },
};

const filterChecked = (x) => x.checked;

function getCheckedAdIds(list) {
    if (list) {
        const tmpList = list.filter(filterChecked);
        if (tmpList.length < list.length) {
            return tmpList.map((x) => x.id);
        }
    }
    return null;
}

function getCheckedPoiList(list) {
    if (list) {
        const tmpList = list.filter(filterChecked);
        if (tmpList.length < list.length) {
            return tmpList.map((x) => x.id);
        }
    }
    return null;
}

function calcSplitInfosByCompany(splitAmount = [], productionName) {
    const splitInfos = [];
    splitAmount.forEach((company) => {
        const { companyName, ownerShip } = company;
        company.qualifications.forEach((qualification, qIndex) => {
            const {
                id,
                partnerBName,
                taxpayerIdNo,
                registerAddress,
                telNo,
                bankName,
                cardNo,
            } = qualification;
            qualification.amountList.filter((el) => el > 0).forEach((el) => {
                splitInfos.push({
                    amountMilli: el * 10000,
                    qualificationId: id,
                    partnerBName,
                    taxpayerIdNo,
                    registerAddress,
                    telNo,
                    bankName,
                    cardNo,
                    companyName,
                    ownerShip,
                    productionName,
                });
            });
            // 如果是此公司最后一个资质,加上剩下的那个值
            if (
                qIndex === company.qualifications.length - 1
        && company.restMilli > 0
            ) {
                splitInfos.push({
                    amountMilli: company.restMilli,
                    qualificationId: id,
                    partnerBName,
                    taxpayerIdNo,
                    registerAddress,
                    telNo,
                    bankName,
                    cardNo,
                    companyName,
                    ownerShip,
                    productionName,
                });
            }
        });
    });
    return splitInfos;
}

const actions = {
    // eslint-disable-next-line object-curly-newline
    submitApply({ state, rootState, commit, rootGetters }) {
        const {
            baseFeeTypeItem,
            invoiceType,
            remark,
            splitAmount,
        } = state;

        commit('changeSubmitting', true);

        const { contractId } = rootState.config;
        const { dateFrom, dateTo } = rootState.amountSelect;
        const isElectronInvoice = rootGetters['apply/isElectronInvoice'];

        const body = {
            contractId,
            poiType: baseFeeTypeItem.poiType,
            baseFeeType: baseFeeTypeItem.baseFeeType,
            partnerType: baseFeeTypeItem.partnerType,
            qualificationType: baseFeeTypeItem.qualificationType,
            amountGatherType: baseFeeTypeItem.amountGatherType,
            invoiceType,
            remark,
            dateFrom,
            dateTo,
        };

        const {
            settleAmountList,
            monthAmountList,
            hiddenList,
        } = rootState.amountSelect;

        if (baseFeeTypeItem.amountGatherType === 1) {
            body.settleAmountRequest = settleAmountList
                .concat(hiddenList)
                .filter(filterChecked)
                .map((item) => ({
                    settleId: item.settleId,
                    poiList: getCheckedPoiList(item.poiList),
                }))
                .filter(({ poiList }) => !poiList || poiList.length > 0); // 最后一步优化，结算设置下实际上没有勾选门店
        } else {
            body.monthAmountRequest = monthAmountList
                .filter(filterChecked)
                .map((item) => ({
                    id: item.id,
                    adIds: getCheckedAdIds(item.adDetails),
                }));
        }

        if (isElectronInvoice) {
            // 电子发票
            body.receiverEmail = rootState.sendInfo.receiverEmail;
        } else {
            // 纸质发票
            const { sendInfo } = rootState;
            body.receiverEmail = sendInfo.receiverEmail;
            body.receiver = sendInfo.receiver;
            body.provinceCode = sendInfo.provinceCode;
            body.expressProvince = sendInfo.expressProvince;
            body.cityCode = sendInfo.cityCode;
            body.expressCity = sendInfo.expressCity;
            body.districtCode = sendInfo.districtCode;
            body.expressDistrict = sendInfo.expressDistrict;
            body.address = sendInfo.address;
            body.phoneNo = sendInfo.phoneNo;
            body.telNo = sendInfo.telNo;
        }
        body.splitInfos = calcSplitInfosByCompany(splitAmount, baseFeeTypeItem.productionName);
        return request.post('/finance/invoice/api/common/application/w/apply', body)
            .then(
                (res) => {
                    commit('changeSubmitting', false);

                    const { code, msg } = res.data;
                    if (code !== 0) {
                        throw new Error(msg);
                    }
                },
                (err) => {
                    commit('changeSubmitting', false);
                    throw err;
                },
            );
    },
    // 最后一步提交内容
    newSubmitApply({
        state, rootState, commit, rootGetters,
    }) {
        const {
            baseFeeTypeItem,
            invoiceType,
            remark,
        } = state;

        const { config } = rootState;

        commit('changeSubmitting', true);

        const { contractId, isShudianHit } = rootState.config;
        const { dateFrom, dateTo, totalSelectedAmount } = rootState.amountSelectNew;
        const isElectronInvoice = rootGetters['apply/isElectronInvoice'];
        // 地址对象
        const receiverAddress = {};

        if (isElectronInvoice) {
            // 电子发票
            receiverAddress.receiverEmail = rootState.sendInfo.receiverEmail;
        } else {
            // 纸质发票
            const { sendInfo } = rootState;
            receiverAddress.receiver = sendInfo.receiver;
            receiverAddress.provinceCode = sendInfo.provinceCode;
            receiverAddress.expressProvince = sendInfo.expressProvince;
            receiverAddress.cityCode = sendInfo.cityCode;
            receiverAddress.expressCity = sendInfo.expressCity;
            receiverAddress.districtCode = sendInfo.districtCode;
            receiverAddress.expressDistrict = sendInfo.expressDistrict;
            receiverAddress.address = sendInfo.address;
            receiverAddress.phoneNo = sendInfo.phoneNo;
            receiverAddress.telNo = sendInfo.telNo;

            if (isShudianHit) {
                receiverAddress.receiverEmail = rootState.sendInfo.receiverEmail;
            }
        }

        const body = {
            contractId,
            poiType: baseFeeTypeItem.poiType,
            baseFeeType: baseFeeTypeItem.baseFeeType,
            partnerType: baseFeeTypeItem.partnerType,
            amountGatherType: baseFeeTypeItem.amountGatherType,
            totalAmountCent: totalSelectedAmount,
            receiverAddress,
            invoiceType,
            dateFrom,
            dateTo,
            invoiceSource: 113,
        };

        body.splitInfos = state.splitInfos.map((ele) => ({
            ...ele,
            remark,
        }));
        // body.splitInfos = newSplitInfos;

        const requestUrl = !config.isSinglePoi ? '/finance/invoice/api/output/waimai/c/apply' : '/finance/invoice/api/pc/settle/apply';

        return request.post(requestUrl, body)
            .then(
                (res) => {
                    commit('changeSubmitting', false);

                    const { code, msg } = res.data;
                    if (code !== 0) {
                        toast.error(msg, 1000);
                        return false;
                    } else {
                        return true;
                    }
                },
                (err) => {
                    commit('changeSubmitting', false);
                    throw err;
                },
            );
    },
    initSplitAmount({ state, commit, rootGetters }) {
        const amountByCity = rootGetters['amountSelect/amountByCity'];
        const { values } = amountByCity;
        const { qualifications } = state;
        const initValues = values.map((item) => ({
            ...item,
            restMilli: item.moneyMilli, // 剩下未分配的值
            qualifications: qualifications.map((el) => ({
                ...el,
                amountList: ['0.00'],
            })),
        }));
        commit('changeSplitAmount', initValues);
    },

    // 当选择金额之后只有一个公司的时候,需要重置mountList中的一个公司
    updateSellerCompany({ state, commit, rootGetters }) {
    // 当只有一个公司的时候才需要这样处理
        const baseFeeTypeItem = state.baseFeeTypeItem;
        const amountList = rootGetters['amountSelect/checkedAmountList'];
        const notEmptylist = amountList.filter((item) => item.moneyMilli !== 0);
        if (!notEmptylist.length) return;
        const { sellerCompanyList = [] } = notEmptylist[0];
        if (sellerCompanyList.length === 1) {
            // 当且仅有一家公司,因为没有经过选择,可能和step 1中选择的不同,所以需要将当前选择的公司设置到state
            const { ownerShip } = sellerCompanyList[0];
            const companyList = baseFeeTypeItem.sellerCompanyList;
            const companyInfo = companyList.find(
                (el) => el.ownerShip === ownerShip,
            );
            commit('changeBaseFeeTypeItem', {
                ...baseFeeTypeItem,
                ...companyInfo,
            });
        }
    },

    getNotice({ commit }) {
        request.get('/finance/invoice/api/output/waimai/q/getNotice').then((res) => {
            const { code, data } = res.data;
            if (code === 0) {
                commit('changeNotice', data);
            } else {
                commit('changeNotice', '');
            }
        }).catch((error) => {
            toast.error(error || '获取通知失败，请稍后重试');
        });
    },
    getNoticeNew({ commit }) {
        const params = {
            signingWorkflow: true,
        };
        request.get('/finance/invoice/api/output/waimai/q/getNotice', { params }).then((res) => {
            const { code, data } = res.data;
            if (code === 0) {
                commit('changeNoticeNew', data);
            } else {
                commit('changeNoticeNew', '');
            }
        }).catch((error) => {
            toast.error(error || '获取通知失败，请稍后重试');
        });
    },
    // 其他 actions
    async checkGray({ commit }) {
        try {
            const res = await request.get('/finance/invoice/api/waimai/output/verifyGray');
            const { code, data } = res.data;
            if (code === 0) {
                commit('SET_NEED_VERIFICATION', data);
            } else {
                commit('SET_NEED_VERIFICATION', false);
            }
        } catch (err) {
            commit('SET_NEED_VERIFICATION', false);
            toast.error(err || '获取灰度信息失败，请稍后重试');
        }
    },
};

const getters = {
    isPublicActivityFee(state) {
        return state.baseFeeTypeItem && state.baseFeeTypeItem.baseFeeType === 15;
    },
    isChildren(state) {
        return state.baseFeeTypeItem && state.baseFeeTypeItem.baseFeeType === 38;
    },
    isNatural(state) {
        return state.baseFeeTypeItem && state.baseFeeTypeItem.baseFeeType === 39;
    },
    isPublicActivityInvoice(state) {
        return (
            state.invoiceType === InvoiceTypeEnum.PublicActivityElectron
      || state.invoiceType === InvoiceTypeEnum.PublicActivityPaper
        );
    },
    isElectronInvoice(state) {
        return (
            state.invoiceType === InvoiceTypeEnum.Electron
      || state.invoiceType === InvoiceTypeEnum.PublicActivityElectron
        );
    },
    // 校验单张发票金额，是否小于minMoney
    singleInvoiceMoneyCheck(state) {
        // minMoney-单张发票的最小金额
        return function (minMoney = 0) {
            const splitC = state.splitContent;
            // 校验每一张发票，已分配金额，不能小于0， 等于0的情况自动过滤，不发送给后端接口
            for (let i = 0; i < splitC.length; i++) {
                const com = this.splitContent[i];
                for (let j = 0; j < com.qualifications.length; j++) {
                    const qua = com.qualifications[j];
                    for (let k = 0; k < qua.invoiceList.length; k++) {
                        const invoice = qua.invoiceList[k];
                        if (invoice.moneyCent < minMoney) {
                            return false;
                        }
                    }
                }
            }
            return true;
        };
    },
    // 计算到第四步的资质列表内容
    qualificationUsed(state) {
        return state.splitInfos.map(ele => ele.qualificationId);
        // const idList = [];
        // state.splitInfos.forEach(ele => {
        //     ele.oneItems.forEach(e => {
        //         idList.push(e.qualificationId);
        //     });
        // });
        // return idList;
        // return state.splitInfos.map(ele => ele.oneItems.forEach(e => e.qualificationId));
    },
    // 计算所有开销方下可分配金额为0, 更宽松的计算方式，忽略门店金额为0，或存在正负金额互相抵消的情况没有被分配
    isAllAlocated(state) {
        return state.splitContent.every(com => com.availableCent === 0 || com.moneyCent === 0);
    },
    productionName(state) {
        const { sellerCompanyList = [] } = state.baseFeeTypeItem || {};
        const companySelectedItem = sellerCompanyList.find(item => item.ownerShip === state.companySelected);
        if (companySelectedItem) {
            return companySelectedItem.productionName;
        }
        return '';
    },
};

export default {
    namespaced: true,
    state: { ...initialState },
    mutations,
    actions,
    getters,
};
