import axios from 'axios';
/* eslint-disable import/no-unresolved, import/extensions */
import request from '$invoice/utils/request';

const { CancelToken } = axios;

const initialState = {
    // 锁定参数
    query: {},

    loading: false,
    total: 0,
    pageNo: 1,
    pageSize: 10,
    list: [],
};

const mutations = {
    changeQuery(state, query) {
        state.query = query;
    },
    changeLoading(state, loading) {
        state.loading = loading;
    },
    changeTotal(state, total) {
        state.total = total;
    },
    changePageNo(state, pageNo) {
        state.pageNo = pageNo;
    },
    changeList(state, list) {
        state.list = list;
    },
    reset(state) {
        state.query = {};
        state.loading = false;
        state.total = 0;
        state.pageNo = 1;
        state.pageSize = 10;
        state.list = [];
    },
};

let source = null;
function getInvoiceList(params) {
    if (source) {
        source.cancel();
        source = null;
    }

    source = CancelToken.source();
    const cancelToken = source.token;
    // 老逻辑
    return request.get('/finance/invoice/api/pc/application/r/queryList', {
        params,
        cancelToken,
    });
}
// 新逻辑
function getInvoiceLists(params) {
    if (source) {
        source.cancel();
        source = null;
    }

    source = CancelToken.source();
    const cancelToken = source.token;
    return request.get('/finance/invoice/api/output/waimai/r/queryApplyList', {
        params,
        cancelToken,
    });
}

const actions = {
    fetchInvoiceList({ state, commit, rootState }, pageNo) {
        const { query, pageSize } = state;
        const { isHit } = rootState.config;

        const params = Object.assign({ pageNo, pageSize, isInvoiceHistory: true }, query);

        commit('changeLoading', true);
        if (isHit) {
            return getInvoiceLists(params)
                .then((res) => {
                    commit('changeLoading', false);

                    const { code, msg, data } = res.data;
                    if (code !== 0) {
                        throw new Error(msg);
                    }

                    commit('changeTotal', data.totalCount);
                    commit('changePageNo', data.pageNo);
                    commit('changeList', data.applyList);
                }, (err) => {
                    commit('changeLoading', false);
                    throw err;
                });
        } else {
            return getInvoiceList(params)
                .then((res) => {
                    commit('changeLoading', false);

                    const { code, msg, data } = res.data;
                    if (code !== 0) {
                        throw new Error(msg);
                    }

                    commit('changeTotal', data.totalCount);
                    commit('changePageNo', data.pageNo);
                    commit('changeList', data.applyList);
                }, (err) => {
                    commit('changeLoading', false);
                    throw err;
                });
        }
    },
};

export default {
    namespaced: true,
    state: initialState,
    mutations,
    actions,
};
