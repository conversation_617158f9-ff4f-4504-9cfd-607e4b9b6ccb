<template>
    <div :class="$style.container">
        <div :class="$style.card">
            <h3>选择查询</h3>

            <div :class="$style.form">
                <div :class="$style.row">
                    <span :class="$style.label">
                        发票种类
                    </span>
                    <roo-select
                        v-model="invoiceType"
                        :class="$style.input"
                        :options="invoiceTypeOptions"
                    />
                    <span :class="$style.label">
                        明细项目
                    </span>
                    <roo-select
                        v-model="baseFeeType"
                        :class="$style.input"
                        :options="feeTypeOptions"
                    />
                    <span :class="$style.label">
                        申请状态
                    </span>
                    <roo-select
                        v-model="status"
                        :class="$style.input"
                        :options="statusOptions"
                    />
                </div>

                <div :class="$style.row">
                    <span :class="$style.label">
                        发票抬头
                    </span>
                    <roo-input
                        v-model="partnerName"
                        :class="$style.input"
                        placeholder="输入发票抬头查询"
                    />
                </div>

                <div :class="$style.row">
                    <span :class="$style.label">
                        提交时间
                    </span>
                    <roo-date-time-picker
                        v-model="startDate"
                        :class="$style.input"
                        :max-date="maxStartDate"
                        clearable
                    />
                    -
                    <roo-date-time-picker
                        v-model="endDate"
                        :class="$style.input"
                        :min-date="minEndDate"
                        :max-date="maxEndDate"
                        clearable
                    />
                    <roo-button
                        :loading="searching"
                        @click="handleQueryClick"
                    >
                        查询
                    </roo-button>
                    <roo-button
                        :loading="exporting"
                        @click="handleExport"
                    >
                        导出
                    </roo-button>
                </div>
            </div>

            <h3>历史开票列表</h3>

            <invoice-table
                v-if="isHit"
                :loading="loading"
                :list="list"
                @express-track="handleExpressTrack"
                @cancel="handleCancel"
                @invalid="handleInvalid"
            />

            <invoice-tables
                v-else
                :loading="loading"
                :list="list"
                @express-track="handleExpressTrack"
                @cancel="handleCancel"
                @invalid="handleInvalid"
            />

            <div
                v-if="total > 0"
                :class="$style.pagination"
            >
                <roo-pagination
                    :total="total"
                    :page-size="pageSize"
                    :current-page="pageNo"
                    @current-change="handlePageChange"
                />
            </div>
        </div>

        <!-- 弹窗 -->
        <express-track-modal
            v-model="displayExpress"
            :loading="expressLoading"
            :company="expressCompany"
            :number="expressNo"
            :track-list="expressTrack"
        />

        <!-- 确认取消 -->
        <prompt-modal
            v-model="displayCancel"
            :loading="canceling"
            :title="`取消开票（ID : ${cancelInvoice && cancelInvoice.id}）`"
            message="发票尚未开出，操作后此发票不会继续开出，确认不开此票吗？"
            placeholder="请填写取消理由（不超过 100 字）"
            required
            @confirm="handleCancelConfirm"
        />

        <!-- 作废 -->
        <invoice-invalid-modal
            :id="invalidInvoice && invalidInvoice.item.id"
            v-model="displayInvalid"
            :loading="invaliding"
            :message="invalidMessage"
            @confirm="handleInvalidConfirm"
        />
    </div>
</template>

<script>
import axios from 'axios';
/* eslint-disable import/no-unresolved, import/extensions */
import request from '$invoice/utils/request';
import { mapState, mapMutations, mapActions } from 'vuex';
import { toast } from '@roo/roo-vue';
import dayjs from 'dayjs';

/* eslint-disable import/extensions, import/no-unresolved */
import toastError from '$lib/toast-error';
import PromptModal from '$components/prompt-modal';
import { NormalInvoiceTypeList, PublicActivityInvoiceTypeList, InvoiceStatusList, InvoiceTypeEnum } from '$config/enum';
/* eslint-enable import/extensions, import/no-unresolved */

import InvoiceTable from './components/invoice-table';
import InvoiceTables from './components/invoice-tables';

import ExpressTrackModal from './components/express-track-modal';
import InvoiceInvalidModal from './components/invoice-invalid-modal';
// eslint-disable-next-line import/no-unresolved, import/extensions
import { navigateTo } from '$lib/utils';


const { CancelToken } = axios;

let source = null;
function queryExpressTrack(params) {
    if (source) {
        source.cancel();
        source = null;
    }

    source = CancelToken.source();
    const cancelToken = source.token;

    return request.get('/finance/invoice/api/common/application/r/queryExpressTrack', {
        params,
        cancelToken,
    });
}

export default {
    name: 'InvoiceHistory',
    components: {
        PromptModal,
        InvoiceTable,
        InvoiceTables,
        ExpressTrackModal,
        InvoiceInvalidModal,
    },
    data() {
        return {
            invoiceType: -1,
            baseFeeType: -1,
            status: -1,
            partnerName: '',
            startDate: null,
            endDate: null,

            searching: false,

            displayExpress: false,
            expressLoading: false,
            expressCompany: '',
            expressNo: '',
            expressTrack: [],

            displayCancel: false,
            cancelInvoice: null,
            canceling: false,

            displayInvalid: false,
            invalidInvoice: null,
            invaliding: false,
            exporting: false,
        };
    },
    computed: {
        ...mapState('config', [
            'contractId',
            'feeTypeList',
            'isHit',
            'isShudianHit',
        ]),
        ...mapState('invoiceHistory', [
            'loading',
            'total',
            'pageNo',
            'pageSize',
            'list',
        ]),

        maxStartDate() {
            return this.endDate || new Date();
        },

        minEndDate() {
            return this.startDate || null;
        },
        maxEndDate() {
            return new Date();
        },

        invoiceTypeOptions() {
            const result = [{
                value: -1,
                label: '全部',
            },
            ...NormalInvoiceTypeList,
            ...PublicActivityInvoiceTypeList,
            ].filter(item => item.value !== InvoiceTypeEnum.VATNormal);

            if (this.isShudianHit) {
                result.forEach((item) => {
                    if (item.value === InvoiceTypeEnum.VATSpecial || item.value === InvoiceTypeEnum.Electron) {
                        item.label = item.value === InvoiceTypeEnum.VATSpecial ? '专票' : '普票';
                    }
                });
            }

            return result;
        },
        statusOptions() {
            return [{
                value: -1,
                label: '全部',
            },
            ...InvoiceStatusList,
            ];
        },
        feeTypeOptions() {
            const { feeTypeList } = this;

            return [{
                value: -1,
                label: '全部',
            }].concat(feeTypeList.map(f => ({
                value: f.baseFeeType,
                label: f.baseFeeTypeName,
            })));
        },

        invalidMessage() {
            return '若作废数电发票，需在电子税务局系统完成确认（72小时内）；若作废纸质发票，请邮寄回美团。当发票状态变为“已作废”后，可重开发票。';
        },
    },
    methods: {
        ...mapMutations('invoiceHistory', [
            'changeQuery',
        ]),
        ...mapActions('invoiceHistory', [
            'fetchInvoiceList',
        ]),
        handleQueryClick() {
            const query = {
                status: this.status,
                contractId: this.contractId,
                invoiceType: this.invoiceType,
                baseFeeType: this.baseFeeType,
                invoiceSource: 113,
            };

            if (this.partnerName) {
                if (this.isHit) {
                    query.partnerBName = this.partnerName;
                } else {
                    query.partnerName = this.partnerName;
                }
            }

            if (this.startDate) {
                query.dateFrom = dayjs(this.startDate).format('YYYY-MM-DD');
            }
            if (this.endDate) {
                query.dateTo = dayjs(this.endDate).format('YYYY-MM-DD');
            }

            // 锁定参数
            this.changeQuery(query);

            // 点查询需要 disable 按钮
            this.searching = true;
            this.fetchInvoiceList(1)
                .catch(toastError)
                .then(() => {
                    this.searching = false;
                });
        },
        // 导出
        handleExport() {
            const params = {
                contractId: this.contractId,
                baseFeeType: this.baseFeeType,
                status: this.status,
                partnerBName: this.partnerName,
                invoiceType: this.invoiceType,
                billType: 100,
            };
            if (this.startDate) {
                params.beginDate = dayjs(this.startDate).format('YYYY-MM-DD');
            }
            if (this.endDate) {
                params.endDate = dayjs(this.endDate).format('YYYY-MM-DD');
            }
            if (!this.startDate || !this.endDate) {
                toast.warn('提交时间不可为空');
                return;
            }
            this.exporting = true;
            request.get('/finance/pc/api/billDownload/createInvoiceExportTask', { params })
                .then((res) => {
                    this.exporting = false;

                    const { code, msg } = res.data;

                    if (code !== 0) {
                        toast.error(msg);
                        throw new Error(msg);
                    }

                    // 跳转到下载专区
                    toast.success('导出成功');
                    navigateTo('/finance/pc/download?type=account-running', true);
                    window.location = '/finance/pc/download?type=account-running';
                })
                .catch((err) => {
                    this.exporting = false;
                    toastError(err);
                });
        },
        handlePageChange(pageNo) {
            this.fetchInvoiceList(pageNo).catch(toastError);
        },

        handleExpressTrack({ id, expressNo }) {
            const params = {
                applyId: id,
                expressNo,
            };

            this.displayExpress = true;
            this.expressLoading = true;
            this.expressNo = expressNo || '';
            queryExpressTrack(params)
                .then((res) => {
                    const { code, msg, data } = res.data;

                    if (code !== 0) {
                        throw new Error(msg);
                    }

                    this.expressCompany = data.expressCompany || '';
                    this.expressTrack = (data.detailList || []).map((item) => {
                        const [date, time] = item.trackTime.split(' ');
                        return {
                            date,
                            time,
                            content: item.trackContent,
                        };
                    });
                    this.expressLoading = false;
                })
                .catch(toastError);
        },

        handleCancel(invoice) {
            this.cancelInvoice = invoice;
            this.canceling = false;
            this.displayCancel = true;
        },
        handleInvalid(invoice) {
            this.invalidInvoice = invoice;
            this.invaliding = false;
            this.displayInvalid = true;
        },
        handleCancelConfirm(comment) {
            if (comment.length > 100) {
                toast.warn('取消理由不能超过 100 个字');
                return;
            }
            const params = {
                applyId: this.cancelInvoice && this.cancelInvoice.id,
                comment,
            };
            this.canceling = true;
            request.get('/finance/invoice/api/common/application/w/cancel', { params })
                .then((res) => {
                    this.canceling = false;
                    const { code, msg } = res.data;

                    if (code !== 0) {
                        throw new Error(msg);
                    }

                    this.displayCancel = false;
                    this.handlePageChange(1);
                })
                .catch((err) => {
                    this.canceling = false;
                    toastError(err);
                });
        },
        handleInvalidConfirm({ invalidReasonCode, comment }) {
            if (comment.length > 100) {
                toast.warn('作废理由不能超过 100 个字');
                return;
            }
            const params = {
                applyId: this.invalidInvoice && this.invalidInvoice.item.id,
                invalidReasonCode,
                comment,
            };

            if (this.isHit) {
                params.mapperId = this.invalidInvoice && this.invalidInvoice.mapperId;
            }

            // /finance/invoice/api/common/application/w/invalid
            this.invaliding = true;
            const api = this.isHit ? '/finance/invoice/api/output/waimai/c/mapper/invalid' : '/finance/invoice/api/common/application/w/invalid';
            request.get(api, { params })
                .then((res) => {
                    this.invaliding = false;
                    const { code, msg } = res.data;

                    if (code !== 0) {
                        throw new Error(msg);
                    }

                    this.displayInvalid = false;
                    this.handlePageChange(1);
                })
                .catch((err) => {
                    this.invaliding = false;
                    toastError(err);
                });
        },
    },
};
</script>

<style lang="scss" module>
.container {
    min-height: 100%;
    padding-top: 50px;
    background: #FFF;
}

.card {
    padding: 40px;

    h3 {
        margin-top: 0;
        margin-bottom: 20px;
        font-size: 20px;
        color: #3F4156;
    }
}

.form {
    margin-bottom: 40px;
}

.row {
    display: block;
    margin-bottom: 10px;
}

.label {
    display: inline-block;
    width: 70px;
    margin-right: 10px;
    text-align: right;
    vertical-align: middle;
}

.input {
    display: inline-block;
    width: 230px;
    vertical-align: middle;
}

.pagination {
    text-align: center;
}
</style>
