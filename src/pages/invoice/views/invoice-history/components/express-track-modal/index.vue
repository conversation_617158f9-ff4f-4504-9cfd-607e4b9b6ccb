<template>
    <roo-modal
        :value="value"
        :title="`${company} 单号：${number}`"
        @input="$emit('input', $event)"
        @hidden="handleHidden"
    >
        <div
            v-if="loading"
            :class="$style.loading"
        >
            <span :class="$style.loadingText">
                正在加载
            </span>
        </div>
        <div
            v-else
            :class="$style.trackList"
        >
            <div
                v-for="(track, index) in trackList"
                :key="index"
                :class="$style.trackItem"
            >
                <div :class="$style.trackDate">
                    <div>{{ track.date }}</div>
                    <div>{{ track.time }}</div>
                </div>
                <div :class="$style.trackMark">
                    <div :class="$style.circle" />
                </div>
                <div :class="$style.trackContent">
                    {{ track.content }}
                </div>
            </div>
        </div>
    </roo-modal>
</template>

<script>
const emptyArray = () => [];

export default {
    name: 'ExpressTrackModal',
    props: {
        value: {
            type: Boolean,
            required: true,
        },
        loading: {
            type: Boolean,
            default: false,
        },
        company: {
            type: String,
            default: '',
        },
        number: {
            type: String,
            default: '',
        },
        // invoiceList: {
        //     type: Array,
        //     default: emptyArray,
        // },
        trackList: {
            type: Array,
            default: emptyArray,
        },
    },
    methods: {
        handleHidden() {
            this.$emit('hidden');
        },
    },
};
</script>

<style lang="scss" module>
.loading {
    display: table;
    width: 100%;
    height: 300px;
    table-layout: fixed;
}

.loading-text {
    display: table-cell;
    text-align: center;
    vertical-align: middle;
    padding-bottom: 20px;
}

.track-list {
    display: block;
    margin-bottom: 20px;
}

.track-item {
    display: flex;
    min-height: 60px;
}

.track-date {
    display: flex;
    flex-shrink: 0;
    flex-direction: column;
    justify-content: center;
    width: 120px;
    text-align: center;
}
.track-mark {
    position: relative;
    display: block;
    flex-shrink: 0;
    width: 50px;
}
.track-content {
    display: flex;
    flex: 1;
    align-items: center;
    padding: 20px 16px;
}

.track-mark {
    &:before,
    &:after {
        content: '';
        position: absolute;
        left: 0;
        right: 0;
        display: block;
        width: 1px;
        height: 50%;
        margin: auto;
        background: #E6E6E6;
    }

    &:before {
        top: 0;
    }
    &:after {
        bottom: 0;
    }

    .track-item:first-child &::before,
    .track-item:last-child &::after {
        display: none;
    }
}

.circle {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: block;
    width: 20px;
    height: 20px;
    margin: auto;
    border: 1px solid #E6E6E6;
    border-radius: 10px;
    background: #FFF;
    z-index: 5;

    &:after {
        content: '';
        position: absolute;
        top: 7px;
        left: 6px;
        display: block;
        width: 7px;
        height: 7px;
        border-top: 1px solid #E6E6E6;
        border-right: 1px solid #E6E6E6;
        transform: rotate(-45deg);
    }
}
</style>
