<template>
  <table :class="$style.table">
    <col style="width: 90px;"/>
    <col style="width: 120px;"/>
    <col style="width: 120px;"/>
    <col style="width: 120px;"/>
    <col style="width: 80px;"/>
    <col style="width: 120px;"/>
    <col style="width: 120px;"/>
    <col style="width: 100px;"/>
    <col style="width: 120px;"/>

    <thead>
      <tr>
        <th>ID</th>
        <th>发票信息</th>
        <th>发票抬头</th>
        <th>开票金额(元)</th>
        <th>申请状态</th>
        <th>发票号</th>
        <th>快递单号</th>
        <th>提交时间</th>
        <th>操作</th>
      </tr>
    </thead>

    <tbody
      v-if="loading"
      key="loading"
    >
      <td
        :class="$style.loading"
        colspan="9">正在加载</td>
    </tbody>
    <tbody
      v-else-if="list.length === 0"
      key="empty">
      <tr>
        <td
          :class="$style.empty"
          colspan="9">暂无数据</td>
      </tr>
    </tbody>
    <template v-else>
      <tbody
        v-for="item in list"
        :key="item.id">
        <template v-for="(amountList, partnerBName, index) in item.splitMap">
          <template
            v-for="({ amountCent, status, invoiceNos, invoiceStatus, expressNos }, index2) in amountList"
          >
            <!--  不存在发票code   -->
            <tr
              v-if="invoiceNos.length === 0"
              :key="`${partnerBName}_${index2}_placeholder`"
            >
              <td
                v-if="(index === 0 && index2 === 0)"
                :rowspan="calcRowSpan(item)"
              >{{ item.id }}</td>

              <td
                v-if="(index === 0 && index2 === 0)"
                :rowspan="calcRowSpan(item)"
              >
                <div>{{ item.invoiceTypeName }}</div>
                <div>{{ item.invoiceFeeTypeName }}</div>
              </td>

              <td
                v-if="index2 === 0"
                :rowspan="calcPartnerRowSpan(amountList)"
              >{{ partnerBName }}</td>

              <td>￥{{ amountCent | formatCent }}</td>

              <td :class="getStatusClass(status)">{{ status | statusName }}</td>

              <!--  公益   -->
              <td
                v-if="index === 0 && index2 === 0 && isPublicActivityInvoice(item)"
                :rowspan="calcRowSpan(item)"
                colspan="2"
              >如有特别需求，请拨打中华环境保护基金会票据咨询电话：010-67173592，或发送邮件至**********************</td>
              <!-- <td
                v-if="index === 0 && index2 === 0 && isChildren(item)"
                :rowspan="calcRowSpan(item)"
                colspan="2"
              >如有特别需求，请拨打深圳壹基金基金会咨询电话：400-6902700，或发送邮件至***************************</td> -->
              <td
                v-if="index === 0 && index2 === 0 && isNatural(item)"
                :rowspan="calcRowSpan(item)"
                colspan="2"
              >如有特别需求，请拨打北京市企业家环保基金会咨询电话：010-57505155，或发送邮件至*****************</td>
              <!--  非公益   -->
              <td v-if="!isPublicActivityInvoice(item) && !isNatural(item)"></td>
              <td v-if="!isPublicActivityInvoice(item) && !isNatural(item)"></td>

              <td
                v-if="index === 0 && index2 === 0"
                :rowspan="calcRowSpan(item)"
              >{{ item.applyTime }}</td>

              <td
                v-if="(index === 0 && index2 === 0)"
                :rowspan="calcRowSpan(item)"
              >
                <router-link
                  key="detail"
                  :to="{ name: 'invoice-detail', query: { id: item.id, partnerId: item.partnerId, partnerType: item.partnerType } }"
                >查看</router-link>
                <span
                  v-if="item.invoiceType !== PublicActivityElectron && item.invoiceType !== PublicActivityPaper"
                >
                  <template v-if="item.status === 1 && !isPublicActivityInvoice(item) && !isChildren(item) && !isNatural(item)">
                    <span
                      key="sp1"
                      :class="$style.separator"
                    ></span>
                    <a
                      key="cancel"
                      href="#noop"
                      @click.prevent="$emit('cancel', item)"
                    >取消</a>
                  </template>
                  <template v-if="(item.status === 2 || item.status === 4) && !isPublicActivityInvoice(item) && !isChildren(item) && !isNatural(item)">
                    <span
                      key="sp2"
                      :class="$style.separator"></span>
                    <a
                      key="invalid"
                      href="#noop"
                      @click.prevent="$emit('invalid', {item})"
                    >作废</a>
                  </template>
                  <template
                    v-if="item.invoiceSendType === 2 && item.status === 2"
                  >
                    <span
                      key="sp3"
                      :class="$style.separator"></span>
                    <a
                      key="download"
                      :href="`/finance/pc/api/invoice/downloadInvoice?applyId=${item.id}`"
                      target="_blank"
                    >下载</a>
                  </template>
                  <template v-if="(isPublicActivityInvoice(item) || isChildren(item) || isNatural(item)) && item.status === 2">
                  <span
                    key="sp3"
                    :class="$style.separator"></span>
                  <a
                    key="download"
                    :href="`/finance/invoice/api/common/application/r/public/downloadInvoice?applyId=${item.id}`"
                    target="_blank"
                  >下载</a>
                </template>
                </span>
              </td>
            </tr>
            <!--  存在发票code   -->
            <tr
              v-for="(invoiceNo, index3) in invoiceNos"
              :key="`${partnerBName}_${index2}_${invoiceNo}`"
            >
              <td
                v-if="(index === 0 && index2 === 0 && index3 === 0)"
                :rowspan="calcRowSpan(item)"
              >{{ item.id }}</td>

              <td
                v-if="(index === 0 && index2 === 0 && index3 === 0)"
                :rowspan="calcRowSpan(item)"
              >
                <div>{{ item.invoiceTypeName }}</div>
                <div>{{ item.invoiceFeeTypeName }}</div>
              </td>

              <td
                v-if="(index2 === 0 && index3 === 0)"
                :rowspan="calcPartnerRowSpan(amountList)"
              >{{ partnerBName }}</td>

              <td
                v-if="index3 === 0"
                :rowspan="invoiceNos.length"
              >￥{{ amountCent | formatCent }}</td>

              <td
                v-if="index3 === 0"
                :rowspan="invoiceNos.length"
                :class="getStatusClass(status)"
              >{{ status | statusName }}</td>
              <!-- 发票号 -->
              <td
                :class="invoiceStatus[index3] !== 1 ? $style.del : null"
                style="word-wrap:break-word;"
              >{{ invoiceNo }}</td>
              <!-- 快递单号 -->
              <td>
                <a
                  v-if="expressNos[index3]"
                  href="#noop"
                  @click.prevent="handleExpressNoClick(item.id, expressNos[index3])"
                >{{ expressNos[index3] }}</a>
                <template v-else>--</template>
              </td>

              <td
                v-if="(index === 0 && index2 === 0 && index3 === 0)"
                :rowspan="calcRowSpan(item)"
              >{{ item.applyTime }}</td>

              <td
                v-if="(index === 0 && index2 === 0 && index3 === 0)"
                :rowspan="calcRowSpan(item)"
              >
                <router-link
                  key="detail"
                  :to="{ name: 'invoice-detail', query: { id: item.id } }"
                >查看</router-link>
                <template v-if="item.status === 1 && !isPublicActivityInvoice(item) && !isChildren(item) && !isNatural(item)">
                  <span
                    key="sp1"
                    :class="$style.separator"
                  ></span>
                  <a
                    key="cancel"
                    href="#noop"
                    @click.prevent="$emit('cancel', item)"
                  >取消</a>
                </template>
                <template v-if="(item.status === 2 || item.status === 4) && !isPublicActivityInvoice(item) && !isChildren(item) && !isNatural(item)">
                  <span
                    key="sp2"
                    :class="$style.separator"></span>
                  <a
                    key="invalid"
                    href="#noop"
                    @click.prevent="$emit('invalid', {item})"
                  >作废</a>
                </template>
                <template v-if="(item.invoiceType === 3 || isContainsShudian(item))&& item.status === 2">
                  <span
                    key="sp3"
                    :class="$style.separator"></span>
                  <a
                    key="download"
                    :href="`/finance/invoice/api/common/application/r/downloadInvoice?applyId=${item.id}`"
                    target="_blank"
                  >下载</a>
                </template>
                <template v-if="(isPublicActivityInvoice(item) || isChildren(item) || isNatural(item)) && item.status === 2">
                  <span
                    key="sp3"
                    :class="$style.separator"></span>
                  <a
                    key="download"
                    :href="`/finance/invoice/api/common/application/r/public/downloadInvoice?applyId=${item.id}`"
                    target="_blank"
                  >下载</a>
                </template>
              </td>
            </tr>
          </template>
        </template>
      </tbody>
    </template>
  </table>
</template>

<script>
/* eslint-disable import/extensions, import/no-unresolved */
import { mapState } from 'vuex';
import { formatNumberThousand } from '$lib/utils';

import { InvoiceTypeEnum, InvoiceStatusNameList, BaseFeeTypeEnum } from '$config/enum';
/* eslint-enable import/extensions, import/no-unresolved */

const { PublicActivityElectron, PublicActivityPaper } = InvoiceTypeEnum;
export default {
  name: 'InvoiceTables',
  filters: {
    formatCent(num) {
      return formatNumberThousand((num / 100).toFixed(2));
    },
    statusName(status) {
      return InvoiceStatusNameList[status];
    },
  },
  props: {
    loading: {
      type: Boolean,
      default: false,
    },
    list: {
      type: Array,
      default() {
        return [];
      },
    },
  },
  computed: {
    ...mapState('apply', ['invoiceType']),
    PublicActivityElectron() {
      return PublicActivityElectron;
    },
    PublicActivityPaper() {
      return PublicActivityPaper;
    },
  },
  methods: {
    isContainsShudian(item) {
      let containsShudian = false;

      Object.keys(item.splitMap).forEach((pName) => {
        const pItems = item.splitMap[pName];
        pItems.forEach((pItem) => {
          // https://km.sankuai.com/collabpage/2137464784
          (pItem.invoiceList || []).forEach((invoice) => {
            if (invoice.invoiceType === 4 || invoice.invoiceType === 5) {
              containsShudian = true;
            }
          });
        });
      });

      return containsShudian;
    },
    calcRowSpan({ splitMap }) {
      let row = 0;

      Object.keys(splitMap).forEach((key) => {
        const arr = splitMap[key];

        row += arr.reduce((acc, cur) => acc + (cur.invoiceNos.length || 1), 0);
      });

      return row;
    },
    isPublicActivityInvoice(invoice) {
      // return (
      //   invoice.invoiceType === InvoiceTypeEnum.PublicActivityElectron
      //   || invoice.invoiceType === InvoiceTypeEnum.PublicActivityPaper
      // );
      return (
        invoice.invoiceFeeType === BaseFeeTypeEnum.PublicActivity
      );
    },
    isChildren(invoice) {
      return (
        invoice.invoiceFeeType === BaseFeeTypeEnum.Children
      );
    },
    isNatural(invoice) {
      return (
        invoice.invoiceFeeType === BaseFeeTypeEnum.Natural
      );
    },
    calcPartnerRowSpan(amountList) {
      return amountList.reduce(
        (acc, cur) => acc + (cur.invoiceNos.length || 1),
        0,
      );
    },
    getStatusClass(status) {
      if (status === 3 || status === 5 || status === 7) {
        // 3-取消 5-作废
        return this.$style.red;
      }
      if (status === 2 || status === 4) {
        // 2-已开票 4-已邮寄
        return this.$style.green;
      }
      return null;
    },
    handleExpressNoClick(id, expressNo) {
      if (expressNo) {
        this.$emit('express-track', {
          id,
          expressNo,
        });
      }
    },
  },
};
</script>

<style lang="scss" module>
.table {
  width: 100%;
  table-layout: fixed;
  border-collapse: collapse;
  border: 1px solid #e9eaf2;
  font-size: 14px;
  line-height: 20px;
  text-align: center;

  thead {
    background: #f7f8fa;
  }

  th {
    height: 40px;
    padding: 0 10px;
    color: #858692;
  }

  td {
    padding: 10px;
    word-break: break-all;
  }

  th,
  td {
    border: 1px solid #e9eaf2;
    text-align: center;
  }

  td.loading,
  td.empty {
    height: 260px;
    text-align: center;
  }

  .green {
    color: #63d29d;
  }
  .red {
    color: #f76c6c;
  }
  .yellow {
    color: #f8b500;
  }
}

.separator {
  display: inline-block;
  width: 1px;
  height: 12px;
  margin: 0 3px;
  background: #e9eaf2;
  vertical-align: middle;
}

.del {
  text-decoration: line-through;
}
</style>