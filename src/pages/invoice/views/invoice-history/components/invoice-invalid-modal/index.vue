<template>
    <roo-modal
        :value="value"
        :backdrop="loading ? 'static' : true"
        :title="`发票作废申请（ID: ${id}）`"
        @input="$emit('input', $event)"
        @hidden="handleHidden"
    >
        <div
            v-if="message"
            :class="$style.row"
        >
            {{ message }}
        </div>
        <div :class="$style.row">
            <div :class="$style.label">
                <span :class="$style.red">*</span>
                发票作废原因
            </div>
            <div :class="$style.right">
                <roo-select
                    v-model="invalidReasonCode"
                    :class="$style.select"
                    :options="invalidReasonOptions"
                    @click.native="handleSelectClick"
                />
            </div>
        </div>
        <div :class="$style.row">
            <div :class="$style.label">
                <span :class="$style.red">*</span>
                具体原因说明
            </div>
            <div :class="$style.right">
                <roo-input
                    v-model="comment"
                    :class="$style.textarea"
                    type="textarea"
                    placeholder="作废理由（至少输入 10 个字，不超过 100 字）"
                    noresize
                />
            </div>
        </div>
        <template slot="footer">
            <roo-button
                :disabled="loading"
                type="text"
                @click="$emit('input', false)"
            >
                取消
            </roo-button>
            <roo-button
                :loading="loading"
                :disabled="!(invalidReasonCode && comment)"
                @click="handleConfirmClick"
            >
                确定
            </roo-button>
        </template>
    </roo-modal>
</template>

<script>
/* eslint-disable import/no-unresolved, import/extensions */
import request from '$invoice/utils/request';
import { toast } from '@roo/roo-vue';

/* eslint-disable import/extensions, import/no-unresolved */
import toastError from '$lib/toast-error';
/* eslint-enable import/extensions, import/no-unresolved */

export default {
    name: 'InvoiceInvalidModal',
    props: {
        value: {
            type: Boolean,
            required: true,
        },
        id: {
            type: Number,
            required: false,
            default: null,
        },
        loading: {
            type: Boolean,
            default: false,
        },
        message: {
            type: String,
            default: '',
        },
    },
    data() {
        return {
            invalidReasons: [],
            invalidReasonCode: null,
            comment: '',
        };
    },
    computed: {
        invalidReasonOptions() {
            if (this.invalidReasons.length > 0) {
                return this.invalidReasons.map(item => ({
                    value: item.code,
                    label: item.message,
                }));
            }
            return [{
                value: -1,
                label: '加载中',
                disabled: true,
            }];
        },
    },
    methods: {
        loadOptions() {
            return request.get('/finance/invoice/api/common/application/r/queryInvalidReasonCodes')
                .then((res) => {
                    const { code, msg, data } = res.data;

                    if (code !== 0) {
                        throw new Error(msg);
                    }

                    this.invalidReasons = data || [];
                })
                .catch(toastError);
        },
        handleSelectClick() {
            if (this.invalidReasons.length === 0) {
                this.loadOptions();
            }
        },
        handleHidden() {
            this.comment = '';
            this.$emit('hidden');
        },
        handleConfirmClick() {
            if (!this.invalidReasonCode) {
                toast.warn('请选择发票作废原因');
                return;
            }
            if (!this.comment) {
                toast.warn('请填写作废理由');
                return;
            }
            this.$emit('confirm', {
                invalidReasonCode: this.invalidReasonCode,
                comment: this.comment,
            });
        },
    },
};
</script>

<style lang="scss" module>
.row {
    display: flex;
    margin-bottom: 15px;

    &:last-child {
        margin-bottom: 0;
    }
}
.label {
    width: 110px;
    line-height: 34px;
    flex-shrink: 0;
}
.right {
    flex: 1;
}
.red {
    color: #F76C6C;
}
.textarea:global(.roo-input) {
    height: 100px;
}
</style>
