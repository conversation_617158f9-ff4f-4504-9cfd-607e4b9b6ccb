<template>
    <roo-modal
        :value="value"
        :title="amount && amount.month"
        size="large"
        @input="$emit('input', $event)"
        @hidden="handleHidden"
    >
        <table :class="$style.table">
            <col style="width: 180px;" />
            <col style="width: 180px;" />
            <col style="width: 150px;" />
            <col />

            <thead>
                <tr>
                    <th>流水号</th>
                    <th>交易完成时间</th>
                    <th>金额 (元)</th>
                    <th></th>
                </tr>
            </thead>

            <tbody>
                <tr
                    v-if="loading"
                    key="loading"
                >
                    <td
                        :class="$style.loading"
                        colspan="4"
                    >
                        正在加载
                    </td>
                </tr>
                <tr
                    v-else-if="list.length === 0"
                    key="empty"
                >
                    <td
                        :class="$style.empty"
                        colspan="4"
                    >
                        暂无数据
                    </td>
                </tr>
                <template v-else>
                    <tr
                        v-for="item in list"
                        :key="item.id"
                    >
                        <td>
                            {{ item.outId }}
                        </td>
                        <td>
                            {{ item.confirmTime }}
                        </td>
                        <td>
                            {{ item.amountCent | formatCent }}
                        </td>
                        <td></td>
                    </tr>
                </template>
            </tbody>
        </table>

        <div
            :class="$style.pagination"
        >
            <roo-pagination
                :total="total"
                :page-size="pageSize"
                :current-page="pageNo"
                @current-change="pageNo = $event"
            />
        </div>
    </roo-modal>
</template>

<script>
import axios from 'axios';
import { mapState, mapGetters } from 'vuex';
/* eslint-disable import/no-unresolved, import/extensions */
import request from '$invoice/utils/request';

/* eslint-disable import/extensions, import/no-unresolved */
import toastError from '$lib/toast-error';
import { formatCent } from '$lib/filters';
/* eslint-enable import/extensions, import/no-unresolved */

const { CancelToken } = axios;

export default {
    name: 'AdDetailModal',
    filters: {
        formatCent,
    },
    props: {
        value: {
            type: Boolean,
            default: false,
        },
        amount: {
            type: Object,
            default: null,
        },
        partnerId: {
            type: Number,
            default: null,
        },
        applyId: {
            type: Number,
            default: null,
        },
        baseFeeType: {
            type: Number,
            default: null,
        },
        partnerType: {
            type: Number,
            default: null,
        },
    },
    data() {
        return {
            loading: false,

            pageNo: 1,
            pageSize: 10,
            detailList: [],
        };
    },
    computed: {
        ...mapState('apply', [
            'baseFeeTypeItem',
            'companySelected',
        ]),
        ...mapGetters('apply', ['productionName']),
        total() {
            return this.detailList.length;
        },
        list() {
            const { pageNo, pageSize, detailList } = this;
            const idx = (pageNo - 1) * pageSize;
            return detailList.slice(idx, idx + pageSize);
        },
    },
    watch: {
        value(val) {
            if (!val && this.source) {
                this.source.cancel();
                this.source = null;
            }
        },
        amount(obj) {
            if (obj) {
                const { id, adDetails } = obj;
                if (adDetails) {
                    this.detailList = adDetails;
                } else {
                    this.fetchAdDetail(id);
                }
            }
        },
    },
    created() {
        this.source = null;
    },
    methods: {
        fetchAdDetail(monthId) {
            if (this.source) {
                this.source.cancel();
                this.source = null;
            }

            const { baseFeeType, partnerType } = this;

            const params = {
                monthId,
                baseFeeType,
                partnerType,
                applyId: this.applyId,
                isInvoiceHistory: true,
            };

            // if (this.companySelected !== 0) {
            //     params.ownerShip = this.companySelected;
            //     params.productionName = this.productionName;
            // }

            this.source = CancelToken.source();
            const cancelToken = this.source.token;

            this.loading = true;
            return request.get('/finance/invoice/api/pc/application/r/adDetails', {
                params,
                cancelToken,
            }).then((res) => {
                this.loading = false;

                const { code, msg, data } = res.data;
                if (code !== 0) {
                    throw new Error(msg);
                }

                this.detailList = data || [];
            }, (err) => {
                this.loading = false;
                throw err;
            }).catch(toastError);
        },

        handleHidden(event) {
            this.loading = false;
            this.pageNo = 1;
            this.detailList = [];
            this.$emit('hidden', event);
        },
    },
};
</script>

<style lang="scss" module>
.modal :global(.roo-modal-body) {
    max-height: 400px;
    overflow-y: auto;
}

.row {
    padding-left: 20px;
    margin-bottom: 20px;
}

.scroller {
    width: 100%;
    max-height: 400px;
    overflow-y: auto;
}

.table {
    width: 100%;
    border: 1px solid #E9EAF2;
    table-layout: fixed;
    border-collapse: collapse;
    color: #3F4156;

    thead,
    tbody tr:nth-of-type(even) {
        background: #F7F8FA;
    }

    th, td {
        height: 40px;
        padding: 0 20px;
        text-align: left;

        &:nth-child(3) {
            text-align: right;
        }
    }

    th {
        font-weight: normal;
        color: #858692;
    }

    .loading, .empty {
        text-align: center;
    }
}

.pagination {
    padding: 10px 0;
    text-align: center;
}
</style>
