<template>
    <table :class="$style.table">
        <col style="width: 100px;">
        <col style="width: 300px;">
        <col style="width: 150px;">
        <col>

        <thead>
            <tr>
                <th />
                <th>抬头</th>
                <th>金额</th>
                <th>发票号</th>
            </tr>
        </thead>

        <tbody>
            <tr
                v-for="({ partnerBName, amountCent, invoiceNos }, index) in list"
                :key="index"
            >
                <td>发票{{ index + 1 }}</td>
                <td>{{ partnerBName }}</td>
                <td>{{ amountCent | formatCent }}</td>
                <td>{{ invoiceNos.join('、') }}</td>
            </tr>
        </tbody>
    </table>
</template>

<script>
/* eslint-disable import/extensions, import/no-unresolved */
import { formatCent } from '$lib/filters';
/* eslint-enable import/extensions, import/no-unresolved */

export default {
    name: 'AmountSplitTable',
    filters: {
        formatCent,
    },
    props: {
        list: {
            type: Array,
            default() {
                return [];
            },
        },
    },
};
</script>

<style lang="scss" module>
// 你会看到很多段这样的代码，这是因为表格的对齐、高度总是有细微不同，最重要的是我代码不精、封装无力、架构松散...
.table {
    width: 100%;
    // table-layout: fixed;
    border-collapse: collapse;
    border: 1px solid #E9EAF2;
    font-size: 14px;
    line-height: 20px;
    margin-bottom: 20px;

    thead,
    tbody tr:nth-of-type(even) {
        background: #F7F8FA;
    }

    th {
        color: #858692;
    }

    th, td {
        height: 40px;
        padding: 0 20px;
        text-align: left;

        &:nth-child(1) {
            text-align: center;
        }
        &:nth-child(3) {
            text-align: right;
        }
    }

    .loading,
    .empty {
        text-align: center;
    }
}
</style>
