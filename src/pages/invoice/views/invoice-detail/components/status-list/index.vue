<template>
    <div>
        <ul :class="$style.statusList">
            <li
                v-for="(item, idx) in list"
                :key="idx"
            >
                <div :class="$style.date">
                    {{ item.date }}
                </div>
                <div :class="$style.status">
                    <div>{{ item.info }}</div>
                    <div :class="$style.tip">{{ item.comment }}</div>
                </div>
            </li>
        </ul>

        <div
            v-if="statusList.length > 1"
            key="button"
            :class="$style.center"
        >
            <roo-button
                :class="$style.btn"
                type="hollow"
                size="mini"
                @click="collapsed = !collapsed"
            >
                {{ collapsed ? '查看所有记录' : '收起所有记录' }}
            </roo-button>
        </div>
    </div>
</template>

<script>
export default {
    name: 'StatusList',
    filters: {
        status2text(status) {
            return [
                '提交中',
                '开票中',
                '已开票',
                '已取消',
                '已邮寄',
                '已作废',
                '作废中',
            ][status];
        },
    },
    props: {
        statusList: {
            type: Array,
            default() {
                return [];
            },
        },
    },
    data() {
        return {
            collapsed: true,
        };
    },
    computed: {
        list() {
            if (this.collapsed) {
                return this.statusList.slice(0, 1);
            }
            return this.statusList;
        },
    },
};
</script>

<style lang="scss" module>
.status-list {
    list-style: none;

    li {
        display: flex;
        margin-bottom: 12px;
    }
}
.date {
    width: 80px;
    flex-shrink: 0;
    margin-right: 25px;
    text-align: right;
    color: #858692;
}
.status {
    flex: 1;
}
.tip {
    color: #858692;
    font-size: 12px;
}
.center {
    padding-left: 250px;
}
.btn :global(.btn) {
    width: 100px;
}
</style>
