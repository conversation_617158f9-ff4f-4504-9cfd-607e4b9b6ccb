<template>
    <table :class="$style.table">
        <col>
        <col>
        <col>
        <col>

        <thead>
            <tr>
                <th>结算设置 ID</th>
                <th>开户名</th>
                <th>金额</th>
                <th>操作</th>
            </tr>
        </thead>

        <tbody>
            <tr
                v-for="item in list"
                :key="item.settleId"
            >
                <td>{{ item.settleId }}</td>
                <td>{{ item.accountName }}</td>
                <td>{{ item.moneyCent | formatCent }}</td>
                <td>
                    <a
                        href="#noop"
                        @click.prevent="$emit('show-detail', item)"
                    >
                        查看详情
                    </a>
                </td>
            </tr>
        </tbody>
    </table>
</template>

<script>
/* eslint-disable import/extensions, import/no-unresolved */
import { formatCent } from '$lib/filters';
import { emptyArray } from '$lib/utils';
/* eslint-enable import/extensions, import/no-unresolved */

export default {
    name: 'SettleAmountList',
    filters: {
        formatCent,
    },
    props: {
        list: {
            type: Array,
            default: emptyArray,
        },
    },
};
</script>

<style lang="scss" module>
// 你会看到很多段这样的代码，这是因为表格的对齐、高度等样式总是有细微不同，最重要的是我代码不精、封装无力、架构松散...
.table {
    width: 100%;
    table-layout: fixed;
    border-collapse: collapse;
    border: 1px solid #E9EAF2;
    font-size: 14px;
    line-height: 20px;

    thead,
    tbody tr:nth-of-type(even) {
        background: #F7F8FA;
    }

    th {
        color: #858692;
    }

    th, td {
        height: 40px;
        padding: 0 20px;
        text-align: left;

        &:nth-child(1) {
            text-align: center;
        }
        &:nth-child(3) {
            padding-right: 80px;
            text-align: right;
        }
    }

    .loading,
    .empty {
        text-align: center;
    }
}
</style>
