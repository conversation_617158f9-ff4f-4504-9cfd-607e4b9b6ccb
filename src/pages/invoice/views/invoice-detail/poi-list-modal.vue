<template>
    <roo-modal
        :value="value"
        size="large"
        @input="$emit('input', $event)"
        @hidden="handleHidden"
    >
        <template slot="title">
            {{ amount && amount.accountName }}
            <span :class="[$style.tip, $style.gray]">
                所属结算设置 ID:
            </span>
            <span :class="$style.tip">
                {{ amount && amount.settleId }}
            </span>
        </template>

        <table :class="$style.table">
            <col style="width: 150px;" />
            <col />
            <col style="width: 80px;" />

            <thead>
                <tr>
                    <th>商家 ID</th>
                    <th>商家名称</th>
                    <th>明细</th>
                </tr>
            </thead>

            <tbody>
                <tr
                    v-if="loading"
                    key="loading"
                >
                    <td
                        :class="$style.loading"
                        colspan="3"
                    >
                        正在加载
                    </td>
                </tr>
                <tr
                    v-else-if="list.length === 0"
                    key="empty"
                >
                    <td
                        :class="$style.empty"
                        colspan="3"
                    >
                        暂无数据
                    </td>
                </tr>
                <template v-else>
                    <tr
                        v-for="item in list"
                        :key="item.id"
                    >
                        <td>
                            {{ item.id }}
                        </td>
                        <td>
                            {{ item.name }}
                        </td>
                        <td>
                            <!-- 开票历史 - 明细弹窗 -->
                            <bubble-comp
                                :param="{
                                    partnerId: item.id,
                                    applyId: applyId,
                                    settleId: amount.settleId,
                                }"
                            />
                        </td>
                    </tr>
                </template>
            </tbody>
        </table>

        <div
            :class="$style.pagination"
        >
            <roo-pagination
                :total="total"
                :page-size="pageSize"
                :current-page="pageNo"
                @current-change="pageNo = $event"
            />
        </div>
    </roo-modal>
</template>

<script>
import axios from 'axios';
/* eslint-disable import/no-unresolved, import/extensions */
import request from '$invoice/utils/request';
import { Bubble } from '@wmfe/svelte-components';
import toVue from 'svelte-adapter/vue';
/* eslint-disable import/extensions, import/no-unresolved */
import toastError from '$lib/toast-error';
/* eslint-enable import/extensions, import/no-unresolved */

const { CancelToken } = axios;

export default {
    name: 'PoiListModal',
    components: {
        BubbleComp: toVue(Bubble),
    },
    props: {
        value: {
            type: Boolean,
            default: false,
        },
        amount: {
            // 结算设置 { settleId, accountName, poiList }
            type: Object,
            default: null,
        },
        applyId: {
            type: Number,
            default: null,
        },
        contractId: {
            type: Number,
            default: null,
        },
        dateFrom: {
            type: String,
            default: '',
        },
        dateTo: {
            type: String,
            default: '',
        },
    },
    data() {
        return {
            loading: false,

            poiList: [],
            pageNo: 1,
            pageSize: 10,
        };
    },
    computed: {
        total() {
            return this.poiList.length;
        },
        list() {
            const { poiList, pageNo, pageSize } = this;
            const offset = (pageNo - 1) * pageSize;
            return poiList.slice(offset, offset + pageSize);
        },
    },
    watch: {
        value(val) {
            if (!val && this.source) {
                this.source.cancel();
                this.source = null;
            }
        },
        amount(obj) {
            if (obj) {
                const { settleId, poiList } = obj;
                if (poiList) {
                    this.poiList = poiList;
                } else {
                    this.fetchPoiList(settleId);
                }
            }
        },
    },
    created() {
        this.source = null;
    },
    mounted() {},
    methods: {
        fetchPoiList(settleId) {
            if (this.source) {
                this.source = null;
                this.source.cancel();
            }

            this.source = CancelToken.source();
            const cancelToken = this.source.token;

            this.loading = true;
            return request.get('/finance/invoice/api/pc/application/r/queryPoiList', {
                params: {
                    settleId,
                    applyId: this.applyId,
                    contractId: this.contractId,
                    dateFrom: this.dateFrom,
                    dateTo: this.dateTo,
                },
                cancelToken,
            })
                .then(
                    (res) => {
                        this.source = null;
                        this.loading = false;

                        const { code, msg, data } = res.data;
                        if (code !== 0) {
                            throw new Error(msg);
                        }

                        this.poiList = (data || []).map((item) => ({
                            id: item.wmPoiId,
                            name: item.wmPoiName,
                        }));
                        this.$set(this.amount, 'poiList', this.poiList);
                    },
                    (err) => {
                        this.source = null;
                        this.loading = false;
                        throw err;
                    },
                )
                .catch(toastError);
        },
        handleHidden(event) {
            this.loading = false;
            this.poiList = [];
            this.pageNo = 1;
            this.$emit('hidden', event);
        },
    },
};
</script>

<style lang="scss" module>
.tip {
  font-weight: normal;
  font-size: 14px;
}
.gray {
  color: #858692;
}

.table {
  width: 100%;
  border: 1px solid #e9eaf2;
  table-layout: fixed;
  border-collapse: collapse;
  color: #3f4156;

  thead,
  tbody tr:nth-of-type(even) {
    background: #f7f8fa;
  }

  th,
  td {
    height: 40px;
    padding: 0 20px;
    text-align: left;
  }

  th {
    font-weight: normal;
    color: #858692;
  }

  .loading,
  .empty {
    text-align: center;
  }
}

.pagination {
  padding: 10px 0;
  text-align: center;
}
</style>
