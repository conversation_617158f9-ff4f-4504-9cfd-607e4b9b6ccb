<template>
    <div :class="$style.container">
        <div>
            <roo-breadcrumb :items="breadcrumb" />

            <roo-button
                :class="$style.back"
                size="mini"
                type="hollow"
                @click="$router.go(-1)"
            >
                返回
            </roo-button>
        </div>

        <div :class="$style.card">
            <h3>发票状态</h3>

            <status-list :status-list="statusList" />

            <h3>基本信息</h3>

            <ul :class="$style.baseInfoList">
                <li
                    v-for="item in baseInfoList"
                    :key="item.key"
                >
                    <div :class="$style.label">
                        {{ item.label }}
                    </div>
                    <div :class="$style.text">
                        {{ item.text }}
                    </div>
                </li>
            </ul>

            <template
                v-if="splitList && splitList.length > 0"
            >
                <h3>金额拆分</h3>

                <amount-split-table
                    :list="splitList"
                />
            </template>

            <h3>开票信息</h3>

            <settle-amount-list
                v-if="amountGatherType === 1 && settleAmountList.length > 0"
                key="settleAmountList"
                :list="settleAmountList"
                @show-detail="poiAmount = $event; displayPoiList = true;"
            />

            <!-- 有详情，可看流水 -->
            <ad-month-amount-list
                v-else-if="partnerType !== 103 && monthAmountList.length > 0"
                key="adMonthAmountList"
                :list="monthAmountList"
                @show-detail="adAmount = $event; displayAdDetail = true;"
            />

            <month-amount-list
                v-else
                key="monthAmountList"
                :list="monthAmountList"
            />

            <div
                v-if="total > 0"
                :class="$style.pagination"
            >
                <roo-button
                    v-if="loadedCount < total"
                    key="next"
                    :class="$style.btn"
                    :loading="loading2"
                    size="mini"
                    type="hollow"
                    @click="loadMore"
                >
                    {{ loading ? '加载中' : `查看更多 第 ${nextPageText} 条 (共 ${total} 条)` }}
                </roo-button>
                <span
                    v-else
                    key="done"
                    :class="$style.done"
                >
                    已加载完毕
                </span>
            </div>
        </div>

        <ad-detail-modal
            v-model="displayAdDetail"
            :apply-id="id"
            :base-fee-type="invoiceFeeType"
            :partner-type="partnerType"
            :amount="adAmount"
            @hidden="adAmount = null"
        />

        <poi-list-modal
            v-model="displayPoiList"
            :apply-id="id"
            :contract-id="contractId"
            :amount="poiAmount"
            :date-from="applyTimeFrom"
            :date-to="applyTimeTo"
            @hidden="poiAmount = null"
        />

        <roo-loading
            :show="loading1"
            fullscreen
            backdrop
            lock
        />
    </div>
</template>

<script>
import axios from 'axios';
/* eslint-disable import/no-unresolved, import/extensions */
import request from '$invoice/utils/request';
import { mapState } from 'vuex';

/* eslint-disable import/extensions, import/no-unresolved */
import { formatCent } from '$lib/filters';
import toastError from '$lib/toast-error';
import { InvoiceTypeEnum } from '$config/enum';
import { getCookieValue } from '$lib/utils';
/* eslint-enable import/extensions, import/no-unresolved */

import StatusList from './components/status-list';
import AmountSplitTable from './components/amount-split-table';

import MonthAmountList from './components/month-amount-list';
import SettleAmountList from './components/settle-amount-list';
import AdMonthAmountList from './components/ad-month-amount-list';

import PoiListModal from './poi-list-modal';
import AdDetailModal from './ad-detail-modal';

const { CancelToken } = axios;

export default {
    name: 'InvoiceDetail',
    filters: {
        formatCent,
    },
    components: {
        StatusList,
        AmountSplitTable,
        MonthAmountList,
        SettleAmountList,
        AdMonthAmountList,

        PoiListModal,
        AdDetailModal,
    },
    data() {
        return {
            loading1: false, // baseInfo
            loading2: false, // amountList

            // baseInfo
            id: null,

            partnerId: null,
            partnerType: 101,
            partnerTypeName: '',

            applyTimeFrom: '',
            applyTimeTo: '',

            invoiceType: 1,
            invoiceTypeName: '',

            invoiceFeeType: 1,
            invoiceFeeTypeName: '',

            invoiceItemName: '', // 开票项目
            contractName: '', // 所属公司

            receiver: '',
            expressProvince: '',
            expressCity: '',
            expressDistrict: '',
            address: '',
            phoneNo: '',
            telNo: '',
            remark: '',
            contact: '', // 发票联络人

            invoiceNos: [],
            invoiceCodes: [],
            statusList: [],
            splitList: [],

            // amountList
            total: 0,
            pageNo: 1,
            pageSize: 10,
            amountGatherType: 1,
            monthAmountList: [],
            settleAmountList: [],

            displayAdDetail: false,
            adAmount: null,

            displayPoiList: false,
            poiAmount: null,
        };
    },
    computed: {
        ...mapState('config', [
            'contractId',
        ]),
        isPublicActivityInvoice() {
            return this.invoiceType === InvoiceTypeEnum.PublicActivityElectron || this.invoiceType === InvoiceTypeEnum.PublicActivityPaper;
        },
        isElectronInvoice() {
            return this.invoiceType === InvoiceTypeEnum.Electron || this.invoiceType === InvoiceTypeEnum.PublicActivityElectron;
        },
        breadcrumb() {
            return [{
                text: '开票历史',
            }, {
                text: '发票详情',
            }];
        },
        wmPoiId() {
            return parseInt(getCookieValue('wmPoiId'), 10) || -1;
        },
        applyId() {
            return parseInt(this.$route.query.id, 10);
        },
        baseInfoList() {
            const list = [{
                key: 'apply-date-from',
                label: '开票起始时间',
                text: this.applyTimeFrom,
            }, {
                key: 'apply-date-to',
                label: '开票结束时间',
                text: this.applyTimeTo,
            }, {
                key: 'invoice-type',
                label: '发票种类',
                text: this.invoiceTypeName,
            }, {
                key: 'invoice-fee-type',
                label: '明细项目',
                text: this.invoiceFeeTypeName,
            }, {
                key: 'invoice-item-name',
                label: '开票项目',
                text: this.invoiceItemName,
            }, {
                key: 'contract-b-name',
                label: '所属公司',
                text: this.contractName,
            }];

            if (this.isElectronInvoice) { // 电子发票
                list.push({
                    key: 'receiver-email',
                    label: '电子邮箱',
                    text: this.receiverEmail,
                });
            } else {
                list.push({
                    key: 'receiver',
                    label: '收件人',
                    text: this.receiver,
                }, {
                    key: 'address',
                    label: '详细地址',
                    text: `${this.expressProvince} ${this.expressCity} ${this.expressDistrict} ${this.address}`,
                }, {
                    key: 'phoneNo',
                    label: '手机',
                    text: this.phoneNo,
                }, {
                    key: 'telNo',
                    label: '固话',
                    text: this.telNo || '无',
                });
            }

            if (!this.isPublicActivityInvoice) {
                list.push({
                    key: 'remark',
                    label: '票面备注',
                    text: this.remark,
                });
            }

            if (this.invoiceNos && this.invoiceNos.length > 0) {
                list.push({
                    key: 'invoiceNo',
                    label: '发票号码',
                    text: this.invoiceNos.join('、'),
                });
            }
            if (this.invoiceCodes && this.invoiceCodes.length > 0) {
                list.push({
                    key: 'invoiceCode',
                    label: '发票代码',
                    text: this.invoiceCodes.join('、'),
                });
            }

            return list;
        },

        loadedCount() {
            return this.settleAmountList.length
                || this.monthAmountList.length;
        },

        nextPageStart() {
            const { pageNo, pageSize } = this;
            return (pageNo * pageSize) + 1;
        },
        nextPageEnd() {
            const { pageNo, pageSize, total } = this;
            return Math.min(total, ((pageNo + 1) * pageSize));
        },
        nextPageText() {
            const { nextPageStart, nextPageEnd } = this;
            if (nextPageStart === nextPageEnd) {
                return `${nextPageStart}`;
            }
            return `${nextPageStart} ~ ${nextPageEnd}`;
        },
    },
    created() {
        this.source1 = null;
        this.source2 = null;
    },
    mounted() {
        this.fetchBaseInfo(this.applyId)
            .then(() => {
                this.fetchAmountList(1);
            });
    },
    methods: {
        fetchBaseInfo(id) {
            if (this.source1) {
                this.source1.cancel();
                this.source1 = null;
            }

            const params = { applyId: id };

            this.source1 = CancelToken.source();
            const cancelToken = this.source1.token;

            this.loading1 = true;
            return request.get('/finance/invoice/api/pc/application/r/queryDetail/basicInfo', {
                params,
                cancelToken,
            }).then((res) => {
                this.loading1 = false;

                const { code, msg, data } = res.data;
                if (code !== 0) {
                    throw new Error(msg);
                }

                Object.assign(this, data);
            }, (err) => {
                this.loading1 = false;
                throw err;
            }).catch(toastError);
        },
        fetchAmountList(pageNo) {
            if (this.source2) {
                this.source2.cancel();
                this.source2 = null;
            }

            const params = {
                applyId: this.applyId,
                pageNo,
                pageSize: this.pageSize,
                isInvoiceHistory: true,
            };

            this.source2 = CancelToken.source();
            const cancelToken = this.source2.token;

            this.loading2 = true;
            return request.get('/finance/invoice/api/pc/application/r/queryDetail/historyAmountList', {
                params,
                cancelToken,
            }).then((res) => {
                this.loading2 = false;

                const { code, msg, data } = res.data;
                if (code !== 0) {
                    throw new Error(msg);
                }

                this.amountGatherType = data.amountGatherType || 1;
                this.total = data.totalCount || 0;
                this.pageNo = data.pageNo || pageNo;
                this.pageSize = data.pageSize || this.pageSize;
                if (this.pageNo === 1) {
                    this.monthAmountList = data.monthAmountList || [];
                    this.settleAmountList = data.settleAmountList || [];
                } else {
                    this.monthAmountList = this.monthAmountList.concat(data.monthAmountList || []);
                    this.settleAmountList = this.settleAmountList.concat(data.settleAmountList || []);
                }
            }, (err) => {
                this.loading2 = false;
                throw err;
            }).catch(toastError);
        },
        loadMore() {
            this.fetchAmountList(this.pageNo + 1);
        },
    },
};
</script>

<style lang="scss" module>
.container {
    padding: 10px;
}

.back {
    position: absolute;
    top: 10px;
    right: 10px;
}

.card {
    padding: 40px;
    background: #FFF;
    box-shadow: 0 0 6px 0 #F3F3F4;

    h3 {
        margin: 0 0 20px 0;
        font-size: 20px;
        color: #3F4156;
    }
}

.base-info-list {
    list-style: none;

    li {
        display: flex;
        margin-bottom: 20px;
    }
}
.label {
    width: 100px;
    margin-right: 20px;
    text-align: right;
    color: #858692;
}
.text {
    flex: 1;
}

.pagination {
    margin-top: 20px;
    text-align: center;
}
.btn {
    min-width: 240px;
}
.done {
    font-size: 14px;
    color: #A0A0A0;
}
</style>
