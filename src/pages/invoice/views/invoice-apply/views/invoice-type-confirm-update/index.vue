<template>
    <div :class="$style.module">
        <roo-alert
            v-if="!isPublicInvoice"
            :class="$style.alert"
            type="warning"
            icon="exclamation-circle"
        >
            <!-- 为响应国税总局全面推广数电发票的工作要求，预计2024年3月起（或按实际情况），部分开票主体将为您开具数电发票。数电发票的法律效力、基本用途等与现有纸质发票相同。请贵司合理安排接收数电发票，为您带来的不便，敬请谅解。 -->
            接中华环境保护基金会通知，从2024年11月1日开始，基金会停止开具纸质发票，已提交开具的纸票不受影响。数电发票的法律效力、基本用途等与现有纸质发票相同。请贵司合理安排接收数电发票，给您带来的不便，敬请谅解。
        </roo-alert>

        <h3 class="h3">
            填写票种信息
        </h3>
        <div>
            <span :class="$style.label">
                发票种类：
            </span>
            <roo-radio-group :class="$style.radioGroup" :value="invoiceType" @input="changeInvoiceType">
                <roo-radio
                    v-for="opt in filteredInvoiceTypeOptions"
                    :key="opt.value"
                    :value="opt.value"
                    :class="$style.invoiceOption"
                >
                    {{ opt.label }}
                    <roo-tooltip
                        v-if="opt.tips"
                        placement="top"
                        :content="opt.tips"
                    />
                    <!-- <span v-if="!isPublicInvoice && opt.value === InvoiceTypeEnum.Electron" :class="$style.invoiceNotice">
                        (电子发票法律效力等同于纸质发票)
                    </span> -->
                </roo-radio>
            </roo-radio-group>
        </div>
        <roo-form v-for="q in showQualificationList" ref="forms" :key="q.id" :class="$style.form" :model="q">
            <h4>发票抬头：{{ q.partnerBName }}</h4>

            <roo-form-item
                v-if="!isPublicInvoice"
                key="taxpayerIdNo"
                label="纳税人识别号"
                prop="taxpayerIdNo"
                required
            >
                <roo-input
                    :class="$style.input"
                    :value="q.taxpayerIdNo"
                    placeholder="根据发票抬头自动带出纳税人识别号"
                    disabled
                    @input="changeQualificationField({ id: q.id, prop: 'taxpayerIdNo', value: $event })"
                />
                <div v-show="!q.taxpayerIdNo" style="color: red;">
                    未查询到有效纳税人识别号，请选择其他发票抬头或新增发票抬头
                </div>
            </roo-form-item>

            <div :class="$style.showMoreInfo">
                <span style="vertical-align:bottom;cursor:pointer;" @click="hideOrShowMoreInfo">
                    <span>更多信息（非必填）</span>
                    <img
                        v-if="!showMoreInfo"
                        :class="$style.showMoreInfoImg"
                        src="https://s3plus.meituan.net/v1/mss_0e4451a82e1d428f9fc3b99c0b3ebf15/pic-manager/ad3744486b8ebee4f51a3d100eae3510/<EMAIL>"
                    />
                    <img
                        v-if="showMoreInfo"
                        :class="$style.showMoreInfoImg"
                        src="https://s3plus.meituan.net/v1/mss_0e4451a82e1d428f9fc3b99c0b3ebf15/pic-manager/ea7cceae3da8b6f28b546b2d79f765f8/<EMAIL>"
                    />
                </span>
            </div>

            <template v-if="showMoreInfo">
                <roo-form-item
                    key="registerAddress"
                    :rules="rules.registerAddress"
                    :required="false"
                    prop="registerAddress"
                    label="注册地址"
                >
                    <roo-input
                        :class="$style.input"
                        :value="q.registerAddress"
                        placeholder="请输入注册地址"
                        @input="changeQualificationField({ id: q.id, prop: 'registerAddress', value: $event })"
                    />
                </roo-form-item>

                <roo-form-item
                    key="telNo"
                    :rules="rules.telNo"
                    :required="false"
                    prop="telNo"
                    label="电话"
                >
                    <roo-input
                        :class="$style.input"
                        :value="q.telNo"
                        placeholder="请输入电话"
                        @input="changeQualificationField({ id: q.id, prop: 'telNo', value: $event })"
                    />
                </roo-form-item>

                <roo-form-item
                    key="bankName"
                    :rules="rules.bankName"
                    :required="false"
                    prop="bankName"
                    label="开户行"
                >
                    <roo-input
                        :class="$style.input"
                        :value="q.bankName"
                        placeholder="请输入开户行"
                        @input="changeQualificationField({ id: q.id, prop: 'bankName', value: $event })"
                    />
                </roo-form-item>

                <roo-form-item
                    key="cardNo"
                    :rules="rules.cardNo"
                    :required="false"
                    prop="cardNo"
                    label="银行账户"
                >
                    <roo-input
                        :class="$style.input"
                        :value="q.cardNo"
                        placeholder="请输入银行账户"
                        @input="changeQualificationField({ id: q.id, prop: 'cardNo', value: $event })"
                    />
                </roo-form-item>
            </template>
        </roo-form>

        <roo-modal
            v-model="displayUpdateConfirm"
            size="small"
        >
            <div :class="$style.confirmTitleWrapper">
                <div :class="$style.confirmTitle">
                    票种信息有变更
                </div>
                <div :class="$style.confirmContent">
                    是否替换原票种信息
                </div>
            </div>
            <template slot="footer">
                <roo-button @click="handleNextClick">
                    确认
                </roo-button>
            </template>
        </roo-modal>
        <div v-if="isPublicInvoice">
            <p>请您务必按实际提供捐赠的公司，正确选择公司抬头信息申请开具公益捐赠票据。如抬头等开立票据信息提供有误，导致捐赠票据无法正常使用，需要您自行联系公益机构进行核对换开。</p>
            <roo-checkbox :checked="isPublicConfirmed" @change="handlePublicConfirmChange">
                已确认
            </roo-checkbox>
        </div>
        <div class="footer-nav">
            <roo-button :class="$style.mr16" :disabled="loading" type="brand-hollow" @click="handlePrevClick">
                上一步
            </roo-button>
            <roo-button
                :disabled="!invoiceType || (isPublicInvoice && !isPublicConfirmed)"
                :loading="loading"
                type="brand"
                @click="handleJudgeValueEqual"
            >
                下一步
            </roo-button>
        </div>
    </div>
</template>

<script>
/* eslint-disable import/no-unresolved, import/extensions */
import request from '$invoice/utils/request';
import { mapState, mapGetters, mapMutations } from 'vuex';

/* eslint-disable import/extensions, import/no-unresolved */
import toastError from '$lib/toast-error';
import { pattern } from '$lib/has-invalid-charactor';
import {
    InvoiceTypeEnum,
    NormalInvoiceTypeList,
    PublicActivityInvoiceTypeList,
    BaseFeeTypeEnum,
    ShudianNormalInvoiceTypeList,
} from '$config/enum';
/* eslint-enable import/extensions, import/no-unresolved */

export default {
    name: 'InvoiceTypeConfirm',
    data() {
        return {
            InvoiceTypeEnum, // todo 可以考虑把这个变量放在vue的全局引入,虽然有损性能,但是引用方便
            loading: false, // 资质保存
            showMoreInfo: false, // 是否显示更多信息
            tempQualificationList: [], // 暂存未改动字段值
            displayUpdateConfirm: false,
            taxpayerIdNoError: '',
            isPublicConfirmed: false,
        };
    },
    computed: {
        ...mapState('config', ['contractId', 'useNewSplit', 'isSinglePoi', 'isShudianHit', 'needVerification']),
        ...mapState('apply', ['baseFeeTypeItem', 'qualifications', 'invoiceType', 'splitInfos']),
        ...mapGetters('amountSelect', ['checkedAmountList', 'checkedMoneyMilli']),
        ...mapGetters('apply', ['qualificationUsed']),
        filteredInvoiceTypeOptions() {
            return this.invoiceTypeOptions.filter(opt => opt.value !== this.InvoiceTypeEnum.VATNormal
                && opt.value !== this.InvoiceTypeEnum.PublicActivityPaper,
            );
        },
        // 使用新的发票拆分方案
        isNew() {
            return this.useNewSplit && this.baseFeeTypeItem.amountGatherType === 1;
        },
        isPublicInvoice() {
            return (
                this.baseFeeTypeItem.baseFeeType === BaseFeeTypeEnum.PublicActivity || this.baseFeeTypeItem.baseFeeType === BaseFeeTypeEnum.Children || this.baseFeeTypeItem.baseFeeType === BaseFeeTypeEnum.Natural
            );
        },
        invoiceTypeOptions() {
            return this.baseFeeTypeItem.baseFeeType === BaseFeeTypeEnum.PublicActivity || this.baseFeeTypeItem.baseFeeType === BaseFeeTypeEnum.Children || this.baseFeeTypeItem.baseFeeType === BaseFeeTypeEnum.Natural
                ? PublicActivityInvoiceTypeList
                : (this.isShudianHit ? ShudianNormalInvoiceTypeList : NormalInvoiceTypeList);
        },
        // !!! 有些抬头开票金额为 0 就不展示了
        showQualificationList() {
            const { qualifications, qualificationUsed } = this;
            const list = [];
            // 走旧的开票逻辑
            for (let i = 0; i < qualifications.length; i++) {
                const q = qualifications[i];
                if (qualificationUsed.includes(q.id)) {
                    list.push(q);
                }
            }
            console.log('showQualificationList:', JSON.parse(JSON.stringify(list)));
            return list;
        },
        isVATSpecialInvoice() { // 是否选中的是增值税专用发票
            return this.invoiceType === InvoiceTypeEnum.VATSpecial;
        },
        rules() {
            return {
                taxpayerIdNo: [
                    {
                        required: this.isVATSpecialInvoice || this.invoiceType === InvoiceTypeEnum.Electron,
                        message: '请填写纳税人识别号',
                    },
                    {
                        pattern: /^[\d\w]{15,20}$/,
                        message: '纳税人识别号为15-20位数字或字母',
                    },
                ],
                // 专票必填
                registerAddress: [
                    {
                        required: false,
                        message: '请填写注册地址',
                    },
                    {
                        pattern,
                        message: '注册地址不能包含空格和特殊字符，如&，？等',
                    },
                ],
                telNo: [
                    {
                        required: false,
                        message: '请填写电话',
                    },
                    {
                        pattern,
                        message: '电话不能包含空格和特殊字符，如&，？等',
                    },
                ],
                bankName: [
                    {
                        required: false,
                        message: '请填写开户行',
                    },
                    {
                        pattern,
                        message: '开户行不能包含空格和特殊字符，如&，？等',
                    },
                ],
                cardNo: [
                    {
                        required: false,
                        message: '请填写银行账户',
                    },
                    {
                        pattern,
                        message: '银行账户不能包含空格和特殊字符，如&，？等',
                    },
                ],
            };
        },
    },
    mounted() {
        const { showQualificationList } = this;
        let invoiceType = InvoiceTypeEnum.Electron;
        const isNeedSetDefaultInvoiceType = this.qualifications.some(
            (item) => !item.hasSetDefaultInvoiceType,
        );
        if (!isNeedSetDefaultInvoiceType) return;
        if (this.isPublicInvoice) {
            // const isAllPager = showQualificationList.length && showQualificationList.every(q => q.invoiceType === InvoiceTypeEnum.PublicActivityPaper);
            // invoiceType = isAllPager ? InvoiceTypeEnum.PublicActivityPaper : InvoiceTypeEnum.PublicActivityElectron;
            invoiceType = InvoiceTypeEnum.PublicActivityElectron;
            this.changeInvoiceType(invoiceType);
            return;
        }

        if (
            showQualificationList.some(
                (q) => q.invoiceType === InvoiceTypeEnum.VATSpecial,
            )
        ) {
            invoiceType = InvoiceTypeEnum.VATSpecial;
        }
        this.changeInvoiceType(invoiceType);

        if (!this.isPublicInvoice && this.isShudianHit) {
            // 命中数电发票灰度，默认选中 专票
            this.changeInvoiceType(InvoiceTypeEnum.VATSpecial);
        }
    },
    methods: {
        ...mapMutations('apply', [
            'changeInvoiceType',
            'changeQualificationField',
            'resetFields',
        ]),
        hideOrShowMoreInfo() {
            this.showMoreInfo = !this.showMoreInfo;
        },
        handlePublicConfirmChange(checked) {
            this.isPublicConfirmed = checked;
        },
        validate() {
            // 青山公益开票没有表单校验
            if (this.isPublicInvoice) {
                return Promise.resolve();
            }
            return new Promise((resolve, reject) => {
                const { forms } = this.$refs;
                const len = forms.length;

                let i = 0;

                function cb(pass) {
                    if (pass) {
                        i += 1;

                        if (i === len) {
                            resolve();
                        }
                    } else {
                        reject(new Error('请检查票面信息填写'));
                    }
                }

                forms.forEach((f) => f.validate(cb));
            });
        },
        save() {
            this.resetFields();
            // 青山公益开票不修改资质
            if (this.isPublicInvoice) {
                return Promise.resolve();
            }

            const {
                contractId,
                baseFeeTypeItem,
                showQualificationList,
                invoiceType,
            } = this;

            // !!! 只保存需要填写（拆分得的金额为 0）的资质
            // 如果不这么做会导致 bug: https://flow.sankuai.com/browse/WAIMAIFM-5351
            const applyList = showQualificationList
                .filter((q) => q.changed)
                .map((q) => ({
                    qualificationId: q.id,
                    partnerBName: q.partnerBName,
                    invoiceType,
                    taxpayerIdNo: q.taxpayerIdNo,
                    registerAddress: q.registerAddress,
                    telNo: q.telNo,
                    bankName: q.bankName,
                    cardNo: q.cardNo,
                    updateType: 2,
                }));

            if (applyList.length === 0) {
                return Promise.resolve();
            }

            const body = {
                contractId,
                invoiceType,
                partnerType: baseFeeTypeItem.partnerType,
                qualificationType: baseFeeTypeItem.qualificationType,
                applyList,
            };

            this.loading = true;

            const requestUrl = !this.isSinglePoi ? '/finance/invoice/api/output/waimai/q/updateInvoiceTitles' : '/finance/invoice/api/common/qualification/w/save';
            if (this.isSinglePoi && this.needVerification === true) {
                body.needCheckCode = false;
            }
            return request
                .post(requestUrl, body)
                .then(
                    (res) => {
                        this.loading = false;

                        const { code, msg, data } = res.data;
                        if (code !== 0) {
                            throw new Error(msg);
                        }

                        const value = this.needCheckCode === true ? data.qualificationId : data;
                        if (value) {
                            this.changeQualificationField({
                                id: -1,
                                prop: 'id',
                                value,
                            });
                        }
                    },
                    (err) => {
                        this.loading = false;
                        throw err;
                    },
                );
        },
        handlePrevClick() {
            this.$router.go(-1);
        },
        handleJudgeValueEqual() {
            const { showQualificationList } = this;
            const changedItem = showQualificationList.filter(item => item.changed);
            if (changedItem && changedItem.length > 0) {
                this.displayUpdateConfirm = true;
            } else {
                this.handleNextClick();
            }
        },
        handleNextClick() {
            if (this.displayUpdateConfirm) {
                this.displayUpdateConfirm = false;
            }
            this.validate()
                .then(this.save)
                .then(() => {
                    // 新的发票拆分方案
                    this.$router.push({
                        name: 'address-confirm-new',
                    });
                })
                .catch(toastError);
        },
    },
};
</script>

<style lang="scss" module>
.module {
    margin: 30px 40px;
}

.h3 {
    margin-top: 0;
    margin-bottom: 20px;
    font-size: 20px;
}

.label {
    margin-right: 10px;
}

.alert {
  margin: 10px 0;
}

.radio-group {
    display: inline-block;
    line-height: 1;
    vertical-align: middle;
}

.form {
    border-bottom: 1px dashed #ebeef2;

    h4 {
        margin: 20px 0;
        font-size: 14px;
        color: #3f4156;
        line-height: 22px;
    }
}

.input {
    display: inline-block;
    width: 300px;
}

.mr16 {
    margin-right: 16px;
}

.invoiceOption {
    position: relative;
}

.invoiceNotice {
    font-size: 10px;
    color: #999;
}

.show-more-info {
    margin: 20px 0 20px 200px;
}

.show-more-info-img {
    width: 20px;
    height: 20px;
    margin: -5px 0 0 -5px;
    vertical-align: bottom;
}

.confirmTitleWrapper {
    display: flex;
    flex-direction: column;
    margin-left: 10px;
    .confirmTitle {
        color:#222222;
        font-size: 18px;
    }
    .confirmContent {
        color: #666666;
        font-size: 14px;
    }
}
</style>
