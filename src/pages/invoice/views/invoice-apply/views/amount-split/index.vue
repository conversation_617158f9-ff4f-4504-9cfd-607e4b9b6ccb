<template>
    <div>
        <SplitNew v-if="useNewSplitWay" />
        <SplitByCity v-else />
    </div>
</template>
<script>
import { mapState } from 'vuex';
import SplitByCity from './splitByCity';
import SplitNew from './splitNew';

export default {
    name: 'AmountSplit',
    components: {
        SplitByCity,
        SplitNew,
    },
    computed: {
        ...mapState('apply', ['baseFeeTypeItem']),
        ...mapState('config', [
            'useNewSplit',
            'isSinglePoi',
        ]),
        useNewSplitWay() {
            return !this.isSinglePoi ? true : this.useNewSplit && this.baseFeeTypeItem.amountGatherType === 1;
        },
    },
};
</script>
