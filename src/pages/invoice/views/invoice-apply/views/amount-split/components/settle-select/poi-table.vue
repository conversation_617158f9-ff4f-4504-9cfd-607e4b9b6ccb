<template>
    <div>
        <modal
            :value="value"
            :is-saves="true"
            :partner-title-rel-save="isSave"
            @cancel="handleCancel"
            @confirm="handleConfirm"
            @partnerTitleRelSave="handlePartnerTitleRelSaves"
        >
            <template slot="header">
                <span>选择门店</span>
            </template>
            <template slot="search">
                <roo-input
                    v-model="idInput"
                    :class="$style.search_input"
                    placeholder="请输入商家 ID"
                    clearable
                    @change="searchPoiId = ''"
                />
                <roo-button @click="handleSearch">
                    搜索
                </roo-button>
            </template>
            <template slot="table_head">
                <table :class="$style.table">
                    <col style="width: 80px;" />
                    <thead>
                        <tr>
                            <th>
                                <roo-checkbox
                                    :checked="checkedCount === total"
                                    :indeterminate="checkedCount > 0 && checkedCount < total"
                                    @change="handleCheckAll"
                                >
                                    全选
                                </roo-checkbox>
                            </th>
                            <th>门店名称</th>
                            <th>门店ID</th>
                            <th>金额</th>
                        </tr>
                    </thead>
                </table>
            </template>
            <template slot="table_body">
                <table :class="$style.table">
                    <col style="width: 80px;" />
                    <tbody>
                        <tr
                            v-if="list.length === 0"
                            key="empty"
                        >
                            <td
                                :class="$style.empty"
                                colspan="4"
                            >
                                暂无数据
                            </td>
                        </tr>
                        <template v-else>
                            <tr
                                v-for="(item, index) in list"
                                :key="item.partnerId"
                                :class="`${item.partnerId}` === searchPoiId ? $style.active : null"
                            >
                                <td>
                                    <roo-checkbox
                                        :disabled="item.checked.length > 0 && item.checked !== flag"
                                        :checked="item.checked.length > 0"
                                        @change="handleSingleCheck(index)"
                                    />
                                </td>
                                <td>{{ item.partnerName }}</td>
                                <td>{{ item.partnerId }}</td>
                                <td>{{ item.moneyCent | formatCent }}</td>
                            </tr>
                        </template>
                    </tbody>
                </table>
            </template>
            <template slot="pagination">
                <span>该结算账户已选金额 ¥{{ checkedMoneyCent | formatCent }}</span>
                <div
                    v-if="total > 0"
                >
                    <roo-pagination
                        :total="total"
                        :page-size="pageSize"
                        :current-page="pageNo"
                        @current-change="pageNo = $event"
                    />
                </div>
            </template>
        </modal>
    </div>
</template>

<script>
import { toast } from '@roo/roo-vue';
import { mapState, mapMutations } from 'vuex';
/* eslint-disable import/extensions, import/no-unresolved */
import { formatNumberThousand } from '$lib/utils';
import Modal from './modal';

export default {
    name: 'PoiTableModal',
    components: {
        Modal,
    },
    props: {
        value: {
            type: Boolean,
            default: false,
        },
        partnerList: {
            type: Array,
            default() {
                return [];
            },
        },
        // 对应销方公司ID
        ownership: {
            type: Number,
            default: -1,
        },
        // 对应的发票ID
        invoiceId: {
            type: Number,
            default: -1,
        },
        qualificationId: {
            type: Number,
            default: -1,
        },
        partnerTitleRelSave: {
            type: Boolean,
            default: true,
        },
    },
    filters: {
        formatCent(num) {
            return formatNumberThousand((num / 100).toFixed(2));
        },
    },
    data() {
        return {
            isShow: false,
            idInput: null,
            searchPoiId: '',
            // // 存储中间结果
            poiList: [],
            pageNo: 1,
            pageSize: 5,
            isSave: false,
        };
    },
    computed: {
        ...mapState('config', []),
        ...mapState('invoiceTitle', ['titleInfos']),
        total() {
            return this.poiList.length;
        },
        list() {
            const { poiList, pageNo, pageSize } = this;
            const offset = (pageNo - 1) * pageSize;
            return poiList.slice(offset, offset + pageSize);
        },
        checkedCount() {
            return this.poiList.filter((x) => x.checked.length > 0).length;
        },
        flag() {
            return `${this.ownership}-${this.qualificationId}-${this.invoiceId}`;
        },
        checkedMoneyCent() {
            let totalMoney = 0;
            this.poiList.forEach(ele => {
                if (ele.checked === this.flag) {
                    totalMoney += ele.moneyCent;
                }
            });
            return totalMoney;
        },
    },
    watch: {
        value(show) {
            if (show) {
                this.poiList = this.partnerList.map(ele => ({ ...ele })); // 未点确定之前使用本地副本
                this.isSave = this.partnerTitleRelSave;
            }
        },
    },
    methods: {
        ...mapMutations('apply', [
            'changepartnerTitleRelSave',
        ]),
        handleSingleCheck(index) {
            const actualIndex = index + this.pageSize * (this.pageNo - 1);
            const item = this.poiList[actualIndex];
            if (item.checked === '') {
                item.checked = this.flag;
            } else {
                item.checked = '';
            }
        },
        handleCheckAll() {
            // 已经是全选状态
            if (this.total === this.checkedCount) {
                this.poiList.forEach((poi) => {
                    if (poi.checked === this.flag) {
                        poi.checked = '';
                    }
                });
            } else {
                // 半选状态
                this.poiList.forEach((poi) => {
                    if (poi.checked === '') {
                        poi.checked = this.flag;
                    }
                });
            }
        },
        handleSearch() {
            const id = this.idInput.trim();
            this.searchPoiId = id;
            const idx = this.poiList.findIndex((poi) => `${poi.partnerId}` === id);
            if (idx > -1) {
                this.pageNo = Math.ceil((idx + 1) / this.pageSize);
            } else {
                toast.warn('查询的门店不在此结算设置下');
            }
        },
        handleCancel() {
            this.$emit('cancel');
            this.handleHidden();
        },
        handleConfirm() {
            this.$emit('confirm', this.checkedMoneyCent, this.isSave, this.poiList);
            this.handleHidden();
        },
        handleHidden() {
            this.idInput = '';
            this.searchPoiId = '';
            this.pageNo = 1;
        },
        // 是否增量保存抬头与门店关系
        handlePartnerTitleRelSaves(val) {
            this.isSave = val;
        },
    },
};
</script>

<style lang="scss" module>
.table {
    width: 100%;
    table-layout: fixed;
    border-top: 1px solid #E9EAF2;
    font-size: 14px;
    line-height: 20px;
    color: #222222;

    thead {
        border-bottom: 1px solid #F7F8FA;
    }

    th {
        color: #999999;
    }

    th, td {
        height: 40px;
        padding: 0 10px;
        text-align: left;
    }

    tr.active {
        border: 1px solid #F8B500;
    }

    :global(.roo-checkbox) {
        display: inline;
    }

    .loading,
    .empty {
        text-align: center;
    }
}
.search_input {
    flex: 1;
}
</style>
