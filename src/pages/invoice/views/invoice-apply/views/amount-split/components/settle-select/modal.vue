<template>
  <div v-show="value" :class="$style.dialog_cover" :style="{'background': bgColor}" @click.self="handleCancel">
      <!--style 通过props 控制内容的样式  -->
      <div :class="$style.dialog_content">
          <div :class="$style.dialog_head">
              <!--弹窗头部 title-->
              <slot name="header">
                  提示信息
              </slot>
              <img
                  src="https://s3plus.meituan.net/v1/mss_0e4451a82e1d428f9fc3b99c0b3ebf15/pic-manager/93de2a0042e0abf9/icon_popup_close.png"
                  @click="handleCancel"
              />
          </div>
          <div :class="$style.dialog_main">
              <!--弹窗的内容-->
              <div :class="$style.search_wrap">
                  <slot name="search">
                      搜索内容
                  </slot>
              </div>
              <div :class="$style.table_wrapper">
                  <slot name="table_head">
                      列表固定头部
                  </slot>
                  <div :class="$style.table_body">
                      <slot name="table_body">
                          列表可滚动内容区域
                      </slot>
                  </div>
              </div>
              <div :class="$style.total_pagination_wrapper">
                  <slot name="pagination">
                      分页器内容
                  </slot>
              </div>
          </div>
          <div :class="$style.dialog_footer">
              <div v-if="isHit && isSaves" style="display: flex;align-items: center;">
                  <roo-checkbox :checked="isSave" @change="checkSave">
                      <span :class="$style.saveMessage">保存发票抬头与所选门店关联关系</span>
                      <span>
                          <img style="width: 14px;height: 14px;cursor: pointer;margin-top: 2px;" src="https://s3plus.meituan.net/v1/mss_0e4451a82e1d428f9fc3b99c0b3ebf15/pic-manager/10e1564dfa01301a/<EMAIL>" /> 
                          <roo-tooltip
                              placement="top"
                          >
                              <slot>
                                  <div>您可在【财务管理-发票申请-抬头管理】中查看及编辑关联关系</div>
                              </slot>
                          </roo-tooltip>
                      </span>
                  </roo-checkbox>
              </div>
              <div style="flex: 1;">
                  <roo-button
                      type="hollow"
                      :class="$style.cancel_button"
                      @click="handleCancel"
                  >
                      取消
                  </roo-button>
                  <roo-button
                      @click="handleConfirm"
                  >
                      确定
                  </roo-button>
              </div>
          </div>
      </div>
  </div>
</template>
<script>
import {
  mapMutations,
  mapState
} from 'vuex';

export default {
  data() {
      return {
          isSave: true,
      };
  },
  computed: {
      ...mapState('config', [
          'isHit',
      ]),
  },
  props: {
      value: {
          type: Boolean,
          default: false,
          required: true,
      },
      bgColor: {
          type: String,
          default: 'rgba(0,0,0,.5)',
      },
      isSaves: {
          type: Boolean,
          default: false,
          required: true,
      },
      partnerTitleRelSave: {
          type: Boolean,
          default: true,
      },
  },
  watch: {
      partnerTitleRelSave(val) {
          this.isSave = val;
      },
  },
  methods: {
      ...mapMutations('apply', [
          'changeHasOwnershipNegative',
      ]),
      handleCancel() {
          this.$emit('cancel');
      },
      handleConfirm() {
          this.$emit('confirm');
          this.changeHasOwnershipNegative();
      },
      // 是否保存发票跟所选门店的关联关系
      checkSave() {
          this.isSave = !this.isSave;
          this.$emit('partnerTitleRelSave', this.isSave);
      },
  },
};
</script>

<style lang="scss" module>
// 最外层 设置position定位
// 遮罩 设置背景层，z-index值要足够大确保能覆盖，高度 宽度设置满 做到全屏遮罩
.dialog_cover {
  pointer-events: auto;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0,0,0,.5);
  z-index: 10;
}
@keyframes scaleAnimation {
  0%{
      width: 0px;
      height: 0px;
  }
  100%{
      width: 600px;
      height: 493px;
  }
}

.dialog_content{
  background: #fff;
  animation: scaleAnimation 0.1s ease;
  overflow: hidden;
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%,-50%);
  display: flex;
  flex-direction: column;
  width: 600px;
  height: 493px;
  .dialog_head {
      padding: 20px;
      font-size: 20px;
      color: #222222;
      font-weight: 500;
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: space-between;
  }
  .dialog_main {
      flex: 1;
      display: flex;
      flex-direction: column;
      margin-left: 20px;
      margin-right: 20px;
      background: #FFFFFF;
      border: 1px solid #E9EAF2;
      .search_wrap {
          padding: 10px;
          width: 100%;
          display: flex;
      }
      .table_wrapper {
          flex: 1;
          .table_body {
              height: 210px;
              overflow-y: auto;
          }
      }
      .total_pagination_wrapper {
          border-top: 1px solid #E9EAF2;
          display: flex;
          height: 40px;
          align-items: center;
          justify-content: space-between;
          padding-left: 10px;
          padding-right: 10px;
      }
  }
  .dialog_footer {
      display: flex;
      align-items: center;
      background: #FFFFFF;
      padding: 10px 20px;
      margin-top: 10px;
      box-shadow: 0 -4px 5px 0 #F7F8FA;
      text-align: right;
      .cancel_button {
          margin-right: 10px;
      }
  }
  .saveMessage{
    color: #91949E;
    font-family: Microsoft YaHei;
    font-weight: 400;
    font-size: 14px;
    line-height: 14px;
    letter-spacing: 0px;
    text-align: left;
    text-decoration: NONE;
  }
}
</style>
