<template>
    <div>
        <modal
            :value="value"
            :is-saves="false"
            @cancel="handleCancel"
            @confirm="handleConfirm"
        >
            <template slot="header">
                <span>分配金额</span>
            </template>
            <template slot="search">
                <roo-input
                    v-model="idInput"
                    :class="$style.search_input"
                    placeholder="请输入结算 ID"
                    clearable
                    @change="searchId = ''"
                />
                <roo-button @click="handleSearch">
                    搜索
                </roo-button>
            </template>
            <template slot="table_head">
                <table :class="$style.table">
                    <col style="width: 56px;" />
                    <thead>
                        <tr>
                            <th>
                                <roo-checkbox
                                    :checked="settleCheckedCount === total"
                                    :disabled="disabledSettleCount === total"
                                    :indeterminate="settleCheckedCount > 0 && settleCheckedCount < total"
                                    @change="handleCheckAllSettle"
                                />
                            </th>
                            <th>结算名称</th>
                            <th>结算 ID</th>
                            <th>金额</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                </table>
            </template>
            <template slot="table_body">
                <table :class="$style.table">
                    <col style="width: 56px;" />
                    <tbody>
                        <tr
                            v-if="total === 0"
                            key="empty"
                        >
                            <td
                                :class="$style.empty"
                                colspan="5"
                            >
                                暂无数据
                            </td>
                        </tr>
                        <template v-else>
                            <tr
                                v-for="(item, index) in list"
                                :key="item.settleId"
                                :class="`${item.settleId}` === searchId ? $style.active : null"
                            >
                                <td>
                                    <roo-checkbox
                                        :disabled="isDisabled(item)"
                                        :checked="checkedCount(item) === totalPoi(item)"
                                        :indeterminate="checkedCount(item) > 0 && checkedCount(item) < totalPoi(item)"
                                        @change="handleCheck(index)"
                                    />
                                </td>
                                <td>
                                    {{ item.settleName }}
                                </td>
                                <td>{{ item.settleId }}</td>
                                <td>
                                    ￥{{ item.moneyCent | formatCent }}
                                </td>
                                <td>
                                    <a
                                        href="#noop"
                                        :class="isDisabled(item) ? $style.disabled : null"
                                        @click.prevent="handleShowPoiModal(index, item)"
                                    >
                                        选择门店
                                    </a>
                                </td>
                            </tr>
                        </template>
                    </tbody>
                </table>
            </template>
            <template slot="pagination">
                <span>该发票已选金额 ¥{{ settleMoney | formatCent }}</span>
                <div
                    v-if="total > 0"
                >
                    <roo-pagination
                        :total="total"
                        :page-size="pageSize"
                        :current-page="pageNo"
                        @current-change="pageNo = $event"
                    />
                </div>
            </template>
        </modal>
        <poi-modal
            :value="displayPoiSelect"
            :invoice-id="invoiceId"
            :ownership="ownership"
            :qualification-id="qualificationId"
            :partner-list="curPoiList"
            :partner-title-rel-save="partnerTitleRelSaves"
            @confirm="handlePoiConfirm"
            @cancel="handlePoiCancel"
        />
    </div>
</template>

<script>
import {
    mapState,
    mapMutations,
} from 'vuex';
import { toast } from '@roo/roo-vue';
/* eslint-disable import/extensions, import/no-unresolved */
import { copySettleList } from '$utils/util';
import { formatNumberThousand } from '$lib/utils';
import PoiModal from './poi-table';
import Modal from './modal';
/* eslint-disable import/extensions, import/no-unresolved */

export default {
    name: 'SettleSelectModal',
    components: {
        PoiModal,
        Modal,
    },
    filters: {
        formatCent(num) {
            return formatNumberThousand((num / 100).toFixed(2));
        },
    },
    props: {
        value: {
            type: Boolean,
            default: false,
        },
        ownership: {
            type: Number,
            default: -1,
        },
        invoiceId: {
            type: Number,
            default: -1,
        },
        qualificationId: {
            type: Number,
            default: -1,
        },
        // 某张发票已选金额
        selectedMoney: {
            type: Number,
            default: 0,
        },
        settleList: {
            type: Array,
            default() {
                return [];
            },
        },
        partnerTitleRelSave: {
            type: Boolean,
            default: true,
        },
    },
    computed: {
        ...mapState('amountSelectNew', ['totalSelectedAmount', 'checkedAmountList']),
        ...mapState('invoiceTitle', ['titleInfos', 'poiRelatedToTitle']),
        // 总结算ID个数
        total() {
            return this.tableList.length;
        },
        list() {
            const { tableList, pageNo, pageSize } = this;
            const offset = (pageNo - 1) * pageSize;
            return tableList.slice(offset, offset + pageSize);
        },
        flag() {
            return `${this.ownership}-${this.qualificationId}-${this.invoiceId}`;
        },
        disabledSettleCount() {
            return this.tableList.filter((item) => this.isDisabled(item)).length;
        },
        settleCheckedCount() {
            let count = 0;
            this.tableList.forEach((item) => {
                item.partnerList.forEach((p) => {
                    if (p.checked.length > 0) {
                        count++;
                    }
                });
            });
            return count;
        },
    },
    watch: {
        value(show) {
            if (show) {
                this.tableList = copySettleList(this.settleList);
                this.settleMoney = this.selectedMoney;
                this.partnerTitleRelSaves = this.partnerTitleRelSave;
                if (this.selectPoiAmountIndex > -1) {
                    const tempPartnerList = copySettleList(this.settleList)[this.selectPoiAmountIndex].partnerList;
                    tempPartnerList.forEach((item) => this.checkedCount(item));
                }
            }
        },
        displayPoiSelect(show) {
            if (show) {
                if (this.selectPoiAmountIndex > -1) {
                    const tempList = this.tableList[this.selectPoiAmountIndex].partnerList;
                    const temp = [];
                    tempList.forEach((item) => {
                        temp.push({
                            ...item,
                        });
                    });
                    this.curPoiList = temp;
                } else {
                    this.curPoiList = [];
                }
            }
        },
    },
    data() {
        return {
            // 副本，当前销方公司下结算ID列表
            tableList: [],
            // 副本,当前操作发票已选金额
            settleMoney: 0,
            idInput: '',
            pageNo: 1,
            pageSize: 5,
            searchId: '',
            displayPoiSelect: false,
            // 当前操作的结算ID下标
            selectPoiAmountIndex: -1,
            // 当前结算ID下已选金额
            selectPoiAmountMoney: 0,
            curPoiList: [],
            defaultCheckedList: [],
            initShouldAdd: false,
            partnerTitleRelSaves: true,
        };
    },
    methods: {
        ...mapMutations('amountSelectNew', [
            'changeCheckedAmountList',
        ]),
        // 是否禁止勾选
        isDisabled(item) {
            return item.partnerList.every(ele => ele.checked.length > 0 && ele.checked !== this.flag);
        },
        // 当前选中个数
        checkedCount(item) {
            return item.partnerList.filter(ele => ele.checked.length > 0).length;
        },
        totalPoi(item) {
            return item.partnerList.length;
        },
        handleShowPoiModal(index, item) {
            const actualIndex = index + (this.pageNo - 1) * this.pageSize;
            if (this.isDisabled(item)) {
                return;
            }
            this.displayPoiSelect = true;
            this.selectPoiAmountIndex = actualIndex;
            let selectM = 0;
            const arr = this.tableList[this.selectPoiAmountIndex].partnerList;
            arr.forEach(ele => {
                if (ele.checked === this.flag) {
                    selectM += ele.moneyCent;
                }
            });
            this.selectPoiAmountMoney = selectM;
        },
        handlePoiConfirm(money, isSave, poiList) {
            this.displayPoiSelect = false;
            this.tableList[this.selectPoiAmountIndex].partnerList = poiList.map(ele => ({ ...ele }));
            const oldM = this.selectPoiAmountMoney;
            const diff = money - oldM;
            // 更新当前操作结算ID下已选金额
            this.selectPoiAmountMoney = money;
            // 更新该发票已选金额
            this.settleMoney += diff;
            this.partnerTitleRelSaves = isSave;
        },
        handlePoiCancel() {
            this.displayPoiSelect = false;
        },
        handleSearch() {
            this.searchId = this.idInput.trim();
            const index = this.tableList.findIndex(ele => `${ele.settleId}` === this.searchId);
            if (index > -1) {
                this.pageNo = Math.ceil((index + 1) / this.pageSize);
            } else {
                toast.warn('查询结算ID不正确');
            }
        },
        handleHidden() {
            this.idInput = '';
            this.pageNo = 1;
            this.searchId = '';
        },
        handleCancel() {
            this.$emit('cancel');
            this.handleHidden();
        },
        handleConfirm() {
            // 点击确认之后
            // 先更新checkAmountList
            this.changeCheckedAmountList({
                ownership: this.ownership,
                settleList: this.tableList,
            });
            // 更新发票剩余金额
            this.$emit('confirm', this.settleMoney, this.partnerTitleRelSaves);
            this.handleHidden();
        },
        handleCheckAllSettle() {
            if (this.settleCheckedCount === this.total) {
                this.tableList.forEach((item) => {
                    item.partnerList.forEach(poi => {
                        if (poi.checked === this.flag) {
                            poi.checked = '';
                            this.settleMoney -= poi.moneyCent;
                        }
                    });
                });
            } else {
                this.tableList.forEach((item, idx) => {
                    if (this.checkedCount(item) !== this.totalPoi(item)) {
                        this.handleCheck(idx);
                    }
                });
            }
        },
        handleCheck(index) {
            const actualIndex = index + (this.pageNo - 1) * this.pageSize;
            const item = this.tableList[actualIndex];
            // 没有全选
            if (this.checkedCount(item) < this.totalPoi(item)) {
                item.partnerList.forEach(poi => {
                    if (poi.checked === '') {
                        poi.checked = this.flag;
                        if (poi.checked) {
                            this.settleMoney += poi.moneyCent;
                        }
                    }
                });
            } else {
                // 已经全选了
                item.partnerList.forEach(poi => {
                    if (poi.checked === this.flag) {
                        poi.checked = '';
                        this.settleMoney -= poi.moneyCent;
                    }
                });
            }
        },
    },
};
</script>
<style lang="scss" module>
.table {
    width: 100%;
    table-layout: fixed;
    border-top: 1px solid #E9EAF2;
    font-size: 14px;
    line-height: 20px;
    color: #222222;

    thead {
        border-bottom: 1px solid #F7F8FA;
    }

    th {
        color: #999999;
    }

    th, td {
        height: 40px;
        padding: 0 10px;
        text-align: left;
        &:nth-child(4) {
            text-align: center;
        }
    }

    tr.active {
        border: 1px solid #F8B500;
    }

    :global(.roo-checkbox) {
        display: inline;
    }

    .loading,
    .empty {
        text-align: center;
    }
}
.disabled {
    color: #EEEEEE;
    cursor: not-allowed;
    pointer-events: none;
}
.search_input {
    flex: 1;
}
</style>
