<template>
    <div class="roo-input-group">
        <div class="roo-input-group-middle">
            <input
                ref="input"
                :value="value"
                class="roo-input"
                type="text"
                @input="handleInput"
            >
        </div>
    </div>
</template>

<script>
/**
 * 一个受控的 RooInput 组件
 *
 * 为什么要有这么个组件？
 * 因为 <roo-input value="123"> 输入框的值会变化，不能适应所有的需求
 */

export default {
    name: 'XInput',
    props: {
        value: {
            type: [String, Number],
            default: undefined,
        },
    },
    methods: {
        handleInput(event) {
            this.$emit('input', event.target.value);

            this.$nextTick(this.setNativeInputValue);
        },
        setNativeInputValue() {
            const { input } = this.$refs;
            if (!input) return;
            if (input.value !== this.nativeInputValue) input.value = this.value;
        },
    },
};
</script>
