<template>
    <div :class="$style.module">
        <div :class="$style.title_first">
            分配金额
        </div>
        <div :class="$style.company_header">
            开票总金额：{{ totalSelectedAmount | formatCent }} 元
        </div>
        <div
            v-for="(company, comIndex) in splitContent"
            :key="comIndex"
        >
            <div :class="$style.divder"></div>
            <div :class="$style.company_header">
                <div>
                    <span>销方公司</span>
                    <span>
                        <img style="width: 10px;height: 10px;cursor: pointer;" src="https://s3plus.meituan.net/v1/mss_0e4451a82e1d428f9fc3b99c0b3ebf15/pic-manager/10e1564dfa01301a/<EMAIL>" />
                        <roo-tooltip
                            placement="top"
                        >
                            <slot>
                                <div>销方公司按照合同签约情况确定</div>
                            </slot>
                        </roo-tooltip>
                    </span>
                    <span>：{{ company.companyName }}</span>
                </div>
                
                <span>可分配金额：¥{{ company.moneyCent | formatCent }}元</span>
            </div>
            <div
                v-for="(q, quaIndex) in company.qualifications"
                :key="q.qualificationId"
                :class="$style.coupon"
            >
                <div :class="$style.invoice_title">
                    <span>
                        发票抬头：
                        {{ q.qualificationName }}
                    </span>
                    <span>
                        已分配 ￥{{ q.moneyCent | formatCent }}
                    </span>
                </div>
                <div
                    v-for="(invoice, idx) in q.invoiceList"
                    :key="`${q.qualificationId}_${idx}`"
                    :class="$style.split_invoice_wrap"
                >
                    <div :class="$style.invoice_start_part">
                        <div :class="$style.invoice_name">
                            发票{{ idx + 1 }}
                        </div>
                        <div :class="$style.invoice_money">
                            ¥ {{ invoice.moneyCent | formatCent }}
                        </div>
                        <div
                            :class="$style.change"
                            @click="openSpiltModal(company.ownerShip, comIndex, q.qualificationId, quaIndex, invoice.invoiceId, invoice.moneyCent, invoice.partnerTitleRelSave)"
                        >
                            {{ invoice.moneyCent > 0 ? '调整金额' : '分配金额' }}
                        </div>
                    </div>
                    <span
                        :class="$style.rm"
                        @click="handleRemove(company.ownerShip, comIndex, q.qualificationId, quaIndex, invoice.invoiceId)"
                    >
                        删除
                    </span>
                </div>
                <div
                    key="add"
                    :class="$style.add"
                    @click="handleAddInvoice(comIndex, quaIndex)"
                >
                    +添加发票
                </div>
            </div>
        </div>
        <div style="height: 1px;width: 100%;background: #E9EAF2;margin-top: 20px;margin-bottom: 20px;"></div>
        <div style="display: flex;align-items: center;">
            <span>票面备注</span>
            <span v-if="isPublicActivityFee || isNatural || isChildren" :class="$style.tooltip">
                <roo-icon
                    name="question2"
                    size="14px"
                />
                <roo-tooltip
                    placement="top"
                    trigger="hover"
                >
                    <slot>
                        <div style="width: 150px;">应基金会要求，票面备注固定为门店与日期信息，不支持商家自行填写，敬请谅解。</div>
                    </slot>
                </roo-tooltip>
            </span>
            <roo-input
                v-model="remark"
                :disabled="isPublicInvoices"
                style="margin-left: 20px;width: 372px;"
                type="text"
                placeholder="（选填）票面备注"
                noresize
                @change="handleRemark"
            />
        </div>
        <div class="footer-nav">
            <roo-button
                :class="$style.mr16"
                type="brand-hollow"
                @click="handlePrevClick"
            >
                上一步
            </roo-button>
            <roo-button
                :disabled="nextButtonIsDisabled"
                type="brand"
                @click="handleNextClick"
            >
                下一步
            </roo-button>
            <div v-show="nextButtonIsDisabled" :class="$style.next_button_tooltip_wrapper" @click="handleNextClick">
                <roo-tooltip placement="top">
                    <span>{{ nextButtonDisabledText }}</span>
                </roo-tooltip>
            </div>
        </div>
        <settle-select-modal
            v-model="showModal"
            :ownership="activeOwnerShip"
            :settle-list="settleList"
            :invoice-id="activeInvoiceId"
            :qualification-id="activeQualificationID"
            :selected-money="activeMoney"
            :partner-title-rel-save="partnerTitleRelSave"
            @confirm="handleSelectConfirm"
            @cancel="handleSelectCancel"
        />
        <confirm-modal
            v-model="displayDeleteConfirm"
            confirm-text="删除"
            @confirm="handleDeleteConfirm"
        >
            <div :class="$style.delete_title_icon_wrapper">
                <img src="https://s3plus.meituan.net/v1/mss_0e4451a82e1d428f9fc3b99c0b3ebf15/pic-manager/d9cd2fa2c93ea6b8/icon_wenhao.png" />
                <div :class="$style.delete_title_wrapper">
                    <div :class="$style.delete_title">
                        删除该发票
                    </div>
                    <div :class="$style.detete_content">
                        删除后将释放已分配金额
                    </div>
                </div>
            </div>
        </confirm-modal>
        <roo-modal
            :value="showHasZeroInvoice"
            size="small"
        >
            <div :class="$style.delete_title_wrapper">
                <div :class="$style.delete_title">
                    部分发票金额为0
                </div>
                <div :class="$style.detete_content">
                    不会生成发票，是否继续
                </div>
            </div>
            <template slot="footer">
                <roo-button
                    type="hollow"
                    @click="showHasZeroInvoice = false"
                >
                    取消
                </roo-button>
                <roo-button
                    @click="handleZeroConfirm"
                >
                    确定
                </roo-button>
            </template>
        </roo-modal>
    </div>
</template>
<script>
import {
    mapState, mapGetters, mapMutations,
} from 'vuex';
/* eslint-disable import/extensions, import/no-unresolved */
import { formatCent, formatMilli, formatMilliToZero } from '$lib/filters';
import request from '$invoice/utils/request';
import toastError from '$lib/toast-error';
// eslint-disable-next-line camelcase
import { MIN_PUBLIC_ACTIVITY_AMOUNT_Milli, MAX_ZERO_AMOUNT_Milli } from '$config/constant';
import SettleSelectModal from './components/settle-select';
import ConfirmModal from '$components/confirm-modal';

export default {
    name: 'AmountSplitNew',
    components: {
        SettleSelectModal,
        ConfirmModal,
    },
    filters: {
        formatCent,
        formatMilli,
        formatMilliToZero,
    },
    data() {
        return {
            // 常量
            // eslint-disable-next-line camelcase
            MIN_PUBLIC_ACTIVITY_AMOUNT: MIN_PUBLIC_ACTIVITY_AMOUNT_Milli / 10000,
            MAX_ZERO_AMOUNT_Milli,
            showModal: false,
            activeOwnerShip: -1,
            activeComIndex: -1,
            activeInvoiceId: -1,
            activeQualificationID: -1,
            activeQuaIndex: -1,
            activeMoney: 0,
            displayDeleteConfirm: false,
            showHasZeroInvoice: false,
            defaultCent: {},
            remark: '',
            partnerTitleRelSave: true,
        };
    },
    computed: {
        ...mapState('apply', ['baseFeeTypeItem', 'qualifications', 'splitContent', 'hasOwnershipNegative', 'hasZeroInvoice']),
        ...mapState('amountSelectNew', ['totalSelectedAmount', 'checkedAmountList']),
        ...mapGetters('amountSelectNew', ['allPoiIsSelected']),
        ...mapGetters('apply', ['singleInvoiceMoneyCheck', 'isAllAlocated', 'isPublicActivityFee', 'isChildren', 'isNatural']),
        ...mapState('config', ['isSinglePoi']),
        ...mapState('invoiceTitle', ['titleInfos']),
        settleList() {
            const idx = this.checkedAmountList.findIndex(ele => ele.ownerShip === this.activeOwnerShip);
            if (idx > -1) {
                return this.checkedAmountList[idx].settleList;
            }
            return [];
        },
        // 是否是青山公益,或者是零废星球
        isPublicInvoice() {
            return this.isPublicActivityFee || this.isChildren;
        },
        // 是否是青山公益
        isPublicInvoices() {
            return this.isPublicActivityFee || this.isChildren || this.isNatural;
        },
        // 点击下一步的时候，文案提示, 返回空字符串的时候意味着可以进入下一步
        nextButtonDisabledText() {
            // 校验存在某个销方公司可分配金额为负数
            if (this.hasOwnershipNegative) {
                return '存在销方公司开票金额为负，请返回第二步重新选择开票金额';
            }
            // 校验所有门店都已经分配到对应的发票
            if (!this.isAllAlocated) {
                return '请先分配所有金额';
            }
            // 校验每一张发票，已分配金额，不能小于0， 等于0的情况自动过滤，不发送给后端接口
            if (!this.singleInvoiceMoneyCheck(0)) {
                return '存在发票金额为负数，请重新分配金额';
            }
            // 青山公益和零废星球公益项目，要求单张发票金额不能小于50
            // if (this.isPublicInvoice) {
            //     if (!this.singleInvoiceMoneyCheck(this.MIN_PUBLIC_ACTIVITY_AMOUNT)) {
            //         return '存在单张公益发票金额小于50，请重新分配金额';
            //     }
            // }
            return '';
        },
        // 下一步是否禁用
        nextButtonIsDisabled() {
            return this.nextButtonDisabledText.length > 0;
        },
    },
    mounted() {
        // 判断资质、明细类型、已选金额
        const { baseFeeTypeItem, qualifications, totalSelectedAmount } = this;
        if (!(baseFeeTypeItem && qualifications && qualifications.length > 0 && totalSelectedAmount > 0)) {
            this.$router.go(-1);
        }
        // this.handleDefaultCheckAmountList(this.splitContent, this.checkedAmountList);
        this.handleDefaultNext();
    },
    methods: {
        ...mapMutations('amountSelectNew', ['deletePoiCheckStatus']),
        ...mapMutations('apply', [
            'addSplitContent',
            'deleteSplitContent',
            'changeSplitContent',
            'changeSplitInfos',
            'changeDefaultCheckedAmountList',
            'chnagepartnerTitleRelSave',
            'changeRemark',
        ]),
        handleDefaultCheckAmountList(splitContent, checkedAmountList) {
            const temp = checkedAmountList;

            temp.forEach((calItem) => {
                splitContent.forEach((scqItem) => {
                    scqItem.qualifications.forEach((quaItem) => {
                        const relatedPoi = calItem.poiRelatedToTitle[`${quaItem.qualificationId}`];
                        calItem.settleList.forEach((sItem) => {
                            const pList = sItem.partnerList;
                            pList.forEach((pItem) => {
                                if (relatedPoi.includes(pItem.partnerId)) {
                                    pItem.checked = `${calItem.ownerShip}-${quaItem.qualificationId}-${quaItem.invoiceList[0].invoiceId}`;
                                }
                            });
                        });
                    });
                });
            });
            this.changeDefaultCheckedAmountList(temp);
        },
        changeActiveStatus(ownership, comIndex, quaId, quaIndex, invoiceId) {
            this.activeOwnerShip = ownership;
            this.activeComIndex = comIndex;
            this.activeQualificationID = quaId;
            this.activeQuaIndex = quaIndex;
            this.activeInvoiceId = invoiceId;
        },
        openSpiltModal(ownership, comIndex, quaId, quaIndex, invoiceId, money, partnerTitleRelSave) {
            this.changeActiveStatus(ownership, comIndex, quaId, quaIndex, invoiceId);
            this.activeMoney = money;
            this.partnerTitleRelSave = partnerTitleRelSave;
            this.showModal = true;
        },
        handleDeleteConfirm() {
            // 遍历checkedAmountList结构，将check为invoiceId的发票修改
            this.deletePoiCheckStatus({
                ownership: this.activeOwnerShip,
                flag: `${this.activeOwnerShip}-${this.activeQualificationID}-${this.activeInvoiceId}`,
            });
            this.deleteSplitContent({
                companyIndex: this.activeComIndex,
                qualificationIndex: this.activeQuaIndex,
                invoiceId: this.activeInvoiceId,
            });
            this.displayDeleteConfirm = false;
        },
        handleRemove(ownership, comIndex, quaId, quaIndex, invoiceId) {
            this.changeActiveStatus(ownership, comIndex, quaId, quaIndex, invoiceId);
            this.displayDeleteConfirm = true;
        },
        handleSelectCancel() {
            this.showModal = false;
        },
        handleSelectConfirm(money, partnerTitleRelSave) {
            this.showModal = false;
            // 更新某张发票已选金额
            this.changeSplitContent({
                companyIndex: this.activeComIndex,
                qualificationIndex: this.activeQuaIndex,
                invoiceId: this.activeInvoiceId,
                moneyCent: money,
                partnerTitleRelSave,
            });
        },
        handleAddInvoice(comIndex, quaIndex) {
            this.addSplitContent({
                companyIndex: comIndex,
                qualificationIndex: quaIndex,
            });
        },
        handlePrevClick() {
            this.$router.go(-1);
        },
        handleDefaultNext() {
            return this.nextButtonIsDisabled;
        },
        handleRemark(val) {
            this.remark = val;
        },
        handleNextClick() {
            // 票面备注
            if (this.remark !== '') {
                this.changeRemark(this.remark);
            }
            if (this.nextButtonIsDisabled) return;
            // 校验发票是否存在为0的，需要过滤掉
            this.changeSplitInfos(this.checkedAmountList);
            // 是否有金额为0的发票需要提醒用户
            if (this.hasZeroInvoice) {
                this.showHasZeroInvoice = true;
            } else {
                this.showHasZeroInvoice = false;
                this.handleJump();
            }
        },
        handleZeroConfirm() {
            this.showHasZeroInvoice = false;
            this.handleJump();
        },
        handleJump() {
            this.$router.push({ name: !this.isSinglePoi ? 'invoice-type-confirm-update' : 'invoice-type-confirm' });
        },
    },
};
</script>
<style lang="scss" module>
    .title_first {
        color: #222222;
        font-size: 20px;
        font-weight: 500;
        margin-bottom: 10px;
    }
    .delete_title_icon_wrapper {
        display: flex;
        flex-direction: row;
        align-items: start;
    }
    .delete_title_wrapper {
        display: flex;
        flex-direction: column;
        margin-left: 10px;
    }
    .delete_title {
        color:#222222;
        font-size: 18px;
    }
    .detete_content {
        color: #666666;
        font-size: 14px;
    }
    .company_header {
        color: #222222;
        font-size: 16px;
        font-weight: 500;
        width: 750px;
        display: flex;
        justify-content: space-between;
        margin-bottom: 10px;
    }
    .divder {
        border-top: 1px solid #E9EAF2;
        width: 100%;
        height: 1px;
        margin-bottom: 20px;
        margin-top: 20px;
    }
    .module {
        position: relative;
        margin: 30px 40px;
    }
    .coupon {
        width: 750px;
        background-color: #F7F8FA;
        margin-bottom: 10px;
        padding: 20px;
    }
    .invoice_title {
        display: flex;
        justify-content: space-between;
        font-size: 14px;
        color: #222222;
        margin-bottom: 16px;
    }
    .rm {
        color: #FB4E44;
        cursor: pointer;
    }

    .next_button_tooltip_wrapper {
        position: absolute;
        top: 10px;
        right: 0px;
        width: 100px;
        height: 36px;
    }
    .add {
        padding-top: 10px;
        color: #FF6A00;
        font-size: 14px;
        cursor: pointer;
    }
    .mr16 {
        margin-right: 16px;
    }
    .tooltip{
        margin-left: 6px;
        cursor: pointer;
    }
    .split_invoice_wrap {
        background-color: white;
        padding: 12px 20px;
        margin-bottom: 10px;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        .invoice_start_part {
            display: flex;
            flex-direction: row;
            .invoice_name {
                width: 85px;
            }
            .invoice_money {
                width: 100px;
                margin-right: 30px;
            }
            .change {
                color: #FF6A00;
                font-size: 14px;
                cursor: pointer;
            }
        }
    }
</style>
