<template>
    <div :class="$style.module">
        <h3 :class="$style.h3">
            分配金额
        </h3>

        <div :class="$style.p">
            开票总金额：{{ checkedMoneyMilli | formatMilli }} 元
        </div>

        <coupon
            v-for="(q, index) in qualifications"
            :key="q.id"
            :class="$style.coupon"
        >
            <template slot="header">
                <span :class="$style.title">
                    发票抬头：
                    {{ q.partnerBName }}
                </span>
                <div :class="$style.sum">
                    ￥{{ calcSum(index) | formatMilli }}
                </div>
            </template>

            <ul :class="$style.splitList">
                <li
                    v-for="(txt, idx) in q.amountList"
                    :key="`${q.id}_${idx}`"
                    :class="$style.splitInvoiceWrap"
                >
                    <div :class="$style.left">
                        发票{{ calcOffset(index) + idx }}
                    </div>
                    <div :class="[$style.mid ,$style.splitInvoiceWrap]">
                        金额
                        <x-input
                            :class="$style.input"
                            :value="txt"
                            placeholder="请输入金额"
                            @input="handleInput(q.id, idx, $event, txt)"
                        />
                        <span
                            v-if="(txt > 0 && txt < MIN_PUBLIC_ACTIVITY_AMOUNT && isPublicInvoice) || (txt > 0 && txt < MIN_PUBLIC_ACTIVITY_AMOUNT && isNatural)"
                            :class="$style.redTip"
                        >
                            发票金额必须大于{{ MIN_PUBLIC_ACTIVITY_AMOUNT }}元
                        </span>
                    </div>
                    <div :class="$style.right">
                        <span
                            v-if="q.amountList.length > ((index === qualifications.length - 1) ? 0 : 1)"
                            key="rm"
                            :class="$style.rm"
                            @click="removeQualificationSplitAmount({ id: q.id, index: idx })"
                        >
                            删除
                        </span>
                        <span
                            v-if="idx === 0"
                            key="add"
                            :class="$style.add"
                            @click="addQualificationSplitAmount(q.id)"
                        >
                            增加发票
                        </span>
                    </div>
                </li>
                <li
                    v-if="index === qualifications.length - 1"
                    key="rest"
                >
                    <div :class="$style.left">
                        发票{{ calcOffset(index) + q.amountList.length }}
                    </div>
                    <div :class="[$style.mid ,$style.splitInvoiceWrap]">
                        金额
                        <roo-input
                            :class="[$style.input, $style.disabled]"
                            :value="restMilli | formatMilli"
                            :status="restMilli <0 ? 'error' : null"
                            placeholder="请输入金额"
                            readonly
                        />
                        <span
                            v-if="restMilli < 0"
                            :class="$style.redTip"
                        >
                            其余发票金额超过开票总金额
                        </span>
                        <span
                            v-if="(restMilli > 0 && restMilli < MIN_PUBLIC_ACTIVITY_AMOUNT*10000 && isPublicInvoice) || (restMilli > 0 && restMilli < MIN_PUBLIC_ACTIVITY_AMOUNT*10000 && isNatural)"
                            :class="$style.redTip"
                        >
                            发票金额必须大于{{ MIN_PUBLIC_ACTIVITY_AMOUNT }}元
                        </span>
                    </div>
                    <div :class="$style.right">
                        <span
                            v-if="q.amountList.length === 0"
                            key="add"
                            :class="$style.add"
                            @click="addQualificationSplitAmount(q.id)"
                        >
                            增加发票
                        </span>
                    </div>
                </li>
            </ul>
        </coupon>

        <div class="footer-nav">
            <roo-button
                :class="$style.mr16"
                type="brand-hollow"
                @click="handlePrevClick"
            >
                上一步
            </roo-button>
            <roo-button
                :disabled="restMilli <= -100"
                type="brand"
                @click="handleNextClick"
            >
                下一步
            </roo-button>
        </div>
    </div>
</template>

<script>
import { mapState, mapGetters, mapMutations } from 'vuex';
import { confirm, alert } from '@roo/roo-vue';

/* eslint-disable import/extensions, import/no-unresolved */
import Coupon from '$components/coupon';
import { BaseFeeTypeEnum } from '$config/enum';
import { formatCent, formatMilli, formatMilliToZero } from '$lib/filters';
// eslint-disable-next-line camelcase
import { MIN_PUBLIC_ACTIVITY_AMOUNT_Milli, MAX_ZERO_AMOUNT_Milli } from '$config/constant';
/* eslint-enable import/extensions, import/no-unresolved */

import XInput from './components/x-input';

function sum(acc, cur) {
    return acc + cur;
}

export default {
    name: 'AmountSplitByQualify',
    components: {
        Coupon,

        XInput,
    },
    filters: {
        formatCent,
        formatMilli,
        formatMilliToZero,
    },
    data() {
        return {
            // 常量
            // eslint-disable-next-line camelcase
            MIN_PUBLIC_ACTIVITY_AMOUNT: MIN_PUBLIC_ACTIVITY_AMOUNT_Milli / 10000,
            MAX_ZERO_AMOUNT_Milli,
        };
    },
    computed: {
        ...mapState('apply', ['baseFeeTypeItem', 'qualifications']),
        ...mapState('config', ['isSinglePoi']),
        ...mapGetters('amountSelect', ['checkedMoneyCent', 'checkedMoneyMilli']),
        restMilli() {
            const { qualifications, checkedMoneyMilli } = this;

            const totalMilli = qualifications
                .map(q => q.amountList
                    .map(str => parseFloat(str) * 10000 || 0)
                    .reduce(sum, 0))
                .reduce(sum, 0);
            const rest = checkedMoneyMilli - totalMilli;
            return (rest / 100).toFixed(0) * 100; // 最后两位四舍五入
        },
        isPublicInvoice() {
            return this.baseFeeTypeItem.baseFeeType === BaseFeeTypeEnum.PublicActivity;
        },
        isChildren() {
            return this.baseFeeTypeItem.baseFeeType === BaseFeeTypeEnum.Children;
        },
        isNatural() {
            return this.baseFeeTypeItem.baseFeeType === BaseFeeTypeEnum.Natural;
        },
    },
    mounted() {
        const { baseFeeTypeItem, qualifications, checkedMoneyMilli } = this;
        if (!(baseFeeTypeItem && qualifications && qualifications.length > 0 && checkedMoneyMilli > 0)) {
            this.$router.go(-1);
        }
    },
    methods: {
        ...mapMutations('apply', [
            'changeQualifications',
            'addQualificationSplitAmount',
            'removeQualificationSplitAmount',
            'changeQualificationSplitAmount',
        ]),
        calcOffset(index) {
            const { qualifications } = this;

            let offset = 1;
            for (let i = 0; i < index; ++i) {
                if (i < qualifications.length) {
                    offset += qualifications[i].amountList.length;
                }
            }

            return offset;
        },
        calcSum(index) {
            const { qualifications } = this;

            let total = qualifications[index].amountList.map(x => parseFloat(x) * 10000 || 0).reduce(sum, 0);

            if (index === qualifications.length - 1) {
                total += this.restMilli;
            }

            return total;
        },
        handleInput(id, idx, value) {
            if (/^\d*\.?\d{0,2}$/.test(value)) {
                this.changeQualificationSplitAmount({ id, index: idx, value });
            }
        },

        handlePrevClick() {
            this.$router.go(-1);
        },

        handleNextClick() {
            const fakeInvoice = [];
            const invalidPublicActivityInvoice = [];
            let no = 0;
            const { qualifications } = this;
            for (let i = 0; i < qualifications.length; ++i) {
                const { amountList } = qualifications[i];

                for (let j = 0; j < amountList.length; ++j) {
                    no += 1; // 发票序号

                    if (!parseFloat(amountList[j])) {
                        fakeInvoice.push(no);
                    }
                    // eslint-disable-next-line camelcase
                    if ((parseFloat(amountList[j]) < this.MIN_PUBLIC_ACTIVITY_AMOUNT && parseFloat(amountList[j]) && this.isPublicInvoice) || (parseFloat(amountList[j]) < this.MIN_PUBLIC_ACTIVITY_AMOUNT && parseFloat(amountList[j]) && this.isNatural)) {
                        invalidPublicActivityInvoice.push(no);
                    }
                }
            }
            no += 1;
            // eslint-disable-next-line camelcase
            if (!this.restMilli) {
                fakeInvoice.push(no);
            }
            // eslint-disable-next-line camelcase
            if ((this.restMilli < MIN_PUBLIC_ACTIVITY_AMOUNT_Milli && this.restMilli && this.isPublicInvoice) || (this.restMilli < MIN_PUBLIC_ACTIVITY_AMOUNT_Milli && this.restMilli && this.isNatural)) {
                invalidPublicActivityInvoice.push(no);
            }
            if (invalidPublicActivityInvoice.length > 0) {
                // eslint-disable-next-line camelcase
                alert(`发票 ${invalidPublicActivityInvoice.join('、')} 金额小于${this.MIN_PUBLIC_ACTIVITY_AMOUNT}元，请调整后再试`, () => {
                });
                return;
            }
            if (fakeInvoice.length > 0) {
                confirm(`发票 ${fakeInvoice.join('、')} 金额为 0，不会生成一张发票，是否继续？`, (yes) => {
                    if (yes) {
                        this.$router.push({ name: !this.isSinglePoi ? 'invoice-type-confirm-update' : 'invoice-type-confirm' });
                    }
                });
                return;
            }
            this.$router.push({ name: !this.isSinglePoi ? 'invoice-type-confirm-update' : 'invoice-type-confirm' });
        },
    },
};
</script>

<style lang="scss" module>
    .module {
        margin: 30px 40px;
    }

    .h3 {
        margin-top: 0;
        margin-bottom: 20px;
        font-size: 20px;
    }

    .p {
        margin: 20px 0;
    }

    .coupon {
        width: 700px;
        margin-bottom: 20px;
    }

    .title {
        line-height: 45px;
        font-weight: bold;
    }

    .sum {
        line-height: 45px;
        float: right;
    }

    .split-list {
        list-style: none;
        margin: 0;
        padding: 0;

        li {
            display: flex;
            align-items: center;
            margin: 20px 0;

            &:first-child {
                margin-top: 0;
            }
            &:last-child {
                margin-bottom: 0;
            }
        }
    }

    .left {
        width: 100px;
    }

    .mid {
        flex: 1;
    }

    .right {
        width: 100px;
        line-height: 1;
    }

    .input {
        display: inline-block;
        width: 150px;
        margin: 0 10px;
    }

    .rm {
        float: left;
        color: #FB4E44;
        cursor: pointer;
    }

    // .sp {
    //     display: inline-block;
    //     width: 1px;
    //     height: 12px;
    //     margin: 0 10px;
    //     background: #E9EAF2;
    // }

    .add {
        float: right;
        color: #06C1AE;
        cursor: pointer;
    }

    .rm + .add {
        padding-left: 8px;
        border-left: 1px solid #E9EAF2;
    }

    .disabled input {
        cursor: not-allowed;
    }

    .red-tip {
        color: #FB4E44;
    }

    .mr16 {
        margin-right: 16px;
    }

    .splitInvoiceWrap {
        position: relative;
    }
</style>
