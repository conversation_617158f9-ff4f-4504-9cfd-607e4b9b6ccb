<template>
    <div :class="$style.module">
        <h3 :class="$style.h3">
            分配金额
        </h3>
        <div :class="$style.p">
            开票总金额：{{ amountByCity.totalMoneyMilli | formatMilli }} 元
        </div>
        <div
            v-for="(company, comIndex) in splitAmount"
            :key="comIndex"
        >
            <div>
                <span :class="$style.company">所属公司：{{ company.companyName }}</span>
                {{ company.moneyMilli | formatMilli }}元
            </div>
            <coupon
                v-for="(q, qIndex) in company.qualifications"
                :key="q.id"
                :class="$style.coupon"
            >
                <template slot="header">
                    <span :class="$style.title">
                        发票抬头：
                        {{ q.partnerBName }}
                    </span>
                    <div :class="$style.sum">
                        ￥{{ calcSum(company, qIndex) | formatMilli }}
                    </div>
                </template>
                <ul :class="$style.splitList">
                    <li
                        v-for="(txt, idx) in q.amountList"
                        :key="`${q.id}_${idx}`"
                        :class="$style.splitInvoiceWrap"
                    >
                        <div :class="$style.left">
                            <span>发票{{ isPublicInvoices ? '' : calcOffset(comIndex,qIndex, idx) }}</span>
                            <span v-if="isPublicInvoices && idx === 0">
                                <roo-icon
                                    name="question2"
                                    :class="$style.questionIcon"
                                >
                                    <roo-tooltip
                                        placement="top"
                                        style="width: 200px;"
                                    >
                                        应基金会开票要求，单次可申请一张票据，多张发票可分开提交
                                    </roo-tooltip>
                                </roo-icon>
                            </span>
                        </div>
                        <div :class="[$style.mid ,$style.splitInvoiceWrap]">
                            金额
                            <roo-input
                                :class="[$style.input, isPublicInvoices ? $style.disabled : '']"
                                :value="isPublicInvoices && idx === 0 ? $options.filters.formatMilli(amountByCity.totalMoneyMilli) : txt"
                                placeholder="请输入金额"
                                :readonly="isPublicInvoices"
                                :disabled="isPublicInvoices"
                                @input="handleInput(comIndex, qIndex, idx, $event)"
                            />
                        </div>
                        <div :class="$style.right">
                            <span
                                v-if="q.amountList.length > ((qIndex === company.qualifications.length - 1) ? 0 : 1) && !isPublicInvoices"
                                key="rm"
                                :class="$style.rm"
                                @click="handleRemove(comIndex, qIndex, idx)"
                            >
                                删除
                            </span>
                            <span
                                v-if="idx === 0 && !isPublicInvoices"
                                key="add"
                                :class="$style.add"
                                @click="addCitySplitAmount({comIndex, qIndex})"
                            >
                                增加发票
                            </span>
                        </div>
                    </li>
                    <li
                        v-if="qIndex === company.qualifications.length - 1 && !isPublicInvoices"
                        key="rest"
                    >
                        <div :class="$style.left">
                            发票{{ calcOffset(comIndex,qIndex, 'last') }}
                        </div>
                        <div :class="[$style.mid ,$style.splitInvoiceWrap]">
                            金额
                            <roo-input
                                :class="[$style.input, $style.disabled]"
                                :value="company.restMilli | formatMilli"
                                :status="company.restMilli <0 ? 'error' : null"
                                placeholder="请输入金额"
                                readonly
                            />
                            <span
                                v-if="company.restMilli < 0"
                                :class="$style.redTip"
                            >
                                其余发票金额超过开票总金额
                            </span>
                        </div>
                        <div :class="$style.right">
                            <span
                                v-if="q.amountList.length === 0"
                                key="add"
                                :class="$style.add"
                                @click="addCitySplitAmount({comIndex, qIndex})"
                            >
                                增加发票
                            </span>
                        </div>
                    </li>
                </ul>
            </coupon>
        </div>
        <div class="footer-nav">
            <roo-button
                :class="$style.mr16"
                type="brand-hollow"
                @click="handlePrevClick"
            >
                上一步
            </roo-button>
            <roo-button
                :disabled="false"
                type="brand"
                @click="handleNextClick"
            >
                下一步
            </roo-button>
        </div>
    </div>
</template>
<script>
import {
    mapState, mapGetters, mapMutations, mapActions,
} from 'vuex';
import { confirm, alert } from '@roo/roo-vue';
/* eslint-disable import/extensions, import/no-unresolved */
import Coupon from '$components/coupon';
import { BaseFeeTypeEnum } from '$config/enum';
import { formatCent, formatMilli, formatMilliToZero } from '$lib/filters';
// eslint-disable-next-line camelcase
import { MIN_PUBLIC_ACTIVITY_AMOUNT_Milli, MAX_ZERO_AMOUNT_Milli } from '$config/constant';
/* eslint-enable import/extensions, import/no-unresolved */
import XInput from './components/x-input';

function sum(acc, cur) {
    return acc + cur;
}
export default {
    name: 'AmountSplitByCity',
    components: {
        Coupon,
        XInput,
    },
    filters: {
        formatCent,
        formatMilli,
        formatMilliToZero,
    },
    data() {
        return {
            // 常量
            // eslint-disable-next-line camelcase
            MIN_PUBLIC_ACTIVITY_AMOUNT: MIN_PUBLIC_ACTIVITY_AMOUNT_Milli / 10000,
            MAX_ZERO_AMOUNT_Milli,
        };
    },
    computed: {
        ...mapState('apply', ['baseFeeTypeItem', 'qualifications', 'companySelected', 'splitAmount']),
        ...mapState('config', ['isSinglePoi']),
        ...mapGetters('amountSelect', ['checkedMoneyCent', 'checkedMoneyMilli', 'amountByCity']),
        companyList() {
            if (!this.baseFeeTypeItem || !this.baseFeeTypeItem.sellerCompanyList.length) return [];
            if (this.companySelected === 0) return this.baseFeeTypeItem.sellerCompanyList;
            return this.baseFeeTypeItem.sellerCompanyList.filter(el => el.ownerShip === this.companySelected);
        },
        isPublicInvoice() {
            return this.baseFeeTypeItem.baseFeeType === BaseFeeTypeEnum.PublicActivity;
        },
        isPublicInvoices() {
            return [
                BaseFeeTypeEnum.PublicActivity, // 青山公益
                BaseFeeTypeEnum.Children, // 乡村儿童操场
                BaseFeeTypeEnum.Natural, // 零废星球
            ].includes(this.baseFeeTypeItem.baseFeeType);
        },
    },
    mounted() {
        const { baseFeeTypeItem, qualifications, checkedMoneyMilli } = this;
        if (!(baseFeeTypeItem && qualifications && qualifications.length > 0 && checkedMoneyMilli > 0)) {
            this.$router.go(-1);
        }
        this.initSplitAmount();
        
        // 公益发票模式下，初始化时设置正确的金额
        if (this.isPublicInvoices && this.splitAmount.length > 0) {
            // 遍历所有公司
            this.splitAmount.forEach((company, comIndex) => {
                // 遍历所有资质
                company.qualifications.forEach((qualification, qIndex) => {
                    // 只保留第一个金额项，并设置为总金额
                    if (qualification.amountList.length > 0) {
                        // 设置第一个金额为总金额
                        this.changeCitySplitAmount({
                            comIndex,
                            qIndex,
                            idx: 0,
                            value: (this.amountByCity.totalMoneyMilli / 10000).toFixed(2),
                        });
                        
                        // 删除多余的金额项
                        while (qualification.amountList.length > 1) {
                            this.removeCitySplitAmount({
                                comIndex,
                                qIndex,
                                idx: qualification.amountList.length - 1,
                            });
                        }
                    }
                });
            });
            
            // 计算剩余金额
            this.cacRest();
        }
    },
    methods: {
        ...mapMutations('apply', [
            'changeQualifications',
            'addQualificationSplitAmount',
            'addCitySplitAmount',
            'changeCitySplitAmount',
            'removeCitySplitAmount',
            'cacRest',
            'removeQualificationSplitAmount',
            'changeQualificationSplitAmount',
        ]),
        ...mapActions('apply', [
            'initSplitAmount',
        ]),
        calcOffset(comIndex, qIndex, idx) {
            const { splitAmount } = this;
            let invoiceNo = 1;
            for (let companyIndex = 0; companyIndex <= comIndex; companyIndex++) {
                const company = splitAmount[companyIndex];
                for (let qualifyIndex = 0; qualifyIndex < company.qualifications.length; qualifyIndex++) {
                    const qualification = company.qualifications[qualifyIndex];
                    for (let amountIndex = 0; amountIndex < qualification.amountList.length; amountIndex++) {
                        if (companyIndex === comIndex && qualifyIndex === qIndex && amountIndex === idx) {
                            return invoiceNo;
                        }
                        invoiceNo += 1;
                    }
                    if (idx === 'last' && companyIndex === comIndex && qualifyIndex === qIndex) {
                        return invoiceNo;
                    }
                }
                invoiceNo += 1; // 加上最后那个不能删除的展示剩余金额
            }
            return invoiceNo;
        },
        calcSum(company, index) {
            const qualification = company.qualifications[index];

            let total = qualification.amountList.map(x => parseFloat(x) * 10000 || 0).reduce(sum, 0);
            if (index === company.qualifications.length - 1) {
                total += company.restMilli;
            }
            return total;
        },
        handleInput(comIndex, qIndex, idx, value) {
            if (this.isPublicInvoices) {
                return; // 公益发票模式下不允许修改金额
            }
            if (/^\d*\.?\d{0,2}$/.test(value)) {
                this.changeCitySplitAmount(
                    {
                        comIndex, qIndex, idx, value,
                    },
                );
                this.cacRest();
            }
        },
        handleRemove(comIndex, qIndex, idx) {
            this.removeCitySplitAmount(
                {
                    comIndex, qIndex, idx,
                },
            );
            this.cacRest();
        },
        handlePrevClick() {
            this.$router.go(-1);
        },
        handleNextClick() {
            const fakeInvoice = []; // 开票金额为0
            const negativeInvoice = [];
            const { splitAmount } = this;
            let invoiceNo = 1;
            for (let companyIndex = 0; companyIndex < splitAmount.length; companyIndex++) {
                const company = splitAmount[companyIndex];
                for (let qualifyIndex = 0; qualifyIndex < company.qualifications.length; qualifyIndex++) {
                    const qualification = company.qualifications[qualifyIndex];
                    for (let amountIndex = 0; amountIndex < qualification.amountList.length; amountIndex++) {
                        if (!parseFloat(qualification.amountList[amountIndex])) {
                            fakeInvoice.push(invoiceNo);
                        }
                        if (parseFloat(qualification.amountList[amountIndex]) < 0) {
                            negativeInvoice.push(invoiceNo);
                        }
                        invoiceNo += 1;
                    }
                }
                // 公司数据的最后一个剩余值也不能为空
                if (!company.restMilli) fakeInvoice.push(invoiceNo);
                if (company.restMilli < 0) negativeInvoice.push(invoiceNo);
                invoiceNo += 1; // 加上最后那个不能删除的展示剩余金额
            }
            if (negativeInvoice.length > 0) {
                alert(`发票 ${negativeInvoice.join('、')} 金额小于0元，请调整后再试`, () => {});
                return;
            }
            if (fakeInvoice.length > 0 && !this.isPublicInvoices) {
                confirm(`发票 ${fakeInvoice.join('、')} 金额为 0，不会生成一张发票，是否继续？`, (yes) => {
                    if (yes) {
                        this.$router.push({ name: !this.isSinglePoi ? 'invoice-type-confirm-update' : 'invoice-type-confirm' });
                    }
                });
                return;
            }
            this.$router.push({ name: !this.isSinglePoi ? 'invoice-type-confirm-update' : 'invoice-type-confirm' });
        },
    },
};
</script>
<style lang="scss" module>
    .module {
        margin: 30px 40px;
    }
    .h3 {
        margin-top: 0;
        margin-bottom: 20px;
        font-size: 20px;
    }
    .company {
        margin-right: 15px;
    }
    .p {
        margin: 20px 0;
    }
    .coupon {
        width: 700px;
        margin-bottom: 20px;
    }
    .title {
        display: inline-block;
        line-height: 45px;
        font-weight: bold;
    }
    .sum {
        line-height: 45px;
        float: right;
    }
    .split-list {
        list-style: none;
        margin: 0;
        padding: 0;
        li {
            display: flex;
            align-items: center;
            margin: 20px 0;
            &:first-child {
                margin-top: 0;
            }
            &:last-child {
                margin-bottom: 0;
            }
        }
    }
    .left {
        width: 100px;
    }
    .mid {
        flex: 1;
    }
    .right {
        width: 100px;
        line-height: 1;
    }
    .input {
        display: inline-block;
        width: 150px;
        margin: 0 10px;
    }
    .rm {
        float: left;
        color: #FB4E44;
        cursor: pointer;

        &.disabled {
            color: #ccc;
            cursor: not-allowed;
        }
    }
    .add {
        float: right;
        color: #06C1AE;
        cursor: pointer;

        &.disabled {
            color: #ccc;
            cursor: not-allowed;
        }
    }
    .rm + .add {
        padding-left: 8px;
        border-left: 1px solid #E9EAF2;
    }
    .disabled input {
        cursor: not-allowed;
    }
    .red-tip {
        color: #FB4E44;
    }
    .mr16 {
        margin-right: 16px;
    }
    .splitInvoiceWrap {
        position: relative;
    }
    .publicTip {
        color: #F89800;
        font-size: 12px;
        margin-left: 5px;
    }
    .questionIcon {
        font-size: 14px;
        margin-left: 5px;
        cursor: pointer;
    }
</style>
