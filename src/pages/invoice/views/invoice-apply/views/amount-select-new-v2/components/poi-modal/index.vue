<template>
    <roo-modal
        :value="value"
        size="large"
        @input="$emit('input', $event)"
        @hidden="handleHidden"
    >
        <template slot="title">
            {{ settleName }}
            <span
                :class="[$style.tip, $style.gray]"
            >所属结算设置 ID:</span>
            <span :class="$style.tip">{{ settleId }}</span>
        </template>

        <div :class="$style.row">
            <div :class="$style.checkbox">
                <roo-checkbox
                    :disabled="allDisabled"
                    :checked="(!!total) && checkedCount === total"
                    :indeterminate="checkedCount > 0 && checkedCount < total"
                    @change="handleCheckAll"
                >
                    全选所有（共 {{ total }} 条，已选 {{ checkedCount }} 条）
                </roo-checkbox>
            </div>
            <div>
                <roo-input
                    v-model="idInput"
                    :class="$style.input"
                    placeholder="请输入商家 ID"
                    clearable
                    @change="searchPoiId = ''"
                />
                <roo-button @click="handleSearch">
                    搜索
                </roo-button>
            </div>
        </div>

        <table :class="$style.table">
            <col style="width: 56px;" />
            <col style="width: 150px;" />
            <col />
            <col style="width: 130px;" />

            <thead>
                <tr>
                    <th></th>
                    <th>商家 ID</th>
                    <th>商家名称</th>
                    <th>金额</th>
                </tr>
            </thead>

            <tbody>
                <tr
                    v-if="total === 0"
                    key="empty"
                >
                    <td
                        :class="$style.empty"
                        colspan="4"
                    >
                        暂无数据
                    </td>
                </tr>
                <template v-else>
                    <tr
                        v-for="item in list"
                        :key="item.partnerId"
                        :class="`${item.partnerId}` === searchPoiId ? $style.active : null"
                    >
                        <td>
                            <roo-checkbox
                                :disabled="item.disabled"
                                :checked="item.checked"
                                @change="item.checked = $event"
                            />
                        </td>
                        <td>{{ item.partnerId }}</td>
                        <td>{{ item.partnerName }}</td>
                        <td>{{ item.checkedMoney | formatCent }}</td>
                    </tr>
                </template>
            </tbody>
        </table>

        <div
            v-if="total > 0"
            :class="$style.pagination"
        >
            <roo-pagination
                :total="total"
                :page-size="pageSize"
                :current-page="pageNo"
                @current-change="pageNo = $event"
            />
        </div>

        <template slot="footer">
            <roo-button
                type="hollow"
                @click="handleCancel"
            >
                取消
            </roo-button>
            <roo-button
                @click="handleConfirm"
            >
                确定
            </roo-button>
        </template>
    </roo-modal>
</template>

<script>
/* eslint-disable import/no-unresolved, import/extensions */
import { mapState, mapMutations } from 'vuex';
import { toast } from '@roo/roo-vue';
import { copyArray } from '$utils/util';
/* eslint-disable import/extensions, import/no-unresolved */
import { formatNumberThousand } from '$lib/utils';

export default {
    name: 'PoiModal',
    props: {
        value: {
            type: Boolean,
            default: false,
        },
        amountIndex: {
            type: Number,
            default: 0,
        },
        // 页面搜索的商家ID
        poiId: {
            type: Number,
            default: -1,
        },
    },
    filters: {
        formatCent(num) {
            return formatNumberThousand((num / 100).toFixed(2));
        },
    },
    data() {
        return {
            idInput: null,
            searchPoiId: '',
            // 结算名称
            settleName: '',
            // 结算ID
            settleId: 0,
            // 存储中间结果
            poiList: [],
            pageNo: 1,
            pageSize: 10,
        };
    },
    computed: {
        ...mapState('amountSelectNew', ['settleAmountList']),
        total() {
            return this.poiList.length;
        },
        allDisabled() {
            return this.poiList.every(ele => ele.disabled);
        },
        list() {
            const { poiList, pageNo, pageSize } = this;
            const offset = (pageNo - 1) * pageSize;
            return poiList.slice(offset, offset + pageSize);
        },
        checkedCount() {
            return this.poiList.filter((x) => x.checked).length;
        },
    },
    watch: {
        value(show) {
            // 如果外面查询的商家ID不为空
            if (show && this.poiId !== -1) {
                this.idInput = `${this.poiId}`;
                this.handleSearch();
            }
            if (show && this.amountIndex >= 0 && this.amountIndex < this.settleAmountList.length) {
                const obj = this.settleAmountList[this.amountIndex];
                const { partnerList, settleId, settleName } = obj;
                this.settleName = settleName;
                this.settleId = settleId;
                this.poiList = partnerList.map(ele => copyArray(ele)); // 未点确定之前使用本地副本
            }
        },
    },
    methods: {
        ...mapMutations('amountSelectNew', ['changeListCheckPoi']),
        handleCheckAll(checked) {
            this.poiList.forEach((poi) => {
                // 小于0的金额禁止用户操作
                if (poi.checkedMoney >= 0) {
                    poi.checked = checked;
                }
            });
        },
        handleSearch() {
            const id = this.idInput.trim();
            this.searchPoiId = id;
            const idx = this.poiList.findIndex((poi) => `${poi.partnerId}` === id);
            if (idx > -1) {
                this.pageNo = Math.ceil((idx + 1) / this.pageSize);
            } else {
                toast.warn('查询的门店不在此结算设置下');
            }
        },
        handleCancel() {
            this.$emit('input', false);
        },
        handleConfirm() {
            this.changeListCheckPoi({ poiTemp: this.poiList, index: this.amountIndex, checkedNum: this.checkedCount });
            this.$emit('input', false);
        },
        handleHidden() {
            this.idInput = '';
            this.searchPoiId = '';
            this.pageNo = 1;
        },
    },
};
</script>

<style lang="scss" module>
.tip {
  font-weight: normal;
  font-size: 14px;
}

.gray {
  color: #858692;
}

.row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.checkbox {
  padding-left: 20px;
  line-height: 1;
}

.input {
  display: inline-block;
  width: 200px;
  vertical-align: middle;
}

.table {
  width: 100%;
  border: 1px solid #e9eaf2;
  table-layout: fixed;
  border-collapse: collapse;
  color: #3f4156;

  // thead 设置一个背景色相同的 bottom-border
  // 防止 active 样式多出边框引起高度变化
  thead {
    border-bottom: 1px solid #f7f8fa;
  }

  thead,
  tbody tr:nth-of-type(even) {
    background: #f7f8fa;
  }

  th,
  td {
    height: 40px;
    padding: 0 20px;
    text-align: left;
  }

  th {
    font-weight: normal;
    color: #858692;
  }

  tr.active {
    border: 1px solid #f8b500;
  }

  :global(.roo-checkbox) {
    display: inline;
  }

  :global(.roo-checkbox .custom-checkbox) {
    margin-right: 0;
  }

  .loading,
  .empty {
    text-align: center;
  }
}

.pagination {
  text-align: center;
}
</style>
