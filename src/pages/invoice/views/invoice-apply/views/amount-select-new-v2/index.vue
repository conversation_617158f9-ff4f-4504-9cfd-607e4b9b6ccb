<template>
    <div>
        <div :class="$style.module">
            <!-- 青山环保项目 -->
            <roo-alert
                v-if="isPublicActivityFee"
                :class="$style.alert"
                type="warning"
                icon="exclamation-circle"
            >
                <ul>
                    <li>为了让贵方的善款能发挥最大的公益价值，节省公益项目成本，累计捐赠超过 50 元人民币可在本系统上直接申请开具公益捐赠票据。请您尽量在捐赠当年申请捐赠票据，申请去年票据请您在每年5月31日前尽早提交。</li>
                    <li>如有特别需求，请拨打中华环境保护基金会票据咨询电话：010-67173592 。</li>
                </ul>
            </roo-alert>
            <!-- 零废星球洁净自然 -->
            <roo-alert
                v-if="isNatural"
                :class="$style.alert"
                type="warning"
                icon="exclamation-circle"
            >
                <ul>
                    <li>为了让贵方的善款能发挥最大的公益价值，节省公益项目成本，累计捐赠超过 50 元人民币可在本系统上直接申请开具公益捐赠票据。请您尽量在捐赠当年申请捐赠票据，每年5月31日前可申请去年发票。</li>
                    <li>如有特别需求，请拨打北京市企业家环保基金会咨询电话：010-57505155。</li>
                </ul>
            </roo-alert>
            <!-- 乡村儿童操场项目 -->
            <roo-alert
                v-if="isChildren"
                :class="$style.alert"
                type="warning"
                icon="exclamation-circle"
            >
                <ul>
                    <li>依据深圳壹基金基金会有关财务管理制度与捐赠票据管理制度的规定，请您尽量在捐赠当年申请捐赠票据。</li>
                    <li>如有特别需求，请拨打深圳壹基金基金会咨询电话：400-6902700。</li>
                </ul>
            </roo-alert>

            <h3 :class="$style.h3">
                选择开票金额
            </h3>

            <!--按结算设置汇总-->
            <template v-if="accountSource == SOURCE.WAIMAI">
                <div v-if="baseFeeTypeItem && baseFeeTypeItem.amountGatherType === 1">
                    <div :class="`${$style.mb10} ${$style.queryRow}`">
                        <div :class="$style.queryContainer">
                            <span :class="$style.queryLabel">选择时间维度</span>
                            <roo-radio-group v-model="queryTimeDimension">
                                <roo-radio
                                    value="byMonth">月份</roo-radio>
                                <roo-radio
                                    :disabled="isPublicActivityFee || isNatural || isChildren"
                                    value="byDate">日期</roo-radio>
                            </roo-radio-group>
                            <span v-if="isPublicActivityFee || isNatural || isChildren" :class="$style.tooltip">
                                <roo-icon
                                    name="question2"
                                    size="14px"
                                />
                                <roo-tooltip
                                    placement="top"
                                    trigger="hover"
                                >
                                    <slot>
                                        <div>根据基金会要求，公益类发票只允许按月开具发票，敬请谅解。</div>
                                    </slot>
                                </roo-tooltip>
                            </span>
                        </div>
                        <div :class="$style.queryContainer">
                            <span :class="$style.queryLabel">时间范围</span>
                            <roo-date-time-picker
                                key="byDate"
                                v-if="queryTimeDimension==='byDate'"
                                v-model="queryDateRange"
                                format="yyyy-MM-dd"
                                type="range"
                                :min-date="minDate"
                                :max-date="maxDate"
                                clearable
                            />
                            <roo-date-time-picker
                                key="byMonth"
                                v-if="queryTimeDimension==='byMonth'"
                                v-model="queryMonthRange"
                                format="yyyy-MM"
                                type="range"
                                clearable
                                :min-date="minDateForRange"
                                :max-date="maxDate"
                            />
                        </div>
                    </div>
                    <div :class="`${$style.mb10} ${$style.queryRow}`">
                        <div :class="$style.queryContainer">
                            <span :class="$style.queryLabel">选择搜索维度</span>
                            <roo-radio-group v-model="queryFilterDimension">
                                <roo-radio
                                    value="byPoiId">门店ID</roo-radio>
                                <roo-radio
                                    value="bySettleId">结算ID</roo-radio>
                            </roo-radio-group>
                        </div>
                        <div :class="$style.queryContainer">
                            <span :class="$style.queryLabel">{{ queryFilterDimension === 'bySettleId' ? '结算' : '门店' }}ID</span>
                            <roo-input
                                v-model="inputIdListStr"
                                :placeholder="`请输入${queryFilterDimension === 'bySettleId' ? '结算' : '门店'}ID；批量输入以“英文逗号”分割`"
                            />
                            <roo-button type="brand-text" @click="openVerifyModal">批量输入</roo-button>
                        </div>
                        <!-- <div v-if="queryFilterDimension === 'bySettleId'" :class="$style.queryContainer">
                            <span :class="$style.queryLabel">结算ID</span>
                            <roo-input
                                v-model="settleIdListStr"
                                placeholder="请输入结算ID；批量输入以"换行"分割"
                            />
                            <roo-button type="brand-text" @click="openVerifyModal">批量输入</roo-button>
                        </div> -->
                        <roo-button @click="() => handleQuery()">查询</roo-button>
                    </div>
                    <div :class="$style.queryRow">
                        <roo-checkbox
                            v-if="queryFilterDimension === 'byPoiId'"
                            :checked="onlyShowSelectedPoi"
                            @change="(newVal) => onlyShowSelectedPoi = newVal"
                        >仅展示已选择门店
                        </roo-checkbox>
                        <roo-button v-if="settleAmountList.length > 0 || partnerAmountList.length > 0" :loading="exporting" type="hollow" :style="{'margin-left':'auto'}" @click="handleExport">导出数据</roo-button>
                    </div>
                </div>
            </template>

            <!-- 按照结算设置选择金额 -->
            <amount-table
                v-if="queryFilterDimension === 'bySettleId'"
                :table-list="settleAmountList"
                :loading="loading"
                @select-poi="handleSelectPoi"
                @check="handleCheck"
                @check-all="handleCheckAll"
            />
            <amount-table-poi
                v-if="queryFilterDimension === 'byPoiId'"
                :table-list="partnerAmountList.filter(item => !onlyShowSelectedPoi || item.selected)"
                :loading="loading"
                @check="handleCheckPoiRow"
                @check-all="handleCheckAllPoiRow"
            />
            <div
                v-if="total > 0"
                :class="$style.pagination"
            >
                <roo-button
                    v-if="queryFilterDimension === 'byPoiId' && partnerAmountList.length < total"
                    key="next"
                    :class="$style.btn"
                    :disabled="loading"
                    :loading="loadingStatus(LoadingType.LoadMore)"
                    size="mini"
                    type="hollow"
                    @click="loadMore"
                >
                    {{ loading ? '加载中' : `查看更多 第 ${nextPageText} 条 (共 ${total} 条)` }}
                </roo-button>
                <span
                    v-else
                    key="done"
                    :class="$style.done"
                >
                    已加载完毕
                </span>
            </div>
        </div>

        <div class="footer-nav">
            <div :class="$style.summary">
                <div
                    v-if="(queryFilterDimension === 'bySettleId' ? checkedAmountCount : checkedPoiAmountCount) > 0"
                    :class="$style.tip1"
                >
                    共选择 <span class="amount">{{ totalSelectedAmount | formatCent }}</span> 元
                </div>
                <div
                    v-else
                    :class="$style.tip1"
                >
                    请选择金额
                </div>
            </div>
            <roo-button
                :class="$style.mr16"
                type="brand-hollow"
                @click="handlePrevClick"
            >
                上一步
            </roo-button>
            <roo-button
                :disabled="totalSelectedAmount <= 0"
                type="brand"
                @click="handleNextClick"
            >
                下一步
            </roo-button>
        </div>
        <poi-modal
            v-model="displayPoiSelect"
            :amount-index="selectPoiAmountIndex"
            :poi-id="poiId"
        />
        <roo-modal
            :title="`批量输入${this.queryFilterDimension === 'byPoiId' ? '门店ID' : '结算ID'}`"
            :value="verifyModalVisible"
            @input="closeVerifyModal"
        >
            <div :class="$style.verifyInputContainer">
                <textarea
                    :placeholder="`输入${this.queryFilterDimension === 'byPoiId' ? '门店ID' : '结算ID'}，一行一个`"
                    v-model="verifyModalIdStr"
                >
                </textarea>
                <div v-if="this.validateResult.length > 0" :class="$style.verifyResult" @click.self="validateResult = []">
                    <roo-tag
                        v-for="item in validateResult"
                        closable
                        :key="item.id"
                        :type="item.value === 0 ? 'primary' : 'danger'"
                        @close="removeId(item.id)">{{ item.id }}</roo-tag>
                </div>
            </div>
            <template slot="footer">
                <roo-button
                    type="hollow"
                    @click="closeVerifyModal"
                >
                    取消
                </roo-button>
                <roo-button
                    @click="verifyId"
                >
                    确定
                </roo-button>
            </template>
        </roo-modal>
    </div>
</template>

<script>
import dayjs from 'dayjs';
import {
    mapState,
    mapGetters,
    mapMutations,
    mapActions,
} from 'vuex';
import { toast } from '@roo/roo-vue';

/* eslint-disable import/extensions, import/no-unresolved */
import { getCookieValue } from '$lib/utils';
import { QueryButtonType } from '$config/enum';
import SOURCE from '$lib/sourceEnum';
import { formatCent } from '$lib/filters';
import request from '$invoice/utils/request';
// eslint-disable-next-line camelcase
import { MIN_PUBLIC_ACTIVITY_AMOUNT_Milli } from '$config/constant';
/* eslint-enable import/extensions, import/no-unresolved */
// eslint-disable-next-line import/no-unresolved, import/extensions
import toastError from '$lib/toast-error';

import AmountTable from './components/amount-table';
import AmountTablePoi from './components/amount-table-poi';
import PoiModal from './components/poi-modal';

export default {
    name: 'AmountSelectNew',
    components: {
        AmountTable,
        PoiModal,
        AmountTablePoi,
    },
    filters: {
        formatCent,
    },
    data() {
        return {
            startDate: null,
            endDate: null,
            loading: false,
            activeQueryButton: QueryButtonType.Date,
            // 查询ID框中内容
            idInput: '',
            // 查询的结算ID
            searchId: 0,
            // 查询的门店ID
            poiId: -1,

            displayPoiSelect: false,
            selectPoiAmountIndex: -1,

            displayAdDetail: false,
            adAmount: null,
            showAlert: false,
            accountSource: getCookieValue('source') || SOURCE.WAIMAI,
            SOURCE,
            LoadingType: null,

            loadMoreFromFirstPage: true, // 点击加载更多时，是否需要从第一页加载数据
            queryTimeDimension: 'byMonth', // 选择时间维度
            queryFilterDimension: 'byPoiId', // 选择搜索维度
            queryDateRange: [],
            queryMonthRange: [],
            inputIdListStr: '',
            // poiIdListStr: '',
            // settleIdListStr: '',
            verifyModalIdStr: '',
            verifyModalVisible: false,
            validateResult: [],
            onlyShowSelectedPoi: false,
            exporting: false, // 导出数据loading
        };
    },
    computed: {
        // 开销方文本，当为平台佣金和配送服务费时，显示另外的文本
        compText() {
            if (this.baseFeeTypeItem && [4, 21].indexOf(this.baseFeeTypeItem.baseFeeType) > -1) {
                return '依据合同签署情况，本次申请可能收到多个公司主体开具的发票';
            } else if (this.baseFeeTypeItem && [15, 38, 39].indexOf(this.baseFeeTypeItem.baseFeeType) > -1) {
                return '根据您参与的公益项目，公益捐赠票据由对应的公益机构开具';
            } else {
                return '门店推广技术服务由北京三快在线科技有限公司、上海三快智送科技有限公司联合提供，依据您签署的推广协议，可能收到上述公司分别开具的发票';
            }
        },
        ...mapState('config', ['contractId', 'feeTypeList']),
        ...mapState('apply', [
            'baseFeeTypeItem',
            'qualifications',
            'companySelected',
            'ownership',
            'splitContent',
        ]),
        ...mapState('amountSelectNew', [
            'dateFrom',
            'dateTo',
            'total',
            'pageNo',
            'pageSize',
            'settleAmountList',
            'partnerAmountList',
            'totalSelectedAmount',
            'checkedAmountList',
        ]),
        ...mapState('invoiceTitle', ['poiRelatedToTitle']),

        ...mapGetters('apply', ['isPublicActivityFee', 'isChildren', 'isNatural']),
        ...mapGetters('amountSelectNew', [
            'currentCount',
            'checkedAmountCount',
            'checkedPoiAmountCount',
        ]),
        companyList() {
            const { sellerCompanyList = [] } = this.baseFeeTypeItem || {};
            return sellerCompanyList;
        },
        companyOptionList() {
            if (!this.companyList.length) return [];
            const options = this.companyList.map((item) => ({
                ...item,
                value: item.ownerShip,
                label: item.companyName,
            }));
            return [
                {
                    value: -1,
                    label: '全部',
                },
                ...options,
            ];
        },

        maxDate() {
            // 对于公益发票(青山环保和零废星球)和乡村儿童操场项目
            if (this.isPublicActivityFee || this.isNatural) {
                const currentDate = dayjs();
                const currentMonth = currentDate.month(); // 0-11

                // 获取上个月的最后一天
                const lastMonthEnd = currentDate.subtract(1, 'month').endOf('month');

                // 如果当前是5月31日之前，可以选择去年的日期
                if (currentMonth <= 4 || (currentMonth === 4 && currentDate.date() <= 31)) {
                    // 返回上个月的最后一天
                    return lastMonthEnd.toDate();
                }

                // 5月31日之后只能选择当年的日期（不含当月）
                return lastMonthEnd.toDate();
            }

            // 乡村儿童操场项目
            if (this.isChildren) {
                // 只能选到上个月底，不受5月31日限制
                return dayjs().subtract(1, 'month').endOf('month').toDate();
            }
            return null;
        },

        // 添加一个新的计算属性来处理最小日期
        minDateForRange() {
            const today = dayjs();
            const currentYear = today.year();
            const currentMonth = today.month(); // 0-11
            // const currentDate = today.date();


            // 对于公益发票(青山环保和零废星球)
            if (this.isPublicActivityFee || this.isNatural) {
                // 如果当前是5月31日之前，可以开上一年的票
                if (currentMonth <= 4 || (currentMonth === 4 && today.date() <= 31)) {
                    return dayjs(`${currentYear - 1}-01-01`).toDate();
                }
                // 5月31日之后只能开当年的票
                return dayjs(`${currentYear}-01-01`).toDate();
            }

            // 乡村儿童操场项目
            if (this.isChildren) {
                return dayjs(`${currentYear - 1}-01-01`).toDate();
                // 如果当前是6月1日之后
                // if (currentMonth > 5 || (currentMonth === 5 && currentDate >= 1)) {
                //     // 不能早于上一年1月1日
                //     return dayjs(`${currentYear - 1}-01-01`).toDate();
                // }
            }

            return null;
        },
        minStartDate() {
            let yearStart = dayjs().startOf('year');
            // 青山环保、零废星球，项目开票时间限制：不能跨年
            if (this.isPublicActivityFee || this.isNatural) {
                if (this.endDate) {
                    // 取结束时间的年初
                    yearStart = dayjs(this.endDate).startOf('year');
                }
                return yearStart.toDate();
            }
            // 乡村儿童操场的特殊判断
            if (this.isChildren) {
                const curMonth = dayjs().month();
                const yearAgo = dayjs().year() - 1;
                const December = dayjs(`${yearAgo}-12-01`);
                // 如果当前是一月份
                if (curMonth === 0) {
                    return December.toDate();
                } else {
                    if (this.endDate) {
                        // 取结束时间的年初
                        yearStart = dayjs(this.endDate).startOf('year');
                    }
                    return yearStart.toDate();
                }
            }
            return null;
        },
        maxStartDate() {
            // 开始时间的限制：结束时间或昨天
            return this.endDate || dayjs().subtract(1, 'd').toDate();
        },

        minEndDate() {
            return this.startDate || null;
        },
        maxEndDate() {
            const yesterday = dayjs().subtract(1, 'd');
            // 青山环保、零废星球，项目开票时间限制：不能跨年
            if (this.isPublicActivityFee || this.isNatural) {
                if (this.startDate) {
                    // 取开始时间的年末
                    const yearEnd = dayjs(this.startDate).endOf('year');
                    if (yesterday.isAfter(yearEnd)) {
                        return yearEnd.toDate();
                    }
                }
                return yesterday.toDate();
            }
            return yesterday.toDate();
        },

        nextPageStart() {
            const { pageNo, pageSize } = this;
            return pageNo * pageSize + 1;
        },
        nextPageEnd() {
            const { pageNo, pageSize, total } = this;
            return Math.min(total, (pageNo + 1) * pageSize);
        },
        nextPageText() {
            const { nextPageStart, nextPageEnd } = this;
            if (nextPageStart === nextPageEnd) {
                return `${nextPageStart}`;
            }
            return `${nextPageStart} ~ ${nextPageEnd}`;
        },
        verifyModalIdList() {
            const list = this.verifyModalIdStr.split('\n').map(id => id.trim()).filter(id => !!id);
            return list;
        },
        invalidIdCount() {
            return this.validateResult.filter(item => item.value !== 0).length;
        },
    },
    created() {
        this.LoadingType = QueryButtonType;
        const { baseFeeTypeItem, qualifications } = this;

        // 进入时检查 qualification
        if (!(baseFeeTypeItem && qualifications && qualifications.length > 0)) {
            this.$router.go(-1);
        }
    },
    mounted() {
        if (this.dateFrom && this.dateTo) {
            this.startDate = new Date(this.dateFrom);
            this.endDate = new Date(this.dateTo);
        }
        this.resetDataList();
        this.updateDateRange();
    },
    watch: {
        queryFilterDimension() {
            this.inputIdListStr = '';
            this.loadMoreFromFirstPage = true;
            this.resetDataList();
        },
        queryTimeDimension() {
            this.loadMoreFromFirstPage = true;
        },
        inputIdListStr() {
            this.loadMoreFromFirstPage = true;
        },
        queryDateRange() {
            this.loadMoreFromFirstPage = true;
        },
        // queryMonthRange() {
        //     this.loadMoreFromFirstPage = true;
        // },
        queryMonthRange(newVal) {
            this.loadMoreFromFirstPage = true;
            if (newVal[0] && newVal[1]) {
                this.checkDateRangeValid(newVal, 'queryMonthRange');

                const startYear = dayjs(newVal[0]).year();
                const endYear = dayjs(newVal[1]).year();

                // 对于公益发票(青山环保和零废星球)
                if (this.isPublicActivityFee || this.isNatural) {
                    const today = dayjs();
                    const currentYear = today.year();
                    const currentMonth = today.month();
                    const currentDate = today.date();

                    // 如果当前时间是5月31日之前
                    if (currentMonth <= 4 || (currentMonth === 4 && currentDate <= 31)) {
                        // 只能开具去年的发票，不能更早
                        if (startYear < currentYear - 1 || endYear < currentYear - 1) {
                            toast.warn('只能开具上一年度的发票，请重新选择开票时间范围');
                            this.queryMonthRange = [null, null];
                        }
                    } else {
                        // 5月31日之后只能开当年的票
                        if (startYear < currentYear || endYear < currentYear) {
                            toast.warn('上一年度的发票必须在当年5月31日前（含5月31日）申请开具，请重新选择开票时间范围');
                            this.queryMonthRange = [null, null];
                        }
                    }
                }
            }
        },
    },
    methods: {
        ...mapMutations('apply', ['changeOwnership', 'changeHasOwnershipNegative', 'initSplitContent', 'changeSubmitComList']),
        updateDateRange() {
            const today = dayjs();
            if (this.isPublicActivityFee || this.isNatural || this.isChildren) {
                this.minDate = today.subtract(1, 'year').startOf('month').toDate();
                this.maxDate = today.toDate();
            } else {
                this.minDate = null;
                this.maxDate = null;
            }
        },
        handleInputChange() {
            // 用户修改查询框中的内容
            // 点击查询按钮的时候才真正赋值
            this.searchId = -1;
            this.poiId = -1;
        },
        handleIconClick() {
            this.showAlert = !this.showAlert;
        },
        // 切换开销方
        handleSelectCompany(option) {
            this.changeOwnership(option);
            // 接口请求的方式
            this.handleDateQuery();
            // 前端自己筛选
            // this.changeAmountListByCompany(option);
        },
        ...mapMutations('amountSelectNew', [
            'changeDateFrom',
            'changeDateTo',
            'changePageNo',
            'changeAmountListByCompany',
            'changeTotalSelectedAmount',
            'changeListBySelect',
            'changeListBySelectAll',
            'getOwnerCompanyList',
            'resetDataList',
            'changePoiListBySelect',
            'changePoiListBySelectAll',
        ]),
        ...mapActions('amountSelectNew', [
            'fetchAmountList',
            'fetchAmountListBySettleId',
            'fetchAmountListByPoiId',
        ]),
        loadingStatus(type) {
            return this.loading && this.activeQueryButton === type;
        },
        openVerifyModal() {
            this.verifyModalIdStr = this.inputIdListStr.split(',').join('\n');
            this.verifyModalVisible = true;
        },
        closeVerifyModal() {
            this.verifyModalVisible = false;
            this.validateResult = [];
        },
        removeId(id) {
            this.verifyModalIdStr = this.verifyModalIdStr.split('\n').filter(str => str !== `${id}`).join('\n');
            this.validateResult = this.validateResult.filter(item => item.id !== id);
        },
        verifyId() {
            if (this.verifyModalIdList.length === 0) {
                this.inputIdListStr = '';
                this.closeVerifyModal();
            } else {
                if (this.verifyModalIdList.some(id => !id.match(/^\d+$/))) {
                    toast.error(`${this.queryFilterDimension === 'byPoiId' ? '门店ID' : '结算ID'}仅支持正整数，请检查输入`, 1000);
                    return;
                }
                this.checkIdValidity(this.verifyModalIdList).then((formatedResult) => {
                    const invalidIds = formatedResult.filter(item => item.value !== 0).map(item => item.id);
                    if (invalidIds.length === 0) {
                        this.inputIdListStr = formatedResult.map(item => item.id).join(',');
                        this.closeVerifyModal();
                    } else {
                        this.validateResult = formatedResult;
                    }
                }, (err) => {
                    toast.error(err.message, 1000);
                });
            }
        },
        checkIdValidity(idList) {
            // 请求接口校验 ID 有效性
            const idsCopy = [...new Set(idList.filter(id => !!id))];
            if (idsCopy.length === 0) {
                return Promise.resolve(idsCopy);
            }
            const type = this.queryFilterDimension === 'byPoiId' ? 'partner' : 'settle';
            return request.get(`/finance/invoice/api/output/waimai/c/${type}/check`, {
                params: {
                    invoiceSource: 113,
                    contractId: this.contractId,
                    [`${type}Ids`]: idsCopy.join(','),
                },
            }).then((res) => {
                const { code, msg, data } = res.data;
                if (code !== 0) {
                    throw new Error(msg);
                    // toast.error(msg, 1000);
                } else {
                    const formatedResult = data.map(item => ({ id: item[`${type}Id`], value: item.value }));
                    return formatedResult;
                }
            });
        },
        handleQuery(pageNum = 1) {
            let dateFrom = '';
            let dateTo = '';

            // 参数检查
            if (this.queryTimeDimension === 'byMonth' && !(!!this.queryMonthRange[0] && !!this.queryMonthRange[1])) {
                toast.warn('请选择时间范围');
                return;
            }
            if (this.queryTimeDimension === 'byDate' && !(!!this.queryDateRange[0] && !!this.queryDateRange[1])) {
                toast.warn('请选择时间范围');
                return;
            }

            dateFrom = this.queryTimeDimension === 'byMonth' ? dayjs(this.queryMonthRange[0]).format('YYYY-MM') : dayjs(this.queryDateRange[0]).format('YYYY-MM-DD');
            dateTo = this.queryTimeDimension === 'byMonth' ? dayjs(this.queryMonthRange[1]).format('YYYY-MM') : dayjs(this.queryDateRange[1]).format('YYYY-MM-DD');


            if (this.inputIdListStr.trim() !== '' && this.inputIdListStr.trim().split(',').some(id => !id.match(/^\d+$/))) {
                toast.error(`${this.queryFilterDimension === 'byPoiId' ? '门店ID' : '结算ID'}仅支持正整数，以英文逗号分割，请检查输入`, 1000);
                return;
            }

            // 先校验 id 列表是否合法
            this.checkIdValidity(this.inputIdListStr.trim().split(',')).then((formatedResult) => {
                const invalidIdList = formatedResult.filter(item => item.value !== 0).map(item => item.id);
                if (invalidIdList.length > 0) {
                    toast.error(`${invalidIdList.length}个无效ID: ${invalidIdList.join(',')}。请确认后重新输入。`, 2000);
                    return;
                }

                if (this.queryFilterDimension === 'bySettleId') {
                    this.loading = true;
                    // 按结算ID查询展示
                    this.fetchAmountListBySettleId({
                        pageNum,
                        dateFrom,
                        dateTo,
                        settleIdListStr: formatedResult.map(item => item.id).join(','),
                    }).finally(() => {
                        this.loading = false;
                        this.loadMoreFromFirstPage = false;
                    });
                } else {
                    // 按门店ID查询展示
                    this.loading = true;
                    this.fetchAmountListByPoiId({
                        pageNum,
                        dateFrom,
                        dateTo,
                        poiIdListStr: formatedResult.map(item => item.id).join(','),
                    }).finally(() => {
                        this.loading = false;
                        this.loadMoreFromFirstPage = false;
                    });
                }
            }, (err) => {
                toast.error(err.message, 1000);
            });
        },
        // 点击查询请求
        handleDateQuery() {
            if (!this.startDate) {
                toast.warn('请选择开始日期');
                return;
            }
            if (!this.endDate) {
                toast.warn('请选择结束日期');
                return;
            }
            // 点击查询的时候将搜索框内容置空，代表当前只是按时间进行筛选
            this.idInput = '';
            this.poiId = -1;
            this.searchId = -1;
            this.changeDateFrom(dayjs(this.startDate).format('YYYY-MM-DD'));
            this.changeDateTo(dayjs(this.endDate).format('YYYY-MM-DD'));
            this.activeQueryButton = QueryButtonType.Date;
            this.handleQueryClick(1);
        },
        handleQueryClick(queryPageNo = 1) {
            // 校验是否有已选时间，必填参数
            if (!this.dateFrom) {
                toast.warn('请选择开始日期');
                return;
            }
            if (!this.dateTo) {
                toast.warn('请选择结束日期');
                return;
            }
            if (this.loading) return;
            this.loading = true;
            const params = {
                settleId: this.searchId === -1 ? null : this.searchId,
                partnerId: this.poiId === -1 ? null : this.poiId,
                pageNum: queryPageNo,
            };
            this.fetchAmountList(params)
                .finally(() => {
                    this.loading = false;
                });
        },
        // 点击查询结算ID请求
        handleSearchSettleId() {
            const settleId = parseInt(this.idInput, 10);

            if (!settleId) {
                toast.error('请输入正确的结算设置 ID');
                return;
            }

            this.idInput = `${settleId}`;
            this.searchId = settleId;
            this.poiId = -1;
            this.activeQueryButton = QueryButtonType.Settle;

            this.handleQueryClick(1);
        },
        // 点击查询门店ID请求
        handleSearchWmPoiId() {
            const id = parseInt(this.idInput, 10);

            if (!id) {
                toast.error('请输入正确的门店 ID');
                return;
            }

            this.idInput = `${id}`;
            this.poiId = id;
            this.searchId = -1;
            this.activeQueryButton = QueryButtonType.Poi;

            this.handleQueryClick(1);
        },

        handleCheckAll() {
            this.changeListBySelectAll();
        },

        handleCheck(index) {
            this.changeListBySelect(index);
        },

        handleCheckAllPoiRow() {
            this.changePoiListBySelectAll();
        },
        handleCheckPoiRow(index) {
            this.changePoiListBySelect(index);
        },

        handleSelectPoi(index) {
            this.selectPoiAmountIndex = index;
            this.displayPoiSelect = true;
        },

        handleSelectDetail(amount) {
            this.adAmount = amount;
            this.displayAdDetail = true;
        },

        loadMore() {
            // TODO: 重新实现
            this.activeQueryButton = QueryButtonType.LoadMore;
            // this.handleQueryClick(this.pageNo + 1);
            if (this.loadMoreFromFirstPage) {
                this.handleQuery(1);
            } else {
                this.handleQuery(this.pageNo + 1);
            }
        },

        handlePrevClick() {
            this.$router.go(-1);
        },
        handleNextClick() {
            const isPublicInvoice = this.isPublicActivityFee || this.isNatural;
            // eslint-disable-next-line camelcase
            const isSufficeAmount = this.totalSelectedAmount * 100 >= MIN_PUBLIC_ACTIVITY_AMOUNT_Milli;
            // 青山公益发票至少是50元
            if (isPublicInvoice && !isSufficeAmount) {
                toast.error(
                    // eslint-disable-next-line camelcase
                    `申请金额必须大于等于${MIN_PUBLIC_ACTIVITY_AMOUNT_Milli / 10000}元，请重新提交`,
                );
                return;
            }
            // 计算下一步分销方公司展示金额
            this.getOwnerCompanyList({ ownership: this.ownership, poiRelatedToTitle: this.poiRelatedToTitle, queryType: this.queryFilterDimension });
            // 初始化分配金额的数据
            this.initSplitContent(this.checkedAmountList);
            // 计算是否存在销方公司金额为负数
            this.changeHasOwnershipNegative();
            // 计算金额大于0的销方公司列表，最后一步提交前的预览需要使用
            this.changeSubmitComList();

            this.$router.push({ name: 'amount-split' });
        },
        handleExport() {
            let dateFrom = '';
            let dateTo = '';

            // 参数检查
            if (this.queryTimeDimension === 'byMonth' && !(!!this.queryMonthRange[0] && !!this.queryMonthRange[1])) {
                toast.warn('请选择时间范围');
                return;
            }
            if (this.queryTimeDimension === 'byDate' && !(!!this.queryDateRange[0] && !!this.queryDateRange[1])) {
                toast.warn('请选择时间范围');
                return;
            }

            dateFrom = this.queryTimeDimension === 'byMonth' ? dayjs(this.queryMonthRange[0]).startOf('month').format('YYYY-MM-DD') : dayjs(this.queryDateRange[0]).format('YYYY-MM-DD');
            dateTo = this.queryTimeDimension === 'byMonth' ? dayjs(this.queryMonthRange[1]).endOf('month').format('YYYY-MM-DD') : dayjs(this.queryDateRange[1]).format('YYYY-MM-DD');


            if (this.inputIdListStr.trim() !== '' && this.inputIdListStr.trim().split(',').some(id => !id.match(/^\d+$/))) {
                toast.error(`${this.queryFilterDimension === 'byPoiId' ? '门店ID' : '结算ID'}仅支持正整数，以英文逗号分割，请检查输入`, 1000);
                return;
            }
            // 先校验 id 列表是否合法
            this.checkIdValidity(this.inputIdListStr.trim().split(',')).then((formatedResult) => {
                const invalidIdList = formatedResult.filter(item => item.value !== 0).map(item => item.id);
                if (invalidIdList.length > 0) {
                    toast.error(`${invalidIdList.length}个无效ID: ${invalidIdList.join(',')}。请确认后重新输入。`, 2000);
                    return;
                }
                const params = {
                    contractId: this.contractId,
                    baseFeeType: this.baseFeeTypeItem.baseFeeType,
                    // invoiceType: 1,
                    beginDate: dateFrom,
                    endDate: dateTo,
                    billType: 98,
                };
                if (this.queryFilterDimension === 'bySettleId') {
                    params.settleIds = formatedResult.map(item => item.id).join(',') || -1;
                } else {
                    params.wmPoiIds = formatedResult.map(item => item.id).join(',') || -1;
                }
                this.exporting = true;
                request.get('/finance/pc/api/billDownload/createInvoiceExportTask', { params })
                    .then((res) => {
                        this.exporting = false;

                        const { code, msg } = res.data;

                        if (code !== 0) {
                            toast.error(msg);
                            throw new Error(msg);
                        }


                        toast.success('导出成功，请到下载专区下载');
                        // window.location = '/finance/pc/download?type=account-running';
                    })
                    .catch((err) => {
                        this.exporting = false;
                        toastError(err);
                    });
            });
        },
        // 修改日期检查方法
        checkDateRangeValid(dateRange, type) {
            // 对于公益发票和乡村儿童操场项目，检查是否跨年
            if ((this.isPublicActivityFee || this.isNatural || this.isChildren) && dateRange[0] && dateRange[1]) {
                const startYear = dayjs(dateRange[0]).year();
                const endYear = dayjs(dateRange[1]).year();
                if (startYear !== endYear) {
                    toast.warn('开票的起止时间必须在同一年，请重新选择');
                    // 重置结束时间，保留开始时间
                    this[type] = [dateRange[0], null];
                }
            }
        },
    },
};
</script>

<style lang="scss" module>
.module {
  margin: 30px 40px;
}

.tooltip{
    margin-bottom: 7px;
    margin-left: 3px;
    cursor: pointer;
}
.h3 {
  margin-top: 0;
  margin-bottom: 20px;
  font-size: 20px;
}
.hidden {
  display: none;
}
.companyWrapper {
  margin: 20px 0 10px;
  display: flex;
  justify-content: space-between;
}
.totalWrapper {
  height: 36px;
  line-height: 36px;
}
.select {
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  .companylabel {
    display: inline-block;
    height: 36px;
    line-height: 36px;
  }
  .roo-select {
    width: 150px;
  }
}
.action-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-left: 20px;
  margin: 20px 0;
}

.checkbox {
  color: #858692;
}

.label {
  display: inline-block;
  width: 70px;
  margin-right: 10px;
  line-height: 36px;
  text-align: right;
  vertical-align: middle;
}

.input {
  display: inline-block;
  width: 200px;
  vertical-align: middle;
}

.radio-group {
  display: inline-block;
  line-height: 1;
  vertical-align: middle;
}

.mr10 {
  margin-right: 8px;
}
.mb10 {
  margin-bottom: 10px;
}

.alert {
  margin: 20px 0;

  &:global(.roo-alert) i {
    left: 10px;
  }

  ul {
    list-style: decimal;
  }

  li {
    padding-left: 20px;
  }
}

.pagination {
  margin-top: 20px;
  text-align: center;
}
.btn {
  min-width: 240px;
}
.done {
  font-size: 14px;
  color: #a0a0a0;
}

.summary {
  display: inline-block;
  margin-right: 16px;
  vertical-align: middle;
}
.tip1 {
  font-size: 14px;
  line-height: 20px;
}
.amount {
  color: #f89800;
}
.tip2 {
  font-size: 12px;
  color: #858692;
  line-height: 16px;
}

.mr16 {
  margin-right: 16px;
}

.query-row {
  display: flex;
  align-items: center;

  :global(.roo-btn-primary) {
    margin-left: auto;
  }

  .show-type-container {
    display: flex;
    :global(.roo-button) {
      border-radius: 0;
    }
  }
}
.query-container {
  display: flex;
  align-items: center;
  min-width: 360px;
  height: 36px;

  :global(.select-input-wrapper) {
    width: 300px;
  }

  :global(.roo-input-group) {
    width: 300px;
  }

  .query-label {
    width: 85px;
    margin-right: 20px;
  }

  &:nth-child(2) {
    .query-label {
      text-align: right;
    }
  }
}
.verify-input-container {
    width: 380px;
    height: 380px;
    position: relative;
    overflow: hidden;

    textarea {
        width: 100%;
        height: 100%;
        resize: none;
    }

    .verify-result {
        position: absolute;
        top: 1px;
        left: 1px;
        width: 378px;
        height: 378px;
        overflow: auto;
        z-index: 100;
        background-color: #FFF;
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        padding: 4px 10px;

        :global(.roo-tag) {
            margin-bottom: 4px;
        }
    }
}
</style>
