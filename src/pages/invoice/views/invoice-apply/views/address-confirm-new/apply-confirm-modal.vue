<template>
    <roo-modal
        :class="$style.modal"
        :value="value"
        :backdrop="submitting ? 'static' : true"
        size="large"
        @input="$emit('input', $event)"
        @hidden="$emit('hidden')"
    >
        <h3>请核对开票信息</h3>
        <h4 :class="$style.h4">
            发票信息
        </h4>

        <ul :class="$style.infoList">
            <li
                v-for="item in baseInfo"
                :key="item.key"
            >
                <div :class="$style.label">
                    {{ item.label }}
                </div>
                <div :class="$style.text">
                    {{ item.text }}
                </div>
            </li>
        </ul>
        <div>
            <div
                v-for="c in splitContent"
                :key="c.companyName"
                :class="$style.com"
            >
                <p :class="$style.comName">
                    销方公司：{{ c.companyName }}
                </p>
                <div v-for="i in showCoupons.filter(i => i.ownerShip === c.ownerShip)" :key="i.qualificationId">
                    <div :class="$style.coupon_title">
                        <span style="font-size: 14px;color: #222222;">
                            发票抬头：{{ i.qualificationName }}
                        </span>
                        <!-- <span>
                            ￥{{ i.totalMoney | formatCent }}
                        </span> -->
                    </div>
                    <div>
                        <div
                            v-for="({ amountCent }, idx) in i.invoiceList"
                            :key="idx"
                            :class="$style.invoiceCart"
                        >
                            <span>
                                发票
                                {{ idx + 1 }}
                            </span>
                            <span style="margin-left: 17px;">
                                ￥{{ amountCent | formatCent }}
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div style="height: 1px;width: 546px;background:#e1e1e1 ;margin: 20px auto;"></div>
        <h4 :class="$style.h4" style="margin-top: 20px;">
            收票信息
        </h4>

        <ul :class="$style.infoList">
            <li
                v-for="item in recoverInvoice"
                :key="item.key"
            >
                <div :class="$style.label">
                    {{ item.label }}
                </div>
                <div :class="$style.text">
                    {{ item.text }}
                </div>
            </li>
        </ul>
        <template slot="footer">
            <roo-button
                :disabled="submitting"
                type="hollow"
                @click="$emit('input', false)"
            >
                取消
            </roo-button>
            <roo-button
                :loading="submitting"
                @click="preSubmitConfirm"
            >
                确认提交
            </roo-button>
        </template>
    </roo-modal>
</template>

<script>
import { mapState, mapGetters, mapActions } from 'vuex';
import { confirm, alert } from '@roo/roo-vue';

/* eslint-disable import/extensions, import/no-unresolved */
// import Coupon from '$components/coupon';


import toastError from '$lib/toast-error';
import { formatCent, formatMilli } from '$lib/filters';
import { InvoiceNameEnum, InvoiceTypeEnum } from '$config/enum';
/* eslint-enable import/extensions, import/no-unresolved */

import { noticeText, secondConfirmText } from './config';

export default {
    name: 'ApplyConfirmModal',
    filters: {
        formatCent,
        formatMilli,
    },
    props: {
        value: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {
            noticeText,
        };
    },
    computed: {
        ...mapState('config', [
            'feeTypeList',
            'contractId',
        ]),
        ...mapState('apply', [
            'baseFeeTypeItem',
            'qualifications',
            'invoiceType',
            'remark',
            'splitInfos',
            'splitAmount',
            'submitting',
            'submitComList',
            'splitContent',
        ]),
        ...mapState('config', [
            'isShudianHit',
        ]),
        ...mapState('amountSelectNew', [
            'dateFrom',
            'dateTo',
        ]),
        ...mapState('sendInfo', [
            'receiver',
            'provinceCode',
            'expressProvince',
            'cityCode',
            'expressCity',
            'districtCode',
            'expressDistrict',
            'address',
            'phoneNo',
            'telNo',
            'receiverEmail',
        ]),
        ...mapGetters('amountSelect', [
            'checkedAmountList',
            'checkedMoneyMilli',
        ]),
        ...mapGetters('apply', [
            'isPublicActivityInvoice',
            'isElectronInvoice',
            'isPublicActivityFee',
            'isChildren',
            'isNatural',
        ]),
        getConfirmText() {
            const typeMap = {
                isPublicActivityFee: 'NATURE_PROTECT',
                isChildren: 'PLAYGROUND',
                isNatural: 'NATURE_CLEAN',
            };

            const type = Object.keys(typeMap).find(key => this[key]);
            return type ? secondConfirmText[typeMap[type]] : '';
        },
        companyStr() {
            return this.submitComList.join(',');
        },
        showCoupons() {
            const arr = [];
            const hashQua = {};
            this.splitInfos.forEach(info => {
                // 筛选同一个开销方，同一个资质下的发票列表
                const curIndex = hashQua[`${info.ownerShip}-${info.qualificationId}`];
                if (curIndex === undefined) {
                    hashQua[`${info.ownerShip}-${info.qualificationId}`] = arr.length;
                    const quaItem = {
                        qualificationId: info.qualificationId,
                        qualificationName: info.qualificationName,
                        totalMoney: info.amountCent,
                        ownerShip: info.ownerShip,
                        invoiceList: [info],
                    };
                    arr.push(quaItem);
                } else {
                    arr[curIndex].invoiceList.push(info);
                    arr[curIndex].totalMoney += info.amountCent;
                }
            });
            return arr;
        },
        // 发票信息
        baseInfo() {
            const { baseFeeTypeItem, invoiceType } = this;

            const arr = [{
                key: 'invoice-fee-type',
                label: '开票项目',
                text: baseFeeTypeItem && baseFeeTypeItem.baseFeeTypeName,
            }, {
                key: 'item-name',
                label: '类目',
                text: baseFeeTypeItem && baseFeeTypeItem.productionName,
            }, {
                key: 'invoice-type',
                label: '票种',
                // eslint-disable-next-line no-nested-ternary
                text: this.isShudianHit ? (invoiceType === InvoiceTypeEnum.VATSpecial ? '专票' : '普票') : InvoiceNameEnum[invoiceType],
            },
            // {
            //     key: 'contract-b-name',
            //     label: '所属公司',
            //     text: this.companyStr,
            // }
            ];

            // arr.push({
            //     key: 'spec',
            //     label: '规格型号',
            //     text: baseFeeTypeItem && baseFeeTypeItem.model,
            // }, {
            //     key: 'unit',
            //     label: '单位',
            //     text: baseFeeTypeItem && baseFeeTypeItem.unit,
            // });
            // arr.push({
            //     key: 'amount',
            //     label: '数量',
            //     text: baseFeeTypeItem && baseFeeTypeItem.productionCount,
            // });

            if (this.dateFrom && this.dateTo) {
                arr.unshift({
                    key: 'date-from',
                    label: '本次开票周期',
                    text: `${this.dateFrom} - ${this.dateTo}`,
                });
            }

            if (!this.isPublicActivityInvoice && this.remark !== '') {
                arr.push({
                    key: 'remark',
                    label: '票面备注',
                    text: this.remark,
                });
            }

            return arr;
        },
        // 收票信息
        recoverInvoice() {
            const recoverArr = [];
            if (this.isElectronInvoice) { // 电子发票
                recoverArr.push({
                    key: 'receiver-email',
                    label: '电子邮箱',
                    text: this.receiverEmail,
                });
            } else {
                recoverArr.push({
                    key: 'receiver',
                    label: '收件人',
                    text: this.receiver,
                },
                this.isShudianHit ? {
                    key: 'receiver-email',
                    label: '电子邮箱',
                    text: this.receiverEmail,
                } : null,
                {
                    key: 'address',
                    label: '详细地址',
                    text: `${this.expressProvince} ${this.expressCity} ${this.expressDistrict} ${this.address}`,
                }, {
                    key: 'phoneNo',
                    label: '手机',
                    text: this.phoneNo,
                }, {
                    key: 'telNo',
                    label: '固定电话',
                    text: this.telNo || '无',
                });
            }
            return recoverArr.filter(item => !!item);
        },
    },
    methods: {
        ...mapActions('apply', [
            'newSubmitApply',
        ]),
        preSubmitConfirm() {
            if (!this.isPublicActivityInvoice) return this.handleSubmitClick();
            return confirm(this.getConfirmText, (yes) => {
                if (yes) {
                    this.handleSubmitClick();
                }
            });
        },
        handleSubmitClick() {
            this.newSubmitApply()
                .then((res) => {
                    this.$emit('input', false);
                    if (res) {
                        alert('发票申请已提交，可在“开票历史”中查看详情', () => {
                            this.$store.commit('qualificationSelect/reset');
                            this.$store.commit('amountSelectNew/reset');
                            this.$store.commit('sendInfo/reset');
                            this.$store.commit('apply/reset');
                            this.$router.push({ name: 'invoice-history' });
                        });
                    }
                })
                .catch(toastError);
        },
    },
};
</script>

<style lang="scss" module>
.modal {
    :global(.roo-modal-content) {
        max-height: 700px;
        overflow: auto;
    }
}

.h4 {
    margin-top: 0;
    margin-bottom: 20px;
    font-size: 20px;
}

.info-list {
    list-style: none;

    li {
        display: flex;
        margin-bottom: 10px;
    }
}

.label {
    width: 100px;
    flex-shrink: 0;
    margin-right: 20px;
    color: #858692;
    text-align: right;
}

.text {
    flex: 1;
    color: #3F4156;
}

.icon:global(.roo-icon) {
    height: auto;
}

.sub-list {
    list-style: none;
    margin: 0;
    padding: 0;

    li + li {
        margin-top: 20px;
    }
}

.invoice-name {
    font-size: 14px;
    color: #3F4156;
    line-height: 22px;
    font-weight: bold;
}

.invoice-amount {
    float: right;
}

.coupon {
    margin: 20px 0;
}

.coupon_title {
    display: flex;
    justify-content: space-between;
    line-height: 45px;
}

.tooltip {
    font-size: 12px;
    color: #666;
    border: solid #e1e1e1 1px;
    margin-bottom: 10px;
    padding: 5px 18px;
}
.com{
  width: 546px;
  background: #F7F8FA;
  padding: 12px;
  margin: 10px auto;
}
.comName{
  color: #222222;
  font-family: PingFang SC;
  font-weight: 500;
  font-size: 14px;
  line-height: 17px;
  letter-spacing: 0px;
  text-align: left;
  text-decoration: NONE;
}
.invoiceCart{
  width: 522px;
  background: #ffffff;
  padding: 10px;
  margin-top: 10px;
}
.invoiceCart:first-child{
  margin-top: 0px;
}
</style>
