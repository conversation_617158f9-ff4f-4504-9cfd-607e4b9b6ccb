<template>
    <div>
        <div :class="$style.module">
            <h3 :class="$style.h3">填写地址</h3>

            <roo-form
                ref="form"
                :class="$style.form"
                :model="{ email, remark, receiver,phoneNo,provinceCode, }"
            >
                <roo-form-item
                    v-if="isElectronInvoice"
                    key="email"
                    :rules="rules.email"
                    prop="email"
                    label="电子邮箱"
                    required
                >
                    <roo-input
                        v-model="email"
                        :class="$style.input"
                        placeholder="邮箱地址"
                    />
                </roo-form-item>
                <template v-else>
                    <roo-form-item
                        key="receiver"
                        :rules="rules.receiver"
                        label="收件人"
                        prop="receiver"
                        required
                    >
                        <roo-input
                            v-model="receiver"
                            :class="$style.input"
                            placeholder="请填写真实姓名"
                        />
                    </roo-form-item>
                    <roo-form-item
                        v-if="isShudianHit"
                        key="email"
                        :rules="rules.email"
                        prop="email"
                        label="电子邮箱"
                        required
                    >
                        <roo-input
                            v-model="email"
                            :class="$style.input"
                            placeholder="邮箱地址"
                        />
                    </roo-form-item>
                    <roo-form-item
                        key="address"
                        label="详细地址"
                        prop="provinceCode"
                    >
                        <roo-select
                            v-model="provinceCode"
                            :class="[$style.input, $style.mb14]"
                            :options="provinceOptions"
                            placeholder="省份"
                        />
                        <roo-select
                            v-model="cityCode"
                            :class="[$style.input, $style.mb14]"
                            :options="cityOptions"
                            placeholder="城市"
                        />
                        <roo-select
                            v-model="districtCode"
                            :class="[$style.input, $style.mb14]"
                            :options="districtOptions"
                            placeholder="县 / 地区"
                        />
                        <roo-input
                            v-model="address"
                            :class="$style.input"
                            placeholder="详细地址"
                        />
                    </roo-form-item>

                    <roo-form-item
                        key="phoneNo"
                        :rules="rules.phoneNo"
                        label="手机"
                        prop="phoneNo"
                    >
                        <roo-input
                            v-model="phoneNo"
                            :class="$style.input"
                            placeholder="手机号码"
                        />
                    </roo-form-item>

                    <roo-form-item
                        key="telNo"
                        label="固话"
                    >
                        <roo-input
                            v-model="telNo"
                            :class="$style.input"
                            placeholder="固定电话"
                        />
                    </roo-form-item>
                </template>

                <!-- <roo-form-item
                    v-if="!isPublicActivityInvoice"
                    key="remark"
                    :rules="rules.remark"
                    prop="remark"
                    label="票面备注"
                >
                    <roo-input
                        v-model="remark"
                        :class="[$style.input, $style.textarea]"
                        type="textarea"
                        placeholder="该备注栏信息会填写至发票右下角备注栏，请谨慎填写"
                        noresize
                    />
                </roo-form-item> -->
            </roo-form>
        </div>

        <div class="footer-nav">
            <roo-button
                :class="$style.btn"
                :disabled="submitting"
                type="brand-hollow"
                @click="$router.go(-1)"
            >
                上一步
            </roo-button>
            <roo-button
                :loading="submitting"
                type="brand"
                @click="handleSubmitClick"
            >
                提交申请
            </roo-button>
        </div>

        <apply-confirm-modal
            v-model="displayConfirmModal"
        />

        <roo-loading
            :show="loading"
            fullscreen
            backdrop
            lock
        />
    </div>
</template>

<script>
import {
    mapState,
    mapGetters,
    mapMutations,
    mapActions,
} from 'vuex';
import { toast } from '@roo/roo-vue';

/* eslint-disable import/extensions, import/no-unresolved */
import toastError from '$lib/toast-error';
import { InvoiceTypeEnum } from '$config/enum';

/* eslint-enable import/extensions, import/no-unresolved */

import ApplyConfirmModal from './apply-confirm-modal';
import { generateRules } from './config';


const getOptions = (arr) => {
    if (!arr) return [];
    if (arr.length > 0) {
        return arr.map(({ code, name }) => ({ value: code, label: name }));
    }
    return [{ value: -1, label: '加载中', disabled: true }];
};

export default {
    name: 'AddressConfirmNew',
    components: {
        ApplyConfirmModal,
    },
    data() {
        return {
            displayConfirmModal: false,
            InvoiceTypeEnum,
            rules: generateRules(this),
        };
    },
    computed: {
        ...mapState('apply', ['baseFeeTypeItem', 'qualifications', 'invoiceType', 'submitting']),
        ...mapState('config', ['isShudianHit']),
        ...mapState('region', ['regions']),
        ...mapState('sendInfo', ['loading']),
        ...mapState('amountSelectNew', [
            'totalSelectedAmount',
        ]),
        ...mapGetters('apply', [
            'isPublicActivityInvoice',
            'isElectronInvoice',
        ]),
        receiver: {
            get() {
                return this.$store.state.sendInfo.receiver;
            },
            set(val) {
                this.changeReceiver(val);
            },
        },

        provinceCode: {
            get() {
                return this.$store.state.sendInfo.provinceCode;
            },
            set(val) {
                this.changeCity({});
                this.changeDistrict({});

                const province = this.provinceList.find(item => item.code === val);

                if (province) {
                    this.changeProvince({
                        code: province.code,
                        name: province.name,
                    });
                    if (province.children.length === 0) {
                        this.loadChildRegions([province.code])
                            .catch(toastError);
                    }
                }
            },
        },

        cityCode: {
            get() {
                return this.$store.state.sendInfo.cityCode;
            },
            set(val) {
                this.changeDistrict({});

                const city = this.cityList.find(item => item.code === val);

                if (city) {
                    this.changeCity({
                        code: city.code,
                        name: city.name,
                    });
                    if (city.children.length === 0) {
                        this.loadChildRegions([this.provinceCode, city.code])
                            .catch(toastError);
                    }
                }
            },
        },

        districtCode: {
            get() {
                return this.$store.state.sendInfo.districtCode;
            },
            set(val) {
                const district = this.districtList.find(item => item.code === val);

                if (district) {
                    this.changeDistrict({
                        code: district.code,
                        name: district.name,
                    });
                }
            },
        },

        address: {
            get() {
                return this.$store.state.sendInfo.address;
            },
            set(val) {
                this.changeAddress(val);
            },
        },

        phoneNo: {
            get() {
                return this.$store.state.sendInfo.phoneNo;
            },
            set(val) {
                this.changePhoneNo(val);
            },
        },

        telNo: {
            get() {
                return this.$store.state.sendInfo.telNo;
            },
            set(val) {
                this.changeTelNo(val);
            },
        },

        email: {
            get() {
                return this.$store.state.sendInfo.receiverEmail;
            },
            set(val) {
                this.changeEmail(val);
            },
        },

        remark: {
            get() {
                return this.$store.state.apply.remark;
            },
            set(val) {
                this.changeRemark(val);
            },
        },

        provinceList() {
            return this.regions;
        },
        provinceOptions() {
            return getOptions(this.provinceList);
        },

        cityList() {
            const { provinceList, provinceCode } = this;
            if (!provinceList) return null;

            for (let i = 0; i < provinceList.length; ++i) {
                const item = provinceList[i];
                if (item.code === provinceCode) {
                    return item.children;
                }
            }

            return null;
        },
        cityOptions() {
            return getOptions(this.cityList);
        },

        districtList() {
            const { cityList, cityCode } = this;
            if (!cityList) return null;

            for (let i = 0; i < cityList.length; ++i) {
                const item = cityList[i];
                if (item.code === cityCode) {
                    return item.children;
                }
            }

            return null;
        },
        districtOptions() {
            return getOptions(this.districtList);
        },
    },
    mounted() {
        if (!(this.baseFeeTypeItem && this.qualifications.length > 0)) {
            this.$router.replace({ name: 'qualification-select' });
            return;
        }

        this.fetchAddress()
            .then(() => {
                const { provinceCode, cityCode } = this;
                this.loadRootRegions()
                    .then(() => {
                        if (provinceCode) {
                            this.loadChildRegions([provinceCode])
                                .then(() => {
                                    if (cityCode) {
                                        this.loadChildRegions([provinceCode, cityCode]);
                                    }
                                });
                        }
                    });
            })
            .catch(toastError);
    },
    methods: {
        ...mapMutations('sendInfo', [
            'changeReceiver',
            'changeProvince',
            'changeCity',
            'changeDistrict',
            'changeAddress',
            'changePhoneNo',
            'changeTelNo',
            'changeEmail',
        ]),
        ...mapMutations('apply', [
            'changeRemark',
            'submitApply',
        ]),
        ...mapActions('sendInfo', ['fetchAddress']),
        ...mapActions('region', [
            'loadRootRegions',
            'loadChildRegions',
        ]),

        handleSubmitClick() {
            this.$refs.form.validate(this.handleValidate);
        },

        handleValidate(pass) {
            if (!pass) return;
            const { totalSelectedAmount } = this;
            if (totalSelectedAmount <= 0) {
                toast.warn('开票金额需大于 0 元');
                return;
            }
            this.displayConfirmModal = true;
        },
    },
};
</script>

<style lang="scss" module>
    .module {
        margin: 30px 40px;
    }

    .h3 {
        margin-top: 0;
        margin-bottom: 20px;
        font-size: 20px;
    }

    .form {
        width: 750px;
    }

    .input {
        width: 300px;
    }

    .textarea {
        height: 120px !important;
    }

    .mb14 {
        margin-bottom: 14px;
    }

    .btn {
        width: 90px;
        margin-right: 10px;
    }
</style>
