<template>
    <div>
        <div :class="$style.module">
            <!-- 青山环保项目 -->
            <roo-alert
                v-if="isPublicActivityFee"
                :class="$style.alert"
                type="warning"
                icon="exclamation-circle"
            >
                <ul>
                    <li>为了让贵方的善款能发挥最大的公益价值，节省公益项目成本，累计捐赠超过 50 元人民币可在本系统上直接申请开具公益捐赠票据。请您尽量在捐赠当年申请捐赠票据，申请去年票据请您在每年5月31日前尽早提交。</li>
                    <li>如有特别需求，请拨打中华环境保护基金会票据咨询电话：010-67173592 。</li>
                </ul>
            </roo-alert>
            <!-- 零废星球洁净自然 -->
            <roo-alert
                v-if="isNatural"
                :class="$style.alert"
                type="warning"
                icon="exclamation-circle"
            >
                <ul>
                    <li>为了让贵方的善款能发挥最大的公益价值，节省公益项目成本，累计捐赠超过 50 元人民币可在本系统上直接申请开具公益捐赠票据。请您尽量在捐赠当年申请捐赠票据，每年5月31日前可申请去年发票。</li>
                    <li>如有特别需求，请拨打北京市企业家环保基金会咨询电话：010-57505155。</li>
                </ul>
            </roo-alert>
            <!-- 乡村儿童操场项目 -->
            <roo-alert
                v-if="isChildren"
                :class="$style.alert"
                type="warning"
                icon="exclamation-circle"
            >
                <ul>
                    <li>依据深圳壹基金基金会有关财务管理制度与捐赠票据管理制度的规定，请您尽量在捐赠当年申请捐赠票据。</li>
                    <li>如有特别需求，请拨打深圳壹基金基金会咨询电话：400-6902700。</li>
                </ul>
            </roo-alert>

            <h3 :class="$style.h3">
                选择开票金额
            </h3>

            <!--按结算设置汇总-->
            <template v-if="accountSource == SOURCE.WAIMAI">
                <div v-if="baseFeeTypeItem && baseFeeTypeItem.amountGatherType === 1">
                    <div :class="$style.mb10">
                        <roo-date-time-picker
                            v-model="startDate"
                            :class="$style.input"
                            :min-date="minStartDate"
                            :max-date="maxStartDate"
                            clearable
                        />
                        -
                        <roo-date-time-picker
                            v-model="endDate"
                            :class="$style.input"
                            :min-date="minEndDate"
                            :max-date="maxEndDate"
                            clearable
                        />
                        <roo-button
                            :disabled="loading"
                            :loading="loadingStatus(LoadingType.Date)"
                            @click="handleDateQuery"
                        >
                            查询
                        </roo-button>
                    </div>
                    <div :class="$style.mb10">
                        <roo-input
                            v-model="idInput"
                            :class="[$style.input, $style.mr10]"
                            placeholder="请输入结算 ID 或商家 ID"
                            @change="handleInputChange"
                        />
                        <roo-button
                            :class="$style.mr10"
                            :disabled="loading"
                            :loading="loadingStatus(LoadingType.Settle)"
                            @click="handleSearchSettleId"
                        >
                            搜索结算 ID
                        </roo-button>
                        <roo-button
                            :disabled="loading"
                            :loading="loadingStatus(LoadingType.Poi)"
                            @click="handleSearchWmPoiId"
                        >
                            搜索商家 ID
                        </roo-button>
                    </div>
                </div>
                <div
                    :class="$style.companyWrapper"
                >
                    <div
                        :class="$style.totalWrapper"
                    >
                        共选中 {{ checkedAmountCount }}/{{ total }} 条
                    </div>
                    <div
                        :class="$style.select"
                    >
                        <span
                            :class="$style.companylabel"
                        >
                            选择开销方
                            <span>
                                <roo-icon
                                    name="question2"
                                    size="18px"
                                />
                                <roo-tooltip
                                    placement="top-right"
                                >
                                    <slot>
                                        <div>{{ compText }}</div>
                                    </slot>
                                </roo-tooltip>
                            </span>
                            ：
                        </span>
                        <roo-select
                            :options="companyOptionList"
                            :value="ownership"
                            @change="handleSelectCompany"
                        />
                    </div>
                </div>
            </template>

            <!-- 按照结算设置选择金额 -->
            <amount-table
                :table-list="settleAmountList"
                :loading="loading"
                @select-poi="handleSelectPoi"
                @check="handleCheck"
                @check-all="handleCheckAll"
            />
            <div
                v-if="total > 0"
                :class="$style.pagination"
            >
                <roo-button
                    v-if="settleAmountList.length < total"
                    key="next"
                    :class="$style.btn"
                    :disabled="loading"
                    :loading="loadingStatus(LoadingType.LoadMore)"
                    size="mini"
                    type="hollow"
                    @click="loadMore"
                >
                    {{ loading ? '加载中' : `查看更多 第 ${nextPageText} 条 (共 ${total} 条)` }}
                </roo-button>
                <span
                    v-else
                    key="done"
                    :class="$style.done"
                >
                    已加载完毕
                </span>
            </div>
        </div>

        <div class="footer-nav">
            <div :class="$style.summary">
                <div
                    v-if="checkedAmountCount > 0"
                    :class="$style.tip1"
                >
                    共选择 <span class="amount">{{ totalSelectedAmount | formatCent }}</span> 元
                </div>
                <div
                    v-else
                    :class="$style.tip1"
                >
                    请选择金额
                </div>
            </div>
            <roo-button
                :class="$style.mr16"
                type="brand-hollow"
                @click="handlePrevClick"
            >
                上一步
            </roo-button>
            <roo-button
                :disabled="totalSelectedAmount <= 0"
                type="brand"
                @click="handleNextClick"
            >
                下一步
            </roo-button>
        </div>
        <poi-modal
            v-model="displayPoiSelect"
            :amount-index="selectPoiAmountIndex"
            :poi-id="poiId"
        />
    </div>
</template>

<script>
import dayjs from 'dayjs';
import {
    mapState,
    mapGetters,
    mapMutations,
    mapActions,
} from 'vuex';
import { toast } from '@roo/roo-vue';

/* eslint-disable import/extensions, import/no-unresolved */
import { getCookieValue } from '$lib/utils';
import { QueryButtonType } from '$config/enum';
import SOURCE from '$lib/sourceEnum';
import { formatCent } from '$lib/filters';
// eslint-disable-next-line camelcase
import { MIN_PUBLIC_ACTIVITY_AMOUNT_Milli } from '$config/constant';
/* eslint-enable import/extensions, import/no-unresolved */

import AmountTable from './components/amount-table';
import PoiModal from './components/poi-modal';

export default {
    name: 'AmountSelectNew',
    components: {
        AmountTable,
        PoiModal,
    },
    filters: {
        formatCent,
    },
    data() {
        return {
            startDate: null,
            endDate: null,
            loading: false,
            activeQueryButton: QueryButtonType.Date,
            // 查询ID框中内容
            idInput: '',
            // 查询的结算ID
            searchId: 0,
            // 查询的门店ID
            poiId: -1,

            displayPoiSelect: false,
            selectPoiAmountIndex: -1,

            displayAdDetail: false,
            adAmount: null,
            showAlert: false,
            accountSource: getCookieValue('source') || SOURCE.WAIMAI,
            SOURCE,
            LoadingType: null,
        };
    },
    computed: {
        // 开销方文本，当为平台佣金和配送服务费时，显示另外的文本
        compText() {
            if (this.baseFeeTypeItem && [4, 21].indexOf(this.baseFeeTypeItem.baseFeeType) > -1) {
                return '依据合同签署情况，本次申请可能收到多个公司主体开具的发票';
            } else if (this.baseFeeTypeItem && [15, 38, 39].indexOf(this.baseFeeTypeItem.baseFeeType) > -1) {
                return '根据您参与的公益项目，公益捐赠票据由对应的公益机构开具';
            } else {
                return '门店推广技术服务由北京三快在线科技有限公司、上海三快智送科技有限公司联合提供，依据您签署的推广协议，可能收到上述公司分别开具的发票';
            }
        },
        ...mapState('config', ['contractId', 'feeTypeList', 'isSinglePoi']),
        ...mapState('apply', [
            'baseFeeTypeItem',
            'qualifications',
            'companySelected',
            'ownership',
            'splitContent',
        ]),
        ...mapState('amountSelectNew', [
            'dateFrom',
            'dateTo',
            'total',
            'pageNo',
            'pageSize',
            'settleAmountList',
            'totalSelectedAmount',
            'checkedAmountList',
        ]),
        ...mapState('invoiceTitle', ['poiRelatedToTitle']),

        ...mapGetters('apply', ['isPublicActivityFee', 'isChildren', 'isNatural']),
        ...mapGetters('amountSelectNew', [
            'currentCount',
            'checkedAmountCount',
        ]),
        companyList() {
            const { sellerCompanyList = [] } = this.baseFeeTypeItem || {};
            return sellerCompanyList;
        },
        companyOptionList() {
            if (!this.companyList.length) return [];
            const options = this.companyList.map((item) => ({
                ...item,
                value: item.ownerShip,
                label: item.companyName,
            }));
            return [
                {
                    value: -1,
                    label: '全部',
                },
                ...options,
            ];
        },
        minStartDate() {
            let yearStart = dayjs().startOf('year');
            // 青山环保、零废星球，项目开票时间限制：不能跨年
            if (this.isPublicActivityFee || this.isNatural) {
                if (this.endDate) {
                    // 取结束时间的年初
                    yearStart = dayjs(this.endDate).startOf('year');
                }
                return yearStart.toDate();
            }
            // 乡村儿童操场的特殊判断
            if (this.isChildren) {
                const curMonth = dayjs().month();
                const yearAgo = dayjs().year() - 1;
                const December = dayjs(`${yearAgo}-12-01`);
                // 如果当前是一月份
                if (curMonth === 0) {
                    return December.toDate();
                } else {
                    if (this.endDate) {
                        // 取结束时间的年初
                        yearStart = dayjs(this.endDate).startOf('year');
                    }
                    return yearStart.toDate();
                }
            }
            return null;
        },
        maxStartDate() {
            // 开始时间的限制：结束时间或昨天
            return this.endDate || dayjs().subtract(1, 'd').toDate();
        },

        minEndDate() {
            return this.startDate || null;
        },
        maxEndDate() {
            const yesterday = dayjs().subtract(1, 'd');
            // 青山环保、零废星球，项目开票时间限制：不能跨年
            if (this.isPublicActivityFee || this.isNatural) {
                if (this.startDate) {
                    // 取开始时间的年末
                    const yearEnd = dayjs(this.startDate).endOf('year');
                    if (yesterday.isAfter(yearEnd)) {
                        return yearEnd.toDate();
                    }
                }
                return yesterday.toDate();
            }
            return yesterday.toDate();
        },

        nextPageStart() {
            const { pageNo, pageSize } = this;
            return pageNo * pageSize + 1;
        },
        nextPageEnd() {
            const { pageNo, pageSize, total } = this;
            return Math.min(total, (pageNo + 1) * pageSize);
        },
        nextPageText() {
            const { nextPageStart, nextPageEnd } = this;
            if (nextPageStart === nextPageEnd) {
                return `${nextPageStart}`;
            }
            return `${nextPageStart} ~ ${nextPageEnd}`;
        },
    },
    created() {
        this.LoadingType = QueryButtonType;
        const { baseFeeTypeItem, qualifications } = this;

        // 进入时检查 qualification
        if (!(baseFeeTypeItem && qualifications && qualifications.length > 0)) {
            this.$router.go(-1);
        }
    },
    mounted() {
        if (this.dateFrom && this.dateTo) {
            this.startDate = new Date(this.dateFrom);
            this.endDate = new Date(this.dateTo);
        }
        this.resetDataList();
    },
    methods: {
        ...mapMutations('apply', ['changeOwnership', 'changeHasOwnershipNegative', 'initSplitContent', 'changeSubmitComList']),
        handleInputChange() {
            // 用户修改查询框中的内容
            // 点击查询按钮的时候才真正赋值
            this.searchId = -1;
            this.poiId = -1;
        },
        handleIconClick() {
            this.showAlert = !this.showAlert;
        },
        // 切换开销方
        handleSelectCompany(option) {
            this.changeOwnership(option);
            // 接口请求的方式
            this.handleDateQuery();
            // 前端自己筛选
            // this.changeAmountListByCompany(option);
        },
        ...mapMutations('amountSelectNew', [
            'changeDateFrom',
            'changeDateTo',
            'changePageNo',
            'changeAmountListByCompany',
            'changeTotalSelectedAmount',
            'changeListBySelect',
            'changeListBySelectAll',
            'getOwnerCompanyList',
            'resetDataList',
        ]),
        ...mapActions('amountSelectNew', [
            'fetchAmountList',
        ]),
        loadingStatus(type) {
            return this.loading && this.activeQueryButton === type;
        },
        // 点击查询请求
        handleDateQuery() {
            if (!this.startDate) {
                toast.warn('请选择开始日期');
                return;
            }
            if (!this.endDate) {
                toast.warn('请选择结束日期');
                return;
            }
            // 点击查询的时候将搜索框内容置空，代表当前只是按时间进行筛选
            this.idInput = '';
            this.poiId = -1;
            this.searchId = -1;
            this.changeDateFrom(dayjs(this.startDate).format('YYYY-MM-DD'));
            this.changeDateTo(dayjs(this.endDate).format('YYYY-MM-DD'));
            this.activeQueryButton = QueryButtonType.Date;
            this.handleQueryClick(1);
        },
        handleQueryClick(queryPageNo = 1) {
            // 校验是否有已选时间，必填参数
            if (!this.dateFrom) {
                toast.warn('请选择开始日期');
                return;
            }
            if (!this.dateTo) {
                toast.warn('请选择结束日期');
                return;
            }
            if (this.loading) return;
            this.loading = true;
            const params = {
                settleId: this.searchId === -1 ? null : this.searchId,
                partnerId: this.poiId === -1 ? null : this.poiId,
                pageNum: queryPageNo,
            };
            this.fetchAmountList(params)
                .finally(() => {
                    this.loading = false;
                });
        },
        // 点击查询结算ID请求
        handleSearchSettleId() {
            const settleId = parseInt(this.idInput, 10);

            if (!settleId) {
                toast.error('请输入正确的结算设置 ID');
                return;
            }

            this.idInput = `${settleId}`;
            this.searchId = settleId;
            this.poiId = -1;
            this.activeQueryButton = QueryButtonType.Settle;

            this.handleQueryClick(1);
        },
        // 点击查询门店ID请求
        handleSearchWmPoiId() {
            const id = parseInt(this.idInput, 10);

            if (!id) {
                toast.error('请输入正确的门店 ID');
                return;
            }

            this.idInput = `${id}`;
            this.poiId = id;
            this.searchId = -1;
            this.activeQueryButton = QueryButtonType.Poi;

            this.handleQueryClick(1);
        },

        handleCheckAll() {
            this.changeListBySelectAll();
        },

        handleCheck(index) {
            this.changeListBySelect(index);
        },

        handleSelectPoi(index) {
            this.selectPoiAmountIndex = index;
            this.displayPoiSelect = true;
        },

        handleSelectDetail(amount) {
            this.adAmount = amount;
            this.displayAdDetail = true;
        },

        loadMore() {
            this.activeQueryButton = QueryButtonType.LoadMore;
            this.handleQueryClick(this.pageNo + 1);
        },

        handlePrevClick() {
            this.$router.go(-1);
        },
        handleNextClick() {
            const isPublicInvoice = this.isPublicActivityFee || this.isNatural;
            // eslint-disable-next-line camelcase
            const isSufficeAmount = this.totalSelectedAmount * 100 >= MIN_PUBLIC_ACTIVITY_AMOUNT_Milli;
            // 青山公益发票至少是50元
            if (isPublicInvoice && !isSufficeAmount) {
                toast.error(
                    // eslint-disable-next-line camelcase
                    `申请金额必须大于等于${MIN_PUBLIC_ACTIVITY_AMOUNT_Milli / 10000}元，请重新提交`,
                );
                return;
            }
            // 计算下一步分销方公司展示金额
            this.getOwnerCompanyList({ ownership: this.ownership, poiRelatedToTitle: this.poiRelatedToTitle });
            // 初始化分配金额的数据
            this.initSplitContent(this.checkedAmountList);
            // 计算是否存在销方公司金额为负数
            this.changeHasOwnershipNegative();
            // 计算金额大于0的销方公司列表，最后一步提交前的预览需要使用
            this.changeSubmitComList();

            this.$router.push({ name: 'amount-split' });
        },
    },
};
</script>

<style lang="scss" module>
.module {
  margin: 30px 40px;
}

.h3 {
  margin-top: 0;
  margin-bottom: 20px;
  font-size: 20px;
}
.hidden {
  display: none;
}
.companyWrapper {
  margin: 20px 0 10px;
  display: flex;
  justify-content: space-between;
}
.totalWrapper {
  height: 36px;
  line-height: 36px;
}
.select {
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  .companylabel {
    display: inline-block;
    height: 36px;
    line-height: 36px;
  }
  .roo-select {
    width: 150px;
  }
}
.action-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-left: 20px;
  margin: 20px 0;
}

.checkbox {
  color: #858692;
}

.label {
  display: inline-block;
  width: 70px;
  margin-right: 10px;
  line-height: 36px;
  text-align: right;
  vertical-align: middle;
}

.input {
  display: inline-block;
  width: 200px;
  vertical-align: middle;
}

.radio-group {
  display: inline-block;
  line-height: 1;
  vertical-align: middle;
}

.mr10 {
  margin-right: 8px;
}
.mb10 {
  margin-bottom: 10px;
}

.alert {
  margin: 20px 0;

  &:global(.roo-alert) i {
    left: 10px;
  }

  ul {
    list-style: decimal;
  }

  li {
    padding-left: 20px;
  }
}

.pagination {
  margin-top: 20px;
  text-align: center;
}
.btn {
  min-width: 240px;
}
.done {
  font-size: 14px;
  color: #a0a0a0;
}

.summary {
  display: inline-block;
  margin-right: 16px;
  vertical-align: middle;
}
.tip1 {
  font-size: 14px;
  line-height: 20px;
}
.amount {
  color: #f89800;
}
.tip2 {
  font-size: 12px;
  color: #858692;
  line-height: 16px;
}

.mr16 {
  margin-right: 16px;
}
</style>
