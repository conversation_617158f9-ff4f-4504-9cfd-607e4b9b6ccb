<template>
    <table :class="$style.table">
        <col style="width: 56px;" />
        <col style="width: 160px;" />
        <col style="width: 240px;" />
        <col style="width: 200px;" />
        <col style="width: 200px;" />

        <thead>
            <tr>
                <th>
                    <roo-checkbox
                        :checked="(!!total) && checkedAmountCount === total"
                        :indeterminate="checkedAmountCount > 0 && checkedAmountCount < total"
                        @change="$emit('check-all')"
                    >
                        全选
                    </roo-checkbox>
                </th>
                <th>结算 ID</th>
                <th>开户名</th>
                <th>可开金额</th>
                <th>操作</th>
            </tr>
        </thead>

        <tbody>
            <tr
                v-if="loading"
                key="loading"
            >
                <td
                    :class="$style.loading"
                    colspan="5"
                >
                    正在加载
                </td>
            </tr>
            <tr
                v-else-if="tableList.length === 0"
                key="empty"
            >
                <td
                    :class="$style.empty"
                    colspan="5"
                >
                    暂无数据
                </td>
            </tr>
            <template v-else>
                <tr
                    v-for="(item, index) in tableList"
                    :key="item.settleId"
                >
                    <td>
                        <roo-checkbox
                            :disabled="item.disabled"
                            :checked="item.selected"
                            @change="$emit('check', index)"
                        />
                    </td>
                    <td>{{ item.settleId }}</td>
                    <td>
                        {{ item.settleName }}
                    </td>
                    <td>
                        ￥{{ item.selectedMoneyCent | formatCent }}
                    </td>
                    <td>
                        <a
                            href="#noop"
                            @click.prevent="$emit('select-poi', index)"
                        >
                            选择门店
                        </a>
                        <span
                            v-if="notAll(item)"
                            :class="$style.tag"
                        >
                            已选部分门店
                        </span>
                        <span
                            v-else-if="notCheck(item)"
                            :class="$style.tag"
                        >
                            当前未选门店
                        </span>
                    </td>
                </tr>
            </template>
        </tbody>
    </table>
</template>

<script>
import {
    mapState,
    mapGetters,
} from 'vuex';
/* eslint-disable import/extensions, import/no-unresolved */
import { formatNumberThousand } from '$lib/utils';

export default {
    name: 'AmountTable',
    filters: {
        formatCent(num) {
            return formatNumberThousand((num / 100).toFixed(2));
        },
    },
    props: {
        loading: {
            type: Boolean,
            default: false,
        },
        tableList: {
            type: Array,
            default() {
                return [];
            },
        },
    },
    data() {
        return {
        };
    },
    computed: {
        ...mapState('amountSelectNew', [
            'total',
        ]),
        ...mapGetters('amountSelectNew', [
            'currentCount',
            'checkedAmountCount',
        ]),
    },
    methods: {
        // 结算ID下的门店是否全选
        notAll(item) {
            const totalPoi = item.partnerList.length;
            const checkedLen = item.partnerList.filter(x => x.checked).length;
            if (checkedLen > 0 && checkedLen < totalPoi) {
                return true;
            }
            return false;
        },
        notCheck(item) {
            return item.partnerList.filter(x => x.checked).length === 0;
        },
    },
};
</script>

<style lang="scss" module>
.table {
    width: 100%;
    table-layout: fixed;
    border-collapse: collapse;
    border: 1px solid #E9EAF2;
    font-size: 14px;
    line-height: 20px;

    thead {
        border-bottom: 1px solid #F7F8FA;
    }

    thead,
    tbody tr:nth-of-type(even) {
        background: #F7F8FA;
    }

    th {
        color: #858692;
    }

    th, td {
        height: 40px;
        padding: 0 20px;
        text-align: left;

        &:nth-child(1) {
            text-align: center;
        }
        &:nth-child(4) {
            padding-right: 80px;
            text-align: right;
        }
    }

    tr.active {
        border: 1px solid #F8B500;
    }

    :global(.roo-checkbox) {
        display: inline;
    }
    :global(.roo-checkbox .custom-checkbox) {
        margin-right: 0;
    }

    .loading,
    .empty {
        text-align: center;
    }
}

.tag {
    padding: 0 5px;
    border: 1px solid #F89800;
    border-radius: 2px;
    color: #F89800;
    font-size: 12px;
}

.disabled {
    color: #E9EAF2;
    cursor: not-allowed;
    pointer-events: none;
}
</style>
