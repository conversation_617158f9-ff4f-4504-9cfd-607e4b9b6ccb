<template>
    <table :class="$style.table">
        <col style="width: 70%;" />
        <col style="width: 15%;" />
        <col style="width: 15%;" />

        <thead>
            <tr>
                <th>
                    <roo-checkbox
                        :checked="chosenCount > 0 && chosenCount === total"
                        :indeterminate="chosenCount > 0 && chosenCount < total"
                        @change="$emit('check-all', $event)"
                    />
                    抬头名称
                </th>
                <th>关联门店</th>
                <th>操作</th>
            </tr>
        </thead>

        <tbody
            v-if="loading"
            key="loading"
        >
            <tr>
                <td colspan="6">
                    加载中
                </td>
            </tr>
        </tbody>

        <tbody
            v-else-if="total == 0"
            key="empty"
            style="height: 180px;"
        >
            <tr>
                <td
                    colspan="6"
                    style="text-align: center;"
                >
                    <div>
                        暂无可用抬头
                    </div>
                    <roo-button
                        size="large"
                        style="margin-top: 10px;"
                        @click="addQualification"
                    >
                        新建抬头
                    </roo-button>
                </td>
            </tr>
        </tbody>

        <tbody
            v-else
            key="list"
        >
            <tr
                v-for="item in list"
                :key="item.id"
            >
                <td>
                    <roo-checkbox
                        :checked="value.indexOf(item.id) > -1"
                        :disabled="item.status !== 1 || !item.taxpayerIdNo"
                        @change="handleChange(item, $event)"
                    />
                    {{ item.partnerBName }}
                </td>
                <td>
                    {{ item.partnerNum > 0 ? item.partnerNum : 0 }}家
                </td>
                <td>
                    <span
                        :class="$style.link"
                        @click="handleJump(item)"
                    >
                        {{ item.partnerNum && item.partnerNum > 0 ? '查看详情' : '关联门店' }}
                    </span>
                </td>
            </tr>
        </tbody>
    </table>
</template>

<script>
/* eslint-disable import/extensions, import/no-unresolved */
import { mapActions, mapMutations, mapState } from 'vuex';
import hasInvalidCharactor from '$lib/has-invalid-charactor';
/* eslint-disable import/extensions, import/no-unresolved */
export default {
    name: 'QualificationTableUpdate',
    computed: {
        ...mapState('invoiceTitle', ['titleInfos', 'titleInfoList']),
        chosenCount() {
            const { titleInfos, titleInfoList } = this;
            let count = 0;
            titleInfoList.forEach((t) => {
                if (titleInfos.includes(t.id)) {
                    count++;
                }
            });
            return count;
        },
    },
    methods: {
        ...mapActions('titleManagement', ['fetchRelatedStoreList']),
        ...mapMutations('invoiceTitle', ['changeEditItemInfo', 'changeTitleInfos']),
        ...mapMutations('titleManagement', ['changeIsEdit']),
        addQualification() {
            this.$router.push({ name: 'add-title' });
        },
        handleChange(item, checked) {
            if (checked) {
                if (hasInvalidCharactor(item.partnerBName)) {
                    // eslint-disable-next-line
                    confirm('该资质包含特殊字符，无法开票，是否进行修改？', (result) => {
                        if (result) {
                            this.$router.push({
                                name: 'edit-qualification',
                                query: { id: item.id },
                            });
                        }
                    });
                    return;
                }
                this.$emit('change', this.value.concat([item.id]));
            } else {
                this.$emit('change', this.value.filter((id) => id !== item.id));
            }
        },
        handleJump(item) {
            this.changeIsEdit(true);
            this.changeEditItemInfo(item);
            // this.fetchRelatedStoreList();
            this.$router.push({ name: 'relate-store', query: { id: item.id } });
        },
    },
    props: {
        list: {
            type: Array,
            default() {
                return [];
            },
        },
        loading: {
            type: Boolean,
            default: false,
        },
        total: {
            type: Number,
            default: 0,
        },
        value: {
            type: Array,
            default() {
                return [];
            },
        },
    },
};
</script>

<style lang="scss" module>
.table {
    width: 100%;
    border: 1px solid #E9EAF2;
    table-layout: fixed;
    border-collapse: collapse;
    color: #3F4156;

    thead tr:first-child {
        background: #F7F8FA;
    }

    th, td {
        padding: 0 20px;
        text-align: start;
        // background: #FFFFFF;
    }

    th {
        font-weight: normal;
        height: 50px;
        color: #858692;
        border-bottom: 1px solid #E9EAF2;
    }

    td {
        height: 50px;
    }

    :global(.roo-checkbox) {
        display: inline;
    }
}
.link {
    color: #F89800;
    cursor: pointer;
}
.emptyInfo {
    height: 180px;
    text-align: center;
}
</style>
