<template>
    <div :class="$style.module">
        <div
            v-for="(item, idx) in finalPromotStr"
            :key="idx"
        >
            <roo-alert
                v-if="item.str !== ''"
                :class="$style.alert"
                type="warning"
                icon="exclamation-circle"
            >
                {{ item.showAll ? item.str : `${item.str.slice(0, 70)}...` }}
                <span
                    v-if="!item.showAll"
                    :class="$style.link"
                    @click="handleShowAllPromotBar(idx)"
                >
                    查看全部
                </span>
                <span
                    v-else
                    :class="$style.link"
                    @click="handleHideAllPromotBar(idx)"
                >
                    收起
                </span>
            </roo-alert>
        </div>

        <div id="feeTypeHeader"></div>

        <qualification-table-update
            :list="titleInfoList"
            :loading="titleInfoLoading"
            :total="titleInfoList.length"
            :value="checked"
            @change="handleCheckChange"
            @check-all="handleCheckAll"
        />

        <div :class="$style.pagination">
            <roo-pagination
                :total="invoiceTitleTotal"
                :page-size="20"
                :current-page="pageNo"
                @current-change="handlePageChange($event)"
            />
        </div>

        <div class="footer-nav">
            <roo-button
                type="brand"
                :disabled="titleInfos.length === 0"
                @click="handleNextClick"
            >
                下一步
            </roo-button>
        </div>
    </div>
</template>

<script>
import { mapState, mapMutations, mapActions } from 'vuex';
/* eslint-disable import/extensions, import/no-unresolved */
import { getCookieValue } from '$lib/utils';
import SOURCE from '$lib/sourceEnum';
import toastError from '$lib/toast-error';
import hasInvalidCharactor from '$lib/has-invalid-charactor';
/* eslint-enable import/extensions, import/no-unresolved */
import { Header } from '@wmfe/svelte-components';
import QualificationTableUpdate from './components/qualification-table-update';

export default {
    name: 'QualificationSelectUpdate',
    components: {
        QualificationTableUpdate,
    },
    computed: {
        ...mapState('apply', ['baseFeeTypeItem', 'qualifications']),
        ...mapState('config', ['isHit', 'feeTypeList', 'contractId', 'useNewSplit', 'contractAName', 'isSinglePoi']),
        ...mapState('invoiceTitle', ['promotBar', 'titleInfoList', 'titleInfoLoading', 'invoiceTitleTotal', 'titleInfos']),
        checked() {
            const { titleInfos } = this;
            return [...titleInfos];
        },
        promotBarStr() {
            const { promotBar } = this;
            const finalStr = promotBar.split('\n').map((item) => ({
                str: item,
                showAll: true,
            }));
            return finalStr;
        },
    },
    data() {
        return {
            accountSource: getCookieValue('source') || SOURCE.WAIMAI,
            queryString: '',
            hidePromot: false,
            SOURCE,
            finalPromotStr: [],
            pageNo: 1,
        };
    },
    methods: {
        // ...mapActions('qualificationSelect', ['fetchQualificationList']),
        ...mapActions('invoiceTitle', ['fetchTitleInfoList', 'fetchPromotBar', 'fetchPoiRelatedToTitle']),
        ...mapMutations('apply', ['changeBaseFeeTypeItem', 'changeQualifications']),
        ...mapMutations('invoiceTitle', ['changeTitleInfos']),
        ...mapMutations('titleManagement', ['changeIsEdit']),
        ...mapMutations('amountSelect', { resetAmount: 'reset' }),
        addQualification() {
            // TODO: 这块要看看是从哪来的
            const { baseFeeTypeItem } = this;
            this.changeIsEdit(false);
            this.$router.push({
                name: 'title-management',
                query: {
                    qualificationType: baseFeeTypeItem && baseFeeTypeItem.qualificationType,
                },
            });
        },
        handleBaseFeeTypeChange(val) {
            const target = this.feeTypeList.find((f) => f.baseFeeType === val.baseFeeType);
            if (target) {
                this.changeBaseFeeTypeItem(target);
                this.resetAmount();
            }
        },
        handleCheckAll(check) {
            if (check) {
                const { qualifications, titleInfos, titleInfoList } = this;
                const list = [...titleInfos];
                const qList = [...qualifications];
                for (let i = 0; i < titleInfoList.length; ++i) {
                    const q = titleInfoList[i];
                    if (hasInvalidCharactor(q.partnerBName)) {
                        // eslint-disable-next-line
                        confirm(
                            '该资质包含特殊字符，无法开票，是否进行修改？',
                            (result) => {
                                if (result) {
                                    this.$router.push({
                                        name: 'edit-qualification',
                                        query: { id: q.id },
                                    });
                                }
                            },
                        );
                        return;
                    } else if (q.status === 1 && q.taxpayerIdNo) {
                        // 审核通过的资质才能选中
                        qList.push({
                            ...q,
                            changed: false,
                            amountList: i < titleInfoList.length - 1 ? ['0.00'] : [],
                        });
                        list.push(q.id);
                    }
                }
                this.changeTitleInfos(list);
                this.changeQualifications(qList);
            } else {
                this.changeTitleInfos([]);
                this.changeQualifications([]);
            }
        },
        handleCheckChange(list) {
            const { qualifications, titleInfoList } = this;
            const seet = new Set(list);
            const qIds = new Set(qualifications.map((q) => q.id));
            const qList = [...qualifications];
            const currPageTitles = titleInfoList.map((t) => t.id);
            let count = 0;

            titleInfoList.forEach((q, idx) => {
                if (seet.has(q.id) && !qIds.has(q.id)) {
                    count += 1;
                    qList.push({
                        ...q,
                        changed: false,
                        amountList: count < list.length ? ['0.00'] : [],
                    });
                } else if (currPageTitles.includes(q.id) && !seet.has(q.id) && qIds.has(q.id)) {
                    count -= 1;
                    qList.splice(idx, 1);
                }
            });

            this.changeTitleInfos([...seet]);
            this.changeQualifications(qList);
        },
        showPromotBar() {
            const { promotBar } = this;
            this.finalPromotStr = promotBar.split('\n').map((item) => ({
                str: item,
                showAll: true,
            }));
        },
        handlePageChange(pageNum) {
            this.pageNo = pageNum;
            this.fetchTitleInfoList({ pageNo: pageNum, fuzzy: this.queryString });
        },
        handleShowAllPromotBar(idx) {
            const { finalPromotStr } = this;
            const temp = [];
            finalPromotStr.forEach((item, index) => {
                if (index === idx) {
                    temp.push({
                        ...item,
                        showAll: true,
                    });
                } else {
                    temp.push({ ...item });
                }
            });
            this.finalPromotStr = temp;
        },
        handleHideAllPromotBar(idx) {
            const { finalPromotStr } = this;
            const temp = [];
            finalPromotStr.forEach((item, index) => {
                if (index === idx) {
                    temp.push({
                        ...item,
                        showAll: false,
                    });
                } else {
                    temp.push({ ...item });
                }
            });
            this.finalPromotStr = temp;
        },
        handleNextClick() {
            console.log('qua:', this.qualifications.map((q) => q.partnerBName));
            if (!this.isSinglePoi) {
                this.fetchPoiRelatedToTitle();
                this.$router.push({
                    name: this.isHit ? 'amount-select-new-v2' : 'amount-select-new',
                });
            } else {
                if (this.useNewSplit && this.baseFeeTypeItem.amountGatherType === 1) {
                    this.$router.push({
                        name: this.isHit ? 'amount-select-new-v2' : 'amount-select-new',
                    });
                } else {
                    this.$router.push({
                        name: 'amount-select',
                    });
                }
            }
        },
    },
    mounted() {
        this.showPromotBar();
        // eslint-disable-next-line no-new
        new Header({
            target: document.querySelector('#feeTypeHeader'),
            props: {
                configParam: {
                    source: this.accountSource,
                },
                // 发票查询的回调函数
                onDataChange: (baseFeeType, inputValue) => {
                    if (inputValue !== this.queryString) {
                        this.queryString = inputValue;
                        this.pageNo = 1;
                        this.fetchTitleInfoList({ pageNo: 1, fuzzy: inputValue });
                    }
                    this.handleBaseFeeTypeChange(baseFeeType);
                },
                // 点击新建资质的回调
                onAddQualification: () => {
                    this.addQualification();
                },
            },
        });
        // 等待 svelte 组件渲染完成后添加内容
        this.$nextTick(() => {
            const svelteElement = document.querySelector('.sv-warning');
            if (svelteElement) {
                // 添加带有正确 class 的 p 标签
                svelteElement.insertAdjacentHTML('beforeend', 
                    '<p style="margin: 0;">若申请通过的发票抬头不可选中，说明根据该发票抬头未查询到有效纳税人识别号，请选择其他发票抬头或新增发票抬头</p>'
                );
            }
        });
        if (this.baseFeeTypeItem && this.contractId) {
            this.fetchTitleInfoList(1).catch(toastError);
        }
    },
    watch: {
        baseFeeTypeItem() {
            this.fetchTitleInfoList(1).catch(toastError);
        },
    },
};
</script>

<style lang="scss" module>
.module {
    margin: 30px 40px;
    :global(.roo-radio-group) {
        display: inline-block;
        margin-left: 20px;
        line-height: 1;
        vertical-align: middle;
    }
}
.alert {
    margin: 10px 0;
}
.link {
    color: #F89800;
    cursor: pointer;
    display: inline-block;
}
.pagination {
    text-align: right;
}
</style>
