<template>
    <div :class="$style.module">
        <roo-alert
            :class="$style.alert"
            type="warning"
            icon="exclamation-circle"
        >
            为响应国税总局全面推广数电发票的工作要求，预计2024年3月起（或按实际情况），部分开票主体将为您开具数电发票。数电发票的法律效力、基本用途等与现有纸质发票相同。请贵司合理安排接收数电发票，为您带来的不便，敬请谅解。
        </roo-alert>
        <roo-alert
            v-if="accountType === 0 && wmPoiId === -1"
            :class="$style.alert"
            type="warning"
            icon="exclamation-circle"
        >
            全部门店模式下，只能开服务费发票，如果需要开推广费发票，请点击右上角切换至单门店模式
        </roo-alert>

        <roo-alert
            v-if="accountSource === SOURCE.WAIMAI"
            :class="$style.alert"
            type="warning"
            icon="exclamation-circle"
        >
            尊敬的美团商户您好，财政部、税务总局在2022年4月29日发布了《财政部 税务总局关于快递收派服务免征增值税政策的公告》。根据上述政策以及主管税务机关沟通，我司配送服务收入属于免征增值税范畴。因此自   2022年5月账期起，我司与您结算的配送服务费收入金额，将开具增值税免税普通发票。此变化因国家疫情防控税收优惠政策调整而起，开票带来的影响还请您理解和知悉。待免税政策到期后，将恢复专票结算的方式，届时请关注并以最新通知为准。请您在开配送服务费发票时单次选择2022年5月1日前时段或2022年5月1日至2022年12月31日内时段，避免时间跨期，谢谢！
        </roo-alert>

        <roo-alert
            v-if="accountSource === SOURCE.WAIMAI"
            :class="$style.alert"
            type="warning"
            icon="exclamation-circle"
        >
            新费率模式下，开具佣金发票需在佣金类业务类型中选择「佣金」，开具商家配送费发票需在配送类业务类型中选择「配送服务费」。针对代理城市，平台仅就在本次合作中提供的平台技术服务提供佣金发票（约1%），剩余部分金额发票由合作商提供。
        </roo-alert>
        <!-- <roo-alert
      :class="$style.alert"
      type="warning"
      icon="exclamation-circle"
    >
      因发票数据安全要求，非本账号提交的资质可能出现“无权操作”提示，为保证您的开票流程顺利进行，请使用本账号申请资质并开票。
    </roo-alert> -->
        <!-- 2020-02-01 后不展示 -->
        <!-- <roo-alert
      v-if="Date.now() < new Date(2020, 1, 1).getTime()"
      :class="$style.alert"
      type="warning"
      icon="exclamation-circle"
    >
      尊敬的商户您好，受春节假期影响，快递公司自1月8日起逐步停发全国各地的快件，如有节前开具纸质发票的需求，请在各地停发日前，提前3-4个工作日提交开票需求，停运期间无法正常寄票，电子发票申请不受影响。快递统一恢复时间为1月31日。具体停发时间地点如下：
      <br />
      1月8日停发西藏自治区；1月11日停发新疆自治区、青海省、云南省、内蒙古自治区；1月14日停发除北京市以外所有省市；1月17日停发北京市。
    </roo-alert> -->

        <div id="feeTypeHeader"></div>

        <!-- <div :class="$style.mb20">
      <span>查询资质</span>
      <roo-input
        v-model="keyword"
        :class="$style.input"
        placeholder="输入发票抬头查询"
        @change="handleKeywordChange"
      />
      <roo-button
        :loading="loading"
        @click="queryString = keyword"
      >
        查询
      </roo-button>
    </div> -->

        <!-- <roo-alert
      :class="$style.alert"
      type="warning"
      icon="exclamation-circle"
    >
      仅展示本账号申请资质。
    </roo-alert> -->
        <div
            v-if="qualificationList.length > 0"
            key="list"
        >
            <!-- <h3 :class="$style.h3">
        选择发票抬头
        <roo-button
          :class="$style.addQualification"
          size="mini"
          type="hollow"
          @click="addQualification"
        >
          新增资质
        </roo-button>
      </h3> -->

            <qualification-table
                :loading="loading"
                :total="qualificationList.length"
                :list="showQualificationList"
                :value="checked"
                :is-public-welfare="isPublicWelfare"
                @show-detail="handleShowDetail"
                @change="handleQualificationsChange"
                @check-all="handleCheckAll"
            />
        </div>

        <div
            v-else
            key="empty"
        >
            <div :class="$style.placeholder">
                <div :class="$style.emptyInfo">
                    门店尚无开票资质，请点击申请开票资质
                </div>

                <roo-button
                    type="brand"
                    size="large"
                    @click="addQualification"
                >
                    新增资质
                </roo-button>
            </div>
        </div>

        <div class="footer-nav">
            <roo-button
                :disabled="!(qualifications && qualifications.length > 0)"
                type="brand"
                @click="handleNextClick"
            >
                下一步
            </roo-button>
        </div>

        <qualification-modal
            :id="displayQualificationId"
            @input="displayQualificationId = null;"
        />
    </div>
</template>

<script>
// import axios from 'axios';
import {
    mapState, mapMutations, mapActions, mapGetters,
} from 'vuex';
import { confirm } from '@roo/roo-vue';

/* eslint-disable import/extensions, import/no-unresolved */
import toastError from '$lib/toast-error';
import { getCookieValue } from '$lib/utils';
import SOURCE from '$lib/sourceEnum';
import hasInvalidCharactor from '$lib/has-invalid-charactor';
/* eslint-enable import/extensions, import/no-unresolved */

import { Header } from '@wmfe/svelte-components';
import QualificationModal from './qualification-modal';
import QualificationTable from './components/qualification-table';

export default {
    name: 'QualificationSelect',
    components: {
        QualificationTable,
        QualificationModal,
    },
    data() {
        return {
            keyword: '',
            queryString: '',

            displayQualificationId: null,

            companyList: [],

            accountSource: getCookieValue('source') || SOURCE.WAIMAI,
            SOURCE,
        };
    },
    computed: {
        wmPoiId() {
            return parseInt(getCookieValue('wmPoiId'), 10);
        },
        ...mapState('config', [
            'feeTypeList',
            'contractId',
            'useNewSplit',
            // 'qualification',
        ]),
        ...mapState('qualificationSelect', [
            'loading',
            'accountType',
            'contractList',
            'qualificationList',
        ]),
        ...mapState('apply', ['baseFeeTypeItem', 'qualifications']),
        ...mapGetters('apply', ['isPublicActivityFee', 'isChildren', 'isNatural']),
        showQualificationList() {
            const { qualificationList, queryString } = this;
            if (queryString) {
                return qualificationList.filter(
                    (item) => (item.partnerBName || '').indexOf(queryString) > -1,
                );
            }
            return qualificationList;
        },
        checked() {
            const { qualifications } = this;
            return qualifications ? qualifications.map((q) => q.id) : [];
        },
        isPublicWelfare() {
            return this.isPublicActivityFee || this.isChildren || this.isNatural;
        },
    },
    watch: {
        baseFeeTypeItem() {
            this.fetchQualificationList().catch(toastError);
        },
        contractId() {
            this.fetchQualificationList().catch(toastError);
        },
    },
    mounted() {
    // eslint-disable-next-line no-new
        new Header({
            target: document.querySelector('#feeTypeHeader'),
            props: {
                configParam: {
                    source: this.accountSource,
                },
                // 发票查询的回调函数
                onDataChange: (baseFeeType, inputValue) => {
                    if (inputValue !== this.queryString) {
                        this.queryString = inputValue;
                    }
                    this.handleBaseFeeTypeChange(baseFeeType);
                },
                // 点击新建资质的回调
                onAddQualification: () => {
                    this.addQualification();
                },
            },
        });
        // 等待 svelte 组件渲染完成后添加内容
        this.$nextTick(() => {
            const svelteElement = document.querySelector('.sv-warning');
            if (svelteElement) {
                // 添加带有正确 class 的 p 标签
                svelteElement.insertAdjacentHTML('beforeend',
                    '<p style="margin: 0;">若申请通过的发票抬头不可选中，说明根据该发票抬头未查询到有效纳税人识别号，请选择其他发票抬头或新增发票抬头</p>',
                );
            }
        });
        if (this.baseFeeTypeItem && this.contractId) {
            this.fetchQualificationList().catch(toastError);
        }
    },
    methods: {
        ...mapMutations('apply', ['changeBaseFeeTypeItem', 'changeQualifications']),
        ...mapActions('invoiceTitle', ['fetchPoiRelatedToTitle']),
        ...mapMutations('amountSelect', {
            resetAmount: 'reset',
        }),
        ...mapActions('qualificationSelect', ['fetchQualificationList']),
        handleBaseFeeTypeChange(val) {
            const target = this.feeTypeList.find((f) => f.baseFeeType === val.baseFeeType);
            if (target) {
                this.changeBaseFeeTypeItem(target);
                this.resetAmount();
            }
        },
        handleKeywordChange(val) {
            if (!val) {
                this.queryString = '';
            }
        },
        addQualification() {
            // TODO: 这块要看看是从哪来的
            const { baseFeeTypeItem } = this;
            this.$router.push({
                name: 'add-qualification',
                query: {
                    qualificationType:
            baseFeeTypeItem && baseFeeTypeItem.qualificationType,
                },
            });
        },
        handleCheckAll(checked) {
            if (checked) {
                const list = [];
                const { qualificationList } = this;
                for (let i = 0; i < qualificationList.length; ++i) {
                    const q = qualificationList[i];
                    if (hasInvalidCharactor(q.partnerBName)) {
                        // eslint-disable-next-line
                        confirm(
                            '该资质包含特殊字符，无法开票，是否进行修改？',
                            (result) => {
                                if (result) {
                                    this.$router.push({
                                        name: 'edit-qualification',
                                        query: { id: q.id },
                                    });
                                }
                            },
                        );
                        return;
                    } else if (q.status === 1 && q.taxpayerIdNo) {
                        // 审核通过的资质才能选中
                        list.push({
                            ...q,
                            changed: false,
                            amountList: i < qualificationList.length - 1 ? ['0.00'] : [],
                        });
                    }
                }
                this.changeQualifications(list);
            } else {
                this.changeQualifications([]);
            }
        },
        handleQualificationsChange(list) {
            const seet = new Set(list);

            const qualifications = [];

            let count = 0;

            this.qualificationList.forEach((q) => {
                if (seet.has(q.id)) {
                    count += 1;

                    qualifications.push({
                        ...q,
                        changed: false,
                        amountList: count < list.length ? ['0.00'] : [],
                    });
                }
            });

            this.changeQualifications(qualifications);
        },
        handleNextClick() {
            // this.fetchPoiRelatedToTitle();
            if (this.useNewSplit && this.baseFeeTypeItem.amountGatherType === 1) {
                this.$router.push({
                    name: 'amount-select-new-v2',
                });
            } else {
                this.$router.push({
                    name: 'amount-select',
                });
            }
        },
        handleShowDetail(id) {
            this.displayQualificationId = id;
        },
    },
};
</script>

<style lang="scss" module>
.module {
  margin: 30px 40px;

  :global(.roo-radio-group) {
    display: inline-block;
    margin-left: 20px;
    line-height: 1;
    vertical-align: middle;
  }
}

.alert {
  margin: 10px 0;
}

.h3 {
  margin-top: 0;
  margin-bottom: 20px;
  font-size: 20px;
  color: #3f4156;
}

.placeholder {
  padding: 100px 0;
  text-align: center;
}

.empty-info {
  margin-bottom: 50px;
  color: #babccc;
  font-size: 20px;
}

.mb20 {
  margin-bottom: 20px;
}

.input {
  display: inline-block;
  width: 200px;
  margin: 0 10px;
  vertical-align: middle;
}

.add-qualification {
  float: right;
  font-weight: normal;
}
</style>
