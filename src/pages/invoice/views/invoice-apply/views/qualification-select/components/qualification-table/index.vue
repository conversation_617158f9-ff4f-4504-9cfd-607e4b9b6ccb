<template>
    <table :class="$style.table">
        <col style="width: 80px;" />
        <col style="width: 150px;" />
        <col style="width: 240px;" />
        <col style="width: 150px;" />
        <col style="width: 150px;" />
        <col style="width: 200px;" />

        <thead>
            <tr>
                <th>
                    <roo-checkbox
                        v-if="!isPublicWelfare"
                        :checked="value.length > 0 && value.length === total"
                        :indeterminate="value.length > 0 && value.length < total"
                        @change="$emit('check-all', $event)"
                    >
                        全选

                        <roo-tooltip
                            :visible="showTip"
                            placement="top"
                            manual
                        >
                            支持开多个发票抬头
                            <roo-icon
                                :class="$style.closeButton"
                                name="close"
                                @click="changeShowTip(false)"
                            />
                        </roo-tooltip>
                    </roo-checkbox>
                    <div v-else>
                        单选

                        <roo-tooltip
                            :visible="showTip"
                            placement="top"
                            manual
                            style="width: 99px"
                        >
                            应基金会要求，公益类发票只允许选择一个抬头开具，敬请谅解。
                            <roo-icon
                                :class="$style.closeButton"
                                name="close"
                                @click="changeShowTip(false)"
                            />
                        </roo-tooltip>
                    </div>
                </th>
                <th>资质ID</th>
                <th>发票抬头</th>
                <th>资质类型</th>
                <th>申请状态</th>
                <th>操作</th>
            </tr>
        </thead>
        <tbody
            v-if="loading"
            key="loading"
        >
            <tr>
                <td colspan="6">
                    加载中
                </td>
            </tr>
        </tbody>
        <tbody
            v-else
            key="list"
        >
            <tr
                v-for="item in list"
                :key="item.id"
            >
                <td>
                    <roo-checkbox
                        :checked="value.indexOf(item.id) > -1"
                        :disabled="item.status !== 1 || !item.taxpayerIdNo"
                        @change="handleChange(item, $event)"
                    />
                </td>
                <td>
                    {{ item.id > 0 ? item.id : '' }}
                    <span
                        v-if="item.isCurrentAccount"
                        :class="$style.currentAccount"
                    >
                        {{ item.isCurrentAccount }}
                    </span>
                </td>
                <td :class="$style.taitou">
                    {{ item.partnerBName }}
                </td>
                <td>
                    <span :class="getQualificationTypeClass(item.qualificationType)">
                        {{ item.qualificationType | getQualificationTypeName }}
                    </span>
                </td>
                <td>
                    <span :class="getStatusClass(item.status)">
                        {{ getStatusText(item.status) }}
                    </span>
                </td>
                <td>
                    <template>
                        <span
                            :class="$style.link"
                            @click="$emit('show-detail', item.id)"
                        >
                            查看详情
                        </span>
                        <span :class="$style.separator"></span>
                        <span
                            :class="disableEdit(item) ? [$style.link, $style.disabled] : $style.link"
                            @click="!disableEdit(item) && editQualification(item.id)"
                        >
                            修改资质
                        </span>
                    </template>
                </td>
            </tr>
        </tbody>
    </table>
</template>

<script>
import { mapState, mapMutations } from 'vuex';
import { confirm } from '@roo/roo-vue';

/* eslint-disable import/extensions, import/no-unresolved */
import hasInvalidCharactor from '$lib/has-invalid-charactor';
/* eslint-enable import/extensions, import/no-unresolved */

export default {
    name: 'QualificationTable',
    filters: {
        getQualificationTypeName(type) {
            return {
                1: '服务费相关',
                2: '推广费相关',
            }[type];
        },
    },
    props: {
        loading: {
            type: Boolean,
            default: false,
        },
        total: {
            type: Number,
            default: 0,
        },
        list: {
            type: Array,
            default() {
                return [];
            },
        },
        value: {
            type: Array,
            default() {
                return [];
            },
        },
        isPublicWelfare: {
            type: Boolean,
            default: false,
        },
    },
    computed: {
        ...mapState('config', ['showTip']),
    },
    watch: {
        // 监听列表数据变化
        list: {
            handler() {
                // 清空选择
                this.$emit('change', []);
            },
            deep: true,
        },
    },
    methods: {
        ...mapMutations('config', ['changeShowTip']),
        // 是否禁用修改资质按钮
        disableEdit(item) {
            // 原始逻辑：人工审核中status为0，则禁用修改资质按钮
            return item.status === 0;
        },
        getStatusClass(status) {
            const statusClassMap = {
                0: this.$style.yellow, // 人工审核中
                1: this.$style.green, // 审核通过
                2: this.$style.red, // 审核驳回
                3: this.$style.yellow, // 系统审核中
                100: this.$style.yellow, // 甲方签约中
                101: this.$style.red, // 甲方签约失败
                102: this.$style.yellow, // 新资质签约中
                103: this.$style.red, // 新资质签约失败
            };
            return statusClassMap[status] || '';
        },
        getStatusText(status) {
            const statusTextMap = {
                0: '人工审核中',
                1: '审核通过',
                2: '审核驳回',
                3: '系统审核中',
                100: '甲方签约中',
                101: '甲方签约失败',
                102: '丙方（新资质）签约中',
                103: '丙方（新资质）签约失败',
            };
            return statusTextMap[status] || '-';
        },
        editQualification(id) {
            this.$router.push({
                name: 'edit-qualification',
                query: { id },
            });
        },
        handleChange(q, checked) {
            if (checked) {
                if (hasInvalidCharactor(q.partnerBName)) {
                    // eslint-disable-next-line
                    confirm('该资质包含特殊字符，无法开票，是否进行修改？', (result) => {
                        if (result) {
                            this.$router.push({
                                name: 'edit-qualification',
                                query: { id: q.id },
                            });
                        }
                    });
                    return;
                }

                if (this.isPublicWelfare) {
                    // 公益开票时只允许选择一个抬头
                    this.$emit('change', [q.id]);
                } else {
                    this.$emit('change', this.value.concat([q.id]));
                }
            } else {
                this.$emit('change', this.value.filter(id => id !== q.id));
            }
        },
        getQualificationTypeClass(type) {
            const { $style } = this;
            return [
                $style.greenTag,
                $style.redTag,
                $style.blueTag,
                $style.yellowTag,
            ][type];
        },
    },
};
</script>

<style lang="scss" module>
// 你会看到很多段这样的代码，这是因为表格的对齐、高度总是有细微不同，最重要的是我代码不精、封装无力、架构松散...
.table {
    width: 100%;
    border: 1px solid #E9EAF2;
    table-layout: fixed;
    border-collapse: collapse;
    color: #3F4156;

    thead,
    tbody tr:nth-of-type(even) {
        background: #F7F8FA;
    }

    .currentAccount {
        margin-left: 8px;
        padding: 2px;
        border: 1px solid #f76c6c;
        border-radius: 2px;
        background: rgba(247, 108, 108, 0.05);
    }

    th, td {
        padding: 0 20px;
        text-align: left;

        &:last-child {
            text-align: center;
        }
    }

    th {
        font-weight: normal;
        height: 40px;
        color: #858692;
    }

    td {
        height: 90px;
    }

    :global(.roo-checkbox) {
        display: inline;
    }
    :global(.roo-checkbox .custom-checkbox) {
        margin-right: 0;
    }

    .green {
        color: #63D29D;
    }
    .red {
        color: #F76C6C;
    }
    .yellow {
        color: #F8B500;
    }
}

.p {
    line-height: 22px;
}
.bold {
    font-weight: bold;
}

.separator {
    display: inline-block;
    width: 1px;
    height: 12px;
    margin: 0 5px;
    background: #E9EAF2;
    vertical-align: middle;
}

.link {
    color: #F89800;
    cursor: pointer;

    &.disabled {
        color: #BABCCC;
        cursor: not-allowed;
        pointer-events: none;
    }
}
.green-tag {
    padding: 2px;
    border: 1px solid #63d29d;
    border-radius: 2px;
    background: rgba(99, 210, 157, 0.05);
}
.red-tag {
    padding: 2px;
    border: 1px solid #f76c6c;
    border-radius: 2px;
    background: rgba(247, 108, 108, 0.05);
}
.yellow-tag {
    padding: 2px;
    border: 1px solid #f8b500;
    border-radius: 2px;
    background: rgba(248, 181, 0, 0.05);
}
.blue-tag {
    padding: 2px;
    border: 1px solid #00abe4;
    border-radius: 2px;
    background: rgba(0, 171, 228, 0.05);
}

.close-button {
    width: auto;
    height: auto;
    margin-left: 10px;
    color: #FAFAFA;
    cursor: pointer;
}
.taitou{
    overflow: auto;
}
</style>
