<template>
    <roo-modal
        :class="$style.modal"
        :value="!!id"
        @input="$emit('input', $event)"
        @close="$emit('close', $event)"
        @hidden="clear"
    >
        <span slot="title">
            资质详情（ ID: {{ qualification ? qualification.id : id }} ）
        </span>

        <div
            v-if="loading"
            key="loading"
            :class="$style.loading"
        >
            <span :class="$style.loadingText">
                正在加载
            </span>
        </div>
        <template v-else>
            <roo-alert
                v-if="alertText && alertText.length > 0"
                key="alert"
                :class="$style.alert"
                :type="alertType"
                :icon="alertIcon"
            >
                <div
                    v-for="(txt, idx) in alertText"
                    :key="idx"
                >
                    {{ txt }}
                </div>
            </roo-alert>

            <ul
                key="infoList"
                :class="$style.infoList"
            >
                <li
                    v-for="row in infoList"
                    :key="row.key"
                >
                    <span :class="$style.label">{{ row.label }}</span>
                    <span :class="$style.text">
                        <template v-if="row.imgs">
                            <a
                                v-for="(url, idx) in row.imgs"
                                :key="url"
                                :href="url"
                                target="_blank"
                            >
                                <img
                                    :src="url"
                                    :alt="`${row.label} (${idx + 1})`"
                                />
                            </a>
                        </template>
                        <template v-if="row.link">
                            <a :href="row.link.url" target="_blank">{{ row.link.text }}</a>
                        </template>
                        <template v-else>
                            {{ row.text }}
                        </template>
                    </span>
                </li>
            </ul>
        </template>

        <template slot="footer">
            <div
                v-if="qualification && qualification.status !== 0"
                key="edit"
                :class="$style.textButton"
                @click="$router.push({ name: 'edit-qualification', query: { id: qualification.id } })"
            >
                修改资质
            </div>
            <roo-button
                @click="$emit('input', false)"
            >
                关闭
            </roo-button>
        </template>
    </roo-modal>
</template>

<script>
import axios from 'axios';
/* eslint-disable import/no-unresolved, import/extensions */
import request from '$invoice/utils/request';

/* eslint-disable import/extensions, import/no-unresolved */
import toastError from '$lib/toast-error';
import { getCookieValue } from '$lib/utils';
import SOURCE from '$lib/sourceEnum';
import SOURCEDESC from '$lib/sourceDesc';
/* eslint-enable import/extensions, import/no-unresolved */

const { CancelToken } = axios;

const REJECT_REASON_MAP = {
    1: '发票抬头公司名称与合同甲方名称不一致，请点击修改资质，下载补充协议模板 http://dpurl.cn/n/5rT1t (请复制地址到电脑端浏览器打开)，打印盖章后重新上传。',
    2: '请上传开票名称一致的营业执照。',
    3: '补充协议原发票抬头与合同甲方名称不一致。',
    4: '请上传开票申请书，点击修改资质，下载开票申请书模板 http://dpurl.cn/n/5rPjm (请复制地址到电脑端浏览器打开)，打印盖章后重新上传。',
    // 5: '其他',
    // 5: '',

    11: '补充协议显示不完整，请重新上传',
    12: '营业执照显示不完整，请重新上传',
    13: '请上传新模版补充协议',
    14: '请上传营业执照',
    15: '补充协议中的原发票抬头、甲方、合同签约甲方名称，三者需一致，请修改',
    16: '补充协议中的新增发票抬头、所上传的营业执照、公司名称，三者需一致，请修改',
    17: '补充协议未盖章，盖章后重新上传（其中：变更原因2和3，需要原抬头和现抬头均盖章）',
    18: '补充协议附件列表无现发票抬头，添加后重新提交',

    21: '请上传开票申请书。',
    22: '开票申请书请盖相应公章。',
    23: '开票申请书发票抬头与公司名称不一致。',
    24: '开票申请书不显示。',
};

const getImgUrl = x => x.imageUrl;

export default {
    name: 'QualificationModal',
    props: {
        id: {
            type: Number,
            default: null,
        },
    },
    data() {
        return {
            loading: false,
            qualification: null,
            accountSource: getCookieValue('source'),
        };
    },
    computed: {
        contractId() {
            return this.$store.state.config.contractId;
        },
        alertType() {
            const { qualification } = this;
            return qualification ? ['warning', null, 'danger'][qualification.status] : null;
        },
        alertIcon() {
            const { qualification } = this;
            return qualification ? ['exclamation-circle', null, 'times-circle'][qualification.status] : null;
        },
        alertText() {
            const { qualification } = this;
            if (qualification) {
                if (qualification.status === 0) {
                    return ['人工审核中'];
                } else if (qualification.status === 3) {
                    return ['系统审核中'];
                } else if (qualification.status === 2) {
                    const { rejectReasons, comment } = qualification;

                    const arr = ['审核驳回，驳回原因：'];

                    (rejectReasons || '').split(',').forEach((code) => {
                        const str = REJECT_REASON_MAP[code];
                        if (str) {
                            arr.push(str);
                        }
                    });

                    if (comment) {
                        arr.push(comment);
                    }

                    return arr;
                }
            }
            return [];
        },
        infoList() {
            const { qualification } = this;
            if (!qualification) {
                return [];
            }

            const arr = [{
                key: 'source',
                label: '来源',
                text: SOURCEDESC[qualification.source],
            }, {
                key: 'partnerId',
                label: `${qualification.partnerTypeName} ID`,
                text: qualification.partnerId,
            }];
            if (`${qualification.source}` === SOURCE.RIDER) {
                arr[0].text = '骑手商城供应商';
            }
            if (`${qualification.source}` === SOURCE.AGENCY_OPERATION) {
                arr[0].text = '服务商管理系统';
            }
            if (`${qualification.source}` === SOURCE.AGENT) {
                arr[0].text = '代欢系统';
            }

            if (qualification.partnerType === 103 || qualification.partnerType === 101) {
                arr.push({
                    key: 'partnerAName',
                    label: '合同签约甲方名称',
                    text: qualification.partnerAName,
                });
            }

            arr.push({
                key: 'partnerName',
                label: '公司名称',
                text: qualification.partnerBName,
            });

            if (qualification.subpoiId) {
                arr.push({
                    key: 'subpoiId',
                    label: '分店 ID',
                    text: qualification.subpoiId,
                });
            }

            const {
                qualificationPicUrls,
                subpoiBusinessLicenseUrls,
                authorizationUrls,
                protocolUrls,
                subpoiConfirmLetterUrls,
                // 新表单展示最终协议附件下载链接
                aPdfUrl,
                cPdfUrl,
            } = qualification;

            if (qualificationPicUrls && qualificationPicUrls.length > 0) {
                arr.push({
                    key: 'qualificationPicUrls',
                    label: '一般纳税人资格扫描件',
                    imgs: qualificationPicUrls.map(getImgUrl),
                });
            }

            if (subpoiBusinessLicenseUrls && subpoiBusinessLicenseUrls.length > 0) {
                arr.push({
                    key: 'subpoiBusinessLicenseUrls',
                    label: '营业执照',
                    imgs: subpoiBusinessLicenseUrls.map(getImgUrl),
                });
            }

            if (aPdfUrl || cPdfUrl) {
                if (aPdfUrl) {
                    arr.push({
                        key: 'aPdfUrl',
                        label: '最终协议(甲方)',
                        link: {
                            url: aPdfUrl,
                            text: '甲方pdf下载地址',
                        },
                    });
                }
                if (cPdfUrl) {
                    arr.push({
                        key: 'cPdfUrl',
                        label: '最终协议(丙方)',
                        link: {
                            url: cPdfUrl,
                            text: '丙方pdf下载地址',
                        },
                    });
                }
            } else if (authorizationUrls && authorizationUrls.length > 0) {
                arr.push({
                    key: 'authorizationUrls',
                    label: '补充协议',
                    imgs: authorizationUrls.map(getImgUrl),
                });
            }

            if (protocolUrls && protocolUrls.length > 0) {
                arr.push({
                    key: 'protocolUrls',
                    label: this.accountSource !== SOURCE.WAIMAI ? '框架合同' : '开票申请书',
                    imgs: protocolUrls.map(getImgUrl),
                });
            }

            if (subpoiConfirmLetterUrls && subpoiConfirmLetterUrls.length > 0) {
                arr.push({
                    key: 'subpoiConfirmLetterUrls',
                    label: '其他附件',
                    imgs: subpoiConfirmLetterUrls.map(getImgUrl),
                });
            }

            return arr;
        },
    },
    watch: {
        id(val) {
            if (val) {
                this.fetchQualification(val).catch(toastError);
            } else {
                if (this.source) {
                    this.source.cancel();
                    this.source = null;
                }
            }
        },
    },
    created() {
        this.source = null;
    },
    methods: {
        fetchQualification(id) {
            if (this.source) {
                this.source.cancel();
                this.source = null;
            }

            if (!id) {
                return Promise.reject(new Error('缺少资质 ID'));
            }

            const params = {
                qualificationId: id,
            };
            if (this.contractId) {
                params.contractId = this.contractId;
            }

            this.source = CancelToken.source();
            const cancelToken = this.source.token;

            this.loading = true;
            return request.get('/finance/invoice/api/common/qualification/r/detail', {
                params,
                cancelToken,
            })
                .then((res) => {
                    const { code, msg, data } = res.data;
                    if (code !== 0) {
                        throw new Error(msg);
                    }

                    this.loading = false;
                    this.qualification = data;
                });
        },
        clear() {
            this.loading = false;
            this.qualification = null;
        },
    },
};
</script>

<style lang="scss" module>
.modal {
    :global(.roo-modal-body) {
        max-height: 400px;
        padding: 20px !important;
        overflow-y: auto;
    }

    &:global(.roo-modal .roo-modal-dialog) {
        width: 500px;
    }
}

.tag {
    display: inline-block;
    width: 20px;
    height: 20px;
    border-radius: 2px;
    border: 1px solid #F89800;
    font-size: 14px;
    line-height: 18px;
    color: #F89800;
    text-align: center;
    vertical-align: text-top;
}

.loading {
    display: table;
    table-layout: fixed;
    width: 100%;
    height: 300px;
}
.loading-text {
    display: table-cell;
    text-align: center;
    vertical-align: middle;
}

.alert {
    margin: 0;
    margin-bottom: 10px;
}

.info-list {
    padding: 0;
    list-style: none;

    li {
        display: flex;
        margin-bottom: 10px;
    }
}
.label {
    display: block;
    flex-shrink: 0;
    width: 140px;
    margin-right: 20px;
    color: #858692;
    text-align: right;
}
.text {
    display: block;
    flex: 1;
    color: #3F4156;

    img {
        display: block;
        max-width: 100%;
    }
}
.text-button {
    display: inline-block;
    margin-right: 16px;
    color: #F89800;
    line-height: 36px;
    vertical-align: middle;
    cursor: pointer;
}
</style>
