<template>
    <table :class="$style.table">
        <col>
        <col>

        <thead>
            <tr>
                <th>月份</th>
                <th>金额</th>
            </tr>
        </thead>

        <tbody>
            <tr
                v-for="item in list"
                :key="item.id"
            >
                <td>{{ item.month }}</td>
                <td>{{ item.moneyCent | formatCent }}</td>
            </tr>
        </tbody>
    </table>
</template>

<script>
/* eslint-disable import/extensions, import/no-unresolved */
import { emptyArray } from '$lib/utils';
import { formatCent } from '$lib/filters';
/* eslint-enable import/extensions, import/no-unresolved */

export default {
    name: 'MonthAmountList',
    filters: {
        formatCent,
    },
    props: {
        list: {
            type: Array,
            default: emptyArray,
        },
    },
};
</script>

<style lang="scss" module>
// 你会看到很多段这样的代码，这是因为表格的对齐、高度总是有细微不同，最重要的是我代码不精、封装无力、架构松散...
.table {
    width: 100%;
    table-layout: fixed;
    border-collapse: collapse;
    border: 1px solid #E9EAF2;
    font-size: 14px;
    line-height: 20px;

    thead,
    tbody tr:nth-of-type(even) {
        background: #F7F8FA;
    }

    th {
        color: #858692;
    }

    th, td {
        height: 40px;
        padding: 0 20px;
        text-align: left;

        &:last-child {
            text-align: right;
        }
    }
}
</style>
