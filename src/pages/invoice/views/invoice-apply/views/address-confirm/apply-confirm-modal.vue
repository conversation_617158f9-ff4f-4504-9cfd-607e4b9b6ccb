<template>
    <roo-modal
        :class="$style.modal"
        :value="value"
        :backdrop="submitting ? 'static' : true"
        size="large"
        @input="$emit('input', $event)"
        @hidden="$emit('hidden')"
    >
        <h4 :class="$style.h4">
            发票基本信息
        </h4>

        <ul :class="$style.infoList">
            <li
                v-for="item in baseInfo"
                :key="item.key"
            >
                <div :class="$style.label">
                    {{ item.label }}
                </div>
                <div :class="$style.text">
                    {{ item.text }}
                </div>
            </li>
        </ul>

        <coupon
            v-for="c in showCoupons"
            v-if="c.subList.length"
            :key="c.id"
            :class="$style.coupon"
        >
            <template slot="header">
                <span :class="$style.title">
                    发票抬头：{{ c.partnerBName }}
                </span>
                <span :class="$style.sum">
                    ￥{{ c.sum | formatMilli }}
                </span>
            </template>

            <ul :class="$style.subList">
                <li
                    v-for="({ no, milli }, idx) in c.subList"
                    :key="idx"
                >
                    <span :class="$style.invoiceName">
                        发票
                        {{ isPublicActivityFee || isChildren || isNatural ? '' : no }}
                    </span>
                    <span :class="$style.invoiceAmount">
                        ￥{{ milli | formatMilli }}
                    </span>
                </li>
            </ul>
        </coupon>

        <template slot="footer">
            <roo-button
                :disabled="submitting"
                type="hollow"
                @click="$emit('input', false)"
            >
                取消
            </roo-button>
            <roo-button
                :loading="submitting"
                @click="preSubmitConfirm"
            >
                确认提交
            </roo-button>
        </template>
    </roo-modal>
</template>

<script>
import { mapState, mapGetters, mapActions } from 'vuex';
import { confirm, alert } from '@roo/roo-vue';

/* eslint-disable import/extensions, import/no-unresolved */
import Coupon from '$components/coupon';


import toastError from '$lib/toast-error';
import { formatCent, formatMilli } from '$lib/filters';
import { InvoiceNameEnum, InvoiceTypeEnum } from '$config/enum';
/* eslint-enable import/extensions, import/no-unresolved */

import { noticeText, secondConfirmText } from './config';

export default {
    name: 'ApplyConfirmModal',
    filters: {
        formatCent,
        formatMilli,
    },
    components: {
        Coupon,
    },
    props: {
        value: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {
            noticeText,
        };
    },
    computed: {
        ...mapState('config', [
            'feeTypeList',
            'contractId',
        ]),
        ...mapState('apply', [
            'baseFeeTypeItem',
            'qualifications',
            'invoiceType',
            'remark',
            'splitAmount',
            'submitting',
        ]),
        ...mapState('config', [
            'isShudianHit',
        ]),
        ...mapState('amountSelect', [
            'dateFrom',
            'dateTo',
            'settleAmountList',
            'monthAmountList',
        ]),
        ...mapState('sendInfo', [
            'receiver',
            'provinceCode',
            'expressProvince',
            'cityCode',
            'expressCity',
            'districtCode',
            'expressDistrict',
            'address',
            'phoneNo',
            'telNo',
            'receiverEmail',
        ]),
        ...mapGetters('amountSelect', [
            'checkedAmountList',
            'checkedMoneyMilli',
            'amountCityNameByMonth',
        ]),
        ...mapGetters('apply', [
            'isPublicActivityInvoice',
            'isElectronInvoice',
            'isPublicActivityFee',
            'isChildren',
            'isNatural',
        ]),

        getConfirmText() {
            const typeMap = {
                isPublicActivityFee: 'NATURE_PROTECT',
                isChildren: 'PLAYGROUND',
                isNatural: 'NATURE_CLEAN',
            };

            const type = Object.keys(typeMap).find(key => this[key]);
            return type ? secondConfirmText[typeMap[type]] : '';
        },
        companyListByCity() {
            return this.splitAmount.map(item => item.companyName).join(',');
        },
        showCoupons() {
            return this.cityCoupons;
        },
        cityCoupons() {
            const splitAmount = this.splitAmount;
            const qualificationMap = {};
            let invoiceNo = 0;
            // 按照资质为key来统计
            for (let companyIndex = 0; companyIndex < splitAmount.length; companyIndex++) {
                const company = splitAmount[companyIndex];
                let qMapItem;
                for (let qualifyIndex = 0; qualifyIndex < company.qualifications.length; qualifyIndex++) {
                    const qualifications = company.qualifications;
                    const key = qualifications[qualifyIndex].id;
                    if (!qualificationMap[key]) qualificationMap[key] = { subList: [] };
                    const amountList = qualifications[qualifyIndex].amountList;
                    const invoiceList = amountList.filter(el => el > 0).map(el => ({
                        milli: el * 10000,
                    }));
                    qualificationMap[key] = {
                        ...qualifications[qualifyIndex],
                        subList: [...qualificationMap[key].subList, ...invoiceList],
                    };
                    qMapItem = qualificationMap[qualifications[qualifyIndex].id];
                    // debugger;
                }
                // 公司数据的最后一个剩余值加到当前的资质上
                if (company.restMilli > 0) {
                    qMapItem.subList = [...qMapItem.subList, {
                        milli: company.restMilli,
                    }];
                }
            }
            const showList = Object.values(qualificationMap);
            // 计算序号
            showList.forEach((el) => {
                el.subList.forEach((e) => {
                    invoiceNo += 1;
                    e.no = invoiceNo;
                });
            });
            // 计算累加值
            showList.forEach((el) => {
                el.sum = el.subList.reduce((pre, item) => pre + parseFloat(item.milli), 0);
            });

            return Object.values(qualificationMap);
        },
        coupons() {
            const { qualifications, checkedMoneyMilli } = this;

            const coupons = [];

            let no = 0;
            let restMilli = checkedMoneyMilli;

            for (let i = 0; i < qualifications.length; ++i) {
                let sum = 0;
                const subList = [];

                const {
                    id,
                    partnerBName,
                    amountList,
                    taxpayerIdNo,
                    registerAddress,
                    telNo,
                    bankName,
                    cardNo,
                } = qualifications[i];

                for (let j = 0; j < amountList.length; ++j) {
                    const milli = parseFloat(amountList[j]) * 10000;

                    if (milli) {
                        no += 1;
                        sum += milli;
                        restMilli -= milli;

                        subList.push({
                            no,
                            milli,
                        });
                    }
                }

                if (i === qualifications.length - 1 && restMilli) {
                    no += 1;
                    sum += restMilli;

                    subList.push({
                        no,
                        milli: restMilli,
                    });
                }

                if (sum > 0) {
                    coupons.push({
                        id,
                        partnerBName,
                        taxpayerIdNo,
                        registerAddress,
                        telNo,
                        bankName,
                        cardNo,
                        sum,
                        subList,
                    });
                }
            }

            return coupons;
        },
        baseInfo() {
            const { baseFeeTypeItem, invoiceType } = this;

            const arr = [{
                key: 'invoice-fee-type',
                label: '明细项目',
                text: baseFeeTypeItem && baseFeeTypeItem.baseFeeTypeName,
            }, {
                key: 'item-name',
                label: '开票项目',
                text: baseFeeTypeItem && baseFeeTypeItem.productionName,
            }, {
                key: 'invoice-type',
                label: '发票种类',
                text: this.isChildren || this.isNatural || this.isPublicActivityFee
                    ? InvoiceNameEnum[invoiceType]
                    : (this.isShudianHit
                        ? (invoiceType === InvoiceTypeEnum.VATSpecial ? '专票' : '普票')
                        : InvoiceNameEnum[invoiceType]
                    ),
            }, {
                key: 'contract-b-name',
                label: '所属公司',
                text: this.companyName(),
            }];

            arr.push({
                key: 'spec',
                label: '规格型号',
                text: baseFeeTypeItem && baseFeeTypeItem.model,
            }, {
                key: 'unit',
                label: '单位',
                text: baseFeeTypeItem && baseFeeTypeItem.unit,
            });
            arr.push({
                key: 'amount',
                label: '数量',
                text: baseFeeTypeItem && baseFeeTypeItem.productionCount,
            });

            if (this.isElectronInvoice) { // 电子发票
                arr.push({
                    key: 'receiver-email',
                    label: '电子邮箱',
                    text: this.receiverEmail,
                });
            } else {
                arr.push({
                    key: 'receiver',
                    label: '收件人',
                    text: this.receiver,
                },
                this.isShudianHit ? {
                    key: 'receiver-email',
                    label: '电子邮箱',
                    text: this.receiverEmail,
                } : null,
                {
                    key: 'address',
                    label: '详细地址',
                    text: `${this.expressProvince} ${this.expressCity} ${this.expressDistrict} ${this.address}`,
                }, {
                    key: 'phoneNo',
                    label: '手机',
                    text: this.phoneNo,
                }, {
                    key: 'telNo',
                    label: '固话',
                    text: this.telNo || '无',
                });
            }

            if (this.dateFrom && this.dateTo) {
                arr.unshift({
                    key: 'date-from',
                    label: '开票起始时间',
                    text: this.dateFrom,
                }, {
                    key: 'date-to',
                    label: '开票结束时间',
                    text: this.dateTo,
                });
            }

            if (!this.isPublicActivityInvoice) {
                arr.push({
                    key: 'remark',
                    label: '票面备注',
                    text: this.remark,
                });
            }

            return arr.filter(item => !!item);
        },
    },
    methods: {
        ...mapActions('apply', [
            'submitApply',
        ]),
        companyName() {
            return this.amountCityNameByMonth;
        },
        preSubmitConfirm() {
            if (!this.isPublicActivityInvoice) return this.handleSubmitClick();
            return confirm(this.getConfirmText, (yes) => {
                if (yes) {
                    this.handleSubmitClick();
                }
            });
        },
        handleSubmitClick() {
            this.submitApply()
                .then(() => {
                    this.$emit('input', false);

                    alert('发票申请已提交，可在“开票历史”中查看详情', () => {
                        this.$store.commit('qualificationSelect/reset');
                        this.$store.commit('amountSelect/reset');
                        this.$store.commit('sendInfo/reset');
                        this.$store.commit('apply/reset');

                        this.$router.push({ name: 'invoice-history' });
                    });
                })
                .catch(toastError);
        },
    },
};
</script>

<style lang="scss" module>
.modal {
    :global(.roo-modal-content) {
        max-height: 700px;
        overflow: auto;
    }
}

.h4 {
    margin-top: 0;
    margin-bottom: 20px;
    font-size: 20px;
}

.info-list {
    list-style: none;

    li {
        display: flex;
        margin-bottom: 10px;
    }
}

.label {
    width: 100px;
    flex-shrink: 0;
    margin-right: 20px;
    color: #858692;
    text-align: right;
}

.text {
    flex: 1;
    color: #3F4156;
}

.icon:global(.roo-icon) {
    height: auto;
}

.sub-list {
    list-style: none;
    margin: 0;
    padding: 0;

    li + li {
        margin-top: 20px;
    }
}

.invoice-name {
    font-size: 14px;
    color: #3F4156;
    line-height: 22px;
    font-weight: bold;
}

.invoice-amount {
    float: right;
}

.coupon {
    margin: 20px 0;
}

.title {
    line-height: 45px;
}

.sum {
    float: right;
    line-height: 45px;
}

.tooltip {
    font-size: 12px;
    color: #666;
    border: solid #e1e1e1 1px;
    margin-bottom: 10px;
    padding: 5px 18px;
}
</style>
