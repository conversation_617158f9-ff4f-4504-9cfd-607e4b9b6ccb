<template>
  <table :class="$style.table">
    <col style="width: 56px;" />
    <col style="min-width: 180px;" />
    <col style="width: 250px;" />
    <col />


    <thead>
      <tr>
        <th>
          <roo-checkbox
            :checked="(!!total) && checkedAmountCount === total"
            :indeterminate="checkedAmountCount > 0 && checkedAmountCount < total"
            @change="$emit('check-all', $event)"
          >
            全选
          </roo-checkbox>
        </th>
        <th style="text-align: center">时间</th>
        <th>金额</th>
        <th></th>
      </tr>
    </thead>

    <tbody>
      <tr
        v-if="loading"
        key="loading"
      >
        <td
          :class="$style.loading"
          colspan="4"
        >
          正在加载
        </td>
      </tr>
      <tr
        v-else-if="tableList.length === 0"
        key="empty"
      >
        <td
          :class="$style.empty"
          colspan="4"
        >
          暂无数据
        </td>
      </tr>
      <template v-else>
        <tr
          v-for="item in tableList"
          :key="item.id"
        >
          <td>
            <roo-checkbox
              :disabled="item.moneyMilli <= 0 || isDisabledByTimeCondition(item)"
              :checked="item.checked"
              @change="$emit('check', { item, checked: $event })"
            />
          </td>
          <td style="text-align: center">
            {{ item.month }}
            <span v-if="isDisabledByTimeCondition(item)" :class="$style.disabledReason">
                <roo-icon
                  name="question2"
                  size="14px"
                />
                <roo-tooltip
                  placement="top"
                >
                  <slot>
                    <div>{{ getDisabledReason(item) }}</div>
                  </slot>
                </roo-tooltip>
              </span>
            </span>
          </td>
          <td>
            <div style="display: flex;">
              ￥{{ item.moneyCent | formatCent }}
              <bubble-comp
                :param="{
                  partnerId: getCookieValue(),
                  dateFrom: getDateFromTo(item.id).from,
                  dateTo: getDateFromTo(item.id).to,
                  baseFeeType: baseFeeTypeItem.baseFeeType,
                  partnerType: baseFeeTypeItem.partnerType,
                  ownerShip: companySelected <= 0 ? null : companySelected,
                  productionName: !productionName ? '' : productionName,
                }"
              />
            </div>
          </td>
          <td></td>
        </tr>
      </template>
    </tbody>
  </table>
</template>

<script>
/* eslint-disable import/extensions, import/no-unresolved */
// eslint-disable-next-line no-unused-vars
import { formatNumberThousand, getCookieValue } from '$lib/utils';
/* eslint-enable import/extensions, import/no-unresolved */
import { mapState, mapGetters } from 'vuex';
import { Bubble } from '@wmfe/svelte-components';
import toVue from 'svelte-adapter/vue';

export default {
  name: 'MonthAmountTable',
  components: {
    BubbleComp: toVue(Bubble),
  },
  filters: {
    formatCent(num) {
      return formatNumberThousand((num / 100).toFixed(2));
    },
  },
  props: {
    loading: {
      type: Boolean,
      default: false,
    },
    list: {
      type: Array,
      default() {
        return [];
      },
    },
    // companyselected: {
    //     type: Number,
    //     default() {
    //         return 0;
    //     },
    // },
  },
  data() {
    return {
      tableList: [],
      partnerId: -1,
      dateFrom: -1,
      dateTo: -1,
      baseFeeType: 1,
      partnerType: 1,
    };
  },
  computed: {
    ...mapState('apply', ['companySelected', 'baseFeeTypeItem']),
    ...mapState('config', ['contractId', 'feeTypeList']),
    ...mapState('amountSelect', ['total']),
    ...mapGetters('amountSelect', ['checkedAmountCount']),
    ...mapGetters('apply', ['productionName', 'isPublicActivityFee', 'isChildren', 'isNatural']),

    currentDate() {
      return new Date();
    },
  },
  watch: {
    list(newVal) {
      if (newVal) {
        this.tableList = this.monthlist(newVal);
      }
    },
    companySelected() {
      this.tableList = this.monthlist(this.list);
    },
  },
  mounted() {
    this.tableList = this.monthlist();
  },
  methods: {
    getCookieValue() {
      return getCookieValue('wmPoiId');
    },
    getDateFromTo(monthId) {
      const cur = new Date(monthId);
      const nextMonth = new Date(cur.getFullYear(), cur.getMonth() + 1, cur.getDate());
      const curLastDay = new Date(nextMonth.getTime() - 1);
      const from = `${cur.getFullYear()}-${cur.getMonth() + 1}-${cur.getDate()}`;
      const to = `${curLastDay.getFullYear()}-${curLastDay.getMonth() + 1}-${curLastDay.getDate()}`;
      return { from, to };
    },
    processAdDetails(item) {
      if (!item.adDetails || !item.adDetails.length) return item;
      
      // 添加时间条件判断标志
      item.isDisabledByTimeCondition = this.isDisabledByTimeCondition(item);
      
      item.moneyCent = item.adDetails
        .filter((el) => el.checked)
        .reduce((pre, el) => pre + el.amountCent, 0);
      item.moneyMilli = item.adDetails
        .filter((el) => el.checked)
        .reduce((pre, el) => pre + el.amountMilli, 0);
      return item;
    },
    processNormalItem(item) {
      // TODO：此代码需要重构，没有使用vuex action去改变状态，导致状态不可追溯
      if (!item.sellerCompanyList || !item.sellerCompanyList.length) {
        return item;
      }

      // 添加时间条件判断标志
      item.isDisabledByTimeCondition = this.isDisabledByTimeCondition(item);

      if (this.companySelected === 0) {
        // 选择全部
        item.moneyCent = item.totalMoneyCent;
        item.moneyMilli = item.totalMoneyMilli;
        item.checked = !item.isDisabledByTimeCondition;
      } else {
        const target = item.sellerCompanyList.find(
          (el) => el.ownerShip === this.companySelected,
        );
        if (!target) {
          item.moneyCent = 0;
          item.moneyMilli = 0;
        }else{
          item.moneyCent = target.moneyCent;
          item.moneyMilli = target.moneyMilli;
        }
        item.checked = (item.moneyCent > 0 || item.moneyCent < 0) && !item.isDisabledByTimeCondition;
      }

      return item;
    },
    monthlist() {
      return this.list.map((item) => {
        if (item.adDetails) {
          return this.processAdDetails(item);
        } else {
          return this.processNormalItem(item);
        }
      });
    },
    notAll(item) {
      if (item.adDetails) {
        return !item.adDetails.every((x) => x.checked);
      }
      return false;
    },
    // 公益开票时间条件判断
    isDisabledByTimeCondition(item) {
      if (!(this.isChildren || this.isNatural || this.isPublicActivityFee)) return false;

      // 从 item.month 解析日期，格式如 "2024年09月"
      const monthStr = item.month;
      if (!monthStr) return false;
      console.log(monthStr,'monthStr')

      // 解析年月
      const matches = monthStr.match(/(\d{4})年(\d{2})月/);
      console.log(matches,'matches')
      if (!matches || matches.length < 3) return false;

      const itemYear = parseInt(matches[1], 10);
      const itemMonth = parseInt(matches[2], 10) - 1; // 月份从0开始

      const currentDate = this.currentDate;
      const currentYear = currentDate.getFullYear();
      const currentMonth = currentDate.getMonth();

      console.log(this.isPublicActivityFee)
      if ((this.isPublicActivityFee || this.isNatural) && itemYear === currentYear - 1) {
        const deadline = new Date(currentYear, 4, 31, 23, 59, 59);
        if (currentDate > deadline) {
          return true;
        }
      }


      // 条件2：最早可申请上一年度发票
      if (itemYear < currentYear - 1) {
        return true;
      }

      // 条件3：不允许开具当月的
      if (itemYear === currentYear && itemMonth === currentMonth) {
        return true;
      }

      return false;
    },
    getDisabledReason(item) {
      if (!(this.isChildren || this.isNatural || this.isPublicActivityFee)) return '';

      // 从 item.month 解析日期，格式如 "2024年09月"
      const monthStr = item.month;
      if (!monthStr) return '';

      // 解析年月
      const matches = monthStr.match(/(\d{4})年(\d{2})月/);
      if (!matches || matches.length < 3) return '';

      const itemYear = parseInt(matches[1], 10);
      const itemMonth = parseInt(matches[2], 10) - 1; // 月份从0开始

      const currentDate = this.currentDate;
      const currentYear = currentDate.getFullYear();
      const currentMonth = currentDate.getMonth();

      
      if (this.isPublicActivityFee || this.isNatural) {
        if (itemYear === currentYear - 1) {
          const deadline = new Date(currentYear, 4, 31, 23, 59, 59);
          if (currentDate > deadline) {
            return '根据基金会要求，上一年度的发票必须在当年5月31日前（含5月31日）申请开具，敬请谅解。';
          }
        }
      }

      if (itemYear < currentYear - 1) {
        return '根据基金会要求，最早可申请上一年度发票，敬请谅解。';
      }

      if (itemYear === currentYear && itemMonth === currentMonth) {
        return '根据基金会要求，不允许开具当月的公益发票，敬请谅解。';
      }

      return '';
    },
  },
};
</script>

<style lang="scss" module>
// 你会看到很多段这样的代码，这是因为表格的对齐、高度总是有细微不同，最重要的是我代码不精、封装无力、架构松散...
.table {
  width: 100%;
  table-layout: fixed;
  border-collapse: collapse;
  border: 1px solid #e9eaf2;
  font-size: 14px;
  line-height: 20px;

  thead,
  tbody tr:nth-of-type(even) {
    background: #f7f8fa;
  }

  th {
    color: #858692;
  }

  th,
  td {
    height: 40px;
    padding: 0 20px;
    text-align: left;

    &:nth-child(1) {
      text-align: center;
    }
    &:nth-child(3) {
      padding-right: 80px;
      // text-align: right;
    }
  }

  :global(.roo-checkbox) {
    display: inline;
  }
  :global(.roo-checkbox .custom-checkbox) {
    margin-right: 0;
  }

  .loading,
  .empty {
    text-align: center;
  }
}

.tag {
  font-size: 12px;
  border: 1px solid #f89800;
  color: #f89800;
}

.disabledReason {
  cursor: pointer;
}
</style>
