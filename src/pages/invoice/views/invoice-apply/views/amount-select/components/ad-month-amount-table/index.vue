<template>
    <table :class="$style.table">
        <col style="width: 56px;">
        <col style="width: 180px;">
        <col style="width: 200px;">
        <col>

        <thead>
            <tr>
                <th>
                    <roo-checkbox
                        :checked="(!!total) && checkedAmountCount === total"
                        :indeterminate="checkedAmountCount > 0 && checkedAmountCount < total"
                        @change="$emit('check-all', $event)"
                    >
                        全选
                    </roo-checkbox>
                </th>
                <th>时间</th>
                <th>金额</th>
                <th>操作</th>
            </tr>
        </thead>

        <tbody>
            <tr
                v-if="loading"
                key="loading"
            >
                <td
                    :class="$style.loading"
                    colspan="4"
                >
                    正在加载
                </td>
            </tr>
            <tr
                v-else-if="tableList.length === 0"
                key="empty"
            >
                <td
                    :class="$style.empty"
                    colspan="4"
                >
                    暂无数据
                </td>
            </tr>
            <template v-else>
                <tr
                    v-for="item in tableList"
                    :key="item.id"
                >
                    <td>
                        <roo-checkbox
                            :disabled="item.moneyMilli <= 0"
                            :checked="item.checked"
                            @change="$emit('check', { item, checked: $event })"
                        />
                    </td>
                    <td>
                        {{ item.month }}
                    </td>
                    <td>
                        ￥{{ item.moneyCent | formatCent }}
                    </td>
                    <td>
                        <a
                            href="#noop"
                            @click.prevent="$emit('select-detail', item)"
                        >选择金额明细</a>
                        <span
                            v-if="notAll(item)"
                            :class="$style.tag"
                        >已选部分明细</span>
                    </td>
                </tr>
            </template>
        </tbody>
    </table>
</template>

<script>
// 带广告流水详情的月汇总金额

/* eslint-disable import/extensions, import/no-unresolved */
import { formatNumberThousand } from '$lib/utils';
/* eslint-enable import/extensions, import/no-unresolved */
import {
    mapState,
    mapGetters,
} from 'vuex';


export default {
    name: 'ADMonthAmountTable',
    filters: {
        formatCent(num) {
            return formatNumberThousand((num / 100).toFixed(2));
        },
    },
    props: {
        loading: {
            type: Boolean,
            default: false,
        },
        list: {
            type: Array,
            default() {
                return [];
            },
        },
    },
    data() {
        return {
            tableList: [],
        };
    },
    computed: {
        ...mapState('amountSelect', [
            'total',
        ]),
        ...mapState('apply', [
            'companySelected',
        ]),
        ...mapGetters('amountSelect', [
            'checkedAmountCount',
        ]),
    },
    watch: {
        list(newVal) {
            if (newVal) {
                this.tableList = this.monthlist();
            }
        },
        companySelected() {
            this.tableList = this.monthlist();
        },
    },
    mounted() {
        this.tableList = this.monthlist();
    },
    methods: {
        processAdDetails(item) {
            if (!item.adDetails || !item.adDetails.length) return item;
            item.moneyCent = item.adDetails.filter(el => el.checked).reduce((pre, el) => pre + el.amountCent, 0);
            item.moneyMilli = item.adDetails.filter(el => el.checked).reduce((pre, el) => pre + el.amountMilli, 0);
            return item;
        },
        processNormalItem(item) {
            if (!item.sellerCompanyList || !item.sellerCompanyList.length) return item;
            if (this.companySelected === 0) { // 选择全部
                item.moneyCent = item.totalMoneyCent;
                item.moneyMilli = item.totalMoneyMilli;
                return item;
            }
            const target = item.sellerCompanyList.find(el => el.ownerShip === this.companySelected);
            if (!target) {
                item.moneyCent = 0;
                item.moneyMilli = 0;
                return item;
            }
            item.moneyCent = target.moneyCent;
            item.moneyMilli = target.moneyMilli;
            return item;
        },
        monthlist() {
            return this.list.map((item) => {
                if (item.adDetails) {
                    return this.processAdDetails(item);
                } else {
                    return this.processNormalItem(item);
                }
            });
        },
        notAll(item) {
            if (item.adDetails) {
                return !item.adDetails.every(x => x.checked);
            }
            return false;
        },
    },
};
</script>

<style lang="scss" module>
// 你会看到很多段这样的代码，这是因为表格的对齐、高度总是有细微不同，最重要的是我代码不精、封装无力、架构松散...
.table {
    width: 100%;
    table-layout: fixed;
    border-collapse: collapse;
    border: 1px solid #E9EAF2;
    font-size: 14px;
    line-height: 20px;

    thead,
    tbody tr:nth-of-type(even) {
        background: #F7F8FA;
    }

    th {
        color: #858692;
    }

    th, td {
        height: 40px;
        padding: 0 20px;
        text-align: left;

        &:nth-child(1) {
            text-align: center;
        }
        &:nth-child(4) {
            padding-right: 80px;
            text-align: right;
        }
    }

    .loading,
    .empty {
        text-align: center;
    }

    :global(.roo-checkbox) {
        display: inline;
    }
    :global(.roo-checkbox .custom-checkbox) {
        margin-right: 0;
    }
}

.tag {
    padding: 0 5px;
    border: 1px solid #F89800;
    border-radius: 2px;
    color: #F89800;
    font-size: 12px;
}
</style>
