<template>
  <div>
    <div :class="$style.module">
      <!-- 青山环保项目 -->
      <roo-alert
        v-if="isPublicActivityFee"
        :class="$style.alert"
        type="warning"
        icon="exclamation-circle"
      >
        <ul>
          <li>为了让贵方的善款能发挥最大的公益价值，节省公益项目成本，累计捐赠超过 50 元人民币可在本系统上直接申请开具公益捐赠票据。请您尽量在捐赠当年申请捐赠票据，申请去年票据请您在每年5月31日前尽早提交。</li>
          <li>如有特别需求，请拨打中华环境保护基金会票据咨询电话：010-67173592 。</li>
        </ul>
      </roo-alert>
    <!-- 零废星球洁净自然 -->
      <roo-alert
        v-if="isNatural"
        :class="$style.alert"
        type="warning"
        icon="exclamation-circle"
      >
        <ul>
          <li>为了让贵方的善款能发挥最大的公益价值，节省公益项目成本，累计捐赠超过 50 元人民币可在本系统上直接申请开具公益捐赠票据。请您尽量在捐赠当年申请捐赠票据，每年5月31日前可申请去年发票。</li>
          <li>如有特别需求，请拨打北京市企业家环保基金会咨询电话：010-57505155。</li>
        </ul>
      </roo-alert>
      <!-- 乡村儿童操场项目 -->
      <roo-alert
        v-if="isChildren"
        :class="$style.alert"
        type="warning"
        icon="exclamation-circle"
      >
        <ul>
          <li>依据深圳壹基金基金会有关财务管理制度与捐赠票据管理制度的规定，请您尽量在捐赠当年申请捐赠票据。</li>
          <li>如有特别需求，请拨打深圳壹基金基金会咨询电话：400-6902700。</li>
        </ul>
      </roo-alert>
      <h3 :class="$style.h3">
        选择开票金额
      </h3>

      <!--按结算设置汇总-->
      <template v-if="accountSource == SOURCE.WAIMAI">
        <div v-if="baseFeeTypeItem && baseFeeTypeItem.amountGatherType === 1">
          <div :class="$style.mb10">
            <roo-date-time-picker
              v-model="startDate"
              :class="$style.input"
              :min-date="minStartDate"
              :max-date="maxStartDate"
              clearable
            />
            -
            <roo-date-time-picker
              v-model="endDate"
              :class="$style.input"
              :min-date="minEndDate"
              :max-date="maxEndDate"
              clearable
            />
            <roo-button
              :loading="querying"
              @click="handleQueryClick"
            >
              查询
            </roo-button>
          </div>
          <div :class="$style.mb10">
            <roo-input
              v-model="idInput"
              :class="[$style.input, $style.mr10]"
              placeholder="请输入结算 ID 或商家 ID"
              @change="searchId = 0"
            />
            <roo-button
              :class="$style.mr10"
              :loading="searching"
              @click="handleSearchSettleId"
            >
              搜索结算 ID
            </roo-button>
            <roo-button
              :loading="searching"
              @click="handleSearchWmPoiId"
            >
              搜索商家 ID
            </roo-button>
          </div>
        </div>
        <div
          :class="$style.companyWrapper"
        >
          <div
            :class="$style.totalWrapper"
          >
            共选中 {{ checkedAmountCount }}/{{ total }} 条
          </div>
          <div
            :class="$style.select"
          >
            <span
              :class="$style.companylabel"
            >
              选择开销方
              <span>
                <roo-icon
                  name="question2"
                  size="18px"
                />
                <roo-tooltip
                  placement="top-right"
                >
                  <slot>
                    <div>{{ compText }}</div>
                  </slot>
                </roo-tooltip>
              </span>
              ：
            </span>
            <roo-select
              :options="companyOptionList"
              :value="companySelected"
              @change="handleSelectCompany"
            />
          </div>
        </div>
      </template>

      <!-- 按照结算设置选择金额 -->
      <settle-amount-table
        v-if="baseFeeTypeItem && baseFeeTypeItem.amountGatherType === 1"
        key="settleAmountList"
        :list="result ? [result] : settleAmountList"
        :target="searchId"
        @select-poi="handleSelectPoi"
        @check="handleCheck"
        @check-all="handleCheckAll"
      />

      <!--  amountGatherType=2 按月汇总  包括 推广费等          -->
      <template v-else-if="baseFeeTypeItem && baseFeeTypeItem.amountGatherType === 2">
        <month-amount-table
          v-if="baseFeeTypeItem.partnerType === 103"
          key="monthAmountList"
          :list="monthAmountList"
          @select-detail="handleSelectDetail"
          @check="handleCheck"
          @check-all="handleCheckAll"
        />
        <ad-month-amount-table
          v-else
          key="adMonthAmountList"
          :list="monthAmountList"
          @select-detail="handleSelectDetail"
          @check="handleCheck"
          @check-all="handleCheckAll"
        />
      </template>

      <div
        v-if="total > 0"
        :class="$style.pagination"
      >
        <roo-button
          v-if="!!result"
          key="back"
          :loading="loading"
          size="mini"
          type="hollow"
          @click="changeResult(null); searchId = 0"
        >
          返回所有可开金额
        </roo-button>
        <roo-button
          v-else-if="amountList.length < total"
          key="next"
          :class="$style.btn"
          :loading="loading"
          size="mini"
          type="hollow"
          @click="loadMore"
        >
          {{ loading ? '加载中' : `查看更多 第 ${nextPageText} 条 (共 ${total} 条)` }}
        </roo-button>
        <span
          v-else
          key="done"
          :class="$style.done"
        >
          已加载完毕
        </span>
      </div>
    </div>

    <div class="footer-nav">
      <div :class="$style.summary">
        <div
          v-if="checkedAmountList.length > 0"
          :class="$style.tip1"
        >
          共选择 <span class="amount">{{ checkedMoneyMilli | formatMilli }}</span> 元
        </div>
        <div
          v-else
          :class="$style.tip1"
        >
          请选择金额
        </div>
      </div>
      <roo-button
        :class="$style.mr16"
        type="brand-hollow"
        @click="handlePrevClick"
      >
        上一步
      </roo-button>
      <roo-button
        :disabled="checkedMoneyMilli <= 0"
        type="brand"
        @click="handleNextClick"
      >
        下一步
      </roo-button>
    </div>

    <ad-detail-modal
      v-model="displayAdDetail"
      :amount="adAmount"
      @hidden="adAmount = null"
    />

    <poi-select-modal
      ref="selectPoiModal"
      v-model="displayPoiSelect"
      :amount="selectPoiAmount"
      @hidden="selectPoiAmount = null"
    />
  </div>
</template>

<script>
import dayjs from 'dayjs';
import {
  mapState,
  mapGetters,
  mapMutations,
  mapActions,
} from 'vuex';
import { toast } from '@roo/roo-vue';

/* eslint-disable import/extensions, import/no-unresolved */
import toastError from '$lib/toast-error';
import { getCookieValue } from '$lib/utils';
import { BaseFeeTypeEnum } from '$config/enum';
import SOURCE from '$lib/sourceEnum';
import { formatCent, formatMilli } from '$lib/filters';
// eslint-disable-next-line camelcase
import { MIN_PUBLIC_ACTIVITY_AMOUNT_Milli } from '$config/constant';
/* eslint-enable import/extensions, import/no-unresolved */

import MonthAmountTable from './components/month-amount-table';
import SettleAmountTable from './components/settle-amount-table';
import AdMonthAmountTable from './components/ad-month-amount-table';

import AdDetailModal from './ad-detail-modal';
import PoiSelectModal from './poi-select-modal';

const AllSelect = 0;
export default {
  name: 'AmountSelect',
  components: {
    MonthAmountTable,
    SettleAmountTable,
    AdMonthAmountTable,

    AdDetailModal,
    PoiSelectModal,
  },
  filters: {
    formatCent,
    formatMilli,
  },
  data() {
    return {
      invoiceFeeTypeInput: null,
      startDate: null,
      endDate: null,
      querying: false,

      idInput: '',
      searchId: 0,

      displayPoiSelect: false,
      selectPoiAmount: null,

      displayAdDetail: false,
      adAmount: null,
      // companySelected: 0,
      showAlert: false,
      accountSource: getCookieValue('source') || SOURCE.WAIMAI,
      SOURCE,
    };
  },
  computed: {
    ...mapGetters('apply', ['isPublicActivityFee', 'isChildren', 'isNatural']),

    isPublicWelfare() {
            return this.isPublicActivityFee || this.isChildren || this.isNatural;
    },
    // 开销方文本，当为平台佣金和配送服务费时，显示另外的文本
    compText() {
      if ([4, 21].indexOf(this.baseFeeTypeItem.baseFeeType) > -1) {
        return '依据合同签署情况，本次申请可能收到多个公司主体开具的发票';
      } else if ([15, 38, 39].indexOf(this.baseFeeTypeItem.baseFeeType) > -1) {
        return '根据您参与的公益项目，公益捐赠票据由对应的公益机构开具';
      } else {
        return '门店推广技术服务由北京三快在线科技有限公司、上海三快智送科技有限公司联合提供，依据您签署的推广协议，可能收到上述公司分别开具的发票';
      }
    },
    wmPoiId() {
      return parseInt(getCookieValue('wmPoiId'), 10) || -1;
    },
    ...mapState('config', ['contractId', 'feeTypeList']),
    ...mapState('apply', [
      'baseFeeTypeItem',
      'qualifications',
      'companySelected',
    ]),
    ...mapState('amountSelect', [
      'dateFrom',
      'dateTo',

      'loading',
      'total',
      'pageNo',
      'pageSize',
      'monthAmountList',
      'settleAmountList',
      'searching',
      'result',
      'hiddenList',
    ]),

    ...mapGetters('apply', ['isPublicActivityFee', 'isChildren', 'isNatural']),
    ...mapGetters('amountSelect', [
      'amountList',
      'checkedAmountList',
      'checkedAmountCount',
      'checkedMoneyCent',
      'checkedMoneyMilli',
    ]),
    companyList() {
      const { sellerCompanyList = [] } = this.baseFeeTypeItem || {};
      return sellerCompanyList;
    },
    companyOptionList() {
      if (!this.companyList.length) return [];
      const options = this.companyList.map((item) => ({
        ...item,
        value: item.ownerShip,
        label: item.companyName,
      }));
      return [
        {
          value: AllSelect,
          label: '全部',
        },
        ...options,
      ];
    },
    minStartDate() {
      // let yearStart = dayjs().startOf('year');
      let today = dayjs().startOf('year').toDate();
      if (this.endDate) {
        today = dayjs(this.endDate).startOf('month').toDate();
      }
      const minDate = new Date(today.getFullYear() - 1, today.getMonth(), today.getDate());
      return minDate;
      // if (this.isPublicActivityFee || this.isNatural) {
        // if (this.endDate) {
        //   yearStart = dayjs(this.endDate).startOf('year');
        // }
        // return yearStart.toDate();
      // }
      // if (this.isChildren) {
        // const curMonth = dayjs().month();
        // const yearAgo = dayjs().year() - 1;
        // const December = dayjs(`${yearAgo}-12-01`);
        // if (curMonth === 0) {
        //   return December.toDate();
        // } else {
        //   if (this.endDate) {
        //     yearStart = dayjs(this.endDate).startOf('year');
        //   }
        // return yearStart.toDate();
        // }
      // }
      // return null;
    },
    maxStartDate() {
      return this.endDate || null;
    },

    minEndDate() {
      return this.startDate || null;
    },
    maxEndDate() {
      // return this.startDate ? this.startDate : this.maxStartDate;

      const yesterday = dayjs();
      // if (this.isPublicActivityFee || this.isNatural) {
        if (this.startDate) {
          const yearEnd = dayjs(this.startDate).endOf('year');

          if (yesterday.isAfter(yearEnd)) {
            return yearEnd.toDate();
          }
        }

        return yesterday.toDate();
      // }
      // return yesterday.toDate();
    },

    nextPageStart() {
      const { pageNo, pageSize } = this;
      return pageNo * pageSize + 1;
    },
    nextPageEnd() {
      const { pageNo, pageSize, total } = this;
      return Math.min(total, (pageNo + 1) * pageSize);
    },
    nextPageText() {
      const { nextPageStart, nextPageEnd } = this;
      if (nextPageStart === nextPageEnd) {
        return `${nextPageStart}`;
      }
      return `${nextPageStart} ~ ${nextPageEnd}`;
    },
    allMoneyMilli() {
      return this.amountList.reduce((acc, cur) => acc + cur.moneyMilli, 0);
    },
  },
  created() {
    const { baseFeeTypeItem, qualifications, monthAmountList } = this;
    // 进入时检查 qualification
    if (!(baseFeeTypeItem && qualifications && qualifications.length > 0)) {
      this.$router.go(-1);
      return;
    }

    if (
      baseFeeTypeItem.amountGatherType === 2
      && monthAmountList.length === 0
    ) {
      // 按月汇总的全部请求,按照结算设置的需要选择时间之后再请求
      this.handleQueryClick();
    }
  },
  mounted() {
    if (this.dateFrom && this.dateTo) {
      this.startDate = new Date(this.dateFrom);
      this.endDate = new Date(this.dateTo);
    }
  },
  methods: {
    ...mapMutations('apply', ['changCompanySelected']),
    ...mapMutations('apply', ['changeBaseFeeTypeItem', 'changeQualifications']),
    handleIconClick() {
      this.showAlert = !this.showAlert;
    },
    handleSelectCompany(option) {
      this.changCompanySelected(option);
      // 把所有的子项的adDetail poiList都初始化
      this.initAmountList();
    },
    ...mapMutations('amountSelect', [
      'changeInvoiceFeeType',
      'changeDateFrom',
      'changeDateTo',

      'checkItem',
      'checkAllAmount',

      'changeResult',
    ]),
    ...mapActions('amountSelect', [
      'fetchBaseItem',
      'fetchAmountList',
      'searchSettleAmount',
      'initAmountList',
    ]),
    ...mapActions('apply', ['updateSellerCompany']),

    handleQueryClick() {
      const { baseFeeTypeItem } = this;
      if (baseFeeTypeItem && baseFeeTypeItem.amountGatherType === 1) {
        if (!this.startDate) {
          toast.warn('请选择开始日期');
          return;
        }
        if (!this.endDate) {
          toast.warn('请选择结束日期');
          return;
        }
        this.changeDateFrom(dayjs(this.startDate).format('YYYY-MM-DD'));
        this.changeDateTo(dayjs(this.endDate).format('YYYY-MM-DD'));
      } else {
        this.changeDateFrom(null);
        this.changeDateTo(null);
      }

      this.changeResult(null);
      this.idInput = '';
      this.searchId = 0;

      this.querying = true;
      this.fetchAmountList(1)
        .catch(toastError)
        .then(() => {
          this.querying = false;
        });
    },

    searchCheck() {
      const { baseFeeTypeItem, dateFrom, dateTo } = this;

      if (!(baseFeeTypeItem && dateFrom && dateTo)) {
        // toast.warn('查询后才能使用搜索功能');
        return false;
      }

      return true;
    },

    handleSearchSettleId() {
      if (!this.searchCheck()) {
        return;
      }

      this.changeResult(null);
      this.searchId = 0;

      const settleId = parseInt(this.idInput, 10);

      if (!settleId) {
        toast.error('请输入正确的结算设置 ID');
        return;
      }

      this.idInput = `${settleId}`;
      this.searchId = settleId;

      this.searchSettleAmount({
        settleId,
      }).catch(toastError);
    },

    openPoiSelectModal(target, wmPoiId) {
      this.displayPoiSelect = true;
      this.selectPoiAmount = target;

      setTimeout(() => {
        if (this.$refs.selectPoiModal) {
          this.$refs.selectPoiModal.idInput = `${wmPoiId}`;
          this.$refs.selectPoiModal.handleSearch();
        }
      }, 100);
    },

    handleSearchWmPoiId() {
      if (!this.searchCheck()) {
        return;
      }

      this.changeResult(null);
      this.searchId = 0;

      const wmPoiId = parseInt(this.idInput, 10);

      if (!wmPoiId) {
        toast.error('请输入正确的门店 ID');
        return;
      }

      this.idInput = `${wmPoiId}`;
      // this.searchId = wmPoiId;

      this.searchSettleAmount({
        poiId: wmPoiId,
      })
        .then((result) => {
          this.searchId = result.settleId;
          this.openPoiSelectModal(result, wmPoiId);
        })
        .catch(toastError);
    },

    handleCheckAll(checked) {
      this.checkAllAmount(checked);
    },

    handleCheck(event) {
      this.checkItem(event);
    },

    handleSelectPoi(amount) {
      this.selectPoiAmount = amount;
      this.displayPoiSelect = true;
    },

    handleSelectDetail(amount) {
      this.adAmount = amount;
      this.displayAdDetail = true;
    },

    loadMore() {
      this.fetchAmountList(this.pageNo + 1).catch(toastError);
    },

    handlePrevClick() {
      this.$router.go(-1);
    },
    handleNextClick() {
      // 公益开票时检查是否跨年
      if (this.isPublicWelfare) {
        const years = new Set();
        this.checkedAmountList.forEach(item => {
            const year = parseInt(item.month.substring(0, 4), 10);
            years.add(year);
        });
        console.log(years)
        
        // 如果年份集合大于1，说明存在跨年数据
        if (years.size > 1) {
            toast.warn('根据基金会要求，发票的时间必须在同一年，请重新选择');
            return;
        }
      }
      const isPublicInvoice = this.baseFeeTypeItem.baseFeeType === BaseFeeTypeEnum.PublicActivity;
      const isNatural = this.baseFeeTypeItem.baseFeeType === BaseFeeTypeEnum.Natural;
      // eslint-disable-next-line camelcase
      const isSufficeAmount = this.checkedMoneyMilli >= MIN_PUBLIC_ACTIVITY_AMOUNT_Milli;
      if ((isPublicInvoice && !isSufficeAmount) || (isNatural && !isSufficeAmount)) {
        // eslint-disable-next-line camelcase
        toast.error(
          // eslint-disable-next-line camelcase
          `申请金额必须大于等于${MIN_PUBLIC_ACTIVITY_AMOUNT_Milli / 10000}元，请重新提交`,
        );
        return;
      }
      this.updateSellerCompany();

      this.$router.push({ name: 'amount-split' });
    },
  },
};
</script>

<style lang="scss" module>
.module {
  margin: 30px 40px;
}

.h3 {
  margin-top: 0;
  margin-bottom: 20px;
  font-size: 20px;
}
.hidden {
  display: none;
}
.companyWrapper {
  margin: 20px 0 10px;
  display: flex;
  justify-content: space-between;
}
.totalWrapper {
  height: 36px;
  line-height: 36px;
}
.select {
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  .companylabel {
    display: inline-block;
    height: 36px;
    line-height: 36px;
  }
  .roo-select {
    width: 150px;
  }
}
.action-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-left: 20px;
  margin: 20px 0;
}

.checkbox {
  color: #858692;
}

.label {
  display: inline-block;
  width: 70px;
  margin-right: 10px;
  line-height: 36px;
  text-align: right;
  vertical-align: middle;
}

.input {
  display: inline-block;
  width: 200px;
  vertical-align: middle;
}

.radio-group {
  display: inline-block;
  line-height: 1;
  vertical-align: middle;
}

.mr10 {
  margin-right: 8px;
}
.mb10 {
  margin-bottom: 10px;
}

.alert {
  margin: 20px 0;

  &:global(.roo-alert) i {
    left: 10px;
  }

  ul {
    list-style: decimal;
  }

  li {
    padding-left: 20px;
  }
}

.pagination {
  margin-top: 20px;
  text-align: center;
}
.btn {
  min-width: 240px;
}
.done {
  font-size: 14px;
  color: #a0a0a0;
}

.summary {
  display: inline-block;
  margin-right: 16px;
  vertical-align: middle;
}
.tip1 {
  font-size: 14px;
  line-height: 20px;
}
.amount {
  color: #f89800;
}
.tip2 {
  font-size: 12px;
  color: #858692;
  line-height: 16px;
}

.mr16 {
  margin-right: 16px;
}
</style>
