<template>
    <roo-modal
        :value="value"
        size="large"
        @input="$emit('input', $event)"
        @hidden="handleHidden"
    >
        <template slot="title">
            {{ amount && amount.accountName }}
            <span
                :class="[$style.tip, $style.gray]"
            >所属结算设置 ID:</span>
            <span :class="$style.tip">{{ amount && amount.settleId }}</span>
        </template>

        <div :class="$style.row">
            <div :class="$style.checkbox">
                <roo-checkbox
                    :checked="(!!total) && checkedCount === total"
                    :indeterminate="checkedCount > 0 && checkedCount < total"
                    @change="handleCheckAll"
                >
                    全选所有（共 {{ total }} 条，已选 {{ checkedCount }} 条）
                </roo-checkbox>
            </div>
            <div>
                <roo-input
                    v-model="idInput"
                    :class="$style.input"
                    placeholder="请输入商家 ID"
                    clearable
                    @change="searchPoiId = ''"
                />
                <roo-button @click="handleSearch">
                    搜索
                </roo-button>
            </div>
        </div>

        <table :class="$style.table">
            <col style="width: 56px;" />
            <col style="width: 150px;" />
            <col />
            <col style="width: 80px;" />

            <thead>
                <tr>
                    <th></th>
                    <th>商家 ID</th>
                    <th>商家名称</th>
                    <th>明细</th>
                </tr>
            </thead>

            <tbody>
                <tr
                    v-if="loading"
                    key="loading"
                >
                    <td
                        :class="$style.loading"
                        colspan="4"
                    >
                        正在加载
                    </td>
                </tr>
                <tr
                    v-else-if="list.length === 0"
                    key="empty"
                >
                    <td
                        :class="$style.empty"
                        colspan="4"
                    >
                        暂无数据
                    </td>
                </tr>
                <template v-else>
                    <tr
                        v-for="item in list"
                        :key="item.id"
                        :class="`${item.id}` === searchPoiId ? $style.active : null"
                    >
                        <td>
                            <roo-checkbox
                                :checked="item.checked"
                                @change="item.checked = $event"
                            />
                        </td>
                        <td>{{ item.id }}</td>
                        <td>{{ item.name }}</td>
                        <td>
                            <!-- 新开发票 - 明细弹窗 -->
                            <bubble-comp
                                :param="{
                                    partnerId: item.id,
                                    dateFrom: dateFrom,
                                    dateTo: dateTo,
                                    baseFeeType:baseFeeTypeItem.baseFeeType,
                                    partnerType: baseFeeTypeItem.partnerType,
                                    settleId: amount.settleId,
                                    ownerShip: companySelected <= 0 ? null : companySelected,
                                    productionName: !productionName ? '' : productionName,
                                }"
                            />
                        </td>
                    </tr>
                </template>
            </tbody>
        </table>

        <div
            v-if="total > 0"
            :class="$style.pagination"
        >
            <roo-pagination
                :total="total"
                :page-size="pageSize"
                :current-page="pageNo"
                @current-change="pageNo = $event"
            />
        </div>

        <template slot="footer">
            <roo-button
                :disabled="saving"
                type="hollow"
                @click="handleCancel"
            >
                取消
            </roo-button>
            <roo-button
                :loading="saving"
                @click="handleConfirm"
            >
                确定
            </roo-button>
        </template>
    </roo-modal>
</template>

<script>
import axios from 'axios';
/* eslint-disable import/no-unresolved, import/extensions */
import request from '$invoice/utils/request';
import { mapState, mapMutations, mapActions, mapGetters } from 'vuex';
import { toast } from '@roo/roo-vue';
import { Bubble } from '@wmfe/svelte-components';
import toVue from 'svelte-adapter/vue';
/* eslint-disable import/extensions, import/no-unresolved */
import toastError from '$lib/toast-error';
/* eslint-enable import/extensions, import/no-unresolved */

import { copyArray, diffChecked } from './lib/utils';

const { CancelToken } = axios;

function poiMapper(poi) {
    return {
        checked: true,
        id: poi.wmPoiId,
        name: poi.wmPoiName,
    };
}

export default {
    name: 'PoiSelectModal',
    components: {
        BubbleComp: toVue(Bubble),
    },
    props: {
        value: {
            type: Boolean,
            default: false,
        },
        amount: {
            // 结算设置 { settleId, accountName, poiList }
            type: Object,
            default: null,
        },
    },
    data() {
        return {
            loading: false,

            idInput: null,
            searchPoiId: '',

            poiList: [],
            pageNo: 1,
            pageSize: 10,

            saving: false,
        };
    },
    computed: {
        ...mapState('apply', ['baseFeeTypeItem', 'companySelected']),
        ...mapState('amountSelect', ['dateFrom', 'dateTo']),
        ...mapGetters('apply', ['productionName']),
        total() {
            return this.poiList.length;
        },
        list() {
            const { poiList, pageNo, pageSize } = this;
            const offset = (pageNo - 1) * pageSize;
            return poiList.slice(offset, offset + pageSize);
        },
        checkedCount() {
            return this.poiList.filter((x) => x.checked).length;
        },
    },
    watch: {
        value(val) {
            if (!val && this.source) {
                this.source.cancel();
                this.source = null;
            }
        },
        amount(obj) {
            if (obj) {
                const { settleId, poiList } = obj;
                if (poiList) {
                    this.poiList = copyArray(poiList); // 未点确定之前使用本地副本
                } else {
                    this.fetchPoiList(settleId);
                }
            }
        },
    },
    created() {
        this.source = null;
    },
    mounted() {},
    methods: {
        ...mapMutations('amountSelect', [
            'setPoiList',
            'setAmount',
            'setPoiAmountList',
        ]),
        ...mapActions('amountSelect', ['getBaseFeeType']),
        fetchPoiList(settleId) {
            if (this.source) {
                this.source = null;
                this.source.cancel();
            }

            this.source = CancelToken.source();
            const cancelToken = this.source.token;

            this.loading = true;
            return request.get('/finance/invoice/api/pc/application/r/queryPoiList', {
                params: {
                    settleId,
                    dateFrom: this.dateFrom,
                    dateTo: this.dateTo,
                },
                cancelToken,
            })
                .then(
                    (res) => {
                        this.source = null;
                        this.loading = false;

                        const { code, msg, data } = res.data;
                        if (code !== 0) {
                            throw new Error(msg);
                        }

                        this.poiList = (data || []).map(poiMapper);
                    },
                    (err) => {
                        this.source = null;
                        this.loading = false;
                        throw err;
                    },
                )
                .catch(toastError);
        },
        handleCheckAll(checked) {
            this.poiList.forEach((poi) => {
                poi.checked = checked;
            });
        },
        handleSearch() {
            const id = this.idInput.trim();
            this.searchPoiId = id;

            const idx = this.poiList.findIndex((poi) => `${poi.id}` === id);
            if (idx > -1) {
                this.pageNo = Math.ceil((idx + 1) / 10);
            } else {
                toast.warn('查询的门店不在此结算设置下');
            }
        },
        handleCancel() {
            this.$emit('input', false);
        },
        handleConfirm() {
            const { amount, poiList, baseFeeTypeItem } = this;
            // 勾选项没有发生变化
            if (amount && !diffChecked(amount.poiList, poiList)) {
                if (!amount.poiList) {
                    // todo ??? 既然没有变化,为什么需要setPoiList  中间还可能丢失?
                    this.setPoiList({
                        amount,
                        poiList: copyArray(poiList),
                    });
                }
                this.$emit('input', false);
                return;
            }

            if (this.saving) {
                return;
            }

            const params = {
                settleId: amount.settleId,
                baseFeeType: baseFeeTypeItem.baseFeeType,
                partnerType: baseFeeTypeItem.partnerType,
                poiIdList: poiList.filter((x) => x.checked).map((x) => x.id),
                dateFrom: this.dateFrom,
                dateTo: this.dateTo,
            };

            if (this.companySelected !== 0) {
                // 选择具体的公司了,重新计算选择金额的时候带上当前选择的公司
                params.ownerShip = this.companySelected;
            }
            this.saving = true;
            request.post('/finance/invoice/api/pc/application/r/selectPois', params)
                .then((res) => {
                    this.saving = false;

                    const { code, msg, data } = res.data;
                    if (code !== 0) {
                        throw new Error(msg);
                    }

                    this.setPoiList({
                        amount,
                        poiList: copyArray(poiList),
                    });

                    this.setPoiAmountList({
                        amount,
                        poiAmountList: data.sellerCompanyList || [],
                    });

                    this.setAmount({
                        amount,
                        moneyCent: data.moneyCent,
                        moneyMilli: data.moneyMilli,
                    });
                    this.$emit('input', false);
                })
                .catch((err) => {
                    this.saving = false;
                    toastError(err);
                });
        },
        handleHidden(event) {
            this.loading = false;
            this.idInput = '';
            this.searchPoiId = '';
            this.poiList = [];
            this.pageNo = 1;
            this.$emit('hidden', event);
        },
    },
};
</script>

<style lang="scss" module>
.tip {
  font-weight: normal;
  font-size: 14px;
}

.gray {
  color: #858692;
}

.row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.checkbox {
  padding-left: 20px;
  line-height: 1;
}

.input {
  display: inline-block;
  width: 200px;
  vertical-align: middle;
}

.table {
  width: 100%;
  border: 1px solid #e9eaf2;
  table-layout: fixed;
  border-collapse: collapse;
  color: #3f4156;

  // thead 设置一个背景色相同的 bottom-border
  // 防止 active 样式多出边框引起高度变化
  thead {
    border-bottom: 1px solid #f7f8fa;
  }

  thead,
  tbody tr:nth-of-type(even) {
    background: #f7f8fa;
  }

  th,
  td {
    height: 40px;
    padding: 0 20px;
    text-align: left;
  }

  th {
    font-weight: normal;
    color: #858692;
  }

  tr.active {
    border: 1px solid #f8b500;
  }

  :global(.roo-checkbox) {
    display: inline;
  }

  :global(.roo-checkbox .custom-checkbox) {
    margin-right: 0;
  }

  .loading,
  .empty {
    text-align: center;
  }
}

.pagination {
  text-align: center;
}
</style>
