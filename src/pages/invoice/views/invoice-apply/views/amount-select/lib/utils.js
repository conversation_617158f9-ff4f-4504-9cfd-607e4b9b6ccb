
export function copyArray(arr) {
    return arr.map(item => ({ ...item }));
}

/**
 * 检查两个 checked 列表 勾选项是否相同 (undefined/null 视为全勾中)
 * @param {Array|null} a
 * @param {Array|null} b
 * @return {boolean} true 不同 false 相同
 */
export function diffChecked(a, b) {
    if (a === b) {
        return false;
    }

    if (!a && b) {
        return !b.every(x => x.checked);
    }
    if (!b && a) {
        return !a.every(x => x.checked);
    }

    const idSet = new Set();
    a.forEach(x => x.checked && idSet.add(x.id));
    for (let i = 0; i < b.length; ++i) {
        const x = b[i];
        if ((x.checked && !idSet.has(x.id)) || (!x.checked && idSet.has(x.id))) {
            return true;
        }
    }
    idSet.clear();
    b.forEach(x => x.checked && idSet.add(x.id));
    for (let i = 0; i < a.length; ++i) {
        const x = a[i];
        if ((x.checked && !idSet.has(x.id)) || (!x.checked && idSet.has(x.id))) {
            return true;
        }
    }
    return false;
}
