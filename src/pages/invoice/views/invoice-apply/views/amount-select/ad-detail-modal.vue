<template>
    <roo-modal
        :value="value"
        :title="amount && amount.month"
        size="large"
        @input="$emit('input', $event)"
        @hidden="handleHidden"
    >
        <div :class="$style.row">
            <roo-checkbox
                :checked="(!!total) && checkedCount === total"
                :indeterminate="checkedCount > 0 && checkedCount < total"
                @change="handleCheckAll"
            >
                全选所有
                当前选中 {{ checkedAmountMilli | formatMilli }} 元
            </roo-checkbox>
        </div>

        <table :class="$style.table">
            <col style="width: 50px;" />
            <col style="width: 180px;" />
            <col style="width: 180px;" />
            <col style="width: 150px;" />
            <col />

            <thead>
                <tr>
                    <th></th>
                    <th>流水号</th>
                    <th>交易完成时间</th>
                    <th>金额 (元)</th>
                    <th></th>
                </tr>
            </thead>

            <tbody>
                <tr
                    v-if="loading"
                    key="loading"
                >
                    <td
                        :class="$style.loading"
                        colspan="5"
                    >
                        正在加载
                    </td>
                </tr>
                <tr
                    v-else-if="list.length === 0"
                    key="empty"
                >
                    <td
                        :class="$style.empty"
                        colspan="5"
                    >
                        暂无数据
                    </td>
                </tr>
                <template v-else>
                    <tr
                        v-for="item in list"
                        :key="item.id"
                    >
                        <td>
                            <roo-checkbox
                                :disabled="item.amountMilli <= 0"
                                :checked="item.checked"
                                @change="item.checked = $event"
                            />
                        </td>
                        <td>
                            {{ item.outId }}
                        </td>
                        <td>
                            {{ item.confirmTime }}
                        </td>
                        <td>
                            {{ item.amountCent | formatCent }}
                        </td>
                        <td></td>
                    </tr>
                </template>
            </tbody>
        </table>

        <div
            v-if="total > 0"
            :class="$style.pagination"
        >
            <roo-pagination
                :total="total"
                :page-size="pageSize"
                :current-page="pageNo"
                @current-change="pageNo = $event"
            />
        </div>

        <template slot="footer">
            <roo-button
                type="hollow"
                @click="$emit('input', false)"
            >
                取消
            </roo-button>
            <roo-button
                type="brand"
                @click="handleConfirmClick"
            >
                确定
            </roo-button>
        </template>
    </roo-modal>
</template>

<script>
import axios from 'axios';
/* eslint-disable import/no-unresolved, import/extensions */
import request from '$invoice/utils/request';
import { mapState, mapMutations, mapGetters } from 'vuex';

/* eslint-disable import/extensions, import/no-unresolved */
import toastError from '$lib/toast-error';
import { formatNumberThousand } from '$lib/utils';
/* eslint-enable import/extensions, import/no-unresolved */

import { copyArray, diffChecked } from './lib/utils';


const { CancelToken } = axios;

export default {
    name: 'AdDetailModal',
    filters: {
        formatCent(num) {
            return formatNumberThousand((num / 100).toFixed(2));
        },
        formatMilli(num) {
            return formatNumberThousand((num / 10000).toFixed(2));
        },
    },
    props: {
        value: {
            type: Boolean,
            default: false,
        },
        amount: {
            type: Object,
            default: null,
        },
    },
    data() {
        return {
            loading: false,

            pageNo: 1,
            pageSize: 10,
            detailList: [], // 本地副本
        };
    },
    computed: {
        ...mapState('apply', [
            'baseFeeTypeItem',
            'companySelected',
        ]),
        ...mapGetters('apply', ['productionName']),
        total() {
            return this.detailList.length;
        },
        list() {
            const { pageNo, pageSize, detailList } = this;
            const idx = (pageNo - 1) * pageSize;
            return detailList.slice(idx, idx + pageSize);
        },
        checkedList() {
            return this.detailList.filter(x => x.checked);
        },
        checkedCount() {
            return this.checkedList.length;
        },
        checkedAmountCent() {
            return this.checkedList.reduce((acc, cur) => acc + cur.amountCent, 0);
        },
        checkedAmountMilli() {
            return this.checkedList.reduce((acc, cur) => acc + cur.amountMilli, 0);
        },
    },
    watch: {
        value(val) {
            if (!val && this.source) {
                this.source.cancel();
                this.source = null;
            }
        },
        amount(obj) {
            if (obj) {
                const { id, adDetails } = obj;
                if (adDetails) {
                    this.detailList = copyArray(adDetails); // 未点确定之前使用本地副本
                } else {
                    this.fetchAdDetail(id);
                }
            }
        },
    },
    created() {
        this.source = null;
    },
    methods: {
        ...mapMutations('amountSelect', [
            'setAdDetails',
            'setAmount',
        ]),
        fetchAdDetail(monthId) {
            if (this.source) {
                this.source.cancel();
                this.source = null;
            }

            const { baseFeeType, partnerType } = this.baseFeeTypeItem;
            const params = {
                monthId,
                baseFeeType,
                partnerType,
                isInvoiceHistory: true,
            };
            if (this.companySelected !== 0) {
                params.ownerShip = this.companySelected;
                params.productionName = this.productionName;
            }

            this.source = CancelToken.source();
            const cancelToken = this.source.token;

            this.loading = true;
            return request.get('/finance/invoice/api/pc/application/r/adDetails', {
                params,
                cancelToken,
            }).then((res) => {
                this.loading = false;

                const { code, msg, data } = res.data;
                if (code !== 0) {
                    throw new Error(msg);
                }

                this.detailList = (data || []).map(item => ({
                    checked: true,
                    ...item,
                }));
            }, (err) => {
                this.loading = false;
                throw err;
            }).catch(toastError);
        },

        handleCheckAll(checked) {
            this.detailList.forEach((item) => {
                if (item.amountMilli > 0) {
                    item.checked = checked;
                }
            });
        },

        handleConfirmClick() {
            const { amount, detailList } = this;
            // 勾选项没有发生变化
            if (amount && !diffChecked(amount.adDetails, detailList)) {
                if (!amount.adDetails) {
                    this.setAdDetails({
                        amount,
                        adDetails: copyArray(detailList),
                    });
                }
                this.$emit('input', false);
                return;
            }

            this.setAdDetails({
                amount,
                adDetails: copyArray(detailList),
            });
            this.setAmount({
                amount,
                moneyCent: this.checkedAmountCent,
                moneyMilli: this.checkedAmountMilli,
            });
            this.$emit('input', false);
        },

        handleHidden(event) {
            this.loading = false;
            this.pageNo = 1;
            this.detailList = [];
            this.$emit('hidden', event);
        },
    },
};
</script>

<style lang="scss" module>
.row {
    padding-left: 20px;
    margin-bottom: 20px;
}

.table {
    width: 100%;
    border: 1px solid #E9EAF2;
    table-layout: fixed;
    border-collapse: collapse;
    color: #3F4156;

    thead,
    tbody tr:nth-of-type(even) {
        background: #F7F8FA;
    }

    th, td {
        height: 40px;
        padding: 0 20px;
        text-align: left;

        &:nth-child(4) {
            text-align: right;
        }
    }

    th {
        font-weight: normal;
        color: #858692;
    }

    :global(.roo-checkbox) {
        display: inline;
    }
    :global(.roo-checkbox .custom-checkbox) {
        margin-right: 0;
    }

    .loading, .empty {
        text-align: center;
    }
}

.pagination {
    text-align: center;
}
</style>
