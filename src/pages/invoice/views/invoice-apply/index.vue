<template>
    <div :class="$style.container">
        <div :class="$style.steps">
            <roo-steps
                :steps="steps"
                :active="active"
                text-position="right"
            />
        </div>

        <router-view />
    </div>
</template>

<script>
/* eslint-disable import/extensions, import/no-unresolved */
import toastError from '$lib/toast-error';
import { mapActions, mapState } from 'vuex';

export default {
    name: 'InvoiceApply',
    data() {
        return {};
    },
    computed: {
        ...mapState('config', ['isSinglePoi']),
        steps() {
            return [
                '选择发票抬头',
                '选择开票金额',
                '分配金额',
                '填写票种信息',
                '填写地址',
            ];
        },
        active() {
            const { matched } = this.$route;
            if (matched.length > 0) {
                const route = matched[matched.length - 1];
                let activeName = route.name;
                if (activeName === 'amount-select-new') {
                    activeName = 'amount-select';
                }
                if (activeName === 'amount-select-new-v2') {
                    activeName = 'amount-select';
                }
                if (activeName === 'qualification-select-update') {
                    activeName = 'qualification-select';
                }
                if (activeName === 'invoice-type-confirm-update') {
                    activeName = 'invoice-type-confirm';
                }
                if (activeName === 'address-confirm-new') {
                    activeName = 'address-confirm';
                }
                return ['qualification-select', 'amount-select', 'amount-split', 'invoice-type-confirm', 'address-confirm'].indexOf(activeName) + 1;
            }
            return 0;
        },
    },
    watch: {
        active(val) {
            if (val === 0) {
                if (!this.isSinglePoi) {
                    this.$router.replace({ name: 'qualification-select-update' });
                } else {
                    this.$router.replace({ name: 'qualification-select' });
                }
            }
        },
    },
    mounted() {
        if (this.active === 0) {
            if (!this.isSinglePoi) {
                this.$router.replace({ name: 'qualification-select-update' });
                this.fetchTitleInfoList(1).catch(toastError);
            } else {
                this.$router.replace({ name: 'qualification-select' });
                this.fetchQualificationList().catch(toastError);
            }
        }
    },
    methods: {
        ...mapActions('qualificationSelect', ['fetchQualificationList']),
        ...mapActions('invoiceTitle', ['fetchTitleInfoList']),
    },
};
</script>

<style module>
.container {
    min-height: 100%;
    padding-top: 70px;
    padding-bottom: 56px;
    background: #FFF;
}

.steps {
    margin: 30px 40px;
}
</style>
