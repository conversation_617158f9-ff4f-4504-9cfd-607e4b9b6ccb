<template>
    <div :class="$style.container">
        <div :class="$style.scrollView">
            <div>
                <roo-breadcrumb
                    :items="breadcrumb"
                />
            </div>

            <div
                :class="$style.card"
                :style="{ 'min-height': minHeight ? `${minHeight}px` : null }"
            >
                <!-- 新表单header -->
                <div v-if="signingWorkflow" :class="$style.headerRow">
                    <h3 :class="$style.h3">
                        修改抬头
                    </h3>
                </div>

                <!-- 旧表单header -->
                <div v-else>
                    <h3>修改资质</h3>
                </div>

                <roo-alert
                    v-if="chooseNoticeText"
                    :class="$style.alert"
                    type="warning"
                    icon="exclamation-circle"
                >
                    <!-- 新表单提示信息更新 -->
                    <span v-html="safeNoticeText"></span>
                </roo-alert>

                <roo-form
                    ref="form"
                    :class="$style.form"
                    :model="formData"
                    :label-width="2"
                >
                    <roo-form-item
                        key="qualificationType"
                        :rules="rules.qualificationType"
                        label="资质类型"
                        required
                    >
                        <roo-radio-group
                            v-model="formData.qualificationType"
                            disabled
                        >
                            <roo-radio
                                v-for="opt in qualificationTypeOptions"
                                :key="opt.value"
                                :value="opt.value"
                            >
                                {{ opt.label }}
                            </roo-radio>
                        </roo-radio-group>
                        <div
                            v-if="formData.qualificationType"
                            :class="$style.tip"
                        >
                            {{ feeTip }}
                        </div>
                    </roo-form-item>

                    <roo-form-item
                        key="partnerId"
                        :label="`${formData.partnerTypeName} ID`"
                        prop="partnerId"
                        required
                    >
                        <roo-input
                            :value="formData.partnerId"
                            :class="$style.input"
                            :placeholder="`请输入 ${formData.partnerTypeName} ID`"
                            disabled
                        />
                    </roo-form-item>

                    <roo-form-item
                        v-if="formData.partnerType === 103"
                        key="partnerAName"
                        prop="partnerAName"
                        label="合同甲方"
                        required
                    >
                        <roo-input
                            :value="formData.partnerAName || contractAName"
                            :class="$style.input"
                            placeholder="请输入合同甲方"
                            disabled
                        />
                    </roo-form-item>

                    <!-- kp签约人信息 新表单新增展示 -->
                    <roo-form-item
                        v-if="signingWorkflow"
                        key="kpSignerInfo"
                        label="kp签约人"
                    >
                        <KpSignerInfo
                            :kp-signer-name="kpSignerName"
                            :kp-signer-mobile="kpSignerMobile"
                            :kp-signer-status="kpSignerStatus"
                        />
                    </roo-form-item>

                    <!-- 资质信息-新表单版本 -->
                    <template v-if="signingWorkflow">
                        <div :class="$style.newQualificationForm">
                            <QualificationForm
                                :form-data="formData"
                                :idx="0"
                                :qualifications="[formData]"
                                :partner-type="formData.partnerType"
                                :customer-number="customerNumber"
                                :disabled="isFormDisabled"
                                :is-edit="true"
                                @update="handleQualificationUpdate"
                            />
                        </div>
                    </template>

                    <!-- 资质信息-旧表单版本 -->
                    <template v-else>
                        <roo-form-item
                            key="partnerBName"
                            :rules="rules.partnerBName"
                            prop="partnerBName"
                            label="发票抬头"
                            required
                        >
                            <roo-input
                                v-model="formData.partnerBName"
                                disabled
                                :class="$style.input"
                                placeholder="请输入发票抬头"
                            />
                        </roo-form-item>
                        <roo-form-item
                            key="taxpayerIdNo"
                            label="纳税人识别号"
                            prop="taxpayerIdNo"
                            required
                        >
                            <roo-input
                                v-model="formData.taxpayerIdNo"
                                :class="$style.input"
                                disabled
                                placeholder="输入发票抬头后自动带出纳税人识别号"
                            />
                        </roo-form-item>
                        <roo-form-item
                            key="subpoiBusinessLicenseUrls"
                            :required="formData.partnerType !== 103 || formData.partnerBName !== contractAName"
                            :rules="(formData.partnerType !== 103 || formData.partnerBName !== contractAName) ? rules.subpoiBusinessLicenseUrls : null"
                            prop="subpoiBusinessLicenseUrls"
                            label="营业执照"
                        >
                            <image-uploader
                                v-model="formData.subpoiBusinessLicenseUrls"
                            />
                        </roo-form-item>

                        <roo-form-item
                            v-if="formData.partnerType !== 103 && accountSource != SOURCE.KA && formData.partnerType !== 101"
                            key="protocolUrls"
                            :rules="rules.protocolUrls"
                            prop="protocolUrls"
                            :label="protocolUrlsText"
                            required
                        >
                            <image-uploader
                                v-model="formData.protocolUrls"
                            />
                            <div :class="$style.row">
                                <template v-if="isSourceWaimai">
                                    <a
                                        href="//s3plus.meituan.net/v1/mss_c4375b35f5cb4e678b5b55a48c40cf9d/waimai-mfe-invoice-static/buchongxieyi-demo.png"
                                        target="_blank"
                                    >
                                        样例
                                    </a>
                                    &nbsp;&nbsp;&nbsp;&nbsp;
                                    <a
                                        href="//s3plus.meituan.net/v1/mss_c4375b35f5cb4e678b5b55a48c40cf9d/waimai-mfe-invoice-static/%E5%A4%96%E5%8D%96%E6%9C%8D%E5%8A%A1%E5%90%88%E5%90%8C-%E5%95%86%E5%AE%B6%E5%8F%91%E7%A5%A8%E5%8F%98%E6%9B%B4%E8%A1%A5%E5%85%85%E5%8D%8F%E8%AE%AE180212V-20180301%201.docx"
                                        target="_blank"
                                    >
                                        下载模板
                                    </a>
                                </template>
                                <span
                                    :class="$style.auditRule"
                                    type="text"
                                    @click="displayAuditRule = true"
                                >
                                    审核规则
                                </span>
                            </div>
                        </roo-form-item>
                    </template>
                    <roo-form-item
                        v-if="!signingWorkflow && (formData.partnerType === 103 || formData.partnerType === 101)"
                        key="authorizationUrls"
                        :required="formData.partnerBName !== contractAName"
                        :rules="formData.partnerBName !== contractAName ? rules.authorizationUrls : null"
                        prop="authorizationUrls"
                        label="补充协议"
                    >
                        <image-uploader
                            v-model="formData.authorizationUrls"
                        />
                        <div :class="$style.row">
                            <a
                                href="//s3.meituan.net/static-prod01/com.sankuai.wmfinance.merchant.pc-files/补充协议 20250408.jpg"
                                target="_blank"
                            >
                                样例
                            </a>
                            &nbsp;&nbsp;&nbsp;&nbsp;
                            <a
                                href="//s3plus.meituan.net/waimai-mfe-dragonfly-online/%E5%8F%91%E7%A5%A8%E8%A1%A5%E5%85%85%E5%8D%8F%E8%AE%AE-20241112.docx"
                                target="_blank"
                            >
                                下载模板
                            </a>
                            <span
                                :class="$style.auditRule"
                                type="text"
                                @click="displayAuditRule = true"
                            >
                                审核规则
                            </span>
                        </div>
                    </roo-form-item>

                    <roo-form-item
                        v-if="!signingWorkflow"
                        key="subpoiConfirmLetterUrls"
                        prop="subpoiConfirmLetterUrls"
                        label="其他附件"
                    >
                        <image-uploader
                            v-model="formData.subpoiConfirmLetterUrls"
                        />
                    </roo-form-item>
                    <roo-form-item>
                        <div :class="$style.bottomText">
                            <p>
                                根据国家税法及发票管理相关规定，任何单位和个人不得让他人为自己或者介绍他人开具与实际经营业务情况不符的发票，否则属于虚开发票行为。
                                我已充分了解上述发票相关规定，并且承诺仅就我司实际购买商品或服务索取发票。如我司未按国家相关规定申请开具或者使用增值税发票，由我司自行承担全部相应的法律后果。
                            </p>
                            <roo-checkbox
                                :checked="isCheck"
                                :disabled="isFormDisabled"
                                @change="handleCheck"
                            >
                                我已阅读并理解上述内容
                            </roo-checkbox>
                        </div>
                    </roo-form-item>
                </roo-form>
            </div>
        </div>

        <div class="footer-nav">
            <div
                v-if="id > 0 && status !== 100 && status !== 102"
                :class="$style.deleteButton"
                @click="displayDeleteConfirm = true"
            >
                删除资质
            </div>
            <roo-button
                :class="$style.back"
                :disabled="submitting"
                type="hollow"
                @click="$router.go(-1)"
            >
                返回
            </roo-button>
            <roo-button
                :disabled="!isCheck"
                :loading="submitting"
                type="brand"
                @click="handleSaveClick"
            >
                提交
            </roo-button>
        </div>

        <sms-yoda-verification
            ref="smsYodaVerification"
            :display-modal="displayModal"
            :check-sms-error="checkSmsError"
            :is-success-code="isSuccessCode"
            @update:displayModal="displayModal = $event"
            @update:smsCodes="handleSmsCodesUpdate"
            @update:mobile="handleMobile"
            @submit="handleSubmit"
        />
        <confirm-modal
            v-model="displayModifyConfirm"
            :loading="submitting"
            message="确认修改吗？"
            confirm-text="确认修改"
            @confirm="submit"
        />

        <confirm-modal
            v-model="displayDeleteConfirm"
            :loading="deleting"
            confirm-text="确认删除"
            @confirm="handleDeleteConfirm"
        >
            <div>删除后将无法恢复</div>
            <div>确认删除吗？</div>
        </confirm-modal>

        <roo-loading
            :show="loading"
            fullscreen
            backdrop
            lock
        />
        <roo-modal
            v-model="displayAuditRule"
            size="normal"
            title="审核规则"
            @close="displayAuditRule=false"
        >
            <template v-if="accountSource == SOURCE.WAIMAI">
                开票主体是合同：<br />
                发票抬头与合同甲方名称一致时，无需上传附件，资质提交后自动过审；<br />
                若发票抬头与合同甲方名称不一致，需要上传营业执照与补充协议，走补充协议审核规定。<br /><br />
                开票主体是商家：<br />
                发票抬头与合同甲方名称一致时，无需上传附件，资质提交后自动过审；<br />
                若发票抬头与合同甲方名称不一致，需要上传营业执照与补充协议，走补充协议审核规定。<br /><br />
                开票主体是广告主：<br />
                需上传营业执照、框架合同。<br /><br />
                开票主体是异业广告主、服务商、团好货商家、品牌商、游戏合作商、骑手商城服务商、外卖代运营商、外卖合作商：<br />
                需上传营业执照、纸质合同扫描件。若合同甲方与填写抬头一致则过审，不一致需上传补充协议。<br /><br />
                开票主体是异业合作商：<br />
                需上传营业执照、纸质合同扫描件。若合同甲方与填写抬头一致则过审，不一致需上传开票申请书，发票抬头与公司名称一致，章一致。<br /><br />
                开票主体是团餐企业客户：<br />
                需要上传营业执照，发票抬头与公司名称一致。<br /><br />
            </template>
            <template v-if="accountSource == SOURCE.RIDER">
                开票主体是骑手商城供应商：<br />
                需上传营业执照、纸质合同扫描件。若合同甲方与填写抬头一致则过审，不一致需上传补充协议，补充协议原发票抬头与合同甲方签约一致，现发票抬头与营业执照和录入的公司名称三者一致。<br />
            </template>

            <template v-if="accountSource == SOURCE.AGENCY_OPERATION">
                开票主体是外卖代运营商：<br />
                需上传营业执照、纸质合同扫描件。若合同甲方与填写抬头一致则过审，不一致需上传补充协议，补充协议原发票抬头与合同甲方签约一致，现发票抬头与营业执照和录入的公司名称三者一致。盖章为原公司和现公司都可以。<br />
            </template>

            <template v-if="accountSource == SOURCE.AGENT">
                开票主体是外卖合作商：<br />
                需上传营业执照、合同扫描件。若合同甲方与填写抬头一致则过审，不一致需上传补充协议，补充协议原发票抬头与合同甲方签约一致，现发票抬头与营业执照和录入的公司名称三者一致。盖章为原公司和现公司都可以。<br />
            </template>
            <template slot="footer">
                <roo-button
                    type="hollow"
                    @click="displayAuditRule = false"
                >
                    我知道了
                </roo-button>
            </template>
        </roo-modal>
        <statement-tip-modal />
        <roo-modal
            v-model="displaySubmitModal"
            size="small"
        >
            <div :class="$style.modalTitle">
                编辑成功
            </div>

            <template slot="footer">
                <roo-button @click="handleConfirmDone">
                    我知道了
                </roo-button>
            </template>
        </roo-modal>
    </div>
</template>

<script>
/* eslint-disable import/no-unresolved, import/extensions */
import request from '$invoice/utils/request';
import { alert, toast } from '@roo/roo-vue';
import { mapState } from 'vuex';

/* eslint-disable import/extensions, import/no-unresolved */
import toastError from '$lib/toast-error';
import { pick, getCookieValue } from '$lib/utils';
import SOURCE from '$lib/sourceEnum';
import ConfirmModal from '$components/confirm-modal';
import { pattern } from '$lib/has-invalid-charactor';
import StatementTipModal from '$components/statement-tip-modal';
import SmsYodaVerification from '$components/sms-yoda-verification';
import ImageUploader from './components/image-uploader';
import KpSignerInfo from '../../components/qualification-form/KpSignerInfo.vue';
import QualificationForm from '../../components/qualification-form';
import { sanitizeHtml } from '../../utils/security';


export default {
    name: 'QualificationEdit',
    components: {
        ConfirmModal,
        ImageUploader,
        StatementTipModal,
        SmsYodaVerification,
        KpSignerInfo,
        QualificationForm,
    },
    data() {
        return {
            loading: false,

            minHeight: null,

            formData: {
                qualificationType: 1,

                partnerType: 101,
                partnerTypeName: '商家',
                partnerId: '',

                partnerAName: '',
                partnerBName: '',
                taxpayerIdNo: '',

                subpoiConfirmLetterUrls: [],
                subpoiBusinessLicenseUrls: [],
                authorizationUrls: [],
                protocolUrls: [],
                updateType: 1,

                // 新表单新增字段
                legalRepName: '',
                legalRepIdCard: '',
                legalRepPhone: '',
            },
            accountSource: getCookieValue('source') || SOURCE.WAIMAI,
            SOURCE,
            isSourceWaimai: getCookieValue('source') === SOURCE.WAIMAI,

            rules: {
                qualificationType: [{
                    required: true,
                    message: '请选择资质类型',
                }],
                partnerBName: [{
                    required: true,
                    message: '请输入发票抬头',
                }, {
                    pattern,
                    message: '发票抬头不能有空格和如下特殊字符，如&，？等',
                }],
                taxpayerIdNo: [{
                    required: true,
                    message: '请输入纳税人识别号',
                }],

                // 广告必须
                subpoiBusinessLicenseUrls: [{ // 合同资质 公司名和甲方名称不同 必须
                    required: true,
                    message: '请上传营业执照',
                }],
                protocolUrls: [{
                    required: true,
                    message: `请上传${getCookieValue('source') !== SOURCE.WAIMAI ? '框架合同' : '开票申请书'}`,
                }],

                authorizationUrls: [{ // 合同资质 公司名和甲方名称不同 必须
                    required: true,
                    message: '请上传补充协议',
                }],
            },

            submitting: false,

            displayDeleteConfirm: false,
            deleting: false,

            displayModifyConfirm: false,
            displayAuditRule: false,
            isCheck: false,
            displayModal: false,
            smsCodes: '',
            mobile: '',
            checkSmsError: '',
            isSuccessCode: false,
            displaySubmitModal: false,

            signingWorkflow: false, // 是否新流程生产
            status: null, // 资质状态
            kpSignerName: '', // kp签约人名称
            kpSignerMobile: '', // kp签约人手机号
            kpSignerStatus: 0, // kp签约人状态：-1未通过实名认证，0信息缺失，1实名认证通过
            customerNumber: '', // 合同甲方税号
        };
    },
    computed: {
        ...mapState('config', [
            'contractId',
            'contractAName',
            'feeTypeList',
        ]),
        ...mapState('apply', ['needVerification', 'noticeText', 'noticeTextNew']),

        chooseNoticeText() {
            return this.signingWorkflow ? this.noticeTextNew : this.noticeText;
        },

        safeNoticeText() {
            return sanitizeHtml(this.chooseNoticeText);
        },

        // 控制表单是否禁用
        isFormDisabled() {
            return this.signingWorkflow; // 新表单模式下所有表单项都禁用
        },

        // 控制是否需要验证码校验
        shouldNeedVerification() {
            // 新表单模式下不需要验证码校验，旧表单模式按照配置确定是否需要验证码校验
            return this.signingWorkflow ? false : this.needVerification;
        },

        protocolUrlsText() {
            return !this.isSourceWaimai ? '框架合同' : '开票申请书';
        },

        feeTip() {
            const { qualificationType, partnerType } = this.formData;
            const arr = this.feeTypeList
                .filter(f => f.qualificationType === qualificationType && f.partnerType === partnerType)
                .map(f => f.baseFeeTypeName);
            return `可申请${arr.join('、')}`;
        },

        // 可申请资质类型从已加载的 feeTypeList 派生
        qualificationTypeOptions() {
            const tmp = new Set();
            this.feeTypeList.forEach(({ qualificationType }) => {
                tmp.add(qualificationType);
            });
            const options = [];
            if (tmp.has(1)) {
                options.push({
                    value: 1,
                    label: '服务费相关资质',
                });
            }
            if (tmp.has(2)) {
                options.push({
                    value: 2,
                    label: '推广费相关资质',
                });
            }
            return options;
        },

        id() {
            return parseInt(this.$route.query.id, 10) || null;
        },

        breadcrumb() {
            return [{
                text: '发票管理',
                link: {
                    name: 'qualification-select',
                },
            }, {
                text: this.signingWorkflow ? '修改抬头' : '修改资质',
            }];
        },
    },
    mounted() {
        this.minHeight = window.innerHeight - 104;
        this.fetchQualificationDetail(this.id); // 包含 partnerAName
        this.getConfig(); // 用于获取kp签约人信息和甲方税号
    },
    methods: {
        handleSmsCodesUpdate(newSmsCodes) {
            this.smsCodes = newSmsCodes;
        },
        handleMobile(newMobile) {
            this.mobile = newMobile;
        },
        handleCheck(checked) {
            this.isCheck = checked;
        },
        fetchQualificationDetail(id) {
            const params = {
                qualificationId: id,
            };

            if (id === -1) {
                params.contractId = this.contractId;
            }

            this.loading = true;
            return request.get('/finance/invoice/api/common/qualification/r/detail', { params })
                .then((res) => {
                    const { code, msg, data } = res.data;

                    if (code !== 0) {
                        throw new Error(msg);
                    }

                    this.loading = false;
                    Object.assign(this.formData, pick(data, [
                        'qualificationType',

                        'partnerAName',
                        'partnerBName',
                        'taxpayerIdNo',
                        'partnerType',
                        'partnerTypeName',
                        'partnerId',

                        'subpoiBusinessLicenseUrls',
                        'subpoiConfirmLetterUrls',
                        'authorizationUrls',
                        'protocolUrls',

                        // 新表单新增字段
                        'legalRepName',
                        'legalRepIdCard',
                        'legalRepPhone',
                    ]));

                    [
                        'subpoiBusinessLicenseUrls',
                        'subpoiConfirmLetterUrls',
                        'authorizationUrls',
                        'protocolUrls',
                    ].forEach((key) => {
                        this.formData[key] = data[key] || [];
                    });

                    this.signingWorkflow = data.signingWorkflow;
                    this.status = data.status;
                })
                .catch(toastError);
        },
        // 获取配置信息
        async getConfig() {
            try {
                const params = {
                    requestHitParam: true, // 需要hit参数
                    contractId: this.contractId,
                };

                const res = await request.get('/finance/invoice/api/qualification/getConfig', { params });

                if (res.data.code === 0 && res.data.data) {
                    const { kpSigner, customerNumber } = res.data.data;
                    this.kpSignerName = (kpSigner && kpSigner.name) || '';
                    this.kpSignerMobile = (kpSigner && kpSigner.mobile) || '';
                    this.kpSignerStatus = (kpSigner && kpSigner.status) || 0;
                    this.customerNumber = customerNumber;
                }
            } catch (error) {
                console.error('获取配置信息失败:', error);
                // 失败时使用默认值
            }
        },
        buildBody() {
            const { id, formData } = this;

            const fields = [
                'partnerBName',

                // 服务费资质 & 推广费资质 都需要
                'subpoiBusinessLicenseUrls',
                'subpoiConfirmLetterUrls',
                'authorizationUrls',
                'protocolUrls',
                'taxpayerIdNo',

                // 新表单新增字段
                'legalRepName',
                'legalRepIdCard',
                'legalRepPhone',
            ];

            const item = Object.assign(
                pick(formData, fields),
                { qualificationId: id, updateType: 1 },
            );

            // 取 imageName
            [
                'subpoiBusinessLicenseUrls',
                'subpoiConfirmLetterUrls',
                'authorizationUrls',
                'protocolUrls',
            ].forEach((key) => {
                if (item[key]) {
                    item[key] = item[key].map(x => x.imageName);
                }
            });

            const { contractId } = this;
            const { partnerType } = this.formData;

            return {
                contractId,
                partnerType,
                qualificationType: formData.qualificationType,
                applyList: [item],
            };
        },

        // 修改提交方法
        handleSubmit() {
            const body = this.buildBody();
            if (this.shouldNeedVerification === true) {
                body.smscode = this.smsCodes;
                body.uuid = getCookieValue('device_uuid');
                body.mobile = this.mobile;
                body.ua = this.getUserAgent();
                body.needCheckCode = true;
            }
            this.submitting = true;
            return request.post('/finance/invoice/api/common/qualification/w/save', body)
                .then((res) => {
                    this.submitting = false;
                    const { code, msg, data } = res.data;
                    if (code === 0) {
                        if (this.shouldNeedVerification === true && data.response_code) {
                            this.isSuccessCode = true;
                            this.displayModal = false;
                        }
                        this.displaySubmitModal = true;
                    }

                    if (code === 2) {
                        alert(msg, () => {
                            this.$router.go(-1);
                        });
                        return;
                    }

                    if (code !== 0) {
                        if (msg && msg.message) {
                            this.checkSmsError = msg.message;
                            return;
                        }
                        this.checkSmsError = '';
                        toast.error(msg);
                    }
                }, (err) => {
                    this.submitting = false;
                    throw err;
                });
        },
        handleSaveClick() {
            if (this.signingWorkflow) {
                this.$refs.form.validate(this.handleVerifiedNew);
            } else {
                this.$refs.form.validate(this.handleVerified);
            }
        },
        handleVerified(pass) {
            if (!pass) return;
            if (this.needVerification) {
                this.save();
            } else {
                this.displayModifyConfirm = true;
            }
        },
        handleVerifiedNew(pass) {
            // 暂定
            if (!pass) {
                toast.warn('资质信息填写有误，请检查');
                return;
            }
            // 校验营业执照OCR识别的税号与资质抬头税号是否一致
            if (this.formData.subpoiBusinessLicenseUrls[0]
                && this.formData.subpoiBusinessLicenseUrls[0].taxpayerIdNo
                && (this.formData.taxpayerIdNo !== this.formData.subpoiBusinessLicenseUrls[0].taxpayerIdNo)) {
                toast.warn('资质税号需与上传的营业执照一致');
                return;
            }
            if (this.shouldNeedVerification) {
                this.save();
            } else {
                this.displayModifyConfirm = true;
            }
        },
        submitCom() {
            this.displayModifyConfirm = true;
        },
        save() {
            this.$nextTick(async () => {
                try {
                    await this.$refs.smsYodaVerification.getMobile();
                } catch (err) {
                    console.log(err);
                    // 错误处理已经在 getMobile 中处理
                }
            });
        },
        submit() {
            this.displayModifyConfirm = false;
            this.handleSubmit();
            this.$router.replace({ name: 'qualification-select' });
        },
        handleDeleteConfirm() {
            const params = {
                qualificationId: this.id,
            };

            this.deleting = true;
            return request.get('/finance/invoice/api/common/qualification/w/invalid', { params })
                .then((res) => {
                    const { code, msg } = res.data;

                    if (code !== 0) {
                        throw new Error(msg);
                    }

                    this.deleting = false;
                    this.displayDeleteConfirm = false;
                    this.$router.go(-1);
                })
                .catch((err) => {
                    this.deleting = false;
                    toastError(err);
                });
        },
        getUserAgent() {
            return navigator.userAgent;
        },
        handleConfirmDone() {
            this.$router.go(-1);
        },
        handleChange(keyword) {
            if (!keyword) {
                return;
            }

            const params = {
                partnerBName: keyword,
            };

            request.get('/finance/invoice/api/output/waimai/q/queryTaxNoByName', { params }).then((res) => {
                const { code, msg, data } = res.data;

                if (code !== 0) {
                    throw new Error(msg);
                }

                // 找到当前正在编辑的表单项
                this.formData.taxpayerIdNo = data;
            }).catch(toastError);
        },
        handleQualificationUpdate(index, newData) {
            // 更新 formData
            Object.keys(newData).forEach(key => {
                this.$set(this.formData, key, newData[key]);
            });
        },
    },
};
</script>

<style lang="scss" module>
.downloadLink {
    color:  #FF6A00;
    text-decoration: none;
    font-size: 14px;
    line-height: 20px;
    letter-spacing: 0px;

    &:hover {
        text-decoration: underline;
    }
}

.container {
    position: relative;
    min-height: 100%;
    padding-top: 10px;
    padding-bottom: 56px;
}

.back {
    margin-right: 16px;
}

.card {
    padding: 40px;
    background: #FFF;
    border-radius: 2px;
    box-shadow: 0 0 6px 0 #F3F3F4;

    h3 {
        margin-top: 0;
        margin-bottom: 20px;
        font-size: 20px;
    }
}

.row {
    margin-top: 10px;
    margin-left: 20px;
}

.form {
    :global(.form-group.row:last-child) {
        margin-bottom: 0;
    }
}

.input {
    width: 300px;
}

.tip {
    color: #9E9E9E;
}

.delete-button {
    display: inline-block;
    margin-right: 16px;
    line-height: 36px;
    color: #F76C6C;
    cursor: pointer;
    vertical-align: middle;
}
.audit-rule {
    color: #f89800;
    cursor: pointer;
    margin-left: 15px;
}
.bottomText{

font-size: 14px;
padding: 0px 100px;
}
.modalTitle{
    font-size: 20px;
    color: #222222;
    font-weight: 500;
}
.headerRow {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.h3 {
    margin: 0;
    font-size: 20px;
}

.newQualificationForm {
    :global(.form-group.row:last-child) {
        margin-bottom: 16px;
    }
}
</style>
