<template>
    <div :class="$style.imageUploader">
        <roo-img-uploader
            v-for="(item, idx) in value"
            :key="idx"
            :value="item.imageUrl"
            name="file"
            accept="image/*"
            upload-url="/finance/pc/api/invoice/qualification/imageUpload"
            uploaded-url-field="data.imageUrl"
            tips-fixed
            clearable
            auto-upload
            @file-change="handleFileChange"
            @change="handleChange(idx, $event)"
            @upload-success="handleImageChange(idx, $event)"
        />
        <roo-img-uploader
            v-if="value.length < max"
            :key="`new-${value.length + 1}`"
            :value="null"
            name="file"
            accept="image/*"
            upload-url="/finance/pc/api/invoice/qualification/imageUpload"
            uploaded-url-field="data.imageUrl"
            tips-fixed
            clearable
            auto-upload
            @file-change="handleFileChange"
            @upload-success="handleAddImage"
        />
    </div>
</template>

<script>
import { toast } from '@roo/roo-vue';

export default {
    name: 'ImageUploader',
    props: {
        value: {
            type: Array,
            required: true,
        },
        // 图片数量
        max: {
            type: Number,
            default: 5,
        },
    },
    methods: {
        handleFileChange(e) {
            const { file } = e;
            if (file.size > 10 * 1024 * 1024) {
                e.cancel = true;
                toast.warn('单个文件大小不能超过 10MB');
            }
        },
        handleChange(index, url) {
            if (!url) {
                const newVal = this.value.filter((item, idx) => idx !== index);
                this.$emit('input', newVal);
            }
        },
        handleImageChange(index, res) {
            const newVal = this.value.slice(0);
            newVal[index] = res.data;
            this.$emit('input', newVal);
        },
        handleAddImage(res) {
            if (res.code === 0 && res.data !== null) {
                this.$emit('input', this.value.concat(res.data));
            } else {
                toast.warn(res.msg || '上传失败，请重新尝试');
            }
        },
    },
};
</script>

<style lang="scss" module>
.image-uploader {
    display: flex;
    flex-wrap: wrap;

    :global(.roo-img-uploader) {
        flex-shrink: 0;
        margin-right: 10px;
        margin-bottom: 10px;
    }
}
</style>
