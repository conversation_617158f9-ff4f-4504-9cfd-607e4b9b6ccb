<template>
    <div :class="$style.container">
        <div>
            <roo-breadcrumb :items="breadcrumb" />
        </div>

        <div :class="$style.card" :style="{ 'min-height': minHeight ? `${minHeight}px` : null }">
            <!-- 新表单header -->
            <div v-if="enableNewForm" :class="$style.headerRow">
                <h3 :class="$style.h3">
                    新增抬头
                </h3>

                <!-- 下载补充协议模版链接（新页面右上角） -->
                <a
                    :class="$style.downloadLink"
                    href="//s3plus.meituan.net/shangouopendeveloper/%E8%A1%A5%E5%85%85%E5%8D%8F%E8%AE%AE%E6%A8%A1%E6%9D%**********.docx"
                    target="_blank"
                >
                    下载补充协议模版
                </a>
            </div>

            <!-- 旧表单header -->
            <div v-else :class="$style.headerRow">
                <h3 :class="$style.h3">
                    新增资质
                </h3>
            </div>

            <!-- 新表单新增提示展示 -->
            <roo-alert
                v-if="enableNewForm && chooseNoticeText"
                :class="$style.alert"
                type="warning"
                icon="exclamation-circle"
            >
                <span v-html="safeNoticeText"></span>
            </roo-alert>

            <roo-form :class="$style.form" :label-width="2">
                <roo-form-item key="qualificationType" :rules="rules.qualificationType" label="资质类型" required>
                    <roo-radio-group v-model="qualificationType">
                        <roo-radio v-for="opt in qualificationTypeOptions" :key="opt.value" :value="opt.value">
                            {{ opt.label }}
                        </roo-radio>
                    </roo-radio-group>
                </roo-form-item>

                <roo-form-item key="baseFeeType" :rules="rules.baseFeeType" label="明细项目" required>
                    <roo-radio-group v-model="baseFeeType">
                        <roo-radio v-for="opt in baseFeeTypeOptions" :key="opt.value" :value="opt.value">
                            {{ opt.label }}
                        </roo-radio>
                    </roo-radio-group>
                </roo-form-item>

                <template v-if="partnerType === 103">
                    <roo-form-item key="contractId" :rules="rules.contractId" prop="contractId" label="合同 ID" required>
                        <roo-input v-model="contractId" :class="$style.input" placeholder="请输入合同 ID" disabled />
                    </roo-form-item>

                    <roo-form-item key="partnerAName" prop="partnerAName" label="合同甲方" required>
                        <roo-input :value="contractAName" :class="$style.input" placeholder="请输入合同甲方" disabled />
                    </roo-form-item>
                </template>

                <roo-form-item v-else key="wmPoiId" :rules="rules.wmPoiId" prop="wmPoiId" label="商家 ID" required>
                    <roo-input v-model="wmPoiId" :class="$style.input" placeholder="请输入商家 ID" disabled />
                </roo-form-item>

                <!-- kp签约人信息 新表单新增展示 -->
                <roo-form-item v-if="enableNewForm" key="kpSignerInfo" label="kp签约人">
                    <KpSignerInfo
                        :kp-signer-name="kpSignerName"
                        :kp-signer-mobile="kpSignerMobile"
                        :kp-signer-status="kpSignerStatus"
                    />
                </roo-form-item>
            </roo-form>

            <!-- 新资质动态表单-新表单版本 -->
            <template v-if="enableNewForm">
                <roo-form
                    v-for="(formData, idx) in qualifications"
                    ref="forms"
                    :key="idx"
                    :class="$style.form"
                    :model="formData"
                    :label-width="2"
                >
                    <QualificationForm
                        :form-data="formData"
                        :idx="idx"
                        :qualifications="qualifications"
                        :partner-type="partnerType"
                        :customer-number="customerNumber"
                        @add-qualification="handleAddQualification"
                        @remove-qualification="handleRemove"
                        @update="handleQualificationUpdate"
                    />
                </roo-form>
            </template>

            <!-- 新资质动态表单-旧表单版本 -->
            <template v-else>
                <roo-form
                    v-for="(formData, idx) in qualifications"
                    ref="forms"
                    :key="idx"
                    :class="$style.form"
                    :model="formData"
                    :label-width="2"
                >
                    <roo-form-item
                        key="partnerBName"
                        :rules="rules.partnerBName"
                        :label="`发票抬头${qualifications.length > 1 ? `${idx + 1}` : ''}`"
                        prop="partnerBName"
                        required
                    >
                        <roo-input
                            v-model="formData.partnerBName"
                            :class="$style.input"
                            placeholder="请输入发票抬头"
                            @change="(val) => handleChange(val, idx)"
                        />
                        <roo-button @click="handleAddQualification">
                            <roo-icon name="plus" />
                        </roo-button>
                        <roo-button :disabled="qualifications.length <= 1" status="danger" @click="handleRemove(idx)">
                            <roo-icon name="minus" />
                        </roo-button>
                    </roo-form-item>
                    <roo-form-item key="taxpayerIdNo" label="纳税人识别号" prop="taxpayerIdNo" required>
                        <roo-input
                            v-model="formData.taxpayerIdNo"
                            :class="$style.input"
                            disabled
                            placeholder="输入发票抬头后自动带出纳税人识别号"
                        />
                        <div v-if="taxpayerIdNoErrors[idx]" style="color: red;">
                            {{ taxpayerIdNoErrors[idx] }}
                        </div>
                    </roo-form-item>
                    <roo-form-item
                        key="subpoiBusinessLicenseUrls"
                        :required="partnerType !== 103 || formData.partnerBName !== contractAName"
                        :rules="(partnerType !== 103 || formData.partnerBName !== contractAName) ? rules.subpoiBusinessLicenseUrls : null"
                        prop="subpoiBusinessLicenseUrls"
                        label="营业执照"
                    >
                        <image-uploader v-model="formData.subpoiBusinessLicenseUrls" />
                    </roo-form-item>

                    <!-- 开票申请书/合同扫描件 -->
                    <roo-form-item
                        v-if="partnerType !== 103 && accountSource != SOURCE.KA && partnerType !== 101"
                        key="protocolUrls"
                        :rules="rules.protocolUrls"
                        prop="protocolUrls"
                        :label="protocolUrlsText"
                        required
                    >
                        <image-uploader v-model="formData.protocolUrls" />
                        <div :class="$style.row">
                            <template v-if="isSourceWaimai">
                                <a
                                    href="//s3plus.meituan.net/v1/mss_c4375b35f5cb4e678b5b55a48c40cf9d/waimai-mfe-invoice-static/invoice-apply-demo.jpg"
                                    target="_blank"
                                >
                                    样例
                                </a>
                                &nbsp;&nbsp;&nbsp;&nbsp;
                                <a
                                    href="//s3plus.meituan.net/v1/mss_c4375b35f5cb4e678b5b55a48c40cf9d/waimai-mfe-invoice-static/%E5%A4%96%E5%8D%96%E5%B9%BF%E5%91%8A%E5%BC%80%E7%A5%A8%E7%94%B3%E8%AF%B7%E4%B9%A6.docx"
                                    target="_blank"
                                >
                                    下载模板
                                </a>
                            </template>
                            <span :class="$style.auditRule" type="text" @click="displayAuditRule = true">
                                审核规则
                            </span>
                        </div>
                    </roo-form-item>
                </roo-form>
            </template>

            <roo-form ref="form2" :model="{ authorizationUrls, subpoiConfirmLetterUrls }">
                <!-- 只在旧表单模式下显示 -->
                <template v-if="!enableNewForm">
                    <roo-form-item
                        v-if="partnerType === 103 || partnerType === 101"
                        key="authorizationUrls"
                        :required="authorizationUrlsRequired"
                        :rules="authorizationUrlsRequired ? rules.authorizationUrls : null"
                        prop="authorizationUrls"
                        label="补充协议"
                    >
                        <image-uploader v-model="authorizationUrls" />
                        <div :class="$style.row">
                            <a
                                href="//s3.meituan.net/static-prod01/com.sankuai.wmfinance.merchant.pc-files/补充协议 20250408.jpg"
                                target="_blank"
                            >
                                样例
                            </a>
                                &nbsp;&nbsp;&nbsp;&nbsp;
                            <a
                                href="//s3plus.meituan.net/waimai-mfe-dragonfly-online/%E5%8F%91%E7%A5%A8%E8%A1%A5%E5%85%85%E5%8D%8F%E8%AE%AE-20241112.docx"
                                target="_blank"
                            >
                                下载模板
                            </a>
                            <span :class="$style.auditRule" type="text" @click="displayAuditRule = true">
                                审核规则
                            </span>
                        </div>
                    </roo-form-item>

                    <roo-form-item key="subpoiConfirmLetterUrls" prop="subpoiConfirmLetterUrls" label="其他附件">
                        <image-uploader v-model="subpoiConfirmLetterUrls" />
                    </roo-form-item>
                </template>

                <roo-form-item>
                    <div :class="$style.bottomText">
                        <p>
                            根据国家税法及发票管理相关规定，任何单位和个人不得让他人为自己或者介绍他人开具与实际经营业务情况不符的发票，否则属于虚开发票行为。
                            我已充分了解上述发票相关规定，并且承诺仅就我司实际购买商品或服务索取发票。如我司未按国家相关规定申请开具或者使用增值税发票，由我司自行承担全部相应的法律后果。
                        </p>
                        <roo-checkbox :checked="isCheck" @change="handleCheck">
                            我已阅读并理解上述内容
                        </roo-checkbox>
                    </div>
                </roo-form-item>
            </roo-form>
        </div>


        <div class="footer-nav">
            <roo-button :class="$style.back" :disabled="submitting" type="hollow" @click="$router.go(-1)">
                返回
            </roo-button>
            <roo-button
                :disabled="!qualificationType || !isCheck"
                :loading="submitting"
                type="brand"
                @click="handleSaveClick"
            >
                提交
            </roo-button>
            <sms-yoda-verification
                ref="smsYodaVerification"
                :display-modal="displayModal"
                :check-sms-error="checkSmsError"
                :is-success-code="isSuccessCode"
                @update:displayModal="displayModal = $event"
                @update:smsCodes="handleSmsCodesUpdate"
                @update:mobile="handleMobile"
                @submit="handleSubmit"
            />
        </div>

        <roo-loading :show="loading" fullscreen backdrop lock />
        <roo-modal v-model="displayAuditRule" size="normal" title="审核规则" @close="displayAuditRule=false">
            <template v-if="accountSource == SOURCE.WAIMAI">
                开票主体是合同：<br />
                发票抬头与合同甲方名称一致时，无需上传附件，资质提交后自动过审；<br />
                若发票抬头与合同甲方名称不一致，需要上传营业执照与补充协议，走补充协议审核规定。<br /><br />
                开票主体是商家：<br />
                发票抬头与合同甲方名称一致时，无需上传附件，资质提交后自动过审；<br />
                若发票抬头与合同甲方名称不一致，需要上传营业执照与补充协议，走补充协议审核规定。<br /><br />
                开票主体是广告主：<br />
                需上传营业执照、框架合同。<br /><br />
                开票主体是异业广告主、服务商、团好货商家、品牌商、游戏合作商、骑手商城服务商、外卖代运营商、外卖合作商：<br />
                需上传营业执照、纸质合同扫描件。若合同甲方与填写抬头一致则过审，不一致需上传补充协议。<br /><br />
                开票主体是异业合作商：<br />
                需上传营业执照、纸质合同扫描件。若合同甲方与填写抬头一致则过审，不一致需上传开票申请书，发票抬头与公司名称一致，章一致。<br /><br />
                开票主体是团餐企业客户：<br />
                需要上传营业执照，发票抬头与公司名称一致。<br /><br />
            </template>

            <template v-if="accountSource == SOURCE.RIDER">
                开票主体是骑手商城供应商：<br />
                需上传营业执照、纸质合同扫描件。若合同甲方与填写抬头一致则过审，不一致需上传补充协议，补充协议原发票抬头与合同甲方签约一致，现发票抬头与营业执照和录入的公司名称三者一致。<br />
            </template>

            <template v-if="accountSource == SOURCE.AGENCY_OPERATION">
                开票主体是外卖代运营商：<br />
                需上传营业执照、纸质合同扫描件。若合同甲方与填写抬头一致则过审，不一致需上传补充协议，补充协议原发票抬头与合同甲方签约一致，现发票抬头与营业执照和录入的公司名称三者一致。盖章为原公司和现公司都可以。<br />
            </template>

            <template v-if="accountSource == SOURCE.AGENT">
                开票主体是外卖合作商：<br />
                需上传营业执照、合同扫描件。若合同甲方与填写抬头一致则过审，不一致需上传补充协议，补充协议原发票抬头与合同甲方签约一致，现发票抬头与营业执照和录入的公司名称三者一致。盖章为原公司和现公司都可以。<br />
            </template>
            <template slot="footer">
                <roo-button type="hollow" @click="displayAuditRule = false">
                    我知道了
                </roo-button>
            </template>
        </roo-modal>

        <roo-modal v-model="displaySubmitModal" size="small">
            <div :class="$style.modalTitle">
                抬头申请已提交
            </div>
            <div v-if="enableNewForm" :class="$style.modalTitleContent">
                资质提交成功。通过系统审核后，请进行线上签署。
            </div>
            <div v-else :class="$style.modalTitleContent">
                已成功提交抬头申请，平台将在3个工作日内进行审核，您可在发票抬头模块中查看审核进度，审核结果将会发送信息提示
            </div>

            <template slot="footer">
                <roo-button @click="handleConfirmDone">
                    我知道了
                </roo-button>
            </template>
        </roo-modal>

        <roo-modal v-model="displayNoticeModal" size="normal" title="重要提示">
            <div v-html="safeNoticeText"></div>
            <template slot="footer">
                <roo-button type="brand" @click="displayNoticeModal = false">
                    我知道了
                </roo-button>
            </template>
        </roo-modal>
    </div>
</template>

<script>
/* eslint-disable import/no-unresolved, import/extensions */
import request from '$invoice/utils/request';
import { mapState } from 'vuex';
import { toast, alert } from '@roo/roo-vue';

/* eslint-disable import/extensions, import/no-unresolved */
import toastError from '$lib/toast-error';
import { getCookieValue, debounce } from '$lib/utils';
import SOURCE from '$lib/sourceEnum';
import { pattern } from '$lib/has-invalid-charactor';
import { sanitizeHtml } from '../../utils/security';


import ImageUploader from './components/image-uploader';
import SmsYodaVerification from '$components/sms-yoda-verification';
import KpSignerInfo from '../../components/qualification-form/KpSignerInfo.vue';
import QualificationForm from '../../components/qualification-form';


const template = {
    partnerBName: '', // 发票抬头
    // subpoiConfirmLetterUrls: [], // 其他附件
    subpoiBusinessLicenseUrls: [], // 营业执照
    // authorizationUrls: [], // 补充协议
    protocolUrls: [], // 开票申请书
    updateType: 1, // 更新类型：1新增，2修改
    legalRepName: '', // 法人名称
    legalRepIdCard: '', // 法人身份证号
    legalRepPhone: '', // 法人手机号
};

export default {
    name: 'AddQualification',
    components: {
        ImageUploader,
        SmsYodaVerification,
        KpSignerInfo,
        QualificationForm,
    },
    data() {
        return {
            loading: false,

            minHeight: null,

            qualificationType: null,

            baseFeeType: null,

            qualifications: [{ ...template }],

            authorizationUrls: [],
            subpoiConfirmLetterUrls: [],
            accountSource: getCookieValue('source') || SOURCE.WAIMAI,
            SOURCE,
            isSourceWaimai: getCookieValue('source') === SOURCE.WAIMAI,

            rules: {
                qualificationType: [{
                    required: true,
                    message: '请选择资质类型',
                }],

                baseFeeType: [{
                    required: true,
                    message: '请选择明细项目',
                }],

                partnerBName: [{
                    required: true,
                    message: '请输入发票抬头',
                }, {
                    pattern,
                    message: '发票抬头不能有空格和如下特殊字符，如&，？等',
                }],
                taxpayerIdNo: [{
                    required: true,
                    message: '请输入纳税人识别号',
                }],

                // 广告必须
                subpoiBusinessLicenseUrls: [{ // 合同资质 发票抬头和甲方名称不同 必须
                    required: true,
                    message: '请上传营业执照',
                }],
                protocolUrls: [{
                    required: true,
                    message: `请上传${getCookieValue('source') !== SOURCE.WAIMAI ? '合同扫描件' : '开票申请书'}`,
                }],

                authorizationUrls: [{ // 合同资质 公司名和甲方名称不同 必须
                    required: true,
                    message: '请上传补充协议',
                }],
            },

            submitting: false,
            displayAuditRule: false,
            isCheck: false,
            displayModal: false,
            smsCodes: '',
            mobile: '',
            checkSmsError: '',
            isSuccessCode: false,
            displaySubmitModal: false,
            displayNoticeModal: false,
            taxpayerIdNoErrors: [],

            // 配置信息
            enableNewForm: false, // 是否启用新表单模型
            kpSignerName: '', // kp签约人名称
            kpSignerMobile: '', // kp签约人手机号
            kpSignerStatus: 0, // kp签约人状态：-1未通过实名认证，0信息缺失，1实名认证通过
            customerNumber: '', // 合同甲方税号
        };
    },
    computed: {
        wmPoiId() {
            return parseInt(getCookieValue('wmPoiId'), 10);
        },

        ...mapState('config', [
            'contractId',
            'contractAName',
            'feeTypeList',
        ]),

        ...mapState('apply', ['needVerification', 'noticeText', 'noticeTextNew']),

        chooseNoticeText() {
            return this.enableNewForm ? this.noticeTextNew : this.noticeText;
        },

        safeNoticeText() {
            return sanitizeHtml(this.chooseNoticeText);
        },

        protocolUrlsText() {
            return this.isSourceWaimai ? '开票申请书' : '框架合同';
        },
        // 添加新的计算属性判断是否显示补充协议的样例和模板
        shouldShowSupplementaryAgreement() {
            return this.qualificationType === 2;
        },
        initialQualificationType() {
            return parseInt(this.$route.query.qualificationType, 10) || null;
        },

        // 可申请资质类型从已加载的 feeTypeList 派生
        qualificationTypeOptions() {
            const tmp = new Set();
            this.feeTypeList.forEach(({ qualificationType }) => {
                tmp.add(qualificationType);
            });
            const options = [];
            if (tmp.has(1)) {
                options.push({
                    value: 1,
                    label: '服务费相关资质',
                });
            }
            if (tmp.has(2)) {
                options.push({
                    value: 2,
                    label: '推广费相关资质',
                });
            }
            return options;
        },

        baseFeeTypeOptions() {
            const {
                qualificationType,
                feeTypeList,
            } = this;

            return (feeTypeList || [])
                .filter(f => f.qualificationType === qualificationType)
                .map(f => ({
                    value: f.baseFeeType,
                    label: f.baseFeeTypeName,
                }));
        },

        partnerType() {
            const { baseFeeType, feeTypeList } = this;
            const item = feeTypeList.find(f => f.baseFeeType === baseFeeType);
            if (item) {
                return item.partnerType;
            }
            return null;
        },

        authorizationUrlsRequired() {
            const { partnerType, qualifications, contractAName } = this;
            if (partnerType === 103 || partnerType === 101) {
                return qualifications.some(q => q.partnerBName !== contractAName);
            } else {
                return false;
            }
        },

        breadcrumb() {
            return [{
                text: '发票管理',
                link: {
                    name: 'qualification-select',
                },
            }, {
                text: this.enableNewForm ? '新增抬头' : '新增资质',
            }];
        },

        showKpSignerDetails() {
            // kp签约人信息缺失时，不展示详情
            if (this.kpSignerStatus === 0) {
                return false;
            }
            return true;
        },

        kpSignerStatusText() {
            if (this.kpSignerStatus === 0) {
                return '缺少kp签约人信息，请联系业务经理补充kp签约人信息';
            } else if (this.kpSignerStatus === -1) {
                return 'kp签约人信息有误，请联系业务经理完善kp签约人信息';
            }
            return '即商户入驻时的签约人，您可以在"店铺设置->门店管理->合同协议中心"查看合同签约人信息';
        },
        kpSignerStatusClass() {
            if (this.kpSignerStatus === 0 || this.kpSignerStatus === -1) {
                return this.$style.kpStatusError; // 信息缺失或信息有误 - 红色
            }
            return this.$style.kpStatusNormal; // 正常状态 - 灰色
        },
        // // 控制表单是否禁用
        // isFormDisabled() {
        //     // kpSignerStatus: -1未通过实名认证，0信息缺失，1实名认证通过
        //     return this.kpSignerStatus === -1 || this.kpSignerStatus === 0;
        // },

        // 控制是否需要验证码校验
        shouldNeedVerification() {
            // 新表单模式下不需要验证码校验，旧表单模式按照配置确定是否需要验证码校验
            return this.enableNewForm ? false : this.needVerification;
        },
    },
    watch: {
        baseFeeTypeOptions(val) {
            // console.log(val);
            const [first] = val;
            if (first) {
                this.baseFeeType = first.value;
            }
        },
    },
    created() {
        this.handleChange = debounce(this.handleChange, 1000);
    },
    mounted() {
        this.qualificationType = this.initialQualificationType;

        // 最小高度
        this.minHeight = window.innerHeight - 104;

        // 获取配置信息
        this.getConfig()
            .then(() => {
                if (this.enableNewForm && this.safeNoticeText) {
                    this.displayNoticeModal = true;
                }
            })
            .catch(() => {
                alert('获取配置信息失败，请稍后重试。');
            });
    },
    methods: {
        // 获取配置信息
        async getConfig() {
            try {
                const params = {
                    requestHitParam: true, // 需要hit参数
                    contractId: this.contractId,
                };

                const res = await request.get('/finance/invoice/api/qualification/getConfig', { params });

                if (res.data.code === 0 && res.data.data) {
                    const { enableNewForm, kpSigner, customerNumber } = res.data.data;
                    this.enableNewForm = enableNewForm || false;
                    this.kpSignerName = (kpSigner && kpSigner.name) || '';
                    this.kpSignerMobile = (kpSigner && kpSigner.mobile) || '';
                    this.kpSignerStatus = (kpSigner && kpSigner.status) || 0;
                    this.customerNumber = customerNumber;
                }
                return Promise.resolve();
            } catch (error) {
                console.error('获取配置信息失败:', error);
                // 失败时使用默认值
                return Promise.reject();
            }
        },

        getVerifyGray() {
            request.get('/finance/invoice/api/output/waimai/q/verifyGray').then((res) => {
                this.verifyGray = res.data;
            });
        },
        handleCheck(checked) {
            this.isCheck = checked;
        },
        handleRemove(idx) {
            this.qualifications = this.qualifications.filter((q, index) => index !== idx);
        },
        handleAddQualification() {
            if (this.qualifications.length >= 50) {
                alert('一次最多增加50个抬头信息，若超过50个请再次提交。');
                return;
            }
            this.qualifications.push({ ...template });
        },
        buildBody() {
            const {
                contractId,
                qualificationType,
                qualifications,
                partnerType,

                authorizationUrls,
                subpoiConfirmLetterUrls,
                enableNewForm,
            } = this;

            const applyList = qualifications.map((q) => {
                const ret = {
                    ...q,
                    authorizationUrls,
                    subpoiConfirmLetterUrls,
                };

                // 取 imageName
                [
                    'subpoiBusinessLicenseUrls',
                    'subpoiConfirmLetterUrls',
                    'authorizationUrls',
                    'protocolUrls',
                ].forEach((key) => {
                    if (ret[key]) {
                        ret[key] = ret[key].map(x => x.imageName);
                    }
                });

                return ret;
            });

            return {
                contractId,
                qualificationType,
                partnerType,
                applyList,
                enableNewForm,
            };
        },
        // 新表单验证
        validateNewForm() {
            const { forms } = this.$refs;

            return new Promise((resolve, reject) => {
                // 校验kp签约人信息（只要存在新资质税号是否与合同甲方税号不同，则校验kp签约人信息）
                const hasDiffFromCustomerNumber = this.qualifications.some(q => q.taxpayerIdNo !== this.customerNumber);
                if (hasDiffFromCustomerNumber && (this.kpSignerStatus === 0 || this.kpSignerStatus === -1)) {
                    reject(new Error('请维护合同kp签约人信息'));
                    return;
                }

                // 校验所有新资质抬头名称和税号是否填写
                const invalidQualifications = this.qualifications.filter(q => !q.partnerBName || !q.taxpayerIdNo);

                if (invalidQualifications.length > 0) {
                    reject(new Error('请检查新资质名称有效性'));
                    return;
                }

                // 校验营业执照OCR识别的税号与新资质抬头税号是否一致
                let errorMsg = '';
                this.qualifications.some(q => {
                    // 如果新资质税号与合同甲方税号相同，不填写营业执照相关内容，字段为空，跳过本轮进入下一轮校验
                    if (q.subpoiBusinessLicenseUrls[0]
                        && q.subpoiBusinessLicenseUrls[0].taxpayerIdNo
                        && (q.taxpayerIdNo !== q.subpoiBusinessLicenseUrls[0].taxpayerIdNo)) {
                        // 税号不匹配
                        errorMsg = '资质税号需与上传的营业执照一致';
                        return true;
                    }

                    return false;
                });

                if (errorMsg) {
                    reject(new Error(errorMsg));
                    return;
                }

                // 新资质表单基础校验
                let count = 0;
                const len = forms.length;

                function cb(pass) {
                    if (pass) {
                        count += 1;
                        if (count === len) {
                            resolve();
                        }
                    } else {
                        reject(new Error('资质信息填写有误，请检查'));
                    }
                }

                for (let i = 0; i < len; ++i) {
                    forms[i].validate(cb);
                }
            });
        },

        // 旧表单验证
        validateOldForm() {
            const { forms, form2 } = this.$refs;
            const arr = forms.concat([form2]);

            return new Promise((resolve, reject) => {
            // 先检查是否所有纳税人识别号都已填写
                const hasEmptyTaxpayerId = this.qualifications.some((q, index) => {
                    if (!q.taxpayerIdNo) {
                        // 设置错误信息
                        this.$set(this.taxpayerIdNoErrors, index, '未查询到有效纳税人识别号');
                        return true;
                    }
                    return false;
                });

                if (hasEmptyTaxpayerId) {
                    toast.warn('请完善纳税人识别号信息');
                    reject();
                    return;
                }
                let count = 0;
                const len = arr.length;

                function cb(pass) {
                    if (pass) {
                        count += 1;

                        if (count === len) {
                            resolve();
                        }
                    } else {
                        reject();
                    }
                }

                for (let i = 0; i < len; ++i) {
                    arr[i].validate(cb);
                }
            });
        },

        handleSmsCodesUpdate(newSmsCodes) {
            this.smsCodes = newSmsCodes;
        },
        handleMobile(newMobile) {
            this.mobile = newMobile;
        },
        save() {
            this.$nextTick(async () => {
                try {
                    await this.$refs.smsYodaVerification.getMobile();
                } catch (err) {
                    console.log(err);
                    // 错误处理已经在 getMobile 中处理
                }
            });
        },
        handleSaveClick() {
            if (!this.qualificationType) {
                toast.warn('请选择资质类型');
                return;
            }

            const validate = this.enableNewForm ? this.validateNewForm : this.validateOldForm;

            if (this.shouldNeedVerification === true) {
                validate()
                    .then(
                        this.save,
                        error => {
                            toast.warn((error && error.message) || '资质信息填写有误，请检查');
                        },
                    );
            } else {
                validate()
                    .then(
                        this.handleSubmit,
                        error => {
                            toast.warn((error && error.message) || '资质信息填写有误，请检查');
                        },
                    );
            }
        },
        async handleSubmit() {
            const body = this.buildBody();
            if (this.shouldNeedVerification === true) {
                body.smscode = this.smsCodes;
                body.uuid = getCookieValue('device_uuid');
                body.mobile = this.mobile;
                body.ua = this.getUserAgent();
                body.needCheckCode = true;
            }
            this.submitting = true;
            try {
                const res = await request.post('/finance/invoice/api/common/qualification/w/save', body);
                this.submitting = false;
                const { code, msg, data } = res.data;
                if (code === 0) {
                    if (this.shouldNeedVerification === true && data.response_code) {
                        this.isSuccessCode = true;
                        this.displayModal = false;
                    }
                    this.displaySubmitModal = true;
                }
                if (code === 2) {
                    alert(msg, () => {
                        this.$router.go(-1);
                    });
                    return;
                }
                if (code !== 0) {
                    if (msg && msg.message) {
                        this.checkSmsError = msg.message;
                        return;
                    }
                    toast.error(msg);
                }
            } catch (err) {
                this.submitting = false;
                toastError(err);
            }
        },
        beforeDestroy() {
            // 组件销毁前清除定时器
            if (this.timer) {
                clearInterval(this.timer);
            }
        },
        getUserAgent() {
            return navigator.userAgent;
        },
        handleConfirmDone() {
            this.$router.go(-1);
        },
        handleChange(keyword, index) {
            if (!keyword) {
                return;
            }

            const params = {
                partnerBName: keyword,
            };

            request.get('/finance/invoice/api/output/waimai/q/queryTaxNoByName', { params }).then((res) => {
                const { code, msg, data } = res.data;
                // 统一处理错误情况（接口失败或无数据）
                if (code !== 0 || !data) {
                    const errorMsg = code !== 0
                        ? (msg || '查询纳税人识别号失败')
                        : '未查询到有效纳税人识别号';

                    // 直接使用传入的 index 更新对应的错误信息
                    this.$set(this.taxpayerIdNoErrors, index, errorMsg);

                    // 清空纳税人识别号
                    this.$set(this.qualifications[index], 'taxpayerIdNo', '');
                    return;
                }

                // 处理成功情况
                this.$set(this.taxpayerIdNoErrors, index, '');
                this.$set(this.qualifications[index], 'taxpayerIdNo', data);
            }).catch(toastError);
        },
        handleQualificationUpdate(index, newData) {
            // 使用 Vue.set 来确保响应式更新
            this.$set(this.qualifications, index, newData);
        },
    },
};
</script>

<style lang="scss" module>
.bottomText{

font-size: 14px;
padding: 0px 100px;
}
.container {
    position: relative;
    min-height: 100%;
    padding-top: 10px;
    padding-bottom: 56px;
}

.back {
    margin-right: 16px;
}

.card {
    padding: 20px 40px;
    background: #FFF;
    border-radius: 2px;
    box-shadow: 0 0 6px 0 #F3F3F4;
}

.tip {
    color: #9E9E9E;
}

.headerRow {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.h3 {
    margin: 0;
    font-size: 20px;
}

.downloadLink {
    color:  #FF6A00;
    text-decoration: none;
    font-size: 14px;
    line-height: 20px;
    letter-spacing: 0px;

    &:hover {
        text-decoration: underline;
    }
}

.row {
    margin-top: 10px;
    margin-left: 20px;
}

.form {
    // width: 800px;
    padding-bottom: 20px;
    margin-bottom: 20px;
    border-bottom: 1px dashed #EBEEF2;

    :global(.form-group.row:last-child) {
        margin-bottom: 0;
    }

    h4 {
        margin-top: 0;
        margin-bottom: 20px;
        font-size: 16px;
    }
}

.input {
    display: inline-block;
    width: 300px;
    vertical-align: middle;
}

.rm-button {
    float: right;
}

.delete-button {
    display: inline-block;
    margin-right: 16px;
    line-height: 36px;
    color: #F76C6C;
    cursor: pointer;
    vertical-align: middle;
}

.add-row {
    margin-top: 20px;
    text-align: center;
}
.audit-rule {
    color:  #f89800;
    cursor: pointer;
    margin-left: 15px;
}
.smsWrapper {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-top: 12px;

    .roo-input {
        flex: 1;
    }

    .smsBtn {
        min-width: 100px;
        white-space: nowrap;
        &[disabled] {
            color: #c5c8ce;
            cursor: not-allowed;
        }
    }
}
.unBindTitle{
    display: flex;
    align-items: center;
   p{
    font-size: 16px;
    font-weight: 500;
    color: #222222;
    margin-left: 10px;
    font-family: PingFang SC;
   }
}
.unBindContent{
    font-family: PingFang SC;
    font-size: 14px;
    color: #666666;
    margin-top: 7px;
    margin-bottom: 24px;
    display: inline-block;
}
.unBindBtn{
    text-align: right;
    margin-bottom: 24px;
}

.yodaContainer {
    margin-top: 16px;
    min-height: 150px;  /* 为验证码UI预留空间 */
    :global(#yoda-root) {
        width: 100% !important;
        :global(.yoda-slider-wrapper) {
            margin: 0 auto;
        }
    }
}
.modalTitle{
    font-size: 20px;
    color: #222222;
    font-weight: 500;
}

</style>
