<template>
    <roo-modal
        :class="$style.modal"
        :value="display"
        size="large"
        title="请选择您要开票的合同"
    >
        <roo-alert
            :class="$style.alert"
            type="warning"
            icon="exclamation-circle"
        >
            本账号下门店关联多个结算合同，每次开票只能选择一个合同，请选择
        </roo-alert>

        <roo-autocomplete
            v-model="keyword"
            :class="$style.input"
            :fetch-suggestions="fuzzySearch"
            :option-renderer="optionRender"
            placeholder="输入门店名称查询"
            @select="handleSelect"
        />

        <contract-table
            :loading="contractLoading"
            :checked="contract && contract.contractId"
            :list="showContractList"
            :target="target"
            @back="target = null"
            @select="contract = $event"
            @show-poi-list="$emit('show-poi-list', $event)"
        />

        <template slot="footer">
            <roo-button
                :disabled="!contract"
                @click="handleConfirmClick"
            >
                确定选择
            </roo-button>
        </template>
    </roo-modal>
</template>

<script>
import axios from 'axios';
/* eslint-disable import/no-unresolved, import/extensions */
import request from '$invoice/utils/request';
import { mapState, mapMutations } from 'vuex';

/* eslint-disable import/extensions, import/no-unresolved */
import toastError from '$lib/toast-error';
import { debounce } from '$lib/utils';
/* eslint-enable import/extensions, import/no-unresolved */

import ContractTable from './components/contract-table';

const { CancelToken } = axios;

function buildQuery(params) {
    const pairs = [];
    Object.keys(params).forEach((key) => {
        const val = params[key];
        if (Array.isArray(val)) {
            val.forEach((v) => pairs.push(`${encodeURIComponent(key)}=${encodeURIComponent(v)}`));
        } else {
            pairs.push(`${encodeURIComponent(key)}=${encodeURIComponent(val)}`);
        }
    });
    return pairs.join('&');
}

export default {
    name: 'ContractModal',
    components: {
        ContractTable,
    },
    data() {
        return {
            keyword: null,
            suggestions: [], // 门店提示
            target: null, // 选中门店

            // 选中合同信息
            contract: null,
        };
    },
    computed: {
        ...mapState('config', [
            'contractLoading',
            'contractList',

            'contractId',
            'contractAName',
        ]),
        display() {
            const { contractList, contractId } = this;
            console.log('contractList, contractId', contractList, contractId);
            // 弹出那个开票合同的弹窗，是当当前poiID下，有多个合同时
            return contractList.length > 0 && !contractId;
        },
        showContractList() {
            const { contractList, target } = this;
            if (target) {
                return contractList.filter(
                    (item) => item.contractId === target.contractId,
                );
            }
            return contractList;
        },
    },
    watch: {
        display(val) {
            if (val) {
                this.sync();
            }
        },
    },
    created() {
        this.sync();

        this.source = null;
        this.fuzzySearch = debounce(this.fuzzySearch);
    },
    methods: {
        ...mapMutations('config', ['changeContractId', 'changeContractAName']),
        sync() {
            const { contractId, contractAName } = this;
            if (contractId) {
                this.contract = {
                    contractId,
                    contractAName,
                };
            }
        },
        fuzzySearch(keyword, callback) {
            if (this.source) {
                this.source.cancel();
                this.source = null;
            }

            if (!keyword) {
                return;
            }

            this.source = CancelToken.source();
            const cancelToken = this.source.token;

            const params = {
                fuzzy: keyword,
                contractIds: this.contractList.map((x) => x.contractId),
            };

            request.get('/finance/invoice/api/pc/application/r/poiInfosFuzzily', {
                params,
                cancelToken,
                paramsSerializer: buildQuery,
            })
                .then((res) => {
                    const { code, msg, data } = res.data;
                    if (code !== 0) {
                        throw new Error(msg);
                    }

                    this.suggestions = (data || []).map((item) => ({
                        ...item,
                        value: item.contractId,
                        label: item.wmPoiName,
                    }));

                    callback(this.suggestions);
                })
                .catch(toastError);
        },
        handleSelect(value) {
            const { suggestions } = this;
            for (let i = 0; i < suggestions.length; ++i) {
                const item = suggestions[i];
                if (item.value === value) {
                    this.target = item;
                    // this.$nextTick(() => {
                    //     this.keyword = this.target.wmPoiName;
                    // });
                    return;
                }
            }
        },
        handleConfirmClick() {
            const { contract } = this;
            if (contract) {
                this.changeContractId(contract.contractId);
                this.changeContractAName(contract.contractAName);
            }
        },
        optionRender(h, { value, label }) {
            const { keyword } = this;
            const content = label.split(keyword).map((part, index) => {
                const replace = index === 0 ? '' : (<span style="color:red;">{keyword}</span>);
                return (
                    <span>
                        {replace}
                        {part}
                    </span>
                );
            });
            return (
                <span>
                    {content} (ID: {value})
                </span>
            );
        },
    },
};
</script>

<style module>
.modal {
    :global(.roo-modal-body) {
        max-height: 400px;
        padding: 20px !important;
        overflow-y: auto;
    }
}

.alert {
    margin: 0 0 10px 0;
}

.input {
    width: 200px;
    margin: 10px 0;
}
</style>
