<template>
    <div v-if="isSinglePoi">
        <contract-modal
            @show-poi-list="displayContractPoiList = $event"
        />
        <poi-modal
            :contract="displayContractPoiList"
            @input="displayContractPoiList = $event ? displayContractPoiList : null"
        />
        <roo-loading
            :show="feeTypeLoading || contractLoading"
            fullscreen
            backdrop
            lock
        />
    </div>
</template>

<script>
import { mapState } from 'vuex';

import PoiModal from './poi-modal';
import ContractModal from './contract-modal';

export default {
    name: 'ContractSelect',
    components: {
        PoiModal,
        ContractModal,
    },
    data() {
        return {
            displayContractPoiList: null,
        };
    },
    computed: {
        ...mapState('config', [
            'feeTypeLoading',
            'contractLoading',
            'isSinglePoi'
        ]),
    },
};
</script>
