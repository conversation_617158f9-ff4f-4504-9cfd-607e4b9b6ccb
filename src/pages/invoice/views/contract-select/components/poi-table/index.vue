<template>
    <table :class="$style.table">
        <col style="width: 140px">
        <col>

        <thead>
            <tr>
                <th>商家 ID</th>
                <th>商家名称</th>
            </tr>
        </thead>

        <tbody>
            <tr v-if="loading">
                <td
                    :class="$style.loading"
                    colspan="2"
                >
                    正在加载
                </td>
            </tr>
            <tr v-else-if="list.length === 0">
                <td
                    :class="$style.empty"
                    colspan="2"
                >
                    暂无数据
                </td>
            </tr>
            <template v-else>
                <tr
                    v-for="item in list"
                    :key="item.wmPoiId"
                >
                    <td>{{ item.wmPoiId }}</td>
                    <td>
                        {{ item.wmPoiName }}
                    </td>
                </tr>
            </template>
        </tbody>
    </table>
</template>

<script>
export default {
    name: 'PoiTable',
    props: {
        loading: {
            type: Boolean,
            default: false,
        },
        list: {
            type: Array,
            default() {
                return [];
            },
        },
    },
};
</script>

<style lang="scss" module>
.table {
    width: 100%;
    table-layout: fixed;
    border-collapse: collapse;
    border: 1px solid #E9EAF2;
    font-size: 14px;
    line-height: 20px;

    thead,
    tbody tr:nth-of-type(even) {
        background: #F7F8FA;
    }

    th, td {
        height: 40px;
        padding: 0 20px;
    }

    th {
        color: #858692;
    }

    .loading,
    .empty {
        text-align: center;
    }
}
</style>
