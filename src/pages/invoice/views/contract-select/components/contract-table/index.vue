<template>
    <table :class="$style.table">
        <col style="width: 100px;">
        <col>
        <col style="width: 160px;">

        <thead>
            <tr>
                <th>选择合同</th>
                <th>公司名称</th>
                <th :class="$style.right">
                    <a
                        v-if="target"
                        href="#noop"
                        @click.prevent="$emit('back')"
                    >
                        返回
                    </a>
                </th>
            </tr>
        </thead>

        <tbody
            v-if="loading"
            key="loading"
        >
            <tr>
                <td colspan="3">
                    加载中
                </td>
            </tr>
        </tbody>
        <tbody
            v-else
            key="list"
        >
            <tr
                v-for="item in list"
                :key="item.contractId"
            >
                <td>
                    <roo-radio
                        :class="$style.radio"
                        :checked="item.contractId === checked"
                        @click.native.prevent="handleRadioClick(item)"
                    />
                </td>
                <td>
                    {{ item.contractAName }}
                    <div v-if="target">
                        <span :class="$style.gray">本合同下包含门店：</span>
                        {{ target.wmPoiId }} {{ target.wmPoiName }}
                    </div>
                </td>
                <td>
                    <a
                        href="#noop"
                        @click.prevent="$emit('show-poi-list', item)"
                    >
                        查看合同关联门店
                    </a>
                </td>
            </tr>
        </tbody>
    </table>
</template>

<script>
export default {
    name: 'ContractTable',
    props: {
        loading: {
            type: Boolean,
            default: false,
        },
        list: {
            type: Array,
            default() {
                return [];
            },
        },
        target: { // 包含的门店
            type: Object,
            default: null,
        },
        checked: {
            type: Number,
            default: null,
        },
    },
    methods: {
        handleRadioClick(contract) {
            if (contract.contractId !== this.checked) {
                this.$emit('select', contract);
            }
        },
    },
};
</script>

<style lang="scss" module>
.table {
    width: 100%;
    table-layout: fixed;
    border-collapse: collapse;
    border: 1px solid #E9EAF2;
    font-size: 14px;
    line-height: 20px;

    thead,
    tbody tr:nth-of-type(even) {
        background: #F7F8FA;
    }

    th, td {
        padding: 10px 20px;
    }

    th {
        color: #858692;
    }

    tbody td:first-child {
        text-align: center;
    }

    .right {
        text-align: right;
    }
}
.radio:global(.roo-radio) { // display 设成 inline 居中对齐
    display: inline;
}
.gray {
    color: #A2A4B3;
}
</style>
