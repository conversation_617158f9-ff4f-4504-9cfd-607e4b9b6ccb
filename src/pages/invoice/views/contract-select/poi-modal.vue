<template>
    <roo-modal
        :value="!!contract"
        :class="$style.modal"
        :title="contractAName || (contract && contract.contractAName)"
        @input="$emit('input', $event)"
        @close="$emit('close', $event)"
        @hidden="handleHidden"
    >
        <poi-table
            :loading="loading"
            :list="list"
        />

        <roo-button
            slot="footer"
            @click="$emit('input', false)"
        >
            知道了
        </roo-button>
    </roo-modal>
</template>

<script>
import axios from 'axios';
/* eslint-disable import/no-unresolved, import/extensions */
import request from '$invoice/utils/request';

/* eslint-disable import/extensions, import/no-unresolved */
import toastError from '$lib/toast-error';
/* eslint-enable import/extensions, import/no-unresolved */

import PoiTable from './components/poi-table';

const { CancelToken } = axios;

export default {
    name: 'PoiModal',
    components: {
        PoiTable,
    },
    props: {
        // 要展示的合同对象 contract { contractId, contractAName }
        contract: {
            type: Object,
            default: null,
        },
    },
    data() {
        return {
            loading: false,

            // 暂存接口返回的合同甲方名称，标题优先取该名称，避免关闭过渡期间标题消失
            contractAName: '',
            list: [],
        };
    },
    watch: {
        contract(val) {
            if (val) {
                this.fetchPoiList(val.contractId).catch(toastError);
            } else {
                if (this.source) {
                    this.source.cancel();
                    this.source = null;
                }
            }
        },
    },
    created() {
        this.source = null;
    },
    methods: {
        fetchPoiList(contractId) {
            if (this.source) {
                this.source.cancel();
                this.source = null;
            }

            const params = { contractId };

            this.source = CancelToken.source();
            const cancelToken = this.source.token;

            this.loading = true;
            return request.get('/finance/invoice/api/pc/application/r/poiInfos', {
                params,
                cancelToken,
            }).then((res) => {
                const { code, msg, data } = res.data;
                if (code !== 0) {
                    throw new Error(msg);
                }

                this.loading = false;
                this.contractAName = data.contractAName || '';
                this.list = data.poiList || [];
            }, (err) => {
                this.loading = false;
                throw err;
            });
        },
        handleHidden(event) {
            this.loading = false;

            this.contractAName = '';
            this.list = [];
            this.$emit('hidden', event);
        },
    },
};
</script>

<style lang="scss" module>
.modal {
    :global(.roo-modal-body) {
        max-height: 400px;
        padding: 20px !important;
        overflow-y: auto;
    }
}
</style>
