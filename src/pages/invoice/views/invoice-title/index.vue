<template>
    <div :class="$style.container">
        <roo-alert
            :class="$style.alert"
            type="warning"
            icon="exclamation-circle"
        >
            设置抬头与门店的对应关系，选择该抬头开票时关联门店金额将自动汇总至该抬头，提升您的开票效率
        </roo-alert>
        <div :class="$style.search">
            <roo-input
                v-model="keyword"
                :class="$style.input"
                placeholder="请输入发票抬头查询"
                @change="handleKeywordChange"
            />
            <roo-button
                type="hollow"
                @click="handleSearchTitle"
            >
                查询
            </roo-button>
            <roo-button
                :class="$style.addQualification"
                @click="addTitle"
            >
                新增抬头
            </roo-button>
        </div>

        <title-table
            key="titleTable"
            :list="titleInfoList"
            :loading="titleInfoLoading"
            :total="invoiceTitleTotal"
            @show-detail="displayDelete = true; deleteQuaItem = $event"
        />

        <roo-modal
            v-model="displayDelete"
            size="small"
        >
            <div :class="$style.flexRow">
                <img
                    :class="$style.img"
                    src="https://s3plus.meituan.net/v1/mss_0e4451a82e1d428f9fc3b99c0b3ebf15/pic-manager/d9cd2fa2c93ea6b8/icon_wenhao.png"
                />
                <div style="margin-left: 10px;">
                    <div :class="$style.deleteTitle">
                        删除该发票抬头
                    </div>
                    <div :class="$style.deleteContent">
                        删除后将无法恢复
                    </div>
                </div>
            </div>

            <template slot="footer">
                <roo-button
                    type="text"
                    @click="displayDelete = false"
                >
                    取消
                </roo-button>
                <roo-button @click="handleConfirmDel">
                    确定
                </roo-button>
            </template>
        </roo-modal>

        <div :class="$style.pagination">
            <roo-pagination
                :total="invoiceTitleTotal"
                :page-size="pageSize"
                :current-page="pageNo"
                @current-change="handlePageChange($event)"
            />
        </div>
    </div>
</template>

<script>
/* eslint-disable import/no-unresolved, import/extensions */
import request from '$invoice/utils/request';
import { mapState, mapMutations, mapActions } from 'vuex';
import toastError from '$lib/toast-error';
/* eslint-disable import/extensions, import/no-unresolved */
import TitleTable from './components/title-table';
/* eslint-disable import/extensions, import/no-unresolved */
export default {
    name: 'InvoiceTitle',
    components: {
        TitleTable,
    },
    computed: {
        ...mapState('config', ['contractId']),
        ...mapState('invoiceTitle', ['titleInfoList', 'titleInfoLoading', 'invoiceTitleTotal']),
    },
    data() {
        return {
            displayDelete: false,
            pageNo: 1,
            pageSize: 20,
            deleteQuaItem: {},
            keyword: '',
            queryString: '',
        };
    },
    methods: {
        ...mapActions('invoiceTitle', ['fetchTitleInfoList']),
        ...mapMutations('titleManagement', ['changeIsEdit']),
        addTitle() {
            this.changeIsEdit(false);
            this.$router.push({ name: 'title-management' });
        },
        handleConfirmDel() {
            const { contractId, deleteQuaItem } = this;
            const params = {
                contractId,
                qualificationId: deleteQuaItem.id,
                partnerBName: deleteQuaItem.partnerBName,
                partnerType: 103,
            };
            request.get('/finance/invoice/api/output/waimai/q/deleteInvoiceTitle', { params }).then((res) => {
                const { code, msg, data } = res.data;

                if (code !== 0) {
                    throw new Error(msg);
                }
                if (code === 0) {
                    this.displayDelete = false;
                    this.fetchTitleInfoList(1);
                }

                return data;
            }).catch(toastError);
        },
        handleKeywordChange(val) {
            if (val !== this.keyword) {
                this.keyword = val;
            }
        },
        handleSearchTitle() {
            this.pageNo = 1;
            // this.queryString = this.keyword;
            this.fetchTitleInfoList({ pageNo: 1, fuzzy: this.keyword });
        },
        handlePageChange(pageNum) {
            this.pageNo = pageNum;
            this.fetchTitleInfoList({ pageNo: pageNum, fuzzy: this.keyword });
        },
    },
    mounted() {
        this.fetchTitleInfoList(1);
    },
};
</script>

<style lang="scss" module>
.container {
    min-height: 100%;
    margin-top: 20px;
    // padding-bottom: 56px;
    background: #FFF;
    padding: 50px 20px 56px;
}
.search {
    width: 100%;
    padding-top: 30px;
    .input {
        display: inline-block;
        width: 260px;
        margin-right: 10px;
        vertical-align: middle;
    }
    .addQualification {
        float: right;
        font-weight: normal;
    }
}
.flexRow {
    display: flex;
    flex-direction: row;
}
.img {
    width: 30px;
    height: 30px;
}
.pagination {
    text-align: right;
}
.alert{
    margin-top: 32px;
}
</style>
