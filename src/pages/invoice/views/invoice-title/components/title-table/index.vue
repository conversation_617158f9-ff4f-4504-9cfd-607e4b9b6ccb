<template>
    <table :class="$style.table">
        <col style="width: 10%;" />
        <col style="width: 10%;" />
        <col style="width: 10%;" />
        <col style="width: 15%;" />
        <col style="width: 10%;" />
        <col style="width: 15%;" />
        <col style="width: 10%;" />
        <col style="width: 10%;" />
        <col style="width: 10%;" />

        <thead>
            <tr>
                <th>抬头名称</th>
                <th>
                    状态
                    <span>
                        <roo-icon
                            name="question2"
                            size="14px"
                        />
                        <roo-tooltip
                            placement="top-right"
                        >
                            <slot>
                                <div>审核通过的抬头可用于开票</div>
                            </slot>
                        </roo-tooltip>
                    </span>
                </th>
                <th>
                    关联门店
                    <span>
                        <roo-icon
                            name="question2"
                            size="14px"
                        />
                        <roo-tooltip
                            placement="top-right"
                        >
                            <slot>
                                <div>设置抬头与门店的对应关系，选择该抬头开票时关联门店金额将自动汇总至该抬头，提升您的开票效率</div>
                            </slot>
                        </roo-tooltip>
                    </span>
                </th>
                <th>纳税人识别号</th>
                <th>开户行</th>
                <th>银行账号</th>
                <th>注册地址</th>
                <th>电话</th>
                <th>操作</th>
            </tr>
        </thead>

        <tbody
            v-if="loading"
            key="loading"
        >
            <tr>
                <td colspan="6">
                    加载中
                </td>
            </tr>
        </tbody>

        <tbody
            v-else
            key="list"
        >
            <tr
                v-for="item in list"
                :key="item.id"
            >
                <td>
                    {{ item.partnerBName }}
                </td>
                <td>
                    <div :class="$style.status">
                        <span :class="getStatusClass(item.status)"></span>
                        <span :class="$style.statusContent">
                            <!-- {{ ['待审核', '审核通过', '驳回'][item.status] }} -->
                            {{ getStatusText(item.status) }}
                        </span>
                    </div>
                </td>
                <td>
                    <a
                        :class="$style.relatedCount"
                        @click="handleJumpRelateStore(item)"
                    >
                        {{ item.partnerNum }}家
                    </a>
                </td>
                <td>
                    {{ item.taxpayerIdNo ? item.taxpayerIdNo : '-' }}
                </td>
                <td>
                    {{ item.bankName ? item.bankName : '-' }}
                </td>
                <td>
                    {{ item.cardNo ? item.cardNo : '-' }}
                </td>
                <td>
                    {{ item.registerAddress ? item.registerAddress : '-' }}
                </td>
                <td>
                    {{ item.telNo ? item.telNo : '-' }}
                </td>
                <td>
                    <template>
                        <span
                            :class="$style.link"
                            @click="handleEdit(item)"
                        >
                            编辑
                        </span>
                        <span
                            :class="disableDelete(item.status) ? [$style.link, $style.disabled] : $style.link"
                            @click="!disableDelete(item.status) && $emit('show-detail', item)"
                        >
                            删除
                        </span>
                    </template>
                </td>
            </tr>
        </tbody>
    </table>
</template>

<script>
/* eslint-disable import/extensions, import/no-unresolved */
import { mapActions, mapMutations } from 'vuex';
/* eslint-disable import/extensions, import/no-unresolved */
export default {
    name: 'TitleTable',
    data() {
        return {};
    },
    methods: {
        ...mapMutations('invoiceTitle', ['changeEditItemInfo']),
        ...mapMutations('titleManagement', ['changeIsEdit']),
        ...mapActions('titleManagement', ['fetchRelatedStoreList']),
        getStatusClass(status) {
            const statusClassMap = {
                0: this.$style.yellow, // 人工审核中
                1: this.$style.green, // 审核通过
                2: this.$style.red, // 驳回
                3: this.$style.yellow, // 系统审核中
                100: this.$style.yellow, // 甲方签约中
                101: this.$style.red, // 甲方签约失败
                102: this.$style.yellow, // 新资质签约中
                103: this.$style.red, // 新资质签约失败
            };
            return statusClassMap[status] || '';
        },
        getStatusText(status) {
            const statusTextMap = {
                0: '人工审核中',
                1: '审核通过',
                2: '驳回',
                3: '系统审核中',
                100: '甲方签约中',
                101: '甲方签约失败',
                102: '丙方（新资质）签约中',
                103: '丙方（新资质）签约失败',
            };
            return statusTextMap[status] || '-';
        },
        handleEdit(item) {
            this.changeEditItemInfo(item);
            this.changeIsEdit(true);
            this.$router.push({ name: 'edit-title', query: { id: item.id } });
        },
        handleJumpRelateStore(item) {
            this.changeEditItemInfo(item);
            this.changeIsEdit(true);
            // this.fetchRelatedStoreList();
            this.$router.push({ name: 'relate-store', query: { id: item.id } });
        },
        handleSearchTitle() {},
        disableDelete(status) {
            // 签约中的抬头不能删除
            return status === 100 || status === 102;
        },
    },
    props: {
        list: {
            type: Array,
            default() {
                return [];
            },
        },
        loading: {
            type: Boolean,
            default: false,
        },
        total: {
            type: Number,
            default: 0,
        },
    },
};
</script>

<style lang="scss" module>
.table {
    width: 100%;
    border: 1px solid #E9EAF2;
    table-layout: fixed;
    border-collapse: collapse;
    color: #3F4156;
    margin-top: 20px;

    thead,
    tbody tr:nth-of-type(even) {
        background: #F7F8FA;
    }

    th, td {
        padding: 0 20px;
        text-align: start;

        &:last-child {
            text-align: center;
        }
    }

    th {
        font-weight: normal;
        height: 50px;
        color: #858692;
    }

    td {
        height: 90px;
        word-break: break-all;
    }
}
.status {
    display: flex;
    justify-content: start;
    align-items: center;
}
.green {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: #00BF7F;
}
.blue {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: #097DFF;;
}
.red {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: #FF192D;
}
.yellow {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: #FFCC33;
}
.statusContent {
    margin-left: 4px;
}
.relatedCount {
    color: #FF7700;
    cursor: pointer;
}
.link {
    color: #F89800;
    cursor: pointer;

    &.disabled {
        color: #999;
        cursor: not-allowed;
        pointer-events: none;
    }
}
</style>
