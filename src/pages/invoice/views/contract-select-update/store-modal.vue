<template>
    <roo-modal
        :class="$style.modal"
        :value="value"
        @input="$emit('input', $event)"
        @close="handleClose($event)"
    >
        <template slot="title">
            <span :class="$style.tip">
                {{ contractAName }}
            </span>
        </template>

        <table :class="$style.table">
            <col style="width: 280px;" />
            <col />

            <thead>
                <tr>
                    <th>门店名称</th>
                    <th>门店ID</th>
                </tr>
            </thead>

            <tbody
                v-if="loading"
                key="loading"
            >
                <tr>
                    <td
                        colspan="6"
                        style="text-align: center;"
                    >
                        加载中
                    </td>
                </tr>
            </tbody>

            <tbody v-else-if="poiInfos.length === 0">
                <tr>
                    <td
                        :class="$style.empty"
                        colspan="2"
                    >
                        暂无数据
                    </td>
                </tr>
            </tbody>

            <tbody
                v-else
                key="poiInfos"
            >
                <tr
                    v-for="item in poiInfos"
                    :key="item.wmPoiId"
                >
                    <td>{{ item.wmPoiName }}</td>
                    <td>{{ item.wmPoiId }}</td>
                </tr>
            </tbody>
        </table>
    </roo-modal>
</template>

<script>
/* eslint-disable import/no-unresolved, import/extensions */
import { mapState, mapMutations } from 'vuex';
/* eslint-disable import/no-unresolved, import/extensions */
export default {
    name: 'StoreModal',
    computed: {
        ...mapState('config', ['contractAName', 'contractId', 'poiInfos']),
    },
    props: {
        value: {
            type: Boolean,
            default: false,
        },
        loading: {
            type: Boolean,
            default: false,
        },
        contract: {
            // { contractId, contractAName }
            type: Object,
            default: null,
        },
    },
    methods: {
        ...mapMutations('config', ['changePoiInfos']),
        handleClose($event) {
            this.$emit('close', $event);
            this.changePoiInfos([]);
        },
    },
};
</script>

<style lang="scss" module>
.modal {
    :global(.roo-modal-body) {
        max-height: 400px;
        padding: 20px !important;
        overflow-y: auto;
    }
    &:global(.roo-modal .roo-modal-dialog) {
        width: 500px;
    }
}
.tip {
    font-weight: 500;
    font-size: 20px;
    color: #222222;
}
.table {
    width: 100%;
    // height: 267px;
    border: 1px solid #e9eaf2;
    table-layout: fixed;
    border-collapse: collapse;
    color: #3f4156;

    th,
    td {
        height: 40px;
        padding: 0 20px;
        text-align: left;
    }

    th {
        font-weight: normal;
        color: #858692;
    }

    .loading,
    .empty {
        text-align: center;
    }
}
</style>
