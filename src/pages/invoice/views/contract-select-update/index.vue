<template>
    <div :class="$style.container">
        <div :class="$style.header" v-if="!contractLoading">
            <img
                :class="$style.headerImg"
                src="./image/<EMAIL>"
            />
            <div>
                <div :class="$style.title">
                    请选择需要开票的合同
                </div>
                <div :class="$style.extra">
                    当前账号下绑定的门店分属于多个客户合同，请选择本次开票的客户合同
                </div>
            </div>
        </div>
        <div :class="$style.content" v-if="!contractLoading">
            <roo-input
                v-model="keyword"
                :class="$style.input"
                clearable
                placeholder="输入门店ID/名称"
                @change="handleKeywordChange"
            />
            <roo-button
                :loading="loading"
                @click="handleSearch"
            >
                查询
            </roo-button>

            <contract-table
                key="contractSelectUpdate"
                :list="showContractList"
                :loading="contractLoading"
                :total="contractList.length"
                @show-detail="contractItem = $event; displayStore = true"
            />

            <store-modal
                v-model="displayStore"
                :contract="contractItem"
                :loading="storeModalLoading"
                @hidden="contractItem = null"
            />

            <div
                v-if="contractList.length > 0"
                :class="$style.pagination"
            >
                <roo-pagination
                    :total="contractList.length"
                    :page-size="pageSize"
                    :current-page="pageNo"
                    @current-change="pageNo = $event"
                />
            </div>
        </div>
        <div :class="$style.loadingContract" v-if="contractLoading">
            <roo-loading show icon-type="circle" />
        </div>
    </div>
</template>

<script>
import { mapState, mapMutations, mapActions } from 'vuex';

/* eslint-disable import/extensions, import/no-unresolved */
import ContractTable from './contract-table';
import StoreModal from './store-modal.vue';
import toastError from '$lib/toast-error';
/* eslint-disable import/extensions, import/no-unresolved */

export default {
    name: 'ContractSelectUpdate',
    components: {
        ContractTable,
        StoreModal,
    },
    computed: {
        ...mapState('config', [
            'contractList',
            'contractLoading',
        ]),
        showContractList() {
            const { contractList } = this;
            const offset = (this.pageNo - 1) * this.pageSize;
            return contractList.slice(offset, offset + this.pageSize);
        },
    },
    data() {
        return {
            contractItem: null,
            displayStore: false,
            keyword: '',
            loading: false,
            pageNo: 1,
            pageSize: 20,
            queryString: null,
            storeModalLoading: false,
        };
    },
    methods: {
        ...mapActions('config', ['loadContractList', 'fetchContractInfo']),
        ...mapActions('invoiceTitle', ['fetchPromotBar']),
        ...mapMutations('config', ['changeFuzzyStr']),
        handleKeywordChange(val) {
            if (val !== this.keyword) {
                this.keyword = val;
            }
        },
        handleSearch() {
            if (this.keyword !== '') {
                this.pageNo = 1;
                this.queryString = this.keyword;
                this.changeFuzzyStr(this.queryString);
                this.fetchContractInfo().catch(toastError);
            } else {
                this.loadContractList().catch(toastError);
            }
        },
    },
    mounted() {
        this.loadContractList().catch(toastError);
        this.fetchPromotBar();
    },
};
</script>

<style lang="scss" module>
.container {
    min-height: 100%;
    padding-bottom: 56px;
    background: #FFF;
}
.header {
    width: 100%;
    height: 137px;
    display: flex;
    background: #FFFAEB;
    flex-direction: row;
    .headerImg {
        width: 118px;
        height: 116px;
        margin-top: 20px;
        margin-left: 20px;
    }
    .title {
        font-weight: 500;
        font-family: PingFangSC-Medium;
        font-size: 24px;
        color: #000000;
        font-weight: 500;
        margin-top: 30px;
        margin-left: 20px;
    }
    .extra {
        font-weight: 400;
        font-family: PingFangSC-Regular;
        font-size: 20px;
        color: #000000;
        font-weight: 400;
        margin-top: 16px;
        margin-left: 20px;
    }
}
.content {
    margin: 20px 20px 0;
}

.loadingContract {
    padding: 56px;
    text-align: center;
}
.input {
    display: inline-block;
    width: 260px;
    margin-right: 10px;
    vertical-align: middle;
}

.pagination {
    padding: 10px 0;
    text-align: right;
}
</style>
