<template>
    <table :class="$style.table">
        <col style="width: 70%;" />
        <col style="width: 15%;" />
        <col style="width: 15%;" />

        <thead>
            <tr>
                <th>公司合同名称</th>
                <th>关联门店</th>
                <th>操作</th>
            </tr>
        </thead>

        <tbody
            v-if="loading"
            key="loading"
        >
            <tr>
                <td
                    colspan="6"
                    style="text-align: center;"
                >
                    加载中
                </td>
            </tr>
        </tbody>

        <tbody
            v-else
            key="list"
        >
            <tr
                v-for="(item, idx) in list"
                :key="idx"
            >
                <td>
                    {{ item.contractAName }}
                </td>
                <td>
                    <a
                        :class="$style.link"
                        href="#noop"
                        @click.prevent="$emit('show-detail', item), handleChangeParams(item)"
                    >
                        查看
                    </a>
                </td>
                <td>
                    <span
                        :class="$style.link"
                        @click="selectQualification(item)"
                    >
                        前往开票
                    </span>
                </td>
            </tr>
        </tbody>
    </table>
</template>

<script>
/* eslint-disable import/no-unresolved, import/extensions */
import { mapActions, mapMutations } from 'vuex';
import toastError from '$lib/toast-error';
/* eslint-disable import/no-unresolved, import/extensions */
export default {
    name: 'ContractTable',
    methods: {
        ...mapActions('config', ['fetchPoiInfos']),
        ...mapMutations('config', ['changeContractId', 'changeContractAName']),
        ...mapActions('invoiceTitle', ['fetchTitleInfoList']),
        handleChangeParams(item) {
            this.changeContractAName(item.contractAName);
            this.changeContractId(item.contractId);
            this.fetchPoiInfos();
            this.fetchTitleInfoList(1);
        },
        selectQualification(item) {
            this.handleChangeParams(item);
            this.$router.push({
                name: 'qualification-select-update',
            });
        },
    },
    mounted() {
        if (this.total === 1) {
            this.selectQualification(this.list[0]);
        }
    },
    props: {
        loading: {
            type: Boolean,
            default: false,
        },
        list: {
            type: Array,
            default() {
                return [];
            },
        },
        total: {
            type: Number,
            default: 0,
        },
    },
};
</script>

<style lang="scss" module>
.table {
    width: 100%;
    border: 1px solid #E9EAF2;
    table-layout: fixed;
    border-collapse: collapse;
    color: #3F4156;
    margin-top: 20px;

    thead,
    tbody tr:nth-of-type(0) {
        background: #F7F8FA;
    }

    th, td {
        padding: 0 20px;
        text-align: center;

        &:first-child {
            text-align: start;
        }
    }
    tr {
        border: 1px solid #E9EAF2;
    }

    th {
        font-weight: normal;
        height: 50px;
        color: #858692;
    }

    td {
        height: 50px;
    }
}
.link {
    color: #F89800;
    cursor: pointer;
}
</style>
