<template>
    <div :class="$style.module">
        <div>
            <!-- 新表单header -->
            <div v-if="enableNewForm" :class="$style.headerRow">
                <h3 :class="$style.h3">
                    新增抬头
                </h3>
                <!-- 下载补充协议模版链接（新页面右上角） -->
                <a
                    :class="$style.downloadLink"
                    href="//s3plus.meituan.net/shangouopendeveloper/%E8%A1%A5%E5%85%85%E5%8D%8F%E8%AE%AE%E6%A8%A1%E6%9D%**********.docx"
                    target="_blank"
                >
                    下载补充协议模版
                </a>
            </div>
            <!-- 旧表单header -->
            <div v-else>
                <h3 :class="$style.h3">
                    抬头信息
                </h3>
            </div>

            <roo-alert
                v-if="chooseNoticeText"
                :class="$style.alert"
                type="warning"
                icon="exclamation-circle"
            >
                <!-- 新表单提示信息更新 -->
                <span v-html="safeNoticeText"></span>
            </roo-alert>

            <roo-form>
                <roo-form-item
                    label="合同ID"
                    required
                >
                    <roo-input
                        :value="contractId"
                        :class="$style.input"
                        disabled
                    />
                </roo-form-item>

                <roo-form-item
                    label="合同甲方"
                    required
                >
                    <roo-input
                        :value="contractAName"
                        :class="$style.input"
                        disabled
                    />
                </roo-form-item>

                <!-- kp签约人信息 新表单新增展示 -->
                <roo-form-item v-if="enableNewForm" key="kpSignerInfo" label="kp签约人">
                    <KpSignerInfo
                        :kp-signer-name="kpSignerName"
                        :kp-signer-mobile="kpSignerMobile"
                        :kp-signer-status="kpSignerStatus"
                    />
                </roo-form-item>
            </roo-form>

            <!-- 新表单模式 -->
            <template v-if="enableNewForm">
                <roo-form
                    v-for="(formData, idx) in qualifications"
                    ref="forms"
                    :key="idx"
                    :class="$style.form"
                    :model="formData"
                    :label-width="2"
                >
                    <QualificationForm
                        :form-data="formData"
                        :idx="idx"
                        :qualifications="qualifications"
                        :partner-type="103"
                        :customer-number="customerNumber"
                        :contract-id="contractId"
                        :is-multiple-stores="true"
                        @add-qualification="handleAddTitle"
                        @remove-qualification="handleRemoveTitle"
                        @update="handleQualificationUpdate"
                    />
                </roo-form>
            </template>

            <!-- 旧表单模式 -->
            <template v-else>
                <roo-form
                    v-for="(formData, idx) in qualifications"
                    ref="forms"
                    :key="idx"
                    :class="$style.form"
                    :model="formData"
                    :label-width="2"
                >
                    <roo-form-item
                        key="partnerBName"
                        :rules="rules.partnerBName"
                        label="抬头名称"
                        prop="partnerBName"
                        required
                    >
                        <roo-autocomplete
                            v-model="formData.partnerBName"
                            :class="$style.input"
                            :fetch-suggestions="(keyword, cb) => fuzzySearch(keyword, cb, idx)"
                            :option-renderer="optionRender"
                            placeholder="请输入抬头名称"
                        />
                        <div :class="$style.buttonGroup">
                            <roo-button
                                type="text"
                                @click="handleAddTitle"
                            >
                                <roo-icon name="plus" />
                            </roo-button>
                            <roo-button
                                type="text"
                                :disabled="qualifications.length <= 1"
                                @click="handleRemoveTitle(idx)"
                            >
                                <roo-icon name="minus" />
                            </roo-button>
                        </div>
                    </roo-form-item>
                    <roo-form-item
                        key="taxpayerIdNo"
                        :rules="rules.taxpayerIdNo"
                        label="纳税人识别号"
                        prop="taxpayerIdNo"
                        required
                    >
                        <roo-input
                            v-model="formData.taxpayerIdNo"
                            :class="$style.input"
                            placeholder="输入发票抬头后自动带出纳税人识别号"
                            disabled
                        />
                        <div v-if="taxpayerIdNoErrors[idx]" style="color: red;">
                            {{ taxpayerIdNoErrors[idx] }}
                        </div>
                    </roo-form-item>

                    <roo-form-item
                        key="subpoiBusinessLicenseName"
                        prop="subpoiBusinessLicenseName"
                        label="营业执照"
                        :required="formData.partnerBName !== contractAName"
                    >
                        <div :class="$style.uploadMethod">
                            方式一：使用已有门店的营业执照
                        </div>
                        <roo-autocomplete
                            v-model="formData.subpoiBusinessLicenseName"
                            :class="$style.input"
                            :fetch-suggestions="fuzzyLicenseSearch"
                            :option-renderer="licenseOptionRender"
                            placeholder="请输入合同内门店名称/ID"
                            @select="(val) => handleLicenseSelect(val, idx)"
                        />
                        <div :class="$style.uploadMethod">
                            方式二：本地上传
                        </div>
                        <roo-form-item
                            key="subpoiBusinessLicenseUrls"
                            prop="subpoiBusinessLicenseUrls"
                            :required="formData.partnerBName !== contractAName"
                            :rules="formData.partnerBName !== contractAName ? rules.subpoiBusinessLicenseUrls : []"
                        >
                            <div :class="$style.uploadContent">
                                <image-uploader
                                    v-model="formData.subpoiBusinessLicenseUrls"
                                />
                                <roo-button
                                    v-if="formData.subpoiBusinessLicenseUrls && formData.subpoiBusinessLicenseUrls.length > 0"
                                    type="text"
                                    @click="imagePreview({images: [formData.subpoiBusinessLicenseUrls[0].imageUrl]})"
                                >
                                    查看大图
                                </roo-button>
                                <div
                                    v-if="subpoiBusinessLicenseUrls.length > 0"
                                    :class="$style.checkHint"
                                >
                                    请检查营业执照名称与抬头名称一致
                                </div>
                            </div>
                        </roo-form-item>
                    </roo-form-item>
                </roo-form>
            </template>
        </div>

        <!-- 补充信息部分 - 仅在旧表单模式下显示 -->
        <template v-if="!enableNewForm">
            <h3 :class="$style.h3">
                补充信息
            </h3>

            <template v-if="allTitlesMatchContractName">
                <div :class="$style.tipTitle">
                    {{ contractAName }}
                </div>
                <div :class="$style.tipContent">
                    新抬头与当前合同甲方一致，无需其他补充材料
                </div>
            </template>

            <template v-else>
                <roo-form
                    ref="form2"
                    :model="{ authorizationUrls, subpoiBusinessLicenseUrls, subpoiBusinessLicenseName }"
                    :class="$style.form"
                    :label-width="2"
                >
                    <!-- <roo-form-item
                        key="subpoiBusinessLicenseName"
                        prop="subpoiBusinessLicenseName"
                        style="margin-top: 10px;"
                        label="营业执照"
                    >
                        <div :class="$style.uploadMethod">
                            方式一：使用已有门店的营业执照
                        </div>
                        <roo-autocomplete
                            v-model="subpoiBusinessLicenseName"
                            :class="$style.input"
                            :fetch-suggestions="fuzzyLicenseSearch"
                            :option-renderer="licenseOptionRender"
                            placeholder="请输入合同内门店名称/ID"
                            @select="handleLicenseSelect"
                        />
                    </roo-form-item> -->
                    <!-- <roo-form-item
                        key="subpoiBusinessLicenseUrls"
                        prop="subpoiBusinessLicenseUrls"
                        style="margin-top: 10px;"
                        label=" "
                        required
                        :rules="rules.subpoiBusinessLicenseUrls"
                    >
                        <div :class="$style.uploadMethod">
                            方式二：本地上传
                        </div>
                        <image-uploader
                            v-model="subpoiBusinessLicenseUrls"
                        />
                        <roo-button
                            v-if="subpoiBusinessLicenseUrls.length > 0"
                            type="text"
                            @click="imagePreview({images: [subpoiBusinessLicenseUrls[0].imageUrl]})"
                        >
                            查看大图
                        </roo-button>
                        <div
                            v-if="subpoiBusinessLicenseUrls.length > 0"
                            :class="$style.checkHint"
                        >
                            请检查营业执照名称与抬头名称一致
                        </div>
                    </roo-form-item> -->
                    <roo-form-item
                        key="reasonId"
                        prop="reasonId"
                        label="抬头变更原因"
                    >
                        <roo-select
                            v-model="reasonId"
                            :class="[$style.input, $style.mb14]"
                            :options="finalReasonsList"
                            placeholder="请选择原因"
                        />
                    </roo-form-item>
                    <roo-form-item
                        key="authorizationUrls"
                        prop="authorizationUrls"
                        label="补充协议"
                        :rules="rules.authorizationUrls"
                        required
                    >
                        <a
                            :class="$style.download"
                            target="_blank"
                            @click="handleDownloadProtocol"
                        >
                            下载填写补充协议
                        </a>

                        <div style="margin-top: 5px;">
                            上传盖章后的补充协议
                        </div>
                        <image-uploader
                            v-model="authorizationUrls"
                            :max="5"
                        />
                    </roo-form-item>
                </roo-form>
            </template>
        </template>

        <roo-modal
            v-model="displaySubmitModal"
            size="small"
        >
            <div :class="$style.modalTitle">
                抬头申请已提交
            </div>
            <div v-if="enableNewForm" :class="$style.modalTitleContent">
                资质提交成功。通过系统审核后，请进行线上签署。
            </div>
            <div v-else :class="$style.modalTitleContent">
                已成功提交抬头申请，平台将在3个工作日内进行审核，您可在发票抬头模块中查看审核进度，审核结果将会发送信息提示
            </div>

            <template slot="footer">
                <roo-button @click="handleConfirmDone">
                    我知道了
                </roo-button>
            </template>
        </roo-modal>

        <roo-modal v-model="displayNoticeModal" size="normal" title="重要提示">
            <div v-html="safeNoticeText"></div>
            <template slot="footer">
                <roo-button type="brand" @click="displayNoticeModal = false">
                    我知道了
                </roo-button>
            </template>
        </roo-modal>
        <div :class="$style.bottomText">
            <p>
                根据国家税法及发票管理相关规定，任何单位和个人不得让他人为自己或者介绍他人开具与实际经营业务情况不符的发票，否则属于虚开发票行为。
                我已充分了解上述发票相关规定，并且承诺仅就我司实际购买商品或服务索取发票。如我司未按国家相关规定申请开具或者使用增值税发票，由我司自行承担全部相应的法律后果。
            </p>
            <roo-checkbox
                :checked="isCheck"
                @change="handleCheck"
            >
                我已阅读并理解上述内容
            </roo-checkbox>
        </div>
        <div class="footer-nav">
            <roo-button
                type="hollow"
                @click="$router.go(-1)"
            >
                取消
            </roo-button>
            <roo-button
                type="brand"
                :disabled="!isCheck"
                @click="handleNextClick"
            >
                提交
            </roo-button>
        </div>
        <statement-tip-modal />
        <sms-yoda-verification
            ref="smsYodaVerification"
            :display-modal="displayModal"
            :check-sms-error="checkSmsError"
            :is-success-code="isSuccessCode"
            @update:displayModal="displayModal = $event"
            @update:smsCodes="handleSmsCodesUpdate"
            @update:mobile="handleMobile"
            @submit="handleSubmit"
        />
    </div>
</template>

<script>

/* eslint-disable import/no-unresolved, import/extensions */
import request from '$invoice/utils/request';
import { mapState, mapMutations } from 'vuex';
import { toast } from '@roo/roo-vue';

/* eslint-disable import/extensions, import/no-unresolved */
import ImageUploader from '../image-uploader';
import toastError from '$lib/toast-error';
import { pattern } from '$lib/has-invalid-charactor';
import { debounce, getCookieValue } from '$lib/utils';
import { sanitizeHtml } from '../../../utils/security';
/* eslint-enable import/extensions, import/no-unresolved */
import imagePreview from 'image-preview-vue';
import StatementTipModal from '$components/statement-tip-modal';
import SmsYodaVerification from '$components/sms-yoda-verification';
import KpSignerInfo from '../../../components/qualification-form/KpSignerInfo.vue';
import QualificationForm from '../../../components/qualification-form';


const template = {
    partnerType: null,
    contractId: null,
    partnerBName: '',
    taxpayerIdNo: null,
    bankName: null,
    cardNo: null,
    registerAdd: null,
    telNo: null,
    reasonId: null,
    subpoiBusinessLicenseName: '',
    subpoiBusinessLicenseUrls: [],
    legalRepName: '',
    legalRepIdCard: '',
    legalRepPhone: '',
};

export default {
    name: 'AddTitle',
    components: {
        ImageUploader,
        StatementTipModal,
        SmsYodaVerification,
        KpSignerInfo,
        QualificationForm,
    },
    computed: {
        ...mapState('config', ['contractAName', 'contractId']),
        ...mapState('titleManagement', ['reasonsList']),
        ...mapState('apply', ['needVerification', 'noticeText', 'noticeTextNew']),

        // 判断是否所有抬头都与合同甲方一致
        allTitlesMatchContractName() {
            return this.qualifications.every(q => q.partnerBName === this.contractAName);
        },

        chooseNoticeText() {
            return this.enableNewForm ? this.noticeTextNew : this.noticeText;
        },

        safeNoticeText() {
            return sanitizeHtml(this.chooseNoticeText);
        },

        // // 判断表单是否禁用
        // isFormDisabled() {
        //     return this.kpSignerStatus === -1 || this.kpSignerStatus === 0;
        // },

        // 控制是否需要验证码校验
        shouldNeedVerification() {
            return this.enableNewForm ? false : this.needVerification;
        },

        finalReasonsList() {
            const { reasonsList } = this;
            const options = reasonsList.map(reason => ({
                label: reason.reasonName,
                value: reason.reasonId,
            }));

            return [...options];
        },
    },
    created() {
        this.source = null;
        this.fuzzySearch = debounce(this.fuzzySearch, 1000);
    },
    mounted() {
        // 获取配置信息
        this.getConfig()
            .then(() => {
                if (this.enableNewForm && this.safeNoticeText) {
                    this.displayNoticeModal = true;
                }
            })
            .catch(() => {
                alert('获取配置信息失败，请稍后重试。');
            });
    },
    data() {
        return {
            nameCheckMsg: '',
            displaySubmitModal: false,
            reasonId: 0,
            subpoiBusinessLicenseUrls: [],
            authorizationUrls: [],
            qualifications: [{ ...template }],
            subpoiBusinessLicenseName: '',
            license: [],
            submitting: false,
            downloadPStr: '',
            taxpayerIdNoErrors: [],
            rules: {
                partnerBName: [
                    {
                        required: true,
                        message: '请输入抬头名称',
                    },
                    {
                        pattern,
                        message: '发票抬头不能有空格和如下特殊字符，如&，？等',
                    },
                    {
                        validator: debounce(this.validatePartnerName, 1000),
                        trigger: 'blur,change',
                    },
                ],
                taxpayerIdNo: [
                    {
                        pattern: /^[A-Z0-9]{15,20}$/,
                        message: '纳税人识别号为15-20位数字或大写字母',
                    },
                ],
                bankName: [
                    {
                        pattern,
                        message: '发票抬头不能有空格和如下特殊字符，如&，？等',
                    },
                ],
                cardNo: [
                    {
                        pattern,
                        message: '发票抬头不能有空格和如下特殊字符，如&，？等',
                    },
                ],
                registerAdd: [
                    {
                        pattern,
                        message: '发票抬头不能有空格和如下特殊字符，如&，？等',
                    },
                ],
                telNo: [
                    {
                        pattern,
                        message: '发票抬头不能有空格和如下特殊字符，如&，？等',
                    },
                ],
                authorizationUrls: [
                    {
                        required: true,
                        message: '请上传补充协议',
                    },
                ],
                subpoiBusinessLicenseUrls: [
                    {
                        required: true,
                        validator: (rule, value, callback) => {
                            const currentForm = this.qualifications.find(q => q.subpoiBusinessLicenseUrls === value);
                            if (currentForm && currentForm.partnerBName === this.contractAName) {
                                callback();
                                return;
                            }

                            if (!value || value.length === 0) {
                                callback(new Error('请上传营业执照'));
                                return;
                            }
                            callback();
                        },
                        trigger: 'change',
                    },
                ],
                addProtocolDownload: [],
            },
            isCheck: false,
            displayModal: false,
            smsCodes: '',
            mobile: '',
            checkSmsError: '',
            isSuccessCode: false,
            displayNoticeModal: false,

            // 新增配置
            enableNewForm: false, // 是否启用新表单模型
            kpSignerName: '', // kp签约人名称
            kpSignerMobile: '', // kp签约人手机号
            kpSignerStatus: 0, // kp签约人状态：-1未通过实名认证，0信息缺失，1实名认证通过
            customerNumber: '', // 合同甲方税号
        };
    },
    methods: {

        // 获取配置信息
        async getConfig() {
            try {
                const params = {
                    requestHitParam: true,
                    contractId: this.contractId,
                };

                const res = await request.get('/finance/invoice/api/qualification/getConfig', { params });

                if (res.data.code === 0 && res.data.data) {
                    const { enableNewForm, kpSigner, customerNumber } = res.data.data;
                    this.enableNewForm = enableNewForm || false;
                    this.kpSignerName = (kpSigner && kpSigner.name) || '';
                    this.kpSignerMobile = (kpSigner && kpSigner.mobile) || '';
                    this.kpSignerStatus = (kpSigner && kpSigner.status) || 1;
                    this.customerNumber = customerNumber;
                }
                return Promise.resolve();
            } catch (error) {
                console.error('获取配置信息失败:', error);
                // 失败时使用默认值
                return Promise.reject();
            }
        },
        // 抬头名称验证方法
        validatePartnerName(rules, value, callback) {
            this.handleCheckName(value).then((res) => {
                const { code, msg } = res.data;
                if (code !== 0) {
                    callback(new Error(msg));
                } else {
                    callback();
                }
            });
        },
        handleSmsCodesUpdate(newSmsCodes) {
            this.smsCodes = newSmsCodes;
        },
        handleMobile(newMobile) {
            this.mobile = newMobile;
        },
        handleCheck(checked) {
            this.isCheck = checked;
        },
        imagePreview,
        ...mapMutations('titleManagement', ['changeTempParams', 'changeCompareQualificationId', 'changeIsEdit']),
        fuzzySearch(keyword, callback, index) {
            if (!keyword) {
                return;
            }

            const params = {
                partnerBName: keyword,
            };

            request.get('/finance/invoice/api/output/waimai/q/queryTaxNoByName', { params }).then((res) => {
                const { code, msg, data } = res.data;

                // 统一处理错误情况（接口失败或无数据）
                if (code !== 0 || !data) {
                    const errorMsg = code !== 0
                        ? (msg || '查询纳税人识别号失败')
                        : '未查询到有效纳税人识别号';

                    // 直接使用传入的 index 更新对应的错误信息
                    this.$set(this.taxpayerIdNoErrors, index, errorMsg);

                    // 清空纳税人识别号
                    this.$set(this.qualifications[index], 'taxpayerIdNo', '');
                    return;
                }

                // 处理成功情况
                this.$set(this.taxpayerIdNoErrors, index, '');
                this.$set(this.qualifications[index], 'taxpayerIdNo', data);

                // 返回空数组以清除下拉建议
                callback([]);
            }).catch(toastError);
        },

        //         this.suggestions = (data || []).map((item) => ({
        //             ...item,
        //             label: item.partnerBName,
        //             value: item.partnerBName,
        //         }));

        //         callback(this.suggestions);
        //     }).catch(toastError);
        // },
        fuzzyLicenseSearch(keyword, callback) {
            if (!keyword) {
                return;
            }

            const { contractId } = this;
            const params = {
                fuzzy: keyword,
                contractId,
            };

            request.get('/finance/invoice/api/output/waimai/q/getLicenseByFuzzy', { params }).then((res) => {
                const { code, msg, data } = res.data;

                if (code !== 0) {
                    throw new Error(msg);
                }

                this.license = (data || []).map((item) => ({
                    ...item,
                    label: item.wmPoiName,
                    value: item.wmPoiName,
                }));

                callback(this.license);
            }).catch(toastError);
        },
        handleLicenseSelect(value, idx) {
            const { license } = this;
            for (let i = 0; i < license.length; ++i) {
                const item = license[i];
                if (item.value === value) {
                    this.qualifications[idx].subpoiBusinessLicenseUrls = [{
                        imageName: item.imageName,
                        imageUrl: item.licenseUrl,
                    }];
                    return;
                }
            }
        },
        licenseOptionRender(h, { value, label }) {
            const { keyword } = this;
            const content = label.split(keyword).map((part, index) => {
                const replace = index === 0 ? '' : (<span style="color:red;">{keyword}</span>);
                return (
                    <span>
                        {replace}
                        {part}
                    </span>
                );
            });
            return (
                <span>
                    {content}
                </span>
            );
        },
        optionRender(h, { value, label }) {
            const { keyword } = this;
            const content = label.split(keyword).map((part, index) => {
                const replace = index === 0 ? '' : (<span style="color:red;">{keyword}</span>);
                return (
                    <span>
                        {replace}
                        {part}
                    </span>
                );
            });
            return (
                <span>
                    {content} (ID: {value})
                </span>
            );
        },
        handleCheckName(val) {
            this.downloadPStr = val;
            const { contractId } = this;
            const params = {
                contractId,
                partnerType: 103,
                partnerBName: val,
            };
            return request.get('/finance/invoice/api/output/waimai/q/invoiceTitleNameCheck', {
                params,
            });
        },
        handleDownloadProtocol() {
            if (this.downloadPStr === '') {
                toast.warn('请输入抬头名称');
            } else if (this.reasonId < 1) {
                toast.warn('请选择抬头变更原因');
            } else {
                const { reasonId, contractAName } = this;
                const acctId = getCookieValue('acctId');
                // const downloadUrl = `/finance/invoice/api/output/waimai/q/downLoadProtocol?partnerBName=${this.downloadPStr}&reasonId=${reasonId}&originalPartnerName=${contractAName}&invoiceSource=${113}&acctId=${acctId}`;
                const downloadUrl = '//s3plus.meituan.net/waimai-mfe-dragonfly-online/%E5%8F%91%E7%A5%A8%E8%A1%A5%E5%85%85%E5%8D%8F%E8%AE%***********.docx';
                window.open(downloadUrl);
            }
        },
        handleNextClick() {
            // 根据表单类型确定验证函数
            const validate = this.enableNewForm ? this.validateNewForm : this.validateOldForm;

            if (this.shouldNeedVerification === true) {
                validate().then(
                    () => {
                        this.save();
                    },
                    error => {
                        toast.warn((error && error.message) || '资质信息填写有误，请检查');
                    },
                );
            } else {
                validate().then(
                    () => {
                        this.handleSubmit();
                    },
                    error => {
                        toast.warn((error && error.message) || '资质信息填写有误，请检查');
                    },
                );
            }
        },
        buildBody() {
            const {
                qualifications,
                authorizationUrls,
            } = this;

            // 构建titleList数组
            const titleList = qualifications.map(q => ({
                partnerBName: q.partnerBName,
                taxpayerIdNo: q.taxpayerIdNo,
                subpoiBusinessLicenseUrls: q.subpoiBusinessLicenseUrls ? q.subpoiBusinessLicenseUrls.map(x => x.imageName) : [],
                authorizationUrls: authorizationUrls.map(x => x.imageName),
                legalRepName: q.legalRepName,
                legalRepIdCard: q.legalRepIdCard,
                legalRepPhone: q.legalRepPhone,
            }));

            return {
                titleList,
            };
        },
        save() {
            this.$nextTick(async () => {
                try {
                    await this.$refs.smsYodaVerification.getMobile();
                } catch (err) {
                    console.log(err);
                    // 错误处理已经在 getMobile 中处理
                }
            });
        },
        getUserAgent() {
            return navigator.userAgent;
        },
        handleSubmit() {
            const { contractId, enableNewForm } = this;
            const body = this.buildBody();
            // console.log(body);
            const params = {
                ...body,
                partnerType: 103,
                contractId,
                enableNewForm,
            };
            this.submitting = true;
            if (this.shouldNeedVerification === true) {
                params.smscode = this.smsCodes;
                params.uuid = getCookieValue('device_uuid');
                params.mobile = this.mobile;
                params.ua = this.getUserAgent();
            }
            return request.post('/finance/invoice/api/output/waimai/q/invoiceTitleMultiSave', params)
                .then((res) => {
                    this.submitting = false;
                    const { code, msg, data } = res.data;
                    if (code === 0) {
                        if (this.shouldNeedVerification === true && data.response_code) {
                            this.isSuccessCode = true;
                            this.displayModal = false;
                        }
                    }
                    if (code !== 0) {
                        if (msg && msg.message) {
                            this.checkSmsError = msg.message;
                            return;
                        }
                        // throw new Error(msg)
                        toast.error(msg);
                    }

                    if (data) {
                        // this.changeCompareQualificationId(data);
                        this.displaySubmitModal = true;
                    }
                }, (err) => {
                    this.submitting = false;
                    throw err;
                })
                .catch(toastError);
        },
        handleConfirmDone() {
            this.displaySubmitModal = false;
            this.changeIsEdit(false);
            this.$router.push({ name: 'qualification-select-update' });
        },
        // 新表单验证方法
        // 新表单验证
        validateNewForm() {
            const { forms } = this.$refs;

            return new Promise((resolve, reject) => {
                // 校验kp签约人信息（只要存在新资质税号是否与合同甲方税号不同，则校验kp签约人信息）
                const hasDiffFromCustomerNumber = this.qualifications.some(q => q.taxpayerIdNo !== this.customerNumber);
                if (hasDiffFromCustomerNumber && (this.kpSignerStatus === 0 || this.kpSignerStatus === -1)) {
                    reject(new Error('请维护合同kp签约人信息'));
                    return;
                }

                // 校验所有新资质抬头名称和税号是否填写
                const invalidQualifications = this.qualifications.filter(q => !q.partnerBName || !q.taxpayerIdNo);

                if (invalidQualifications.length > 0) {
                    reject(new Error('请检查新资质名称有效性'));
                    return;
                }

                // 校验营业执照OCR识别的税号与新资质抬头税号是否一致
                let errorMsg = '';
                this.qualifications.some(q => {
                    // 如果新资质税号与合同甲方税号相同，不填写营业执照相关内容，字段为空，跳过本轮进入下一轮校验
                    if (q.subpoiBusinessLicenseUrls[0]
                        && q.subpoiBusinessLicenseUrls[0].taxpayerIdNo
                        && (q.taxpayerIdNo !== q.subpoiBusinessLicenseUrls[0].taxpayerIdNo)) {
                        // 税号不匹配
                        errorMsg = '资质税号需与上传的营业执照一致';
                        return true;
                    }

                    return false;
                });

                if (errorMsg) {
                    reject(new Error(errorMsg));
                    return;
                }

                // 新资质表单基础校验
                let count = 0;
                const len = forms.length;

                function cb(pass) {
                    if (pass) {
                        count += 1;
                        if (count === len) {
                            resolve();
                        }
                    } else {
                        reject(new Error('资质信息填写有误，请检查'));
                    }
                }

                for (let i = 0; i < len; ++i) {
                    forms[i].validate(cb);
                }
            });
        },

        // 旧表单验证方法
        validateOldForm() {
            const { forms, form2 } = this.$refs;
            const arr = forms.concat([form2]);

            return new Promise((resolve, reject) => {
                // 先检查是否所有纳税人识别号都已填写
                const hasEmptyTaxpayerId = this.qualifications.some((q, index) => {
                    if (!q.taxpayerIdNo) {
                        // 设置错误信息
                        this.$set(this.taxpayerIdNoErrors, index, '未查询到有效纳税人识别号');
                        return true;
                    }
                    return false;
                });

                if (hasEmptyTaxpayerId) {
                    toast.warn('请完善纳税人识别号信息');
                    reject();
                    return;
                }
                let count = 0;
                let len = 0;
                arr.forEach((item) => {
                    if (item) {
                        len += 1;
                    }
                });

                function callback(pass) {
                    if (pass) {
                        count += 1;
                        if (count === len) {
                            resolve();
                        }
                    } else {
                        reject();
                    }
                }

                for (let i = 0; i < len; ++i) {
                    if (arr[i]) {
                        arr[i].validate(callback);
                    }
                }
            });
        },
        handleAddTitle() {
            this.qualifications.push({
                ...template,
                partnerBName: '',
                taxpayerIdNo: '',
                subpoiBusinessLicenseName: '',
                subpoiBusinessLicenseUrls: [],
            });
        },
        handleRemoveTitle(idx) {
            this.qualifications.splice(idx, 1);
        },

        handleQualificationUpdate(index, newData) {
            // 更新对应的表单数据
            Object.keys(newData).forEach(key => {
                this.$set(this.qualifications[index], key, newData[key]);
            });
        },


    },
};
</script>

<style lang="scss" module>
.module {
    margin-top: 20px;

    :global(.roo-radio-group) {
        display: inline-block;
        margin-left: 20px;
        line-height: 1;
        vertical-align: middle;
    }
}

.headerRow {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.h3 {
    margin-top: 0;
    margin-bottom: 20px;
    font-size: 20px;
}

.downloadLink {
    color:  #FF6A00;
    text-decoration: none;
    font-size: 14px;
    line-height: 20px;
    letter-spacing: 0px;

    &:hover {
        text-decoration: underline;
    }
}

.alert {
    margin-bottom: 20px;
}

.form {
    padding-bottom: 20px;
    margin-bottom: 20px;
    border-bottom: 1px dashed #EBEEF2;

    :global(.form-group.row:last-child) {
        margin-bottom: 0;
    }
}
.input {
    display: inline-block;
    width: 480px;
    vertical-align: middle;
}
.extra {
    font-weight: 400;
    font-family: PingFangSC-Regular;
    font-size: 12px;
    color: #999999;
    font-weight: 400;
    margin-top: 8px;
}
.mb14 {
    margin-bottom: 14px;
}
.uploadBtn {
    display: block;
    margin-top: 10px;
}
.tipTitle {
    font-weight: 500;
    font-family: PingFangSC-Medium;
    font-size: 16px;
    color: #222222;
    text-align: left;
    line-height: 24px;
}
.tipContent {
    width: 630px;
    margin-top: 13px;
    background: #F7F8FA;
    font-weight: 400;
    font-family: PingFangSC-Regular;
    font-size: 18px;
    color: #000000;
    padding: 20px;
}
.checkHint {
    width: 367px;
    height: 32px;
    margin-top: 10px;
    background: #FFF9DB;
    padding: 6px;
    font-size: 14px;
}
.uploadMethod {
    font-family: PingFangSC-Medium;
    font-size: 14px;
    color: #222222;
    margin: 14px 0 8px;
}
.uploadContent {
    margin-left: 0;
}
.download {
    cursor: pointer;
}
.modalTitle {
    font-size: 20px;
    color: #222222;
    font-weight: 500;
}
.bottomText{

font-size: 14px;
padding: 0px 100px;
}
.buttonGroup {
    display: inline-flex;
    align-items: center;
    margin-left: 10px;
    vertical-align: middle;
    :global {
        .roo-button {
            padding: 0 4px;
            .roo-icon {
                font-size: 20px;
                color: #666;
                &:hover {
                    color: #f89800;
                }
            }
            &[disabled] {
                .roo-icon {
                    color: #ccc;
                    cursor: not-allowed;
                    &:hover {
                        color: #ccc;
                    }
                }
            }
        }
    }
}
.uploadTip {
    color: #999;
    font-size: 12px;
    margin-top: 8px;
}
</style>
