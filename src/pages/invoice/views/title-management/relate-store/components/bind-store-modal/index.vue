<template>
    <roo-modal
        :class="$style.modal"
        :value="value"
        @input="$emit('input', $event)"
    >
        <template slot="title">
            <span :class="$style.tip">
                批量选择门店
            </span>
        </template>

        <div
            :class="$style.form"
        >
            <roo-input
                v-model="inputModel"
                :class="$style.input"
                noresize
                placeholder="输入门店ID，一行一个"
                type="textarea"
                @change="handleInput"
            />
        </div>

        <template slot="footer">
            <roo-button
                type="hollow"
                @click="$emit('input', false)"
            >
                取消
            </roo-button>
            <roo-button
                @click="handleConfirmClick"
            >
                确定
            </roo-button>
        </template>
    </roo-modal>
</template>

<script>
/* eslint-disable import/extensions, import/no-unresolved */
import { mapState, mapMutations } from 'vuex';
/* eslint-disable import/extensions, import/no-unresolved */
export default {
    name: 'BindStoreModal',
    computed: {
        ...mapState('titleManagement', ['relatedStoreIds', 'relatedStoreList']),
    },
    data() {
        return {
            ids: [],
            inputModel: '',
        };
    },
    methods: {
        ...mapMutations('titleManagement', ['changeRelatedStoreIds']),
        handleConfirmClick() {
            const { ids, relatedStoreIds, relatedStoreList } = this;
            const allIds = relatedStoreList.map((r) => r.wmPoiId);
            const temp = ids.filter((id) => !relatedStoreIds.includes(id) && allIds.includes(id));
            this.inputModel = (relatedStoreIds.concat(temp)).join('\n');
            this.changeRelatedStoreIds([...relatedStoreIds].concat(temp));
            this.$emit('input', false);
        },
        handleInput(val) {
            if (val) {
                this.ids = val.split(/\n/).map((id) => Number(id));
            }
        },
    },
    mounted() {
        this.inputModel = this.relatedStoreIds.join('\n');
    },
    props: {
        value: {
            type: Boolean,
            default: false,
        },
    },
};
</script>

<style lang="scss" module>
.modal {
    :global(.roo-modal-body) {
        max-height: 400px;
        padding: 20px !important;
        overflow-y: auto;
    }
}
.tip {
    font-family: PingFangSC-Medium;
    font-size: 20px;
    color: #222222;
    font-weight: 500;
}
.input {
    width: 380px;
    height: 280px !important;
}
</style>
