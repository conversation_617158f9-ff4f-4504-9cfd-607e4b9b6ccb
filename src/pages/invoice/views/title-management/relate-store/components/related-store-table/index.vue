<template>
    <table :class="$style.table">
        <col style="width: 50%;" />
        <col style="width: 50%;" />

        <thead>
            <tr>
                <th>
                    <roo-checkbox
                        :checked="value.length > 0 && value.length === total"
                        :indeterminate="value.length > 0 && value.length < total"
                        @change="$emit('check-all', $event)"
                    >
                        门店名称/ID
                    </roo-checkbox>
                </th>
                <th>当前关联抬头</th>
            </tr>
        </thead>

        <tbody
            v-if="list.length === 0"
            key="empty"
        >
            <tr>
                <td
                    colspan="6"
                    style="text-align: center;padding: 20px;"
                >
                    暂无数据
                </td>
            </tr>
        </tbody>

        <tbody key="list">
            <tr
                v-for="item in list"
                :key="item.partnerId"
            >
                <td>
                    <roo-checkbox
                        :checked="value.indexOf(item.wmPoiId) > -1"
                        @change="handleChange(item, $event)"
                    />
                    {{ item.wmPoiName }}-{{ item.wmPoiId }}
                </td>
                <td>
                    {{ item.qualificationName }}
                </td>
            </tr>
        </tbody>
    </table>
</template>

<script>
export default {
    name: 'RelatedStoreTable',
    methods: {
        handleChange(item, checked) {
            if (checked) {
                this.$emit('change', this.value.concat([item.wmPoiId]));
            } else {
                this.$emit('change', this.value.filter((id) => id !== item.wmPoiId));
            }
        },
    },
    props: {
        list: {
            type: Array,
            default() {
                return [];
            },
        },
        total: {
            type: Number,
            default: 0,
        },
        value: {
            type: Array,
            default() {
                return [];
            },
        },
    },
};
</script>

<style lang="scss" module>
.table {
    width: 100%;
    border: 1px solid #E9EAF2;
    table-layout: fixed;
    border-collapse: collapse;
    color: #3F4156;

    thead tr:first-child {
        background: #F7F8FA;
    }

    th, td {
        padding: 0 20px;
        text-align: start;
        border: 1px solid #E9EAF2;
    }

    th {
        font-weight: normal;
        height: 50px;
        color: #858692;
    }

    td {
        height: 50px;
    }

    :global(.roo-checkbox) {
        display: inline;
    }
}
</style>
