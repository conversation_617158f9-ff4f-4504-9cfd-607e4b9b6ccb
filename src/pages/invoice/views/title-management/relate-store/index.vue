<template>
    <div :class="$style.module">
        <div>
            <h3 :class="$style.h3">
                当前抬头
                <span :class="$style.partnerBNameHint">
                    <!-- {{ isEdit ? editItemInfo.partnerBName : compareQualificationId.partnerBName }} -->
                    {{ editItemInfo.partnerBName }}
                </span>
            </h3>
            <roo-alert
                :class="$style.alert"
                type="warning"
                icon="exclamation-circle"
            >
                设置抬头与门店的对应关系，选择该抬头开票时关联门店金额将自动汇总至该抬头，提升您的开票效率
            </roo-alert>

            <div :class="$style.search">
                <roo-input
                    v-model="keyword"
                    :class="$style.input"
                    placeholder="请输入门店名称/ID"
                    @change="handleKeywordChange"
                />
                <roo-button
                    type="hollow"
                    @click="handleSearch"
                >
                    查询
                </roo-button>
                <roo-button
                    :class="$style.choose"
                    type="hollow"
                    @click="displayBind = true"
                >
                    批量选择门店
                </roo-button>
            </div>

            <div :class="$style.nav">
                <roo-tabs
                    :key="listActive"
                    v-model="listActive"
                    type="fill"
                    @active-change="isChecked = false; keyword = ''; queryString = ''"
                >
                    <roo-tab-pane
                        name="all"
                        label="全部"
                    />
                    <roo-tab-pane
                        name="notConnected"
                        label="未关联"
                    />
                    <roo-tab-pane
                        name="connected"
                        label="已关联"
                    />
                </roo-tabs>

                <div :class="$style.showConnected">
                    <roo-checkbox
                        :checked="isChecked"
                        @change="handleFilterConnected($event)"
                    />
                    仅展示当前抬头关联门店
                </div>
            </div>

            <div
                v-if="relatedStoreList.length > 0"
                key="list"
            >
                <related-store-table
                    :list="showRelatedStoreList"
                    :total="relatedStoreList.length"
                    :value="checked"
                    @change="handleCheckChange"
                    @check-all="handleCheckAll"
                />
            </div>

            <template v-if="displayBind">
                <bind-store-modal v-model="displayBind" />
            </template>

            <roo-modal
                v-model="displaySubmitModal"
                size="small"
            >
                <div :class="$style.modalTitle">
                    关联门店变更成功
                </div>
                <!-- <div :class="$style.modalTitleContent">
                    已成功提交抬头申请
                </div> -->

                <template slot="footer">
                    <roo-button @click="handleConfirmDone">
                        我知道了
                    </roo-button>
                </template>
            </roo-modal>
        </div>

        <div class="footer-nav">
            <roo-button
                v-if="!isEdit"
                :class="$style.btn"
                type="brand-hollow"
                @click="handleGoBack"
            >
                上一步
            </roo-button>
            <roo-button
                type="brand"
                @click="handleSubmitClick"
            >
                {{ isEdit ? '保存' : '提交' }}
            </roo-button>
        </div>
    </div>
</template>

<script>
import { mapActions, mapMutations, mapState } from 'vuex';
/* eslint-disable import/extensions, import/no-unresolved */
import request from '$invoice/utils/request';
import BindStoreModal from './components/bind-store-modal';
import RelatedStoreTable from './components/related-store-table';
import toastError from '$lib/toast-error';
/* eslint-disable import/extensions, import/no-unresolved */
export default {
    name: 'RelateStore',
    components: {
        BindStoreModal,
        RelatedStoreTable,
    },
    computed: {
        ...mapState('config', ['contractId']),
        ...mapState('invoiceTitle', ['editItemInfo']),
        ...mapState('titleManagement', ['relatedStoreIds', 'relatedStoreList', 'isEdit', 'compareQualificationId']),
        checked() {
            const { relatedStoreIds } = this;
            return relatedStoreIds ? relatedStoreIds.map(r => r) : [];
        },
        showRelatedStoreList() {
            const {
                queryString,
                relatedStoreList,
                isChecked,
                // isEdit,
                editItemInfo,
                // compareQualificationId,
            } = this;
            // const checkedId = isEdit ? editItemInfo.id : compareQualificationId.qualificationId;
            if (queryString) {
                return relatedStoreList.filter(
                    ((item) => (`${item.wmPoiId}` || '').indexOf(queryString) > -1 || (item.wmPoiName || '').indexOf(queryString) > -1),
                );
            }
            if (this.listActive === 'connected') {
                return relatedStoreList.filter(
                    ((item) => item.bindedTitle),
                );
            } else if (this.listActive === 'notConnected') {
                return relatedStoreList.filter(
                    ((item) => !item.bindedTitle),
                );
            } else if (this.listActive === 'all') {
                if (isChecked) {
                    return relatedStoreList.filter(
                        ((item) => (item.bindedTitle && item.qualificationId === editItemInfo.id)),
                    );
                } else {
                    return relatedStoreList;
                }
            }
            return relatedStoreList;
        },
    },
    data() {
        return {
            listActive: 'all',
            displayBind: false,
            keyword: '',
            queryString: '',
            isChecked: false,
            displaySubmitModal: false,
        };
    },
    methods: {
        ...mapActions('titleManagement', ['fetchRelatedStoreList', 'fetchBindPoiAndTitleRelManageRes']),
        ...mapMutations('titleManagement', ['changeRelatedStoreIds', 'changeRelatedStoreList']),
        handleCheckAll(check) {
            if (check) {
                const list = [];
                const { relatedStoreList } = this;
                relatedStoreList.map((r) => list.push(r.wmPoiId));
                this.changeRelatedStoreIds(list);
            } else {
                this.changeRelatedStoreIds([]);
            }
        },
        handleCheckChange(list) {
            const seet = new Set(list);
            const stores = [];
            this.relatedStoreList.forEach((q) => {
                if (seet.has(q.wmPoiId)) {
                    stores.push(q.wmPoiId);
                }
            });
            this.changeRelatedStoreIds(stores);
        },
        handleDefaultCheck() {
            this.changeRelatedStoreIds([]);
            // const { isEdit, compareQualificationId } = this;
            const defaultBindList = [];
            this.relatedStoreList.forEach((item) => {
                if (item.bindedTitle) {
                    // const compareId = isEdit ? this.editItemInfo.id : compareQualificationId.qualificationId;
                    if (item.qualificationId === this.editItemInfo.id) {
                        defaultBindList.push(item.wmPoiId);
                    }
                }
            });
            this.changeRelatedStoreIds(defaultBindList);
        },
        handleFilterConnected(checked) {
            this.isChecked = checked;
            this.listActive = 'all';
            this.keyword = '';
            this.queryString = '';
        },
        handleGoBack() {
            if (this.isEdit) {
                this.$router.push({ name: 'edit-title' });
            } else {
                this.$router.push({ name: 'add-title' });
            }
        },
        handleKeywordChange(inputVal) {
            if (!inputVal) {
                this.queryString = '';
            }
        },
        handleSearch() {
            if (this.keyword !== '') {
                this.listActive = 'all';
            }
            this.queryString = this.keyword;
        },
        handleSubmitClick() {
            const {
                editItemInfo,
                relatedStoreIds,
                contractId,
                // isEdit,
                // compareQualificationId,
            } = this;
            const params = {
                contractId,
                partnerIdList: relatedStoreIds,
                partnerType: 103,
            };

            // if (!isEdit) {
            //     params.qualificationId = compareQualificationId.qualificationId;
            // } else {
            params.qualificationId = editItemInfo.id;
            // }

            return request.post('/finance/invoice/api/output/waimai/q/poiAndTitleRelManage', params)
                .then((res) => {
                    if (res == null) {
                        return [];
                    }

                    const { code, msg, data } = res.data;

                    if (code === 0) {
                        this.displaySubmitModal = true;
                    } else {
                        throw new Error(msg);
                    }

                    return data;
                }, (err) => {
                    throw err;
                }).catch(toastError);
        },
        handleConfirmDone() {
            this.displaySubmitModal = false;
            this.changeRelatedStoreIds([]);
            this.$router.push({ name: 'invoice-title' });
        },
    },
    mounted() {
        this.fetchRelatedStoreList().then(() => {
            this.handleDefaultCheck();
        });
    },
};
</script>

<style lang="scss" module>
.module {
    :global(.roo-radio-group) {
        display: inline-block;
        margin-left: 20px;
        line-height: 1;
        vertical-align: middle;
    }
}
.h3 {
    font-weight: 500;
    font-family: PingFangSC-Medium;
    font-size: 20px;
    color: #222222;
    text-align: left;
    font-weight: 500;
    .partnerBNameHint {
        font-size: 14px;
        color: #999999;
        font-weight: 500;
    }
}
.alert {
    margin: 10px 0;
}
.btn {
    width: 90px;
    margin-right: 10px;
}
.search {
    width: 100%;
    .input {
        display: inline-block;
        width: 260px;
        margin-right: 10px;
        vertical-align: middle;
    }
    .choose {
        float: right;
        font-weight: normal;
    }
}
.nav {
    display: flex;
    // flex-direction: row;
    justify-content: space-between;
    align-items: baseline;
    width: 100%;
    margin-top: 20px;
}
.showConnected {
    display: flex;
    align-items: baseline;
}
.modalTitle {
    font-weight: 500;
}
</style>
