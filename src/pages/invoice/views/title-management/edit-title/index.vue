<template>
    <div :class="$style.module">
        <div>
            <!-- 新表单header -->
            <div v-if="signingWorkflow" :class="$style.headerRow">
                <h3 :class="$style.h3">
                    修改抬头
                </h3>
            </div>
            <!-- 旧表单header -->
            <div v-else>
                <h3 :class="$style.h3">
                    抬头信息
                </h3>
            </div>

            <roo-alert
                v-if="chooseNoticeText"
                :class="$style.alert"
                type="warning"
                icon="exclamation-circle"
            >
                <!-- 新表单提示信息更新 -->
                <span v-html="safeNoticeText"></span>
            </roo-alert>
            <roo-form
                ref="forms"
                :class="$style.form"
                :model="formData"
                :label-width="2"
            >
                <roo-form-item
                    label="合同ID"
                    required
                >
                    <roo-input
                        :value="contractId"
                        :class="$style.input"
                        disabled
                    />
                </roo-form-item>

                <roo-form-item
                    label="合同甲方"
                    required
                >
                    <roo-input
                        :value="contractAName"
                        :class="$style.input"
                        disabled
                    />
                </roo-form-item>

                <!-- 新增KP签约人信息展示 -->
                <roo-form-item
                    v-if="signingWorkflow"
                    key="kpSignerInfo"
                    label="kp签约人"
                >
                    <KpSignerInfo
                        :kp-signer-name="kpSignerName"
                        :kp-signer-mobile="kpSignerMobile"
                        :kp-signer-status="kpSignerStatus"
                    />
                </roo-form-item>

                <!-- 新表单模式 -->
                <template v-if="signingWorkflow">
                    <QualificationForm
                        :form-data="formData"
                        :idx="0"
                        :qualifications="[formData]"
                        :partner-type="103"
                        :customer-number="customerNumber"
                        :disabled="isFormDisabled"
                        :is-edit="true"
                        @update="handleQualificationUpdate"
                    />
                </template>

                <!-- 旧表单模式 -->
                <template v-else>
                    <roo-form-item
                        key="partnerBName"
                        :rules="rules.partnerBName"
                        label="抬头名称"
                        prop="partnerBName"
                        required
                    >
                        <!-- <roo-autocomplete
                        v-model="comment"
                        :class="$style.input"
                        :fetch-suggestions="fuzzySearch"
                        :option-renderer="optionRender"
                        placeholder="请输入抬头名称"
                        @select="handleSelect"
                    /> -->
                        <roo-input
                            v-model="formData.partnerBName"
                            :class="$style.input"
                            disabled
                            placeholder="请输入抬头名称"
                        />
                    </roo-form-item>

                    <roo-form-item
                        key="taxpayerIdNo"
                        label="纳税人识别号"
                        prop="taxpayerIdNo"
                        required
                    >
                        <roo-input
                            v-model="formData.taxpayerIdNo"
                            :class="$style.input"
                            disabled
                            placeholder="输入发票抬头后自动带出纳税人识别号"
                        />
                        <!-- <div :class="$style.extra">
                        若开增值税专用发票，抬头名称、税号、开户行、银行账号、注册地址、电话为必填项；若开增值税普通发票，抬头名称、税号为必填项，其余为非必填项
                    </div> -->
                    </roo-form-item>
                </template>
            </roo-form>
        </div>

        <!-- 旧表单模式补充信息 -->
        <template v-if="!signingWorkflow">
            <h3 :class="$style.h3">
                补充信息
            </h3>

            <template v-if="contractAName === formData.partnerBName">
                <div :class="$style.tipTitle">
                    {{ contractAName }}
                </div>
                <div :class="$style.tipContent">
                    新抬头与当前合同甲方一致，无需其他补充材料
                </div>
            </template>

            <template v-else>
                <roo-form
                    ref="form2"
                    :model="formData"
                    :class="$style.form"
                    :label-width="2"
                >
                    <roo-form-item
                        key="subpoiBusinessLicenseName"
                        prop="subpoiBusinessLicenseName"
                        style="margin-top: 10px;"
                        label="营业执照"
                    >
                        <div :class="$style.uploadMethod">
                            方式一：使用已有门店的营业执照
                        </div>
                        <roo-autocomplete
                            v-model="formData.subpoiBusinessLicenseName"
                            :class="$style.input"
                            :fetch-suggestions="fuzzyLicenseSearch"
                            :option-renderer="licenseOptionRender"
                            :disabled="isFormDisabled"
                            placeholder="请输入合同内门店名称/ID"
                            @select="handleLicenseSelect"
                        />
                    </roo-form-item>
                    <roo-form-item
                        key="subpoiBusinessLicenseUrls"
                        prop="subpoiBusinessLicenseUrls"
                        style="margin-top: 10px;"
                        label=" "
                        required
                        :rules="rules.subpoiBusinessLicenseUrls"
                    >
                        <div :class="$style.uploadMethod">
                            方式二：本地上传
                        </div>
                        <image-uploader
                            v-model="formData.subpoiBusinessLicenseUrls"
                            :disabled="isFormDisabled"
                        />
                        <roo-button
                            v-if="formData.subpoiBusinessLicenseUrls.length > 0"
                            type="text"
                            @click="imagePreview({images: [formData.subpoiBusinessLicenseUrls[0].imageUrl]})"
                        >
                            查看大图
                        </roo-button>
                        <div
                            v-if="subpoiBusinessLicenseUrls.length > 0"
                            :class="$style.checkHint"
                        >
                            请检查营业执照名称与抬头名称一致
                        </div>
                    </roo-form-item>
                    <roo-form-item
                        key="reasonId"
                        prop="reasonId"
                        label="抬头变更原因"
                    >
                        <roo-select
                            v-model="reasonId"
                            :class="[$style.input, $style.mb14]"
                            :options="finalReasonsList"
                            :disabled="isFormDisabled"
                            placeholder="请选择原因"
                        />
                    </roo-form-item>
                    <roo-form-item
                        key="authorizationUrls"
                        prop="authorizationUrls"
                        label="补充协议"
                        :rules="rules.authorizationUrls"
                        required
                    >
                        <a
                            :class="$style.download"
                            target="_blank"
                            @click="handleDownloadProtocol"
                        >
                            下载填写补充协议
                        </a>

                        <div style="margin-top: 5px;">
                            上传盖章后的补充协议
                        </div>
                        <image-uploader
                            v-model="formData.authorizationUrls"
                            :disabled="isFormDisabled"
                            :max="5"
                        />
                    </roo-form-item>
                </roo-form>
            </template>
        </template>


        <roo-modal
            v-model="displaySubmitModal"
            size="small"
        >
            <div :class="$style.modalTitle">
                编辑成功
            </div>

            <template slot="footer">
                <roo-button @click="handleConfirmDone">
                    我知道了
                </roo-button>
            </template>
        </roo-modal>
        <div :class="$style.bottomText">
            <p>
                根据国家税法及发票管理相关规定，任何单位和个人不得让他人为自己或者介绍他人开具与实际经营业务情况不符的发票，否则属于虚开发票行为。
                我已充分了解上述发票相关规定，并且承诺仅就我司实际购买商品或服务索取发票。如我司未按国家相关规定申请开具或者使用增值税发票，由我司自行承担全部相应的法律后果。
            </p>
            <roo-checkbox
                :checked="isCheck"
                :disabled="isFormDisabled"
                @change="handleCheck"
            >
                我已阅读并理解上述内容
            </roo-checkbox>
        </div>
        <div class="footer-nav">
            <roo-button
                type="hollow"
                @click="$router.go(-1)"
            >
                取消
            </roo-button>
            <roo-button
                type="brand"
                :disabled=" isFormDisabled || !isCheck"
                @click="handleSave"
            >
                保存
            </roo-button>
        </div>
        <statement-tip-modal />
        <sms-yoda-verification
            ref="smsYodaVerification"
            :display-modal="displayModal"
            :check-sms-error="checkSmsError"
            :is-success-code="isSuccessCode"
            @update:displayModal="displayModal = $event"
            @update:smsCodes="handleSmsCodesUpdate"
            @update:mobile="handleMobile"
            @submit="handleSubmit"
        />
    </div>
</template>

<script>

/* eslint-disable import/no-unresolved, import/extensions */
import request from '$invoice/utils/request';
import { mapState, mapMutations } from 'vuex';
import { toast } from '@roo/roo-vue';

/* eslint-disable import/extensions, import/no-unresolved */
import ImageUploader from '../image-uploader';
import toastError from '$lib/toast-error';
import { pattern } from '$lib/has-invalid-charactor';
import { debounce, pick, getCookieValue } from '$lib/utils';
import StatementTipModal from '$components/statement-tip-modal';
/* eslint-enable import/extensions, import/no-unresolved */
import imagePreview from 'image-preview-vue';
import SmsYodaVerification from '$components/sms-yoda-verification';
import KpSignerInfo from '../../../components/qualification-form/KpSignerInfo.vue';
import QualificationForm from '../../../components/qualification-form';
import { sanitizeHtml } from '../../../utils/security';


export default {
    name: 'AddTitle',
    components: {
        ImageUploader,
        StatementTipModal,
        SmsYodaVerification,
        KpSignerInfo, // 注册KP签约人组件
        QualificationForm,
    },
    computed: {
        ...mapState('config', ['contractAName', 'contractId']),
        ...mapState('titleManagement', ['reasonsList']),
        ...mapState('invoiceTitle', ['editItemInfo']),
        ...mapState('apply', ['needVerification', 'noticeText', 'noticeTextNew']),

        // 控制表单是否禁用
        isFormDisabled() {
            return this.signingWorkflow || this.itemDisabled; // 新表单模式下或原有禁用条件下禁用表单
        },

        // 控制是否需要验证码校验
        shouldNeedVerification() {
            // 新表单模式下不需要验证码校验，旧表单模式按照配置确定是否需要验证码校验
            return this.signingWorkflow ? false : this.needVerification;
        },

        chooseNoticeText() {
            return this.signingWorkflow ? this.noticeTextNew : this.noticeText;
        },

        // 新增安全提示文本处理
        safeNoticeText() {
            return sanitizeHtml(this.chooseNoticeText);
        },

        finalReasonsList() {
            const { reasonsList } = this;
            const options = reasonsList.map(reason => ({
                label: reason.reasonName,
                value: reason.reasonId,
            }));

            return [...options];
        },
        id() {
            return parseInt(this.$route.query.id, 10) || null;
        },
    },
    created() {
        this.source = null;
        this.fuzzySearch = debounce(this.fuzzySearch, 1000);
        const { id } = this;
        if (id > 0) {
            this.fetchEditDetail(this.id);
        }
    },
    data() {
        return {
            itemDisabled: false,
            nameCheckMsg: '',
            defaultPartnerBName: '',
            displaySubmitModal: false,
            reasonId: 0,
            subpoiBusinessLicenseUrls: [],
            // authorizationUrls: [],
            // subpoiBusinessLicenseName: '',
            license: [],
            submitting: false,

            // 新增配置项
            signingWorkflow: false, // 是否启用新表单模型
            kpSignerName: '', // kp签约人名称
            kpSignerMobile: '', // kp签约人手机号
            kpSignerStatus: 0, // kp签约人状态：-1未通过实名认证，0信息缺失，1实名认证通过
            customerNumber: '', // 合同甲方税号

            formData: {
                partnerType: null,
                contractId: null,
                partnerBName: '',
                taxpayerIdNo: null,
                bankName: null,
                cardNo: null,
                registerAdd: null,
                telNo: null,
                reasonId: null,
                subpoiBusinessLicenseUrls: [],
                authorizationUrls: [],
                // 新表单新增字段
                legalRepName: '',
                legalRepIdCard: '',
                legalRepPhone: '',
            },
            downloadPStr: '',
            rules: {
                partnerBName: [
                    {
                        required: true,
                        message: '请输入抬头名称',
                    },
                    {
                        pattern,
                        message: '发票抬头不能有空格和如下特殊字符，如&，？等',
                    },
                    {
                        validator: (rule, value, callback) => {
                            this.handleCheckName(value).then((res) => {
                                const { code, msg } = res.data;

                                if (code !== 0 && this.defaultPartnerBName !== value) {
                                    callback(new Error(msg));
                                    return;
                                }

                                callback();
                            });
                        },
                    },
                ],
                taxpayerIdNo: [
                    {
                        pattern: /^[A-Z0-9]{15,20}$/,
                        message: '纳税人识别号为15-20位数字或字母',
                    },
                ],
                bankName: [
                    {
                        pattern,
                        message: '发票抬头不能有空格和如下特殊字符，如&，？等',
                    },
                ],
                cardNo: [
                    {
                        pattern,
                        message: '发票抬头不能有空格和如下特殊字符，如&，？等',
                    },
                ],
                registerAdd: [
                    {
                        pattern,
                        message: '发票抬头不能有空格和如下特殊字符，如&，？等',
                    },
                ],
                telNo: [
                    {
                        pattern,
                        message: '发票抬头不能有空格和如下特殊字符，如&，？等',
                    },
                ],
                authorizationUrls: [
                    {
                        required: true,
                        message: '请上传补充协议',
                    },
                ],
                subpoiBusinessLicenseUrls: [
                    {
                        required: true,
                        message: '请上传营业执照',
                    },
                ],
            },
            isCheck: false,
            displayModal: false,
            smsCodes: '',
            mobile: '',
            checkSmsError: '',
            isSuccessCode: false,
        };
    },
    mounted() {
        const { editItemInfo } = this;
        if (editItemInfo.id > 0) {
            this.fetchEditDetail(editItemInfo.id);
        }
        if (editItemInfo.status === 0) {
            this.itemDisabled = true;
        }
        this.getConfig(); // 用于获取kp签约人信息和甲方税号
    },
    methods: {
        ...mapMutations('invoiceTitle', ['changeEditInvoiceTitleItem']),

        handleSmsCodesUpdate(newSmsCodes) {
            this.smsCodes = newSmsCodes;
        },
        handleMobile(newMobile) {
            this.mobile = newMobile;
        },
        handleCheck(checked) {
            this.isCheck = checked;
        },
        imagePreview,
        ...mapMutations('titleManagement', ['changeTempParams']),
        // fuzzySearch(keyword, callback) {
        //     if (this.source) {
        //         this.source.cancel();
        //         this.source = null;
        //     }

        //     if (!keyword) {
        //         return;
        //     }

        //     const params = {
        //         fuzzy: keyword,
        //     };

        //     request.get('/finance/invoice/api/output/waimai/q/invoiceTitle', { params }).then((res) => {
        //         const { code, msg, data } = res.data;

        //         if (code !== 0) {
        //             throw new Error(msg);
        //         }

        //         this.suggestions = (data || []).map((item) => ({
        //             ...item,
        //             label: item.partnerBName,
        //             value: item.partnerBName,
        //         }));

        //         callback(this.suggestions);
        //     }).catch(toastError);
        // },
        fetchEditDetail(id) {
            const params = {
                qualificationId: id,
            };

            this.loading = true;
            return request.get('/finance/invoice/api/output/waimai/q/getInvoiceTitle', { params })
                .then((res) => {
                    const { code, msg, data } = res.data;

                    if (code !== 0) {
                        throw new Error(msg);
                    }

                    this.loading = false;
                    this.changeEditInvoiceTitleItem(data);
                    this.defaultPartnerBName = data.partnerBName;
                    this.downloadPStr = data.partnerBName;
                    Object.assign(this.formData, pick(data, [
                        'partnerBName',
                        'bankName',
                        'cardNo',
                        'registerAdd',
                        'taxpayerIdNo',
                        'subpoiBusinessLicenseUrls',
                        'authorizationUrls',
                        'telNo',
                        // 新表单新增字段
                        'legalRepName',
                        'legalRepIdCard',
                        'legalRepPhone',
                    ]));

                    [
                        'subpoiBusinessLicenseUrls',
                        'authorizationUrls',
                    ].forEach((key) => {
                        this.formData[key] = data[key] || [];
                    });

                    this.signingWorkflow = data.signingWorkflow;
                })
                .catch(toastError);
        },
        // 获取配置信息
        async getConfig() {
            try {
                const params = {
                    requestHitParam: true, // 需要hit参数
                    contractId: this.contractId,
                };

                const res = await request.get('/finance/invoice/api/qualification/getConfig', { params });

                if (res.data.code === 0 && res.data.data) {
                    const { kpSigner, customerNumber } = res.data.data;
                    this.kpSignerName = (kpSigner && kpSigner.name) || '';
                    this.kpSignerMobile = (kpSigner && kpSigner.mobile) || '';
                    this.kpSignerStatus = (kpSigner && kpSigner.status) || 0;
                    this.customerNumber = customerNumber;
                }
            } catch (error) {
                console.error('获取配置信息失败:', error);
                // 失败时使用默认值
            }
        },
        fuzzyLicenseSearch(keyword, callback) {
            if (!keyword) {
                return;
            }

            const { contractId } = this;
            const params = {
                fuzzy: keyword,
                contractId,
            };

            request.get('/finance/invoice/api/output/waimai/q/getLicenseByFuzzy', { params }).then((res) => {
                const { code, msg, data } = res.data;

                if (code !== 0) {
                    throw new Error(msg);
                }

                this.license = (data || []).map((item) => ({
                    ...item,
                    label: item.wmPoiName,
                    value: item.wmPoiName,
                }));

                callback(this.license);
            }).catch(toastError);
        },
        handleLicenseSelect(value) {
            const { license } = this;
            for (let i = 0; i < license.length; ++i) {
                const item = license[i];
                if (item.value === value) {
                    this.formData.subpoiBusinessLicenseUrls = [{ imageName: item.imageName, imageUrl: item.licenseUrl }];
                    return;
                }
            }
        },
        licenseOptionRender(h, { value, label }) {
            const { keyword } = this;
            const content = label.split(keyword).map((part, index) => {
                const replace = index === 0 ? '' : (<span style="color:red;">{keyword}</span>);
                return (
                    <span>
                        {replace}
                        {part}
                    </span>
                );
            });
            return (
                <span>
                    {content}
                </span>
            );
        },
        // handleSelect(value) {
        //     const { suggestions } = this;
        //     for (let i = 0; i < suggestions.length; ++i) {
        //         const item = suggestions[i];
        //         if (item.value === value) {
        //             return;
        //         }
        //     }
        // },
        optionRender(h, { value, label }) {
            const { keyword } = this;
            const content = label.split(keyword).map((part, index) => {
                const replace = index === 0 ? '' : (<span style="color:red;">{keyword}</span>);
                return (
                    <span>
                        {replace}
                        {part}
                    </span>
                );
            });
            return (
                <span>
                    {content} (ID: {value})
                </span>
            );
        },
        handleCheckName(val) {
            this.downloadPStr = val;
            const { contractId } = this;
            const params = {
                contractId,
                partnerType: 103,
                partnerBName: val,
            };
            return request.get('/finance/invoice/api/output/waimai/q/invoiceTitleNameCheck', {
                params,
            });
        },
        handleDownloadProtocol() {
            if (this.downloadPStr === '') {
                toast.warn('请输入抬头名称');
            } else if (this.reasonId < 1) {
                toast.warn('请选择抬头变更原因');
            } else {
                const { reasonId, contractAName } = this;
                const acctId = getCookieValue('acctId');
                // const downloadUrl = `/finance/invoice/api/output/waimai/q/downLoadProtocol?partnerBName=${this.downloadPStr}&reasonId=${reasonId}&originalPartnerName=${contractAName}&invoiceSource=${113}&acctId=${acctId}`;
                const downloadUrl = '//s3plus.meituan.net/waimai-mfe-dragonfly-online/%E5%8F%91%E7%A5%A8%E8%A1%A5%E5%85%85%E5%8D%8F%E8%AE%***********.docx';
                window.open(downloadUrl);
            }
        },
        handleSave() {
            // 根据表单类型确定验证函数
            const validate = this.signingWorkflow ? this.validateNewForm : this.validateOldForm;

            if (this.shouldNeedVerification === true) {
                validate().then(
                    () => {
                        this.save();
                    },
                    error => {
                        toast.warn((error && error.message) || '资质信息填写有误，请检查');
                    },
                );
            } else {
                validate().then(
                    () => {
                        this.handleSubmit();
                    },
                    error => {
                        toast.warn((error && error.message) || '资质信息填写有误，请检查');
                    },
                );
            }
        },
        buildBody() {
            const {
                formData,
                id,
                contractId,
                editItemInfo,
            } = this;
            const fields = [
                'partnerBName',
                'bankName',
                'cardNo',
                'registerAdd',
                'taxpayerIdNo',
                'telNo',
                'subpoiBusinessLicenseUrls',
                'authorizationUrls',
                // !!!新表单新增字段(暂不启用，后端接口更新后确认)
                'legalRepName',
                'legalRepIdCard',
                'legalRepPhone',
            ];

            const item = Object.assign(
                pick(formData, fields),
                { qualificationId: id || editItemInfo.id, contractId, partnerType: 103 },
            );

            [
                'subpoiBusinessLicenseUrls',
                'authorizationUrls',
            ].forEach((key) => {
                if (item[key]) {
                    item[key] = item[key].map(x => x.imageName);
                }
            });

            return item;
        },
        save() {
            this.$nextTick(async () => {
                try {
                    await this.$refs.smsYodaVerification.getMobile();
                } catch (err) {
                    console.log(err);
                    // 错误处理已经在 getMobile 中处理
                }
            });
        },
        getUserAgent() {
            return navigator.userAgent;
        },
        handleSubmit() {
            const { contractId } = this;
            const body = this.buildBody();
            const params = {
                ...body,
                partnerType: 103,
                contractId,
            };
            this.submitting = true;

            if (this.shouldNeedVerification === true) {
                params.smscode = this.smsCodes;
                params.uuid = getCookieValue('device_uuid');
                params.mobile = this.mobile;
                params.ua = this.getUserAgent();
                params.needVerification = true;
            }

            return request.post('/finance/invoice/api/output/waimai/q/invoiceTitleEdit', params)
                .then((res) => {
                    this.submitting = false;
                    const { code, msg, data } = res.data;

                    if (code === 0) {
                        if (this.shouldNeedVerification === true && data.response_code) {
                            this.isSuccessCode = true;
                            this.displayModal = false;
                            setTimeout(() => {
                                this.displaySubmitModal = true;
                            }, 1000);
                        }
                        this.displaySubmitModal = true;
                    } else {
                        if (msg && msg.message) {
                            this.checkSmsError = msg.message;
                            return;
                        }
                        this.checkSmsError = '';
                        toast.error(msg);
                        // throw new Error(msg);
                    }
                }, (err) => {
                    this.submitting = false;
                    throw err;
                })
                .catch(toastError);
        },
        handleConfirmDone() {
            this.displaySubmitModal = false;
            this.$router.push({ name: 'qualification-select-update' });
        },
        validateNewForm() {
            const { forms } = this.$refs;

            return new Promise((resolve, reject) => {
                // 暂定

                // 校验营业执照OCR识别的税号与新资质抬头税号是否一致
                let errorMsg = '';
                if (this.formData.subpoiBusinessLicenseUrls[0]
                    && this.formData.subpoiBusinessLicenseUrls[0].taxpayerIdNo
                    && (this.formData.taxpayerIdNo !== this.formData.subpoiBusinessLicenseUrls[0].taxpayerIdNo)) {
                    // 税号不匹配
                    errorMsg = '资质税号需与上传的营业执照一致';
                }

                if (errorMsg) {
                    reject(new Error(errorMsg));
                    return;
                }

                // 新资质表单基础校验
                let count = 0;
                const len = forms.length;

                function cb(pass) {
                    if (pass) {
                        count += 1;
                        if (count === len) {
                            resolve();
                        }
                    } else {
                        reject(new Error('资质信息填写有误，请检查'));
                    }
                }

                for (let i = 0; i < len; ++i) {
                    forms[i].validate(cb);
                }
            });
        },
        validateOldForm() {
            // console.log('$refs:', this.$refs);
            const { forms, form2 } = this.$refs;
            const arr = [forms].concat([form2]);

            return new Promise((resolve, reject) => {
                let count = 0;
                let len = 0;
                arr.forEach((item) => {
                    if (item) {
                        len += 1;
                    }
                });

                function callback(pass) {
                    if (pass) {
                        count += 1;
                        if (count === len) {
                            resolve();
                        }
                    } else {
                        reject();
                    }
                }
                for (let i = 0; i < len; ++i) {
                    if (arr[i]) {
                        arr[i].validate(callback);
                    }
                }

                // arr[0].validate(callback);
            });
        },
        handleQualificationUpdate(index, newData) {
            // 更新 formData
            Object.keys(newData).forEach(key => {
                this.$set(this.formData, key, newData[key]);
            });
        },
    },
};
</script>

<style lang="scss" module>
.module {
    margin-top: 20px;

    :global(.roo-radio-group) {
        display: inline-block;
        margin-left: 20px;
        line-height: 1;
        vertical-align: middle;
    }
}

.headerRow {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.h3 {
    margin-top: 0;
    margin-bottom: 20px;
    font-size: 20px;
}

.downloadLink {
    color: #FF6A00;
    text-decoration: none;
    font-size: 14px;
    line-height: 20px;
    letter-spacing: 0px;

    &:hover {
        text-decoration: underline;
    }
}

.form {
    padding-bottom: 20px;
    margin-bottom: 20px;
    border-bottom: 1px dashed #EBEEF2;

    :global(.form-group.row:last-child) {
        margin-bottom: 0;
    }
}
.input {
    display: block;
    width: 480px;
    // vertical-align: middle;
}
.extra {
    font-weight: 400;
    font-family: PingFangSC-Regular;
    font-size: 12px;
    color: #999999;
    font-weight: 400;
    margin-top: 8px;
}
.mb14 {
    margin-bottom: 14px;
}
.uploadBtn {
    display: block;
    margin-top: 10px;
}
.tipTitle {
    font-weight: 500;
    font-family: PingFangSC-Medium;
    font-size: 16px;
    color: #222222;
    text-align: left;
    line-height: 24px;
}
.tipContent {
    width: 630px;
    margin-top: 13px;
    background: #F7F8FA;
    font-weight: 400;
    font-family: PingFangSC-Regular;
    font-size: 18px;
    color: #000000;
    padding: 20px;
}
.checkHint {
    width: 367px;
    height: 32px;
    margin-top: 10px;
    background: #FFF9DB;
    padding: 6px;
    font-size: 14px;
}
.uploadMethod {
    // font-weight: 500;
    font-family: PingFangSC-Medium;
    font-size: 14px;
    color: #222222;
    margin: 5px 0;
}
.download {
    cursor: pointer;
}
.modalTitle {
    font-size: 20px;
    color: #222222;
    font-weight: 500;
}
.bottomText{

font-size: 14px;
padding: 0px 100px;
}
.alert {
    margin-bottom: 24px;
}
</style>
