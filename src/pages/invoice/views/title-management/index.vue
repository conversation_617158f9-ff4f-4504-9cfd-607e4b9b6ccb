<template>
    <div :class="$style.container">
        <div>
            <roo-breadcrumb
                :items="breadcrumb"
            />
        </div>

        <div
            v-if="isEdit && editItemInfo.status !== 2"
            :class="$style.statusCheck"
        >
            <div style="margin-left: 30px;">
                <div
                    v-if="editItemInfo.status === 1"
                    :class="$style.statusTitle"
                >
                    审核通过
                </div>
                <div
                    v-if="editItemInfo.status === 0"
                    :class="$style.statusTitle"
                >
                    发票抬头审核中
                </div>
                <div
                    v-if="editItemInfo.status === 0"
                    :class="$style.statusContent"
                >
                    大约1-3个工作日完成审核，审核通过后可使用该抬头开发票
                </div>
            </div>
            <img
                :class="$style.alertIcon"
                src="./image/shengluehao.png"
            />
        </div>

        <div
            v-if="isEdit && editItemInfo.status === 2"
            :class="$style.statusRejected"
        >
            <div style="margin-left: 30px;">
                <div :class="$style.statusTitle">
                    发票抬头已驳回
                </div>
                <div
                    v-if="alertText && alertText.length > 1"
                    :class="$style.statusErrorContent"
                >
                    <div
                        v-for="(txt, idx) in alertText"
                        :key="idx"
                    >
                        {{ txt }}
                    </div>
                </div>
            </div>
            <img
                :class="$style.alertIcon"
                src="./image/gantanhao.png"
            />
        </div>

        <div :class="$style.header">
            <tab-header v-if="isEdit" />
            <router-view />
        </div>
    </div>
</template>

<script>
import { mapState, mapActions, mapMutations } from 'vuex';
/* eslint-enable import/extensions, import/no-unresolved */
/* eslint-disable import/extensions, import/no-unresolved */
import TabHeader from './tab-header';
/* eslint-disable import/extensions, import/no-unresolved */

const REJECT_REASON_MAP = {
    1: '发票抬头公司名称与合同甲方名称不一致，请点击修改资质，下载补充协议模板 http://dpurl.cn/n/5rT1t (请复制地址到电脑端浏览器打开)，打印盖章后重新上传。',
    2: '请上传开票名称一致的营业执照。',
    3: '补充协议原发票抬头与合同甲方名称不一致。',
    4: '请上传开票申请书，点击修改资质，下载开票申请书模板 http://dpurl.cn/n/5rPjm (请复制地址到电脑端浏览器打开)，打印盖章后重新上传。',
    // 5: '其他',
    // 5: '',

    11: '补充协议显示不完整，请重新上传',
    12: '营业执照显示不完整，请重新上传',
    13: '请上传新模版补充协议',
    14: '请上传营业执照',
    15: '补充协议中的原发票抬头、甲方、合同签约甲方名称，三者需一致，请修改',
    16: '补充协议中的新增发票抬头、所上传的营业执照、公司名称，三者需一致，请修改',
    17: '补充协议未盖章，盖章后重新上传（其中：变更原因2和3，需要原抬头和现抬头均盖章）',
    18: '补充协议附件列表无现发票抬头，添加后重新提交',

    21: '请上传开票申请书。',
    22: '开票申请书请盖相应公章。',
    23: '开票申请书发票抬头与公司名称不一致。',
    24: '开票申请书不显示。',
};

export default {
    name: 'TitleManagement',
    components: {
        TabHeader,
    },
    computed: {
        ...mapState('invoiceTitle', ['editItemInfo', 'editInvoiceTitleItem']),
        ...mapState('titleManagement', ['isEdit', 'isChecking', 'isRejected']),
        breadcrumb() {
            return [
                {
                    text: '抬头管理',
                    link: {
                        name: 'invoice-title',
                    },
                }, {
                    text: this.isEdit ? '编辑抬头' : '新增抬头',
                },
            ];
        },
        alertText() {
            const { editInvoiceTitleItem } = this;
            if (!editInvoiceTitleItem) {
                return [];
            }
            const { rejectReasons, comment } = editInvoiceTitleItem;

            const arr = ['审核驳回，驳回原因：'];

            (rejectReasons || '').split(',').forEach((code) => {
                const str = REJECT_REASON_MAP[code];
                if (str) {
                    arr.push(str);
                }
            });

            if (comment) {
                arr.push(comment);
            }

            return arr;
        },
    },
    data() {
        return {};
    },
    methods: {
        ...mapActions('titleManagement', ['fetchReasonsList', 'fetchRelatedStoreList']),
        ...mapActions('invoiceTitle', ['fetchTitleInfoList']),
    },
    mounted() {
        this.fetchTitleInfoList(1);
        this.fetchReasonsList();
        if (this.isEdit) {
            this.$router.replace({ name: 'edit-title' });
        } else {
            this.$router.replace({ name: 'add-title' });
        }
    },
    watch: {
        isEdit(val) {
            if (val) {
                this.$router.replace({ name: 'edit-title' });
            } else {
                this.$router.replace({ name: 'add-title' });
            }
        },
    },
};
</script>

<style lang="scss" module>
.container {
    position: relative;
    min-height: 100%;
    padding-top: 10px;
    padding-bottom: 56px;
    // background: #FFF;
}
.header {
    margin-top: 16px;
    padding: 20px;
    border-bottom: 1px solid #E9EAF2;;
    background: #fff;
}
.statusCheck {
    background-image: linear-gradient(180deg, #F0F7FF 0%, #D1E8FF 100%);
    border-radius: 8px;
    height: 74px;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    padding: 6px 0;
    box-sizing: border-box;
    align-items: center;
}
.statusRejected {
    background-image: linear-gradient(179deg, #FFF2F8 0%, #FFDBE7 100%);
    height: 74px;
    border-radius: 8px;
    padding: 12px 0;
    box-sizing: border-box;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
}
.statusTitle {
    font-weight: 600;
    font-family: PingFangSC-Semibold;
    font-size: 18px;
    color: #222222;
    font-weight: 600;
}
.statusContent {
    font-weight: 500;
    font-family: PingFangSC-Medium;
    font-size: 14px;
    color: #222222;
    font-weight: 500;
}
.statusErrorContent {
    font-weight: 500;
    font-family: PingFangSC-Medium;
    font-size: 14px;
    color: #222222;
    font-weight: 500;
    display: flex;
}
.alertIcon {
    width: 100px;
    height: 74px;
    object-fit: contain;
}
</style>
