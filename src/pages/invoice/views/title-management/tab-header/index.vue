<template>
    <div :class="$style.tabs">
        <roo-tabs
            v-model="active"
            type="border"
        >
            <roo-tab-pane
                name="edit-title"
                label="编辑抬头"
            />
            <roo-tab-pane
                name="relate-store"
                label="关联门店"
            />
        </roo-tabs>
    </div>
</template>

<script>
export default {
    name: 'TabHeader',
    computed: {
        active: {
            get() {
                const [top, sec] = this.$route.matched;
                if (
                    sec && ['edit-title', 'relate-store'].indexOf(sec.name) > -1
                ) {
                    return sec.name;
                }
                return null;
            },
            set(val) {
                if (val === this.active) {
                    return;
                }
                switch (val) {
                    case 'edit-title':
                        this.$router.push({ name: 'edit-title' });
                        break;
                    case 'relate-store':
                        this.$router.push({ name: 'relate-store' });
                        break;
                    default:
                        break;
                }
            },
        },
    },
};
</script>

<style lang="scss" module>
.tabs {
    background: #fff;
}
</style>
