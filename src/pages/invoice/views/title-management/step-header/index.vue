<template>
    <div :class="$style.steps">
        <roo-steps
            :steps="steps"
            :active="active"
            text-position="right"
        />
    </div>
</template>

<script>
export default {
    name: 'StepHeader',
    computed: {
        active() {
            const { matched } = this.$route;
            if (matched.length > 0) {
                const route = matched[matched.length - 1];
                const activeName = route.name;
                return ['add-title', 'relate-store'].indexOf(activeName) + 1;
            }
            return 0;
        },
        steps() {
            return ['新增抬头', '关联门店'];
        },
    },
    mounted() {
        if (this.active === 0) {
            this.$router.replace({ name: 'add-title' });
        }
    },
    watch: {
        active(val) {
            if (val === 0) {
                this.$router.replace({ name: 'add-title' });
            }
        },
    },
};
</script>

<style lang="scss" module>
.steps {
    margin-top: 16px;
    padding: 20px;
    border-bottom: 1px solid #E9EAF2;;
    background: #fff;
}
</style>
