import '@babel/polyfill';
import Vue from 'vue';

import '@roo/roo-vue';
import '@roo/roo-vue/dist/css/roo-vue.css';
import MTD from '@ss/mtd-vue';
import '@ss/mtd-vue/lib/theme2/index.css';


import 'image-preview-vue/lib/imagepreviewvue.css';

import App from './app';
import store from './store';
import router from './router';

Vue.use(MTD);

/* eslint-disable no-new */
new Vue({
    el: '#app',
    store,
    router,
    render(h) {
        return h(App);
    },
});
/* eslint-enable no-new */
