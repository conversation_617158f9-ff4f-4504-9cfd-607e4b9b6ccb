<!DOCTYPE html>
<html lang="zh-CN">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta http-equiv="X-UA-Compatible" content="ie=edge">
  <!-- 上报通道标识 -->
  <meta name="lx:category" content="waimai_e">
  <!-- 上报应用标识 -->
  <meta name="lx:appnm" content="waimai_e_center">
  <!-- 页面名称：商家对账首页，上报页面标识 -->
  <meta name="lx:cid" content="c_uo7tkvg4">
  <link rel="dns-prefetch" href="//lx1.meituan.net" />
  <link rel="dns-prefetch" href="//lx2.meituan.net" />
  <link rel="dns-prefetch" href="//plx.meituan.com" />
  <title>Document</title>

  <script src="https://appsec-mobile.meituan.com/h5guard/H5guard.js"></script>
  <script>
    if(window.H5guard){
          H5guard.init({
              xhrHook: true,
              fetchHook: true,
          });
      }
  </script>
  <script>
    // owl
    "use strict"; !function (u, d) { var t = "owl", e = "_Owl_", n = "Owl", r = "start", c = "error", p = "on" + c, f = u[p], h = "addEventListener", l = "attachEvent", v = "isReady", b = "dataSet"; u[t] = u[t] || function () { try { u[t].q = u[t].q || []; var e = [].slice.call(arguments); e[0] === r ? u[n] && u[n][r] ? u[n][r](e[1]) : u[t].q.unshift(e) : u[t].q.push(e) } catch (e) { } }, u[e] = u[e] || { preTasks: [], pageData: [], use: function (e, t) { this[v] ? u[n][e](t) : this.preTasks.push({ api: e, data: [t] }) }, run: function (t) { if (!(t = this).runned) { t.runned = !0, t[b] = [], u[p] = function () { t[v] || t[b].push({ type: "jsError", data: arguments }), f && f.apply(u, arguments) }, u[h] && u[h]("unhandledrejection", function (e) { t[v] || t[b].push({ type: "jsError", data: [e] }) }); var e = function (e) { !t[v] && e && t[b].push({ type: "resError", data: [e] }) }; u[h] ? u[h](c, e, !0) : u[l] && u[l](p, e); var n = "MutationObserver", r = u[n] || u["WebKit" + n] || u["Moz" + n], a = u.performance || u.WebKitPerformance, s = "disableMutaObserver"; if (r && a && a.now) try { var i = -1, o = u.navigator.userAgent; -1 < o.indexOf("compatible") && -1 < o.indexOf("MSIE") ? (new RegExp("MSIE (\\d+\\.\\d+);").test(o), i = parseFloat(RegExp.$1)) : -1 < o.indexOf("Trident") && -1 < o.indexOf("rv:11.0") && (i = 11), -1 !== i && i <= 11 ? t[s] = !0 : (t.observer = new r(function (e) { t.pageData.push({ mutations: e, startTime: a.now() }) })).observe(d, { childList: !0, subtree: !0 }) } catch (e) { } else t[s] = !0 } } }, u[e].runned || u[e].run() }(window, document);
  </script>
  <script>
    // 在 head 标签内，其他静态资源之前，以 script 内联方式引入以下的 JS 内容（以下这部分不能动）    
    !(function (win, doc, ns) {
      win['_MeiTuanALogObject'] = ns;
      if (!win[ns]) {
        var _LX = function () {
          var t = function () {
            var inst = function () {
              inst.q.push([arguments, +new Date()]);
            }
            inst.q = [];
            t.q.push([arguments, inst]);
            return inst;
          }
          t.q = [];
          t.t = +new Date();
          _LX.q.push([arguments, t]);
          return t;
        };
        _LX.q = _LX.q || [];
        _LX.l = +new Date();
        win[ns] = _LX;
      }
    })(window, document, 'LXAnalytics');
    // 灵犀种子代码结束（以上这部分不能动）

    // 灵犀初始化配置
    LXAnalytics('config', {
      defaultCategory: 'waimai_e', // 页面默认通道，将 channelIdentifier 替换为您的上报通道标识 
      appName: 'waimai_e_center' // 页面应用名，将 appIdentifier 替换为您在配置平台配置的应用标识
    });
  </script>
</head>

<body>
  <!-- 灵犀sdk -->
  <script src="//lx.meituan.net/lx.5.min.js" type="text/javascript" async></script>
  <!-- raptor -->
  <script crossorigin="anonymous"
    src="https://s3.meituan.net/mnpm-cdn/@mtfe-mt-apm-web-1.12.1/owl_1.12.1.min.js"></script>
  <script>
    try {
      const devMode = '<%= htmlWebpackPlugin.options.DEPLOY_ENV %>' !== 'production'
      window.owl && window.owl('start', {
        project: 'com.sankuai.wmfinance.merchant.invoicepc',
        devMode,
        pageUrl: location.origin + location.pathname + location.hash.split('?')[0],
        enableLogTrace: true, // 开启请求链路日志功能
        SPA: {
          autoPV: true,
          getFST: true
        },
        resource: {
          enableStatusCheck: true, // 支持上报具体的Http状态码
        },
        logan: {
          enable: true,
          version: '2.1.5',
          config: {
            devMode: devMode
          }
        },
        ignoreList: {
          // 过滤自动采集和手动 addError 上报的异常信息, 异常名称以元素字符串为开头则忽略
          js: ["ResizeObserver loop"],
          // 忽略 Owl 自动采集到的 ajax 请求
          ajax: [
            "https?://dreport.meituan.net",
            'https?://report.meituan.com',
            'https?://ws.meituan.net',
            'https?://w.meituan.net',
            'https?://s3plus.meituan.net',
            'https?://image.meituan.net',
            'https?://p0.meituan.net',
            'https?://p1.meituan.net',
            'https?://img.meituan.net',
            'https?://lx1.meituan.net'
          ],
          // 忽略 Owl 自动采集到的静态资源请求, 默认全部采集
          resource: [],
          // 过滤 Script error(来自跨域 JS 的错误), 默认true
          noScriptError: true,
        },
        autoCatch: {
          fetch: true, // 自动采集 fetch
        },
        page: {
          fstPerfAnalysis: true, // 开启首屏性能指标
          logSlowView: true, // 采集上报慢访问的个案数据
        },
        error: {
          formatUnhandledRejection: true, // 区分 UnhandledRejection 类型
        },
        onErrorPush: function (modal) {
          // 过滤非js错误导致的错误
          var _allErrorType = ['EvalError', 'RangeError', 'ReferenceError', 'SyntaxError', 'TypeError', 'URIError', 'AggregateError', 'Error']
          if (modal && modal.category === "jsError" && modal.sec_category.indexOf("unhandledrejection") > -1) {
            var _errorMatch = modal.content && modal.content.toString().match(/[a-zA-Z]+/)
            var _errorType = _errorMatch ? _errorMatch[0] : '';
            // 非js错误全部改成warn
            if (_allErrorType.indexOf(_errorType) < 0) {
              modal.level = 'warn'
            }
          }
          if (modal.content.indexOf('cashier-') > -1) {
            modal.level = 'info';
          }
          return modal
        },
        onBatchPush: function (m) {
          // 302转换为warn
          // 过滤掉因为页面跳转导致接口被取消的报错
          if (m && m.logContent && m.logContent.indexOf('from: xhr') > -1) return false;
          return true;
        },
      })
    } catch (error) {

    }

  </script>
  <div id="app"></div>
</body>

</html>