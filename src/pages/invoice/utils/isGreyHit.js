import axios from 'axios';
import { toast } from '@roo/roo-vue';
import ComParams from './com_params';
import owlReport from '../../../utils/owlReport';
import { setCookie } from '../../../lib/utils';

function isGreyHit() {
    const comParams = ComParams.get();

    const options = {
        url: '/finance/invoice/api/waimai/output/switch',
        method: 'get',
        params: { acctId: comParams.acctId },
    };

    setCookie('invoiceSource', 113, { expires: new Date(new Date().getTime() + 10 * 24 * 60 * 60 * 1000) });

    return axios(options).then((response) => {
        if (response.status >= 200 && response.status < 300) {
            return response;
        } else {
            throw response.statusText;
        }
    }).then((response) => {
        // 统一对code非0做处理
        const { data } = response;
        if (data.code !== 0) {
            const errmsg = `${response.request && response.request.url} code(${data.code}) message(${data.msg}) params(${JSON.stringify({ acctId: comParams.acctId })})`;
            owlReport('ajax异常', errmsg);
            // 鉴权失败
            if (data.code === 100) {
                toast.error('访问失败，请重新从业务系统跳转至开票页面', 10000);
            }
        }
        return response;
    }).catch((err) => {
        if (err && err.message && err.message.indexOf('timeout') > -1) {
            toast.error('请求超时，请重试~', 1000);
        }
        if (err && err.config) {
            // ajax异常
            const errmsg = `${err.request && err.request.url} message(${err.message}) params(${JSON.stringify({ acctId: comParams.acctId })})`;
            owlReport('ajax异常', errmsg);
            throw err;
        }
    });
}

export default isGreyHit;
