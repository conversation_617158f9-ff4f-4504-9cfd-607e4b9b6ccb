import axios from 'axios';
import { toast } from '@roo/roo-vue';
import owlReport from '../../../utils/owlReport';
import ComParams from './com_params';
import { getCookieValue, setCookie } from '../../../lib/utils';
import SOURCE from '../../../lib/sourceEnum';

// 默认设置
axios.defaults.headers.post['Content-Type'] = 'application/x-www-form-urlencoded';
// proxy的最大超时时间是20s
// todo:时间改为15000
axios.defaults.timeout = 15000; // 默认超时时间15s

setCookie('invoiceSource', 113, { expires: new Date(new Date().getTime() + 10 * 24 * 60 * 60 * 1000) });

const isGreyConst = getCookieValue('wmPoiId') === '-1';

/**
 * @param url
 * @param method get/post
 * @param params 参数
 * @param config 其他配置，如：{timeout: 2000, headers: {'X-Requested-With': 'XMLHttpRequest'}}
 * @param settings 工具配置，如：{showError: false} 异常不报错
 */
function ajax(url, method, params, settings) {
    const options = {
        method,
        url,
        // withCredentials: true,
    };

    const {
        showError = true,
    } = settings;
    // 新增通用参数 source
    // 增加反爬_token https://km.sankuai.com/page/********，_token竟然达到1k长度，容易引起get参数过大问题
    if (window.Rohr_Opt && window.Rohr_Opt.reload) {
        params._token = window.Rohr_Opt.reload(url);
    }
    const accountSource = isGreyConst ? getCookieValue('invoiceSource') : getCookieValue('source') || SOURCE.WAIMAI;
    // 添加请求通用参数
    const comParams = ComParams.get();
    if (method === 'get') {
        const query = params.params || {};
        if (query.isInvoiceHistory) {
            query.source = getCookieValue('source') || SOURCE.WAIMAI;
        } else {
            if (isGreyConst || query.requestHitParam) {
                query.invoiceSource = 113;
            } else {
                query.source = accountSource;
            }
        }
        Object.assign(query, comParams);
        params.params = query;
        Object.assign(options, params);
    } else if (method === 'post') {
        if (isGreyConst) {
            params.invoiceSource = 113;
        } else {
            params.source = accountSource;
        }
        Object.assign(params, comParams);
        options.data = params;
    }

    return axios(options).then((response) => {
        if (response.status >= 200 && response.status < 300) {
            return response;
        } else {
            throw response.statusText;
        }
    }).then((response) => {
        // 统一对code非0做处理
        const { data } = response;
        if (data.code !== 0) {
            const errmsg = `${response.request && response.request.url} code(${data.code}) message(${data.msg}) params(${JSON.stringify(params)})`;
            owlReport('ajax异常', errmsg);
            // 鉴权失败
            if (data.code === 100) {
                toast.error('访问失败，请重新从业务系统跳转至开票页面', 10000);
            }
        }
        return response;
    }).catch((err) => {
        if (err && err.message && err.message.indexOf('timeout') > -1) {
            if (showError) {
                toast.error('请求超时，请重试~', 1000);
            }
        }
        if (err && err.config) {
            // ajax异常
            const errmsg = `${err.request && err.request.url} message(${err.message}) params(${JSON.stringify(params)})`;
            owlReport('ajax异常', errmsg);
            throw err;
        }
    });
}

export default {
    get(url, params = {}, settings = {}) {
        return ajax(url, 'get', params, settings);
    },
    post(url, params = {}, settings = {}) {
        return ajax(url, 'post', params, settings);
    },
};
