/**
 * HTML内容安全处理工具
 */

const ALLOWED_TAGS = ['BR', 'SPAN', 'A'];
const ALLOWED_ATTRS = {
    A: ['href', 'target', 'style'],
    SPAN: ['class', 'style'],
    BR: [],
};

/**
 * 验证URL是否为合法的美团html地址
 * @param {string} url - 需要验证的URL
 * @returns {boolean} - 是否为合法URL
 */
function isValidUrl(url) {
    if (!url) return false;
    return url.startsWith('https://page.meituan.net/html');
}

/**
 * 净化HTML内容，移除潜在的XSS威胁
 * @param {string} htmlContent - 需要净化的HTML内容
 * @returns {string} - 净化后的安全HTML内容
 */
export function sanitizeHtml(htmlContent) {
    if (!htmlContent) {
        return '';
    }

    // 使用正则表达式匹配HTML标签（包括闭合标签）和文本内容
    let result = '';
    let lastIndex = 0;
    const tagPattern = /<\/?([a-zA-Z0-9]+)((?:\s+[a-zA-Z0-9-]+(?:="[^"]*")?)*)\s*>/g;
    const matches = Array.from(htmlContent.matchAll(tagPattern));

    matches.forEach(match => {
        // 添加标签之前的文本
        result += htmlContent.slice(lastIndex, match.index);

        const [fullMatch, tag, attrs] = match;
        const isClosingTag = fullMatch.startsWith('</');
        const tagUpper = tag.toUpperCase();

        // 处理标签
        if (ALLOWED_TAGS.includes(tagUpper)) {
            if (!isClosingTag) {
                // 处理属性
                const allowedAttrs = ALLOWED_ATTRS[tagUpper] || [];
                const attrMatches = (attrs.match(/\s+([a-zA-Z0-9-]+)(?:="([^"]*)")?/g) || []);

                const validAttrs = attrMatches
                    .map(attr => {
                        const [name, ...rest] = attr.trim().split('=');
                        const value = rest.join('='); // 处理属性值中可能包含的等号
                        return {
                            name,
                            value: value ? value.slice(1, -1) : '', // 移除引号
                        };
                    })
                    .filter(({ name }) => allowedAttrs.includes(name.toLowerCase()))
                    .map(({ name, value }) => {
                        // 特殊处理href属性
                        if (name.toLowerCase() === 'href' && !isValidUrl(value)) {
                            return null;
                        }
                        return value ? `${name}="${value}"` : name;
                    })
                    .filter(Boolean);

                const attrString = validAttrs.length ? ` ${validAttrs.join(' ')}` : '';
                result += `<${tagUpper}${attrString}>`;
            } else {
                // 处理结束标签
                result += `</${tagUpper}>`;
            }
        }

        lastIndex = match.index + fullMatch.length;
    });

    // 添加剩余的文本
    result += htmlContent.slice(lastIndex);

    return result;
}

export default sanitizeHtml;
