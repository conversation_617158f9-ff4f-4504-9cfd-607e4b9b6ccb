<template>
    <div :class="[$style.imageUploader, { [$style.disabled]: disabled }]">
        <Upload
            :multiple="false"
            :show-upload-list="false"
            type="drag"
            accept="image/*"
            :max-size="10240"
            action="/finance/pc/api/invoice/qualification/imageUpload?invoiceSource=113"
            :before-upload="beforeUpload"
            :on-success="onUploadSuccess"
            :on-error="onUploadError"
            :on-progress="onUploadProgress"
            :on-exceeded-size="onExceededSize"
            :disabled="disabled || currentStatus === STATUS.UPLOADING"
        >
            <!-- 默认上传状态 -->
            <div v-if="displayStatus === STATUS.IDLE" :class="$style.uploadContent">
                <Icon type="cloud-upload" size="24" :class="$style.uploadIcon" />
                <p>点击或拖拽文件至此上传</p>
                <p :class="$style.uploadHint">支持JPG、PNG格式，单个文件不超过10MB</p>
            </div>

            <!-- 上传中状态 -->
            <div v-else-if="displayStatus === STATUS.UPLOADING" :class="$style.uploadingContent">
                <Icon type="loading" size="20" :class="$style.uploadingIcon" />
                <p>{{ uploadData.fileName }}</p>
                <div :class="$style.progressBar">
                    <div :class="$style.progressFill" :style="{ width: uploadData.progress + '%' }"></div>
                </div>
                <button :disabled="disabled" :class="$style.cancelBtn" @click="cancelUpload">取消</button>
            </div>

            <!-- 上传失败状态 -->
            <div v-else-if="displayStatus === STATUS.ERROR" :class="$style.errorContent">
                <div :class="$style.errorInfo">
                    <Icon type="error" size="14" style="color: #ff4d4f" />
                    <span style="font-size: 14px; color: #ff4d4f;">文件上传失败，请重新上传</span>
                </div>
                <button :disabled="disabled" :class="$style.retryBtn" @click="retryUpload">重新上传</button>
            </div>

            <!-- 上传成功状态 -->
            <div v-else-if="displayStatus === STATUS.SUCCESS" :class="$style.successContent">
                <div :class="$style.fileInfo">
                    <!-- 文件图标 -->
                    <Icon type="attachment" :class="$style.fileIcon" />
                    <!-- 文件名 -->
                    <span :class="$style.fileName">{{ value[0].fileName || value[0].imageName || '图片文件' }}</span>
                </div>

                <div :class="$style.uploadInfo">
                    <!-- 营业执照读取状态 -->
                    <div :class="$style.uploadStatus">
                        <template v-if="readSuccess">
                            <Icon type="check-circle" :class="$style.statusIcon" />
                            <span :class="$style.statusText">营业执照读取成功</span>
                        </template>
                        <template v-else>
                            <Icon type="error" :class="[$style.statusIcon, $style.errorIcon]" />
                            <span :class="[$style.statusText, $style.errorText]">营业执照读取失败</span>
                        </template>
                    </div>
                    <!-- 操作按钮 -->
                    <div :class="$style.actions">
                        <button :disabled="disabled" :class="$style.retryBtn" @click="reuploadFile">重新上传</button>
                    </div>
                </div>
            </div>
        </Upload>
    </div>
</template>

<script>
import { toast } from '@roo/roo-vue';
import { Upload, Icon } from '@roo-design/roo-vue';
import '@roo-design/roo-vue/dist/styles/yellow.css';

export default {
    name: 'ImageUploader',
    components: {
        Upload,
        Icon,
    },
    props: {
        value: {
            type: Array,
            required: true,
        },
        disabled: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {
            // 状态管理
            currentStatus: 'idle',
            // 数据管理（与状态分离）
            uploadData: {
                progress: 0,
                fileName: '',
                errorMessage: '',
            },
        };
    },
    computed: {
        // 状态枚举
        STATUS() {
            return {
                IDLE: 'idle', // 空闲状态（默认上传界面）
                UPLOADING: 'uploading', // 上传中
                SUCCESS: 'success', // 上传成功
                ERROR: 'error', // 上传失败
            };
        },
        // 根据props和状态计算当前应该显示的状态
        displayStatus() {
            // 如果有文件数据，显示成功状态
            if (this.value && this.value[0] && this.value[0].imageUrl) {
                return this.STATUS.SUCCESS;
            }
            // 否则使用当前状态
            return this.currentStatus;
        },
        readSuccess() {
            return this.value && this.value[0] && this.value[0].taxpayerIdNo;
        },
    },
    created() {
        console.log('uploader value', this.value);
    },
    methods: {

        // roo-design Upload 组件的方法
        beforeUpload(file) {
            // 设置上传状态（格式和大小验证由Upload组件内置处理）
            this.currentStatus = this.STATUS.UPLOADING;
            this.uploadData.progress = 0;
            this.uploadData.fileName = file.name;
            this.uploadData.errorMessage = '';

            return true;
        },
        onExceededSize(file, fileList) {
            // 文件超出指定大小限制时的钩子
            toast.warn('单个文件大小不能超过 10MB');
            console.warn('Size exceeded:', file, fileList);
        },
        onUploadProgress(event) {
            // 上传进度回调
            if (event.percent) {
                this.uploadData.progress = Math.round(event.percent);
            }
        },
        onUploadSuccess(response, file, fileList) {
            // 上传成功回调
            console.log('Upload success:', response, file, fileList);

            if (response.code === 0 && response.data) {
                const fileData = {
                    ...response.data,
                    fileName: file.name,
                };

                // 更新数组的第一个元素
                const newValue = [...this.value];
                newValue[0] = fileData;
                this.$emit('input', newValue);

                this.currentStatus = this.STATUS.SUCCESS;

                // 检查ocr是否识别成功
                if (response.data.taxpayerIdNo) {
                    toast.success('上传成功');
                } else {
                    toast.error('营业执照税号读取失败，请重新上传');
                }

            } else {
                // 服务器返回错误
                this.currentStatus = this.STATUS.ERROR;
                this.uploadData.errorMessage = response.msg || '上传失败，请重新尝试';
                toast.error('上传失败');
            }
        },
        onUploadError(error, file, fileList) {
            // 上传失败回调
            console.error('Upload error:', error, file, fileList);
            this.currentStatus = this.STATUS.ERROR;
            this.uploadData.errorMessage = '上传失败，请重新尝试';
        },
        // 取消上传
        cancelUpload() {
            this.currentStatus = this.STATUS.IDLE;
            this.resetUploadData();
        },
        // 重试上传
        retryUpload() {
            this.currentStatus = this.STATUS.IDLE;
            this.uploadData.errorMessage = '';
        },
        // 重新上传文件
        reuploadFile() {
            // 清空数组的第一个元素
            const newValue = [...this.value];
            newValue.splice(0, 1);
            this.$emit('input', newValue);

            this.currentStatus = this.STATUS.IDLE;
            this.resetUploadData();
        },
        // 重置上传状态数据
        resetUploadData() {
            this.uploadData.progress = 0;
            this.uploadData.fileName = '';
            this.uploadData.errorMessage = '';
        },

    },
    watch: {
        value: {
            immediate: true, // 组件创建时立即执行一次
            deep: true, // 深度监听数组内部变化
            handler(newVal) {
                // console.log('value changed:', newVal);
                // 如果外部value被清空，且当前是成功状态，则重置组件状态
                if (!newVal || newVal.length === 0) {
                    this.currentStatus = this.STATUS.IDLE;
                    this.resetUploadData();
                }
            },
        },
    },
};
</script>

<style lang="scss" module>
.image-uploader {
    width: 300px;
    height: 100px;
    border-radius: 2px;

    :global {
        .boo-upload {
            height: 100%;  // 让上传组件撑满容器高度

            .boo-upload-drag {
                height: 100%;  // 让拖拽区域撑满上传组件高度
                display: flex;
                align-items: center;
                justify-content: center;
            }
        }
    }

    &.disabled {
        cursor: not-allowed;
        opacity: 0.7;

        p {
            color: #999;
            font-weight: 400;
        }
    }
}

// 默认上传内容
.upload-content {
    background-color: #F5F6FA;
    height: 100%;
    padding: 16px 48px;
    box-sizing: border-box;
    text-align: center;
    display: flex;
    flex-direction: column;
    justify-content: center;

    p {
        color: #666;
        margin: 2px 0;

        &:first-of-type {
            font-size: 14px;
        }
    }
}

.upload-hint {
    font-size: 12px;
    color: #999 !important;
}

// 上传中状态
.uploading-content {
    background-color: #F5F6FA;
    height: 100%;
    width: 100%;
    padding: 8px 48px;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    justify-content: center;
    text-align: center;

    p {
        color: #333;
        font-size: 12px;
        margin: 2px 0;
    }
}

.uploading-icon {
    color: #1890ff;
    display: block;
    margin: 0 auto;
}

.progress-bar {
    width: 200px;
    height: 6px;
    padding: 2px 0;
    margin: 2px 0;
    background-color: #f0f0f0;
    border-radius: 3px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background-color: #1890ff;
    transition: width 0.3s ease;
}

.cancel-btn {
    background-color: transparent;
    border: none;
    color: #666;
    cursor: pointer;
    font-size: 12px;

    &:hover {
        border-color: #40a9ff;
        color: #40a9ff;
    }
}

// 错误状态
.error-content {
    width: 100%;
    height: 100%;
    background-color: #F5F6FA;
    padding: 8px 48px;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    justify-content: center;
    text-align: center;
}

.retry-btn {
    margin-top: 0;  // 移除顶部间距
    padding: 0;     // 移除内边距
    height: 20px;   // 设置固定高度
    line-height: 20px; // 行高与高度一致
    background: transparent;
    border: none;
    cursor: pointer;
    color: #FF6A39;
    transition: all 0.2s ease;
    white-space: nowrap;
    font-size: 14px;

    &:hover {
        color: #FF8F6D;
    }

    &:active {
        color: #CC5023;
    }

    &:disabled {
        color: #999999;
        cursor: not-allowed;
    }
}

// 上传成功内容
.success-content {
    background-color: #F5F6FA;
    height: 100%;
    width: 100%;
    padding: 8px 0px;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.file-info {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    margin-bottom: 8px;
}

.file-icon {
    margin-right: 8px;
    color: #222222;
    font-size: 16px;
    display: inline-block;
}

.file-name {
    font-size: 14px;
    color: #222222;
    margin-right: 12px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 120px;
}

.upload-info {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    font-size: 14px;
}

.upload-status {
    display: flex;
    flex-direction: row;
    justify-content: center;
}

.status-icon {
    color: #52c41a;
    margin-right: 4px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    &.errorIcon {
        color: #ff4d4f;
    }
}

.status-text {
    color: #52c41a;
    margin-right: 8px;

    &.errorText {
        color: #ff4d4f;
    }
}

.actions {
    display: flex;
    gap: 8px;
}

.reuploadBtn {
    font-size: 14px;
    margin-right:4px;
    cursor: pointer;
    color: #FF6A39;
    transition: all 0.2s ease;
    white-space: nowrap;

    &:hover {
        color: #FF8F6D;
    }

    &:active {
        color: #CC5023;
    }

    &[disabled] {
        color: #999999;
        cursor: not-allowed;
    }
}

.remove-btn {
    color: #ff4d4f;

    &:hover {
        background-color: #fff1f0;
        color: #cf1322;
    }

    &:active {
        background-color: #ffccc7;
    }
}

.uploadIcon {
    color: #222222;
}

.disabled {
    pointer-events: none;
    opacity: 0.7;

    .uploadIcon {
        color: #999;
    }

    :global {
        .boo-upload,
        .boo-upload-drag,
        .boo-upload *,
        .boo-upload-drag * {
            cursor: not-allowed !important;
            pointer-events: all !important;
        }

        .boo-upload-drag:hover {
            border-color: #d9d9d9 !important;
        }
    }

    p {
        color: #999;
        font-weight: 400;
    }
}

</style>
