<template>
    <div :class="$style.kpSignerInfo">
        <div :class="$style.kpSignerStatus">
            <span :class="kpSignerStatusClass">{{ kpSignerStatusText }}</span>
        </div>
        <div v-show="showKpSignerDetails" :class="$style.kpSignerDetails">
            <div :class="$style.kpSignerItem">
                <span :class="$style.kpLabel">签约人:</span>
                <span :class="$style.kpValue">{{ kpSignerName }}</span>
            </div>
            <div :class="$style.kpSignerItem">
                <span :class="$style.kpLabel">签约资质实名认证手机号:</span>
                <span :class="$style.kpValue">{{ kpSignerMobile }}</span>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    name: 'KpSignerInfo',
    props: {
        kpSignerName: String,
        kpSignerMobile: String,
        kpSignerStatus: {
            type: Number,
            default: 0,
        },
    },
    computed: {
        kpSignerStatusText() {
            if (this.kpSignerStatus === 0) {
                return '缺少kp签约人信息，请联系业务经理补充kp签约人信息';
            } else if (this.kpSignerStatus === -1) {
                return 'kp签约人信息有误，请联系业务经理完善kp签约人信息';
            }
            return '即商户入驻时的签约人，您可以在"店铺设置->门店管理->合同协议中心"查看合同签约人信息';
        },
        kpSignerStatusClass() {
            if (this.kpSignerStatus === 0 || this.kpSignerStatus === -1) {
                return this.$style.kpStatusError;
            }
            return this.$style.kpStatusNormal;
        },
        showKpSignerDetails() {
            return this.kpSignerStatus !== 0;
        },
    },
};
</script>

<style lang="scss" module>
.kpSignerInfo {
  padding-top: 7px;
  padding-bottom: 7px;
}
.kpSignerStatus {
  font-size: 14px;
  margin-bottom: 12px;
}
.kpStatusError {
  color: #FF192D;
}
.kpStatusNormal {
  color: #999999;
}
.kpSignerDetails {
  background: #F5F6FA;
  padding: 20px;
  width: 408px;
  height: 88px;
  border-radius: 2px;
  .kpSignerItem {
    margin-bottom: 8px;
    &:last-child {
      margin-bottom: 0;
    }
  }
}
.kpLabel {
  font-size: 14px;
  color: #222222;
}
.kpValue {
  font-size: 14px;
  color: #222222;
  font-weight: 600;
}
</style>
