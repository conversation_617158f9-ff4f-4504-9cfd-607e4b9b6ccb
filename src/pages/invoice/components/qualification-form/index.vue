<template>
    <div>
        <roo-form-item
            key="partnerBName"
            :rules="rules.partnerBName"
            :label="`发票抬头${qualifications.length > 1 ? `${idx + 1}` : ''}`"
            prop="partnerBName"
            required
        >
            <roo-input
                v-model="localFormData.partnerBName"
                :class="$style.input"
                :disabled="disabled"
                placeholder="请输入发票抬头"
                @change="(val) => handleChange(val, idx)"
            />
            <roo-button
                v-if="!isEdit"
                :disabled="disabled"
                @click="handleAddQualification"
            >
                <roo-icon name="plus" />
            </roo-button>
            <roo-button
                v-if="!isEdit"
                :disabled="qualifications.length <= 1 || disabled"
                status="danger"
                @click="handleRemove(idx)"
            >
                <roo-icon name="minus" />
            </roo-button>
        </roo-form-item>
        <roo-form-item
            key="taxpayerIdNo"
            label="纳税人识别号"
            prop="taxpayerIdNo"
            required
        >
            <roo-input
                v-model="localFormData.taxpayerIdNo"
                :class="$style.input"
                disabled
                placeholder="输入发票抬头后自动带出纳税人识别号"
            />
            <div
                v-if="taxpayerIdNoErrors[idx]"
                style="color: red;"
            >
                {{ taxpayerIdNoErrors[idx] }}
            </div>
        </roo-form-item>
        <roo-form-item
            v-if="isDiffFromCustomerNumber"
            key="subpoiBusinessLicenseUrls"
            :rules="rules.subpoiBusinessLicenseUrls"
            prop="subpoiBusinessLicenseUrls"
            label="营业执照"
            required
        >
            <image-uploader
                v-model="localFormData.subpoiBusinessLicenseUrls"
                :disabled="disabled"
            />
            <p
                v-if="localFormData.subpoiBusinessLicenseUrls[0] && localFormData.subpoiBusinessLicenseUrls[0].taxpayerIdNo"
                style="font-size: 14px; color: #999; margin-top: 4px;"
            >
                营业执照税号：{{ localFormData.subpoiBusinessLicenseUrls[0].taxpayerIdNo }}
            </p>
        </roo-form-item>
        <!-- 新增法人信息 -->
        <roo-form-item
            v-if="isDiffFromCustomerNumber"
            key="legalRepName"
            label="法人姓名"
            prop="legalRepName"
            :rules="rules.legalRepName"
            required
        >
            <roo-input
                v-model="localFormData.legalRepName"
                :class="$style.input"
                placeholder="请输入法人姓名"
                :disabled="disabled"
            />
        </roo-form-item>
        <roo-form-item
            v-if="isDiffFromCustomerNumber"
            key="legalRepIdCard"
            label="法人身份证号"
            prop="legalRepIdCard"
            :rules="rules.legalRepIdCard"
            required
        >
            <roo-input
                v-model="localFormData.legalRepIdCard"
                :class="$style.input"
                placeholder="请输入法人身份证号"
                :disabled="disabled"
            />
        </roo-form-item>
        <roo-form-item
            v-if="isDiffFromCustomerNumber"
            key="legalRepPhone"
            label="法人手机号"
            prop="legalRepPhone"
            :rules="rules.legalRepPhone"
            required
        >
            <roo-input
                v-model="localFormData.legalRepPhone"
                :class="$style.input"
                placeholder="请输入法人手机号"
                :disabled="disabled"
            />
        </roo-form-item>
    </div>
</template>

<script>
import { pattern } from '$lib/has-invalid-charactor';
import request from '$invoice/utils/request';
import { debounce } from '$lib/utils';
import toastError from '$lib/toast-error';
import ImageUploader from '../image-uploader';

export default {
    name: 'QualificationForm',
    components: {
        ImageUploader,
    },
    props: {
        formData: {
            type: Object,
            required: true,
        },
        idx: {
            type: Number,
            required: true,
        },
        qualifications: {
            type: Array,
            required: true,
        },
        partnerType: {
            type: Number,
            default: null,
        },
        customerNumber: {
            type: String,
            default: '',
        },
        disabled: {
            type: Boolean,
            default: false,
        },
        isEdit: {
            type: Boolean,
            default: false,
        },
        isMultipleStores: {
            type: Boolean,
            default: false,
        },
        contractId: {
            type: Number,
            default: null,
        },
    },
    data() {
        return {
            localFormData: { ...this.formData },
            taxpayerIdNoErrors: [],
        };
    },
    computed: {
        isDiffFromCustomerNumber() {
            return this.localFormData.taxpayerIdNo !== this.customerNumber;
        },
        rules() {
            return {
                partnerBName: this.getPartnerBNameRules(),
                taxpayerIdNo: [{
                    required: true,
                    message: '请输入纳税人识别号',
                }],
                subpoiBusinessLicenseUrls: [{
                    required: true,
                    message: '请上传营业执照',
                }],
                legalRepName: [{
                    required: true,
                    message: '请输入法人姓名',
                    trigger: 'blur',
                }],
                legalRepIdCard: [{
                    required: true,
                    message: '请输入法人身份证号',
                    trigger: 'blur',
                }, {
                    pattern: /^\d{17}(\d|X|x)$/,
                    message: '请输入正确的身份证号',
                    trigger: 'blur',
                }],
                legalRepPhone: [{
                    required: true,
                    message: '请输入法人手机号',
                    trigger: 'blur',
                }, {
                    pattern: /^1\d{10}$/,
                    message: '请输入11位手机号',
                    trigger: 'blur',
                }],
            };
        },
    },
    watch: {
        formData: {
            handler(newVal) {
                // 使用 Object.keys 遍历对象的所有属性，逐个使用 $set 更新
                Object.keys(newVal).forEach(key => {
                    this.$set(this.localFormData, key, newVal[key]);
                });
            },
            deep: true,
        },
        localFormData: {
            handler(newVal) {
                this.$emit('update', this.idx, { ...newVal });
            },
            deep: true,
        },
    },
    created() {
        this.handleChange = debounce(this.handleChange, 1000);
        if (this.isEdit) {
            // 编辑表单时，默认将返回的表单数据中的税号赋值给营业执照税号字段，后续如果有更新，可覆盖
            this.localFormData.subpoiBusinessLicenseUrls[0].taxpayerIdNo = this.localFormData.taxpayerIdNo;
        }
    },
    methods: {
        // 获取抬头名称的校验规则
        getPartnerBNameRules() {
            const baseRules = [{
                required: true,
                message: '请输入发票抬头',
            }, {
                pattern,
                message: '发票抬头不能有空格和如下特殊字符，如&，？等',
            }];

            // 如果是多门店环境，添加防抖校验
            if (this.isMultipleStores) {
                baseRules.push({
                    validator: debounce(this.validatePartnerName, 800),
                    trigger: 'blur,change',
                });
            }

            return baseRules;
        },

        // 抬头名称验证方法（仅在多门店环境下使用）
        validatePartnerName(rules, value, callback) {
            this.handleCheckName(value).then((res) => {
                const { code, msg } = res.data;
                if (code !== 0) {
                    callback(new Error(msg));
                } else {
                    callback();
                }
            }).catch(() => {
                callback(new Error('网络错误，请重试'));
            });
        },

        // 抬头名称检查方法（仅在多门店环境下使用）
        handleCheckName(val) {
            const params = {
                contractId: this.contractId,
                partnerType: this.partnerType || 103,
                partnerBName: val,
            };
            return request.get('/finance/invoice/api/output/waimai/q/invoiceTitleNameCheck', {
                params,
            });
        },

        handleAddQualification() {
            this.$emit('add-qualification');
        },
        handleRemove(idx) {
            this.$emit('remove-qualification', idx);
        },
        handleChange(keyword, index) {
            if (!keyword) {
                return;
            }

            const params = {
                partnerBName: keyword,
            };

            request.get('/finance/invoice/api/output/waimai/q/queryTaxNoByName', { params }).then((res) => {
                const { code, msg, data } = res.data;
                if (code !== 0 || !data) {
                    const errorMsg = code !== 0
                        ? (msg || '查询纳税人识别号失败')
                        : '未查询到有效纳税人识别号';

                    this.$set(this.taxpayerIdNoErrors, index, errorMsg);
                    this.$set(this.localFormData, 'taxpayerIdNo', '');
                    return;
                }

                this.$set(this.taxpayerIdNoErrors, index, '');
                this.$set(this.localFormData, 'taxpayerIdNo', data);
            }).catch(toastError);
        },
    },
};
</script>

<style lang="scss" module>
.input {
    display: inline-block;
    width: 300px;
    vertical-align: middle;
}
</style>
