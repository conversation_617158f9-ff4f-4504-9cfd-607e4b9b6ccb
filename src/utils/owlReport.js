// import Owl from '@dp/owl';
/**
 * 自定义上报错误
 * @param name 错误标题
 * @param msg 错误信息
 * @param level 错误级别
 */

const Owl = window.Owl;
const owlReport = (name, msg, level = 'info') => {
    try {
        if (Owl) {
            Owl.addError(
                {
                    name,
                    msg,
                },
                {
                    level,
                    combo: false,
                },
            );
        }
    } catch (e) {
        // eslint-disable-next-line no-console
        console.log('OWL 自定义上报异常：', e);
    }
};

export default owlReport;
