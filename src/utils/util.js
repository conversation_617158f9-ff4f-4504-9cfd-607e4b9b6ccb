// 只拷贝arr中的部分字段
export function copyArray(arr) {
    return {
        partnerId: arr.partnerId,
        partnerName: arr.partnerName,
        checkedMoney: arr.checkedMoney,
        checked: arr.checked,
        disabled: arr.disabled,
    };
}
// 深拷贝结算ID列表
export function copySettleList(settleList) {
    const arr = [];
    settleList.forEach(item => {
        const obj = {
            settleId: item.settleId,
            settleName: item.settleName,
            moneyCent: item.moneyCent,
            partnerList: [],
        };
        item.partnerList.forEach(poi => {
            obj.partnerList.push({
                ...poi,
            });
        });
        arr.push(obj);
    });
    return arr;
}
