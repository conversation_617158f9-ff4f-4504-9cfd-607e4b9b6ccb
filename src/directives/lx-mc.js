
/* eslint-disable import/no-unresolved, import/extensions */
import { getCookies } from '$lib/utils';
/* eslint-enable import/no-unresolved, import/extensions */

const LXMCMeta = Symbol('LXMCMeta');

function LXMCHandler(/* event */) {
    const meta = this[LXMCMeta];
    if (meta) {
        const cookies = getCookies();
        const acctId = parseInt(cookies.acctId, 10) || null;
        const wmPoiId = parseInt(cookies.wmPoiId, 10) || null;
        LXAnalytics('moduleClick', meta.bid, {
            poi_id: wmPoiId,
            custom: {
                acctId,
                wmPoiId: wmPoiId || -1,
            },
        }, meta.options);
    }
}

function updateMeta(el, binding) {
    if (!binding.value) return;

    const { bid, custom = null, options = null } = binding.value;

    // bid 赋值给 dataset.bidMc 便于观察
    el.dataset.bidMc = bid;

    // 事件元数据
    el[LXMCMeta] = {
        bid,
        custom,
        options,
    };
}

/**
 * Usage:
 *
 * <div v-lx-mc:b_waimai_e_tsasdi8l_mc>
 *   some module
 * </div>
 */
export default {
    name: 'lx-mc',
    bind(el, binding) {
        updateMeta(el, binding);
        el.addEventListener('click', LXMCHandler);
    },
    update(el, binding) {
        updateMeta(el, binding);
    },
    unbind(el) {
        // clean up
        delete el.dataset.bidMc;
        delete el[LXMCMeta];
        el.removeEventListener('click', LXMCHandler);
    },
};
