import { jumpTo } from '$lib/bridge';
import { parse as UrlParse } from '@utiljs/param';

function getValByUrlQuery(key) {
    const urlParams = UrlParse(window.location.href);
    return urlParams[key] || '';
}


export function getCookies() {
    const ret = {};
    const pairs = document.cookie.split(';').map((x) => x.trim());
    pairs.forEach((str) => {
        try {
            const idx = str.indexOf('=');
            const key = decodeURIComponent(str.slice(0, idx));
            const val = decodeURIComponent(str.slice(idx + 1));
            ret[key] = val;
        } catch (e) {
            console.warn('cookie 解析异常 error', str, e);
        }
    });
    return ret;
}

export function getCookieValue(key) {
    return getCookies()[key] || getValByUrlQuery(key); // 迁移Webstatic之后，之前html的set-cookie没有了，需要把一些只从url里面取出来作为兜底
}

/**
 * 设置 Cookie
 * @param {String} key Cookie 的 key 值
 * @param {String} value Cookie 的 Value 值
 * @param {Number} exp Cookie 的过期时间，不传递则设置当前会话有效
 *            示例：一天过期 1 * 24 * 60 * 60 * 1000
 *                 5 分钟过期 5 * 60 * 60 * 1000
 */
export function setCookie(key, value, exp) {
    const env = {
        production: 'prod',
        stage: 'staging',
        test: 'test',
        dev: 'dev',
    }[process.env.DEPLOY_ENV];
    const d = new Date();
    d.setTime(d.getTime() + exp);
    const expires = `expires=${d.toGMTString()}`;
    if (env === 'prod' || env === 'staging') {
        document.cookie = `${key}=${value}; ${expires}; Secure; SameSite=none`;
    } else {
        document.cookie = `${key}=${value}; ${expires};`;
    }
}

/**
 * 删除 Cookie
 * @param {String}} key Cookie 的 key 值
 */
export function delCookie(key) {
    const exp = new Date();
    exp.setTime(exp.getTime() - 1);
    const cval = getCookieValue(key);
    if (cval != null) {
        document.cookie = `${key}=${cval};expires=${exp.toGMTString()}`;
    }
}

export function formatNumberThousand(num) {
    let str = `${num}`;
    let sign = '';
    if (str[0] === '+' || str[0] === '-') {
        /* eslint-disable prefer-destructuring */
        sign = str[0];
        /* eslint-enable prefer-destructuring */
        str = str.slice(1);
    }

    let idx = str.lastIndexOf('.');
    const int = idx > -1 ? str.slice(0, idx) : str;
    const dec = idx > -1 ? str.slice(idx) : '';

    idx = int.length % 3;
    const arr = idx > 0 ? [str.slice(0, idx)] : [];

    for (let i = idx; i < int.length; i += 3) {
        arr.push(str.slice(i, i + 3));
    }

    return `${sign}${arr.join(',')}${dec}`;
}

export function isDef(val) {
    return val !== null && val !== undefined;
}

export function pick(obj, arr, skipUndef) {
    const ret = {};
    for (let i = 0; i < arr.length; ++i) {
        const key = arr[i];
        const val = obj[key];
        if (obj.hasOwnProperty(key)) {
            if (!skipUndef || isDef(val)) {
                ret[key] = val;
            }
        }
    }
    return ret;
}

export function emptyArray() {
    return [];
}

export function noop() {
    // noop
}

export function debounce(func, wait = 300, leading = false) {
    if (typeof func !== 'function') {
        throw new TypeError('Expected a function');
    }

    let lastCtx = null;
    let lastArgs = [];
    let lastResult;
    let lastCallTime = -1;
    let timer = 0;

    if (leading) {
        return function debounced(...args) {
            const now = Date.now();
            // leading call
            if (lastCallTime < 0 || now - lastCallTime >= wait) {
                lastResult = func.apply(this, args);
            }
            lastCallTime = now;
            return lastResult;
        };
    }

    function invokeFunc() {
        lastResult = func.apply(lastCtx, lastArgs);
        lastCtx = null;
        lastArgs = [];
        timer = 0;
    }
    return function debounced(...args) {
        if (timer) clearTimeout(timer);
        lastCtx = this;
        lastArgs = args;
        timer = setTimeout(invokeFunc, wait);
        return lastResult;
    };
}

export function getQueryString(params) {
    if (!params) {
        return '';
    }
    const pairs = [];
    Object.keys(params).forEach((key) => {
        const k = encodeURIComponent(key);
        const v = encodeURIComponent(params[key]);
        pairs.push(`${k}=${v}`);
    });
    return pairs.join('&');
}

export function navigateTo(path, nojump = false) {
    const href = path[0] === '/' ? `${window.location.origin}${path}` : path;
    jumpTo(href, nojump);
}
export const getHost = () => {
    const env = process.env.DEPLOY_ENV;
    const EnvMap = {
        test: 'http://paymp.pay.test.sankuai.com',
        prod: 'https://paymp.meituan.com',
        stage: 'https://paymp.pay.st.sankuai.com',
    };
    return EnvMap[env] ? EnvMap[env] : EnvMap.prod;
};
export const getHosts = () => {
    const env = process.env.DEPLOY_ENV;
    const EnvMap = {
        test: 'http://e.b.waimai.test.sankuai.com',
        prod: 'https://waimaie.meituan.com',
        stage: 'http://e.waimai.st.sankuai.com',
    };
    return EnvMap[env] ? EnvMap[env] : EnvMap.prod;
};
/**
 * 合规检查专用方法，用于获取通参
 */
export function getCommonParams() {
    const wmPoiId = getCookieValue('wmPoiId');
    const acctId = getCookieValue('acctId');
    const token = getCookieValue('token');
    return { wmPoiId, acctId, token };
}
