
import { formatNumberThousand } from './utils';

export function formatCent(num) {
    const str = (num / 100).toFixed(2);
    return formatNumberThousand(str === '-0.00' ? '0.00' : str);
}

export function formatMilli(num) {
    const str = (num / 10000).toFixed(2);
    return formatNumberThousand(str === '-0.00' ? '0.00' : str);
}

export function formatMoney(money, hasPlus = false, hasUnit = true) {
    let result = '';
    const unit = hasUnit ? '￥' : '';
    if (money > 0) {
        result = `${unit}${formatNumberThousand((money / 100).toFixed(2))}`;
        if (hasPlus) {
            result = `+${result}`;
        }
    } else if (money === 0) {
        result = `${unit}0.00`;
    } else {
        result = `-${unit}${formatNumberThousand((-money / 100).toFixed(2))}`;
    }
    return result;
}

/**
 * 处理毫值,如果num>0,则舍去最后的毫值,如果num<0,则将
 * @param num
 * @returns {number|*}
 */
export function formatMilliToZero(num, threshold = 100) {
    if (num < threshold && num > -threshold) return 0;
    return num;
}
