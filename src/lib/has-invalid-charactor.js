// 发票票面信息特殊字符校验

export const spchRegExp = /[&[\]∏§℅€℃£℉№℡‰$¥¢∮※•？?<>￥☞➡➕'【】\s\r\f\n]/;

export const pattern = /^[^&[\]∏§℅€℃£℉№℡‰$¥¢∮※•？?<>￥☞➡➕'【】\s\r\f\n]*$/;

// eslint-disable-next-line no-irregular-whitespace
export const bankPattern = /^[^&∏§℅€℃£℉№℡‰$¥¢∮※•？<　>￥☞➡➕' \s\t\r\f\n]*$/;

// eslint-disable-next-line no-irregular-whitespace
export const addressPhonePattern = /^[^∏§℅€℃£℉№℡‰$¥¢∮※•？<　>￥☞➡➕' \s\t\r\f\n]*$/;

export default function hasInvalidCharactor(str) {
    return spchRegExp.test(str);
}
