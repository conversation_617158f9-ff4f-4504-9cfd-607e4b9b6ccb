
import axios from 'axios';
import { toast } from '@roo/roo-vue';

export default function toastError(err) {
    if (axios.isCancel(err)) {
        return;
    }
    if (process.env.NODE_ENV !== 'production') {
        // eslint-disable-next-line no-console
        console.error(err);
    }
    if (err.response) {
        toast.error(`请求失败#${err.response.status}`);
    } else if (err.request) {
        toast.error('网络请求失败，请检查网络连接或稍后重试');
    } else {
        toast.error(err.message || '未知错误');
    }
}
