
/**
 * 桥调用方法封装
 * https://km.sankuai.com/page/36700787
 */

function invoke(method, ...args) {
    const message = JSON.stringify({
        method,
        args,
    });
    window.parent.postMessage(message, '*');
}

const envOrigin = {
    production: 'https://waimaieapp.meituan.com',
    stage: 'https://proxy.waimai.st.sankuai.com',
    test: 'http://e.platform.proxy.b.waimai.test.sankuai.com',
    dev: 'http://e.platform.proxy.b.waimai.test.sankuai.com',
}[process.env.DEPLOY_ENV];

export function selectPoi(wmPoiId, wmPoiName, jumpUrl) {
    const param = {
        wmPoiId,
        wmPoiName,
    };
    // 要跳转的 url ，不指定则跳转当前链接
    // 不指定就不传，避免被序列化传递
    if (jumpUrl) {
        param.jumpUrl = `${envOrigin}${jumpUrl}`;
    }
    invoke('selectWmPoi', param);
}

export function jumpTo(href, nojump = false) {
    invoke('jumpTo', { href, nojump });
}
