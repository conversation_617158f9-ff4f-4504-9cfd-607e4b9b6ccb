export const InvoiceTypeEnum = {
    VATSpecial: 1,
    VATNormal: 2,
    Electron: 3,
    PublicActivityPaper: 4,
    PublicActivityElectron: 5,
};

export const NormalInvoiceNameEnum = {
    [InvoiceTypeEnum.VATSpecial]: '增值税专用发票（纸质）',
    [InvoiceTypeEnum.VATNormal]: '增值税普通发票',
    [InvoiceTypeEnum.Electron]: '增值税普通发票（电子）',
};

export const PublicActivityInvoiceNameEnum = {
    [InvoiceTypeEnum.PublicActivityElectron]: '公益事业捐赠统一票据（电子）',
    [InvoiceTypeEnum.PublicActivityPaper]: '公益事业捐赠统一票据（纸质）',
};

export const InvoiceNameEnum = {
    ...NormalInvoiceNameEnum,
    ...PublicActivityInvoiceNameEnum,
};

export const NormalInvoiceTypeList = Object.keys(NormalInvoiceNameEnum).map(invoice => ({
    value: +invoice,
    label: NormalInvoiceNameEnum[invoice],
}));

export const ShudianNormalInvoiceTypeList = [
    {
        value: 1,
        label: '专票',
        tips: '为响应国税总局全面推广数电发票的工作要求，开票主体可能开具数电专票或纸质专票。数电发票的法律效力、基本用途等与纸质发票相同。',
    },
    {
        value: 3,
        label: '普票',
        tips: '为响应国税总局全面推广数电发票的工作要求，开票主体可能开具数电普票或电子普票。数电发票的法律效力、基本用途等与电子发票相同。',
    },
];

export const PublicActivityInvoiceTypeList = Object.keys(PublicActivityInvoiceNameEnum).map(invoice => ({
    value: +invoice,
    label: PublicActivityInvoiceNameEnum[invoice],
}));

export const BaseFeeTypeEnum = {
    // 青山公益
    PublicActivity: 15,
    Children: 38,
    Natural: 39,
};


export const InvoiceStatusNameList = [
    '提交中',
    '开票中',
    '已开票',
    '已取消',
    '已邮寄',
    '已作废',
    '作废中',
    '部分作废',
    '已提交',
];

const AllShowInvoiceStatusList = InvoiceStatusNameList.map((item, index) => ({
    value: index,
    label: item,
}));

AllShowInvoiceStatusList.shift();

export const InvoiceStatusList = AllShowInvoiceStatusList;

export const GatherTypeEnum = {
    settle: 1, // 按照结算设置结算
    month: 2, // 按月结算
};

export const QueryButtonType = {
    // 日期
    Date: 0,
    // 结算ID
    Settle: 1,
    // 门店
    Poi: 2,
    // 加载更多
    LoadMore: 3,
};
