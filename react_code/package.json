{"name": "test", "version": "0.1.0", "description": "react app", "author": "", "main": "./src/index.tsx", "scripts": {"build": "bash ./pre-deploy", "dev": "nine dev --mock", "start": "nine build && node server.js", "dev-no-mock": "nine dev", "lint": "npx precommit-eslint", "analyze": "nine build --analyze"}, "overrides": {"@mtfe/sso-client": "3.3.0"}, "license": "ISC", "dependencies": {"moment": "^2.29.4", "@babel/polyfill": "^7.8.7", "@nine/nine-preset-wm-b": "^0.0.6", "@rematch/core": "^1.3.0", "@roo/roo": "^1.11.6-beta.4", "@roo/roo-b": "^0.0.6", "@utiljs/cookie": "^0.1.2", "@utiljs/date": "^0.2.3", "@utiljs/math": "^0.1.7", "@utiljs/param": "^0.6.5", "axios": "^0.18.1", "classnames": "^2.2.6", "react": "^16.8.6", "react-dom": "^16.8.6", "react-redux": "5.1.0", "react-router-dom": "^5.1.2", "redux": "^4.0.1", "tslib": "^2.4.0", "query-string": "7.1.1"}, "devDependencies": {"express": "^4.18.2", "@mfe/precommit-eslint": "2.1.0", "@roo/babel-roo-import-plugin": "0.0.11", "@types/node": "^10.12.1", "@types/react": "^16.9.35", "@types/react-dom": "^16.9.8", "@types/react-redux": "^6.0.14", "@types/react-router-dom": "^5.1.5", "@types/webpack-env": "^1.14.1", "@typescript-eslint/eslint-plugin": "2.11.0", "@typescript-eslint/parser": "2.11.0", "@wmfe/commitmsg-lint": "^1.0.15", "@wmfe/eslint-config-mt": "0.3.5", "argv-parser": "^0.1.4", "babel-eslint": "10.1.0", "babel-plugin-const-enum": "^1.0.1", "eslint": "7.11.0", "eslint-config-airbnb": "18.0.1", "eslint-import-resolver-alias": "^1.1.2", "eslint-plugin-import": "2.22.1", "eslint-plugin-jsx-a11y": "6.2.3", "eslint-plugin-react": "7.16.0", "typescript": "4.0.3", "webpack-bundle-analyzer": "^3.7.0"}, "repository": {}}