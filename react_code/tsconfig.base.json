{
    "compilerOptions": {
      "target": "es5", // 指定 ECMAScript 版本
      "module": "ESNext", // 指定模块代码生成，"es2015" for tree-shaking
      "lib": [
        "dom",
        "dom.iterable",
        "esnext"
      ], // 要包含在编译中的依赖库文件列表
      "allowJs": true, // 允许编译 JavaScript 文件
      "skipLibCheck": true, // 跳过所有声明文件的类型检查
      "esModuleInterop": true, // 禁用命名空间引用 (import * as fs from "fs") 启用 CJS/AMD/UMD 风格引用 (import fs from "fs")
      "allowSyntheticDefaultImports": true, // 允许从没有默认导出的模块进行默认导入
      "strict": true, // 启用所有严格类型检查选项
      "noImplicitReturns": true, // 不允许显式的Return
      "forceConsistentCasingInFileNames": true, // 不允许对同一个文件使用不一致格式的引用
      "moduleResolution": "node", // 使用 Node.js 风格解析模块
      "resolveJsonModule": true, // 允许使用 .json 扩展名导入的模块
      "noEmit": true, // 不输出(意思是不编译代码，只执行类型检查)
      "jsx": "react", // 在.tsx文件中支持JSX
      "sourceMap": true, // 生成相应的.map文件
      "noUnusedLocals": true, // 报告未使用的本地变量的错误
      "noUnusedParameters": true, // 报告未使用参数的错误
      "experimentalDecorators": true, // 启用对ES装饰器的实验性支持
      "incremental": true, // 通过从以前的编译中读取/写入信息到磁盘上的文件来启用增量编译
      "importHelpers": true, // 从 tslib 导入辅助工具函数，而不是每个文件单独引入
      "removeComments": true, // 删除所有注释，除了以 /!*开头的版权信息
      "rootDir": "./",                          /* 指定输入文件的根目录，仅用来控制输出的目录结构 --outDir */
      "baseUrl": "./",                          /* 解析非相对模块名的基准目录 */
      "paths": {                            /* 模块名到基于 baseUrl 的路径映射的列表 */
        "@/*": ["src/*"],
        "@components/*": ["src/components/*"],
        "@utils/*": ["src/utils/*"],
        "@assets/*": ["src/assets/*"]
       },
       "typeRoots": [ // 要包含的类型声明文件路径列表
         "./node_modules/@types",
         "./types"
       ],
           "types": [
         "node",
         "webpack-env"
       ],
    },
    "include": [ // TypeScript文件应该进行类型检查
      "src/**/*"
    ],
    "exclude": [ // 不进行类型检查的文件
      "node_modules",
      "build"
    ]
  }