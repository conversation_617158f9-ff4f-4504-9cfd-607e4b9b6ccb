#!/bin/bash
set -ex

#检测系统信息
echo "PWD:" $(pwd)
echo "system version: " $(cat /proc/version)
echo "who am i: " $(whoami)

# if command -v node >/dev/null 2>&1 && [[ $(node -v) == 'v19.5.0' ]]; then
#   echo '【node 检测通过】'
# else
#   if command -v nvm >/dev/null 2>&1; then
#     echo '【nvm 检测通过】'
#   else
#     echo '【安装 nvm】'
#     curl -L -o- http://build.sankuai.com/nvm/install | bash
#     source ~/.bashrc
#   fi
#   echo '【安装 node】'
#   NVM_NODEJS_ORG_MIRROR=http://npm.sankuai.com/mirrors/node

#   nvm unalias default
#   nvm install v19.5.0
#   nvm use v19.5.0
# fi

#检测node版本
echo "node version: " $(node -v)
echo "npm version: " $(npm -v)

#安装nine相关配置
npm install -g @nine/nine-cli --registry=http://r.npm.sankuai.com

echo "start mkdir .nine"
proj=$(pwd)
cd
if [ -d ".nine" ]; then
  rm -rf ".nine"
  echo "Folder '.nine' and its contents have been deleted."
else
  echo "end mkdir .nine"
fi

mkdir .nine
cd .nine
touch .ninerc.yml
nine config set registry http://r.npm.sankuai.com

export SASS_BINARY_SITE=https://npm.taobao.org/mirrors/node-sass/
export NPM_REGISTRY=http://r.npm.sankuai.com
export NODE_DIST=http://nodejs.mirrors.mx.sankuai.com/dist/

cd $proj

# 安装依赖
npm install --registry=$NPM_REGISTRY --disturl=$NODE_DIST --userconfig=$HOME/.mnpmrc --cache=$HOME/.cache/mnpm --legacy-peer-deps

npm ls @mtfe/sso-client

#进行打包
echo "start build"

# Enable legacy OpenSSL provider
# export NODE_OPTIONS=--openssl-legacy-provider
nine build --upload