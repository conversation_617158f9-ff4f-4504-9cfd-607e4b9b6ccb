<% var customConfig=htmlWebpackPlugin.options; %>
  <!DOCTYPE html>
  <html>

  <head>
    <meta charset="utf-8">
    <% if (customConfig.appnm&&customConfig.category) {%>
      <!-- 灵犀 start-->
      <meta name="lx:category" content="<%= customConfig.category %>">
      <meta name="lx:appnm" content="<%= customConfig.appnm %>">
      <link rel="dns-prefetch" href="//analytics.meituan.net" />
      <!-- 灵犀 end-->
      <% } %>
        <title>
          <%= customConfig.title %>
        </title>
        <!-- Cat 预采集模块 start -->
        <script>
          "use strict"; !function (u, d) { var t = "owl", e = "_Owl_", n = "Owl", r = "start", c = "error", p = "on" + c, f = u[p], h = "addEventListener", l = "attachEvent", v = "isReady", b = "dataSet"; u[t] = u[t] || function () { try { u[t].q = u[t].q || []; var e = [].slice.call(arguments); e[0] === r ? u[n] && u[n][r] ? u[n][r](e[1]) : u[t].q.unshift(e) : u[t].q.push(e) } catch (e) { } }, u[e] = u[e] || { preTasks: [], pageData: [], use: function (e, t) { this[v] ? u[n][e](t) : this.preTasks.push({ api: e, data: [t] }) }, run: function (t) { if (!(t = this).runned) { t.runned = !0, t[b] = [], u[p] = function () { t[v] || t[b].push({ type: "jsError", data: arguments }), f && f.apply(u, arguments) }, u[h] && u[h]("unhandledrejection", function (e) { t[v] || t[b].push({ type: "jsError", data: [e] }) }); var e = function (e) { !t[v] && e && t[b].push({ type: "resError", data: [e] }) }; u[h] ? u[h](c, e, !0) : u[l] && u[l](p, e); var n = "MutationObserver", r = u[n] || u["WebKit" + n] || u["Moz" + n], a = u.performance || u.WebKitPerformance, s = "disableMutaObserver"; if (r && a && a.now) try { var i = -1, o = u.navigator.userAgent; -1 < o.indexOf("compatible") && -1 < o.indexOf("MSIE") ? (new RegExp("MSIE (\\d+\\.\\d+);").test(o), i = parseFloat(RegExp.$1)) : -1 < o.indexOf("Trident") && -1 < o.indexOf("rv:11.0") && (i = 11), -1 !== i && i <= 11 ? t[s] = !0 : (t.observer = new r(function (e) { t.pageData.push({ mutations: e, startTime: a.now() }) })).observe(d, { childList: !0, subtree: !0 }) } catch (e) { } else t[s] = !0 } } }, u[e].runned || u[e].run() }(window, document);
        </script>
        <!-- Cat End -->
        <% if (customConfig.appnm&&customConfig.category) {%>
          <!-- 灵犀 start-->
          <script type="text/javascript">
            !(function (win, doc, ns) {
              var cacheFunName = '_MeiTuanALogObject';
              win[cacheFunName] = ns;
              if (!win[ns]) {
                var _LX = function () {
                  _LX.q.push(arguments);
                  return _LX;
                };
                _LX.q = _LX.q || [];
                _LX.l = +new Date();
                win[ns] = _LX;
              }
            })(window, document, 'LXAnalytics');
          </script>
          <!-- 灵犀 end-->
          <% } %>
            <% if (customConfig.extra&&customConfig.extra.css) {%>
              <!--Extra css start -->
              <% for(var i=0;i < customConfig.extra.css.length;i++){ %>
                <link rel="stylesheet" href="<%= customConfig.extra.css[i] %>" />
                <% } %>
                  <!-- Extra css end -->
                  <% } %>
                    <% if (customConfig.iGateAppKey) {%>
                      <!--iGate start -->
                      <script>
                        (function () { window.igate = { set appName(c) { console.log(c); var a = void 0, b = new Date; a || (a = 360); b.setDate(b.getDate() + a); document.cookie = "igateApp=" + escape(c) + (null == a ? "" : ";expires=" + b.toGMTString()) + ";path=/" } } })();
                        igate.appName = '<%= customConfig.iGateAppKey %>'
                      </script>
                      <!-- iGate End -->
                      <% } %>
  </head>

  <body>
    <div id="root"></div>
    <!-- owl CDN方式引入完整的采集上报模块 -->
    <script crossorigin="anonymous"
      src="https://s3.meituan.net/mnpm-cdn/@mtfe-mt-apm-web-1.12.1/owl_1.12.1.min.js"></script>
      <script src="https://appsec-mobile.meituan.com/h5guard/H5guard.js"></script>
      <script>
        if(window.H5guard){
             H5guard.init({
                 xhrHook: true,
                 fetchHook: true,
             });
         }
      </script>

    <script>
      try {
        const devMode = '<%= process.env.DEPLOY_ENV %>' !== 'production';
        window.owl && window.owl('start', {
          project: 'com.sankuai.wmfinance.common.financeinfo',
          devMode,
          pageUrl: location.origin + location.pathname + location.hash.split('?')[0],
          page: {
            fstPerfAnalysis: true,
          },
          autoCatch: {
            fetch: true, // 自动采集 fetch
          },
          page: {
            fstPerfAnalysis: true, // 开启首屏性能指标
            logSlowView: true, // 采集上报慢访问的个案数据
          },
          resource: {
            enableStatusCheck: true, // 区分 http 响应码
          },
          error: {
            formatUnhandledRejection: true, // 区分 UnhandledRejection 类型
          },
          logan: {
            enable: true, // 开启 logan 日志
            version: '2.1.5', // 最新版本
            config: {
              devMode,
            },
          },
          SPA: { // 将监听 History pushState 和 replaceState 方法的调用以实现路由变化的监听。
            autoPV: true,
            getFST: true,
          },
          enableLogTrace: true, // 开启 TraceID 上报，后端服务需要接入 MTrac e与 LogCenter。
          // 过滤策略
          ignoreList: {
            // 过滤自动采集和手动 addError 上报的异常信息, 异常名称以元素字符串为开头则忽略
            js: ["ResizeObserver loop"],
            // 忽略 Owl 自动采集到的 ajax 请求
            ajax: [
              "https?://dreport.meituan.net",
            ],
            // 忽略 Owl 自动采集到的静态资源请求, 默认全部采集
            resource: [],
            // 过滤 Script error(来自跨域 JS 的错误), 默认true
            noScriptError: true,
          },
          onErrorPush: function (modal) {
            // 过滤非js错误导致的错误
            var _allErrorType = ['EvalError', 'RangeError', 'ReferenceError', 'SyntaxError', 'TypeError', 'URIError', 'AggregateError', 'Error']
            if (modal && modal.category === "jsError" && modal.sec_category.indexOf("unhandledrejection") > -1) {
              var _errorMatch = modal.content && modal.content.toString().match(/[a-zA-Z]+/)
              var _errorType = _errorMatch ? _errorMatch[0] : '';
              // 非js错误全部改成warn
              if (_allErrorType.indexOf(_errorType) < 0) {
                modal.level = 'warn'
              }
            }
            return modal
          },
          onBatchPush: function (m) {
            // 302转换为warn
            // 过滤掉因为页面跳转导致接口被取消的报错
            if (m && m.logContent && m.logContent.indexOf('from: xhr') > -1) return false;
            return true;
          },
        });
      } catch (error) { }
    </script>
    <% if (customConfig.appnm&&customConfig.category) {%>
      <!-- 灵犀统计sdk -->
      <script src="//lx.meituan.net/lx.js" type="text/javascript" charset="utf-8" async defer></script>
      <!-- 灵犀统计sdk -->
      <% } %>
        <% if (customConfig.extra&&customConfig.extra.js) {%>
          <% for(var i=0;i < customConfig.extra.js.length;i++){ %>
            <script src="<%= customConfig.extra.js[i] %>"></script>
            <% } %>
              <% } %>
  </body>

  </html>