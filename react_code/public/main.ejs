<% var customConfig=htmlWebpackPlugin.options; %>
  <!DOCTYPE html>
  <html>

  <head>
    <meta charset="utf-8">
    <% if (customConfig.appnm&&customConfig.category) {%>
      <!-- 灵犀 start-->
      <meta name="lx:category" content="<%= customConfig.category %>">
      <meta name="lx:appnm" content="<%= customConfig.appnm %>">
      <link rel="dns-prefetch" href="//analytics.meituan.net" />
      <!-- 灵犀 end-->
      <% } %>
        <title>
          <%= customConfig.title %>
        </title>
        <!-- Cat 预采集模块 start -->
        <script type="text/javascript">
          "use strict"; !function () { var e = 0 < arguments.length && void 0 !== arguments[0] ? arguments[0] : "_Owl_", a = window; a[e] || (a[e] = { isRunning: !1, isReady: !1, preTasks: [], dataSet: [], pageData: [], disableMutaObserver: !1, observer: null, use: function (e, t) { this.isReady && a.Owl && a.Owl[e](t), this.preTasks.push({ api: e, data: [t] }) }, add: function (e) { this.dataSet.push(e) }, run: function () { var t = this; if (!this.isRunning) { this.isRunning = !0; var e = a.onerror; a.onerror = function () { this.isReady || this.add({ type: "jsError", data: arguments }), e && e.apply(a, arguments) }.bind(this), (a.addEventListener || a.attachEvent)("error", function (e) { t.isReady || t.add({ type: "resError", data: [e] }) }, !0); var i = window.MutationObserver || window.WebKitMutationObserver || window.MozMutationObserver, r = window.performance || window.WebKitPerformance; if (i && r) { var n = -1, s = window.navigator.userAgent; if (-1 < s.indexOf("compatible") && -1 < s.indexOf("MSIE") ? (new RegExp("MSIE (\\d+\\.\\d+);").test(s), n = parseFloat(RegExp.$1)) : -1 < s.indexOf("Trident") && -1 < s.indexOf("rv:11.0") && (n = 11), -1 !== n && n <= 11) return void (this.disableMutaObserver = !0); try { this.observer = new i(function (e) { t.pageData.push({ mutations: e, startTime: r.now() }) }), this.observer.observe(document, { childList: !0, subtree: !0 }) } catch (e) { console.log("mutationObserver err") } } else this.disableMutaObserver = !0 } } }, a[e].run()) }();
        </script>
        <!-- 加载先富菜单 -->
        <script src="//mss.sankuai.com/v1/mss_c4375b35f5cb4e678b5b55a48c40cf9d/waimai-mfe/bundle.js"
          type="text/javascript" charset="utf-8"></script>
        <!-- Cat End -->
        <% if (customConfig.appnm&&customConfig.category) {%>
          <!-- 灵犀 start-->
          <script type="text/javascript">
            !(function (win, doc, ns) {
              var cacheFunName = '_MeiTuanALogObject';
              win[cacheFunName] = ns;
              if (!win[ns]) {
                var _LX = function () {
                  _LX.q.push(arguments);
                  return _LX;
                };
                _LX.q = _LX.q || [];
                _LX.l = +new Date();
                win[ns] = _LX;
              }
            })(window, document, 'LXAnalytics');
          </script>
          <!-- 灵犀 end-->
          <% } %>
            <% if (customConfig.extra&&customConfig.extra.css) {%>
              <!--Extra css start -->
              <% for(var i=0;i < customConfig.extra.css.length;i++){ %>
                <link rel="stylesheet" href="<%= customConfig.extra.css[i] %>" />
                <% } %>
                  <!-- Extra css end -->
                  <% } %>
                    <% if (customConfig.iGateAppKey) {%>
                      <!--iGate start -->
                      <script>
                        (function () { window.igate = { set appName(c) { console.log(c); var a = void 0, b = new Date; a || (a = 360); b.setDate(b.getDate() + a); document.cookie = "igateApp=" + escape(c) + (null == a ? "" : ";expires=" + b.toGMTString()) + ";path=/" } } })();
                        igate.appName = '<%= customConfig.iGateAppKey %>'
                      </script>
                      <!-- iGate End -->
                      <% } %>
  </head>

  <body>
    <div class="header"></div>
    <div class="page-sidebar"></div>
    <div id="main-container">
      <div id="root"></div>
    </div>
    <!-- owl CDN方式引入完整的采集上报模块 -->
    <script crossorigin="anonymous" src="//www.dpfile.com/app/owl/static/owl_latest.js"></script>
    <script src="https://appsec-mobile.meituan.com/h5guard/H5guard.js"></script>
    <script>
      if(window.H5guard){
           H5guard.init({
               xhrHook: true,
               fetchHook: true,
           });
       }
    </script>
    <script>
      try {
        const devMode = '<%= process.env.DEPLOY_ENV %>' !== 'production';
        Owl.start({
          project: 'com.sankuai.wmfinance.xianfu.invoice',
          devMode: devMode,
          page: {
            fstPerfAnalysis: true,
          },
          autoCatch: {
            fetch: true, // 自动采集 fetch
          },
          page: {
            fstPerfAnalysis: true, // 开启首屏性能指标
            logSlowView: true, // 采集上报慢访问的个案数据
          },
          resource: {
            enableStatusCheck: true, // 区分 http 响应码
          },
          error: {
            formatUnhandledRejection: true, // 区分 UnhandledRejection 类型
          },
          logan: {
            enable: true, // 开启 logan 日志
            version: '2.1.5', // 最新版本
            config: {
              devMode,
            },
          },
          SPA: { // 将监听 History pushState 和 replaceState 方法的调用以实现路由变化的监听。
            autoPV: true,
            getFST: true,
          },
          enableLogTrace: true, // 开启 TraceID 上报，后端服务需要接入 MTrac e与 LogCenter。
          // 过滤策略
          ignoreList: {
            // 过滤自动采集和手动 addError 上报的异常信息, 异常名称以元素字符串为开头则忽略
            js: ["ResizeObserver loop"],
            // 忽略 Owl 自动采集到的 ajax 请求
            ajax: [
              "https?://dreport.meituan.net",
            ],
            // 忽略 Owl 自动采集到的静态资源请求, 默认全部采集
            resource: [],
            // 过滤 Script error(来自跨域 JS 的错误), 默认true
            noScriptError: true,
          },
          onErrorPush: function (modal) {
            // 过滤非js错误导致的错误
            var _allErrorType = ['EvalError', 'RangeError', 'ReferenceError', 'SyntaxError', 'TypeError', 'URIError', 'AggregateError', 'Error']
            if (modal && modal.category === "jsError" && modal.sec_category.indexOf("unhandledrejection") > -1) {
              var _errorMatch = modal.content && modal.content.toString().match(/[a-zA-Z]+/)
              var _errorType = _errorMatch ? _errorMatch[0] : '';
              // 非js错误全部改成warn
              if (_allErrorType.indexOf(_errorType) < 0) {
                modal.level = 'warn'
              }
            }
            if (modal.content && modal.content.toString().indexOf('__requestError__') > -1) {
              modal.level = 'warn'
            }
            return modal
          },
          onBatchPush: function (m) {
            // 302转换为warn
            // 过滤掉因为页面跳转导致接口被取消的报错
            if (m && m.logContent && m.logContent.indexOf('from: xhr') > -1) return false;
            return true;
          }
        });
      } catch (error) { }
    </script>
    <% if (customConfig.appnm&&customConfig.category) {%>
      <!-- 灵犀统计sdk -->
      <script src="//lx.meituan.net/lx.js" type="text/javascript" charset="utf-8" async defer></script>
      <!-- 灵犀统计sdk -->
      <% } %>
        <% if (customConfig.extra&&customConfig.extra.js) {%>
          <% for(var i=0;i < customConfig.extra.js.length;i++){ %>
            <script src="<%= customConfig.extra.js[i] %>"></script>
            <% } %>
              <% } %>
  </body>

  </html>