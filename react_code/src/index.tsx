import '@babel/polyfill';
import React from 'react';
import ReactDOM from 'react-dom';
import ComParams from '@/utils/comParam';
import Router from './router';
import './assets/styles/index.scss';

// 发票录入页面入口文件

// 初始化通参
ComParams.init();

ReactDOM.render(
    <Router />,
    document.getElementById('root'),
);

if (process.env.NODE_ENV === 'development') {
    if ((module as any).hot) {
        (module as any).hot.accept();
    }
}