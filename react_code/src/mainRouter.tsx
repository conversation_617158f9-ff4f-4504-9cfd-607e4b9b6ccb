import React, { Suspense } from 'react';
import {
  BrowserRouter as Router,
  Switch,
} from 'react-router-dom';
import {
  Loading,
} from '@roo/roo';
import '@roo/roo/theme/default/index.css';
import FrontendAuth from '@components/FrontendAuth';
import InputTaxInvoice from './pages/InputTaxInvoice'
import InputTaxInvoiceDetail from './pages/InputTaxInvoiceDetail'
import PaymentDoc from './pages/PaymentDoc'
import PaymentDocDetail from './pages/PaymentDocDetail'
import OrderSearch from './pages/OrderSearch'


const NoMatch = () => (
  <div
    style={{
      textAlign: 'center',
      height: '300px',
      paddingTop: '100px',
    }}
  >
    <p>您访问的地址有误</p>
  </div>
);

const configRouteItems: Array<RouterConfigItem> = [
  {
    path: "/invoiceInfoQuery",
    name: 'invoiceInfoQuery',
    component: InputTaxInvoice,
    auth: true,
  },
  {
    path: "/invoiceDetail",
    name: 'invoiceDetail',
    component: InputTaxInvoiceDetail,
    auth: true,
  },
  {
    path: "/paymentInvoiceVerification",
    name: 'paymentInvoiceVerification',
    component: PaymentDoc,
    auth: true,
  },
  {
    path: "/paymentDetail",
    name: 'paymentDetail',
    component: PaymentDocDetail,
    auth: true,
  },
  {
    path: "/orderSearch",
    name: 'orderSearch',
    component: OrderSearch,
    auth: false,
  },
  {
    path: "/",
    name: 'noMatch',
    component: NoMatch,
    auth: false,
  }
]

const RouterConfig = () => (
  <Router basename='/money/invoice-new-input-tax'>
    <Suspense
      fallback={(
        <Loading
          text="正在加载..."
        >
          <p style={{ height: '500px' }} />
        </Loading>
      )}
    >
      <Switch>
        <FrontendAuth
          routerConfig={configRouteItems}
          redirectRoute="/invoiceInfoQuery"
          clientId={['xianfu_waimai', 'xianfu_waimai']}
          apiUrl="/money/outputTax/api/authority-new"
        />
      </Switch>
    </Suspense>
  </Router>
);
export default RouterConfig;