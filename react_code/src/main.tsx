import '@babel/polyfill';
import React from 'react';
import ReactDOM from 'react-dom';
import ComParams from '@/utils/comParam';
import MainRouter from './mainRouter';
import './assets/styles/index.scss';

// 进项税后台管理页面-需要加载先富菜单
// 初始化通参
ComParams.init();

ReactDOM.render(
    <MainRouter />,
    document.getElementById('root'),
);

if (process.env.NODE_ENV === 'development') {
    if ((module as any).hot) {
        (module as any).hot.accept();
    }
}