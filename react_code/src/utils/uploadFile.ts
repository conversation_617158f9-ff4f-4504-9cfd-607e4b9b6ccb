import axios from 'axios';
import Loading from '@/components/Loading';
import Util from '@/utils/util';
import ComParams from '@/utils/comParam';
import OwlReport from '@/utils/owlReport';
import { stringify } from '@utiljs/param';


type ReqOptions = {
    method: string;
    url: string;
    params?: MyTS.CommonObject;
    data?: MyTS.CommonObject | FormData;
    headers?: MyTS.CommonObject;
}

function hideLoading(loadingIsHere: boolean) {
    if (loadingIsHere) {
        Loading.hide();
    }
}

function uploadFile<TResInner>(url: string, file: File, params: MyTS.CommonObject = {}, options: Request.Options = {}) {

    const env = process.env.NODE_ENV;
    const isOnline = env === 'production' || env === 'staging';
    const SSOHOST = isOnline ? 'https://ssosv.sankuai.com/sson' : 'http://ssosv.it.test.sankuai.com/sson';

    const ClientID = 'xianfu_waimai';
    const ssoCallbackUrl = '/sso/callback';
    const getSSOLoginUrl = () => {
      // 登录域名
      const loginOrigin = `${SSOHOST}/login`;
      // clientId
      const clientId = ClientID;
      const { origin, href } = window.location;
      const currentPath = href.replace(new RegExp(`^${origin}`), '');
      // 例：https://ssosv.sankuai.com/sson/login?client_id=com.iph.zhaopin&redirect_uri=http%3A%2F%2Fzhaopin.sankuai.com%2Fsso%2Fcallback%3Foriginal-url%3D%252F
      return `${loginOrigin}?client_id=${clientId}&redirect_uri=${encodeURIComponent(`${origin}${ssoCallbackUrl}?original-url=${encodeURIComponent(currentPath)}`)}`;
    };
    // 添加通参
    const comParams = ComParams.get();
    params = {
        ...comParams,
        ...params,
    };


    const reqOptions: ReqOptions = {
        method: 'post',
        url,
        data: file,
        headers: {
            'Content-Type': 'multipart/form-data',
        },
    };

    const {
        showError = true,
        showLoading = false,
        loadingOptions = {} as Request.LoadingOptions,
    } = options;

    if (showLoading) {
        const { text, showMask } = loadingOptions;
        Loading.show({ text, showMask });
    }

    return axios(reqOptions).then((response) => {
        if (response.status >= 200 && response.status < 300) {
            return response.data;
        } else {
            throw response.statusText;
        }
    }).then((res: Request.ResponseData<TResInner>) => {
        if (res.code !== 0 && res.code !== '0') {
            if (showError) {
                Util.toast('error', res.msg || '文件上传失败~');
            }
            throw res;
        } else {
            hideLoading(showLoading);
            return (res.data || res) as TResInner;
        }
    }).catch((err) => {
        hideLoading(showLoading);
        if (err.request && err.config) {
            if (showError) {
                Util.toast('error', err.request.status == 401 ? '未登录' : '请求异常，请重试~');
            }
            if (err.request.status == 403) {
                window.location.href = '/unauthorized';
            }
            if (err.request.status == 401) {
                window.location.href = getSSOLoginUrl();
            }
            const errmsg = `${reqOptions.url} message(${err.message}) params(${stringify(params)})`;
            OwlReport('ajax异常', errmsg);
            throw new Error(`__requestError__  ${errmsg}`);
        } else if (err.code) {
            const errmsg = `${reqOptions.url} code(${err.code}) message(${err.msg || '--'}) params(${stringify(params)})`;
            OwlReport('ajax异常', errmsg);
            throw new Error(`__requestError__  ${errmsg}`);
        }
        return {} as TResInner;
    });
}

export default {
    uploadFile,
};