import axios from 'axios';
import { stringify } from '@utiljs/param';
import Loading from '@/components/Loading';
import Util from '@/utils/util';
import ComParams from '@/utils/comParam';
import OwlReport from '@/utils/owlReport';
import JUMPSSO from '@/utils/jump_sso';


declare global {
    interface Window {
        jsonCallBack: Function;
    }
}

// 默认设置
axios.defaults.headers.post['Content-Type'] = 'application/x-www-form-urlencoded';
axios.defaults.timeout = 15000; // 默认超时时间15s

type ReqOptions = {
    method: string;
    url: string;
    params?: MyTS.CommonObject;
    data?: MyTS.CommonObject | string;
    headers?: MyTS.CommonObject;
}
// 隐藏loading
function hideLoading(loadingIsHere: boolean) {
    if (loadingIsHere) {
        Loading.hide();
    }
}
/**
 * @param url
 * @param method get/post
 * @param params 参数
 * @param config 其他配置，如：{timeout: 2000, headers: {'X-Requested-With': 'XMLHttpRequest'}}
 */
function ajax<TResInner>(url: string, method: string, params: MyTS.CommonObject, options: Request.Options = {}) {
    const reqOptions: ReqOptions = {
        method,
        url,
        // headers: { 'x-eapi-gw-swimlane': '11927-quady' },
        // withCredentials: true,
    };
    const {
        showError = true,
        showLoading = false,
        isJson = false,
        isArray = false,
        loadingOptions = {} as Request.LoadingOptions,
    } = options;

    // 添加通参
    const comParams = ComParams.get();
    // isArra用于给后端接口传参时处理 如果传的是数组 就去掉外层的对象 
    if (!isArray) {
      params = {
        ...comParams,
        ...params,
    };
    }
   

    if (method === 'get') {
       reqOptions.params = params;
    } else if (method === 'post') {
        reqOptions.headers = {
          'Content-Type': 'application/json;charset=UTF-8',
        };
        if (isJson) {
            reqOptions.data = params;
        } else {
            reqOptions.data = stringify(params);
        }
    }
    if (showLoading) {
        // 显示loading
        const {
            text,
            showMask,
        } = loadingOptions;
        Loading.show({
            text,
            showMask,
        });
    }


    const env = process.env.NODE_ENV;

const isOnline = env === 'production' || env === 'staging';
const SSOHOST = isOnline ? 'https://ssosv.sankuai.com/sson' : 'http://ssosv.it.test.sankuai.com/sson';
    const ClientID = 'xianfu_waimai';
    const ssoCallbackUrl = '/sso/callback';
    const getSSOLoginUrl = () => {
      // 登录域名
      const loginOrigin = `${SSOHOST}/login`;
      // clientId
      const clientId = ClientID;
      const { origin, href } = window.location;
      const currentPath = href.replace(new RegExp(`^${origin}`), '');
      // 例：https://ssosv.sankuai.com/sson/login?client_id=com.iph.zhaopin&redirect_uri=http%3A%2F%2Fzhaopin.sankuai.com%2Fsso%2Fcallback%3Foriginal-url%3D%252F
      return `${loginOrigin}?client_id=${clientId}&redirect_uri=${encodeURIComponent(`${origin}${ssoCallbackUrl}?original-url=${encodeURIComponent(currentPath)}`)}`;
    };
    /**
     * 业务不catch，则会上报cat异常
     * 默认showError=true，不抛异常，否则抛异常（但不抛异常，则会走到.then()，会引起报错）
     * 不reture试试？
     *
     */
    return axios(reqOptions).then((response) => {
        if (response.status >= 200 && response.status < 300) {
            return response.data;
        } else {
            throw response.statusText;
        }
    }).then((res: Request.ResponseData<TResInner>) => {
        if (res.code !== 0 && res.code !== '0') {
            console.log(res)
            if (showError) {
                Util.toast('error', res.msg || '数据请求错误~');
            } else {
              return res.msg
            }
            // throw res.msg;
        } else {
            hideLoading(showLoading);
            return (res.data || res) as TResInner;
        }
    }).catch((err) => {
        hideLoading(showLoading);
        // 抛出异常，在Owl.start添加上报拦截：此类异常已自定义上报，需要过滤
        if (err.request && err.config) {
            // ajax异常：超时、502等等
            if (showError) {
                Util.toast('error', err.request.status == 401 ? '未登录' : '请求异常，请重试~');
            } 
            if (err.request.status == 403) {
              window.location.href = '/unauthorized'
            } 
            if (err.request.status == 401) {
              window.location.href = getSSOLoginUrl();
            }
            const errmsg = `${reqOptions.url} message(${err.message}) params(${stringify(params)})`;
            OwlReport('ajax异常', errmsg);
            throw new Error(errmsg);
        } else if (err.code) {
            // 业务异常
            const errmsg = `${reqOptions.url} code(${err.code}) message(${err.msg || '--'}) params(${stringify(params)})`;
            OwlReport('ajax异常', errmsg);
            throw new Error(errmsg);
        }
        return {} as TResInner;
    });
}

interface IAjaxReq {
    <TReq, TRes>(url: string, params: TReq, options?: Request.Options): Promise<TRes>;
}

const get: IAjaxReq = (url, params, options) => ajax(url, 'get', params, options);
const post: IAjaxReq = (url, params, options) => ajax(url, 'post', params, options);
export default {
    get,
    post,
};
