export const PartnerTypeEnum = {
  PARTNERS: 104, // 合作商
  SPECIAL: 441, // 校园配送
  OFF_SITE: 484, // 站外渠道
}

export const TspInputTaxInvoiceTypeList = [
  { value: 0, label: '增值税专用发票' },
  { value: 1, label: '增值税普通发票' },
  { value: 2, label: '增值税电子普通发票' },
  { value: 3, label: '增值税电子专用发票' },
  { value: 31, label: '电子发票（增值税专用发票）' },
  { value: 32, label: '电子发票（普通发票）' },
];
export const TspInputTaxInvoiceTypeEnum = {
    0 : '增值税专用发票',
    1 : '增值税普通发票',
    2 : '增值税电子普通发票',
    3 : '增值税电子专用发票',
    31 : '电子发票（增值税专用发票）',
    32 : '电子发票（普通发票）'
}
export const TspInputTaxInvoiceFileTypeEnum = [
  { value: 0, label: '未签收' },
  { value: 1, label: '已匹配' },
  { value: 2, label: '已审核' },
  { value: 3, label: '已驳回' },
  { value: 4, label: '已核销' },
  { value: 5, label: '未匹配' },
  { value: 6, label: '核销失败' },
  { value: 7, label: '发票查验中' },
  { value: 8, label: '发票查验失败' },
  { value: 9, label: '已退票' },
];

// 发票类型枚举
export const InvoiceTypeEnum = {
  PAPER_SPECIAL_INVOICE: 0,
  PAPER_ORDINARY_INVOICE: 1,
  ELECTRONIC_ORDINARY_INVOICE: 2,
  ELECTRONIC_SPECIAL_INVOICE: 3,
  EXHAUSTIVE_ELECTRONIC_SPECIAL_INVOICE: 31,
  EXHAUSTIVE_ELECTRONIC_ORDINARY_INVOICE: 32
}

// 发票状态枚举
export const InvoiceStatusList = [
  { value: 1, label: '查验中' },
  { value: 2, label: '查验失败' },
  { value: 3, label: '待签收' },
  { value: 4, label: '已签收' },
  { value: 5, label: '已驳回' },
  { value: 6, label: '待核销' },
  { value: 7, label: '已核销' },
  { value: 8, label: '已退票' },
  { value: 9, label: '未匹配' },

]
export const InvoiceStatusEnum = {
  1: '查验中',
  2 : '查验失败',
  3 : '待签收',
  4 : '已签收',
  5 : '已驳回',
  6 : '待核销',
  7 : '已核销',
  8 : '已退票',
  9 : '未匹配'
}
export const InvoiceStatusEnums = {
  INSPECTION: 1,
  VERIFICATION_FAILED: 2,
  TOBE_SIGNED: 3,
  SIGNED: 4,
  REJECTED: 5,
  TOBE_WRITTEN_OFF: 6,
  WRITTEN_OFF: 7,
  TICKET_REFUNDED: 8
}

// 发票状态枚举
export const InputTaxInvoiceStatusList = [
  { value: -1, label: '全部' },
  { value: 4, label: '已签收' },
  { value: 5, label: '已驳回' },
  { value: 6, label: '待核销' },
  { value: 7, label: '已核销' },
  { value: 8, label: '已退票' },
  { value: 9, label: '未匹配' },
]
export const InputTaxInvoiceStatusEnum = {
  4 : '已签收',
  5 : '已驳回',
  6 : '待核销',
  7 : '已核销',
  8 : '已退票',
  9 : '未匹配'
}
export const InputTaxInvoiceStatusEnums = {
  ALL: -1,
  SIGNED: 4,
  REJECTED: 5,
  TOBE_WRITTEN_OFF: 6,
  WRITTEN_OFF: 7,
  TICKET_REFUNDED: 8,
  UNMATCHED: 9
}
export const PartnerTypeEnums = [
  { value: 441, label: '校园配送' },
]

// 付款单状态
export const ApplyStatusEnum = [
  { value: 1, label: '已核销' },
  { value: 2, label: '未核销' },
]


// 付款单状态
export const ApplyStatusEnums = {
  WRITTEN_OFF: 1, // 已核销
  UNWRITTEN_OFF : 2 // 未核销
}

// 发票信息查询操作项
export const InvoiceAction = {
  5 : '通过',
  6 : '驳回'
}