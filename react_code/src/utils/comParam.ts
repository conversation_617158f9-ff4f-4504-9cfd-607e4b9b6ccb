import { getCookie, setCookie } from '@utiljs/cookie';
import { parse as UrlParse } from '@utiljs/param';

const urlParams: MyTS.CommonObject = UrlParse(window.location.href);

const getParamByKey: (key: string) => string = (key) => urlParams[key] || getCookie(key) || sessionStorage.getItem(key);
export default {
    /**
     * session存储：
     * 1、避免跳转路由无url参数
     */
    init() {
        // 过期时间10天
        const cookieOptions = {
            expires: new Date(new Date().getTime() + 10 * 24 * 60 * 60 * 1000),
        };
        if (urlParams.token) {
            sessionStorage.setItem('token', urlParams.token);
            setCookie('token', urlParams.token, cookieOptions);
        }
        if (urlParams.acctId) {
            sessionStorage.setItem('acctId', urlParams.acctId);
            setCookie('acctId', urlParams.acctId, cookieOptions);
        }
        if (urlParams.wmPoiId) {
            sessionStorage.setItem('wmPoiId', urlParams.wmPoiId);
            setCookie('wmPoiId', urlParams.wmPoiId, cookieOptions);
        }
        if (urlParams.bsid) {
            sessionStorage.setItem('bsid', urlParams.bsid);
            setCookie('bsid', urlParams.bsid, cookieOptions);
        }
    },
    /**
     * 获取请求通参，三层兜底：url参数、cookie参数、sessionStorage
     */
    get() {
        const token = getParamByKey('token');
        const acctId = getParamByKey('acctId');
        const wmPoiId = getParamByKey('wmPoiId');
        const bsid = getParamByKey('bsid');
        return {
            token,
            acctId,
            wmPoiId,
            bsid,
            appType: 3, // pc
        } as Request.ComParams;
    },
    /**
     * 获取url通参
     * @param key
     */
    getFromUrl(key: string) {
        return urlParams && urlParams[key];
    },
};
