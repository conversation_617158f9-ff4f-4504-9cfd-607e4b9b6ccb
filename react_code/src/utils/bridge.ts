const messageHandlers: { [key: string]: Array<(...args: any) => any> } = {};

function postMessageToParent(method: string, args: any) {
    const message = JSON.stringify({
        method,
        args: Array.prototype.slice.call(args || []),
    });
    parent.postMessage(message, '*');
}

interface IJumpToParams {
    // 要跳转的地址
    href: string;
    // 需要强制选中的左侧菜单的文案
    selectLabel?: string;
    autoOrigin?: boolean;
}

const bridgeApp = {
    /**
     * 唤起主页面协议签署弹框
     *
     * @param options 参数对象
     */
    signContract(options: { url: string; noAutoReloadAfterSign?: boolean }) {
        postMessageToParent('signContract', [options]);
    },
    /**
     * 通知外层页面切换 iframe 的地址，会走 waimaie 统一的逻辑在URL中补充必要的参数
     */
    jumpTo(params: IJumpToParams) {
        if (params.autoOrigin) {
            params.href = `${window.location.origin}${params.href}`;
        }
        postMessageToParent('jumpTo', [params]);
    },
    hashJump(url: string, props: MyTS.CommonObject, selectLabel?: string) {
        postMessageToParent('jumpTo', [{
            href: `${window.location.origin}${url}`,
            nojump: true,
            selectLabel,
        }]);
        props.history.push(url);
    },
    /**
     * 添加一个消息回调
     *
     * @param messageType 消息类型
     * @param handler 回调函数
     */
    addMessageHandler(messageType: string, handler: (...args: any) => any) {
        if (!messageHandlers[messageType]) {
            messageHandlers[messageType] = [];
        }

        messageHandlers[messageType].push(handler);
    },
    /**
     * 移除一个消息回调
     *
     * @param messageType 消息类型
     * @param handler 回调函数
     */
    removeMessageHandler(messageType: string, handler?: (...args: any) => any) {
        if (!handler) {
            messageHandlers[messageType] = [];
        } else if (messageHandlers[messageType] && handler) {
            messageHandlers[messageType] = messageHandlers[messageType].filter((h) => h !== handler);
        }
    },
};

window.addEventListener('message', (event) => {
    let messageData;
    try {
        messageData = JSON.parse(event.data) as { type: string; data: any };
        const handlers = messageHandlers[messageData.type];
        if (handlers && handlers.length > 0) {
            for (let i = 0; i < handlers.length; i++) {
                const result = !!handlers[i].call(null, messageData.data);
                // 返回值为false则不继续往下执行
                if (result === false) {
                    break;
                }
            }
        }
    } catch (e) {
        // console.log(e); // eslint-disable-line no-console
    }
});

export default bridgeApp;
