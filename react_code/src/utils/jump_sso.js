import axios from 'axios';
import { setCookie } from '@utiljs/cookie';

const env = process.env.NODE_ENV;

const isOnline = env === 'production' || env === 'staging';
// const isOnline = true;

// 默认设置
axios.defaults.headers.post['Content-Type'] = 'application/x-www-form-urlencoded';
axios.defaults.headers['X-Requested-With'] = 'XMLHttpRequest';
// proxy的最大超时时间是20s
// todo:时间改为15000
axios.defaults.timeout = 15000; // 默认超时时间15s

export default {
  // clientId: 不同域名线下clientId[0]，线上clientId[1]环境对应的token
  // apiUrl: 请求到后端的地址
  jumpToSSO: (callback, clientId, apiUrl) => {
    // sso_host 线上：https://ssosv.sankuai.com/sson  线下：http://ssosv.it.test.sankuai.com/sson
    // 后端callback地址 sso_callback_url（完整url，如https://123.sankuai.com/sso/callback
    const SSOHOST = isOnline ? 'https://ssosv.sankuai.com/sson' : 'http://ssosv.it.test.sankuai.com/sson';
    const ClientID = isOnline ? clientId[1] : clientId[0];
    const ssoCallbackUrl = '/sso/callback';
    const getSSOLoginUrl = () => {
      // 登录域名
      const loginOrigin = `${SSOHOST}/login`;
      // clientId
      const clientId = ClientID;
      const { origin, href } = window.location;
      const currentPath = href.replace(new RegExp(`^${origin}`), '');
      // 例：https://ssosv.sankuai.com/sson/login?client_id=com.iph.zhaopin&redirect_uri=http%3A%2F%2Fzhaopin.sankuai.com%2Fsso%2Fcallback%3Foriginal-url%3D%252F
      return `${loginOrigin}?client_id=${clientId}&redirect_uri=${encodeURIComponent(`${origin}${ssoCallbackUrl}?original-url=${encodeURIComponent(currentPath)}`)}`;
    };

    const options = {
      method: 'get',
      url: apiUrl,
    };

    return new Promise((resolve, reject) => {
      axios(options).then((res) => {
        // `axios`不是必须的，这里的意思是，判断每个业务接口响应结果是不是未授权的，如果是则重定向到sso登录页
        if (res.data.status === 401) {
          resolve(false);
          // setCookie('invoice_isAuth', 'true');
          // 开启跨应用信任和单设备拦截时使用，否则直接重定向到sso登入页
          if (res.data.data.code === '30005' || res.data.data.code === '30006') {
            window.location.href = res.data.data.url;
          } else {
            // 重定向到sso登入页
            window.location.href = getSSOLoginUrl(); // 默认登出后再登录是重定向到应用首页
          }
        } else if(res.data.status === 403) {
          window.location.href = '/unauthorized';
        }else {
          resolve(true);
        }
        if (callback) {
          callback();
        }
      }).catch((error) => {
        reject(error);
        window.location.href = getSSOLoginUrl();
      });
    })
  },
};