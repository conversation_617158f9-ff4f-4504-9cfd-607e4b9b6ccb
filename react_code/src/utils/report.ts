/**
 * 埋点util，自动增加wmpoiId埋点信息
 */
import ComParams from '@/utils/comParam';

type ValLabType = {
    // eslint-disable-next-line @typescript-eslint/camelcase
    poi_id: string;
    acctId?: string;
    custom?: MyTS.CommonObject;
}

const comParams = ComParams.get();
const wmPoiId = comParams.wmPoiId;
const acctId = comParams.acctId;

export default {
    // pv/uv事件，业务参数除了poi_id，存于custom; 参考 https://km.sankuai.com/page/238558523
    pv(cid: string, val: MyTS.CommonObject = {}) {
        // eslint-disable-next-line @typescript-eslint/camelcase
        const valLab: ValLabType = { poi_id: wmPoiId, custom: { ...val, acctId } };
        window.LXAnalytics('pageView', valLab, '', cid);
    },
    // 点击事件，业务参数在valLab拍平
    click(cid: string, bid: string, val: MyTS.CommonObject = {}) {
        // eslint-disable-next-line @typescript-eslint/camelcase
        const valLab: ValLabType = { poi_id: wmPoiId, acctId, ...val };
        window.LXAnalytics('moduleClick', bid, valLab, { cid });
    },
    // 曝光，业务参数在valLab拍平
    view(cid: string, bid: string, val: MyTS.CommonObject = {}) {
        // eslint-disable-next-line @typescript-eslint/camelcase
        const valLab: ValLabType = { poi_id: wmPoiId, acctId, ...val };
        window.LXAnalytics('moduleView', bid, valLab, { cid });
    },
    // 简化版的埋点上报, 自动识别类型，调用方法如： report.do('b_efwnfretqer_mc')，如需携带额外信息，可 report.do('b_efwnfretqer_mc', {useId: 13342})
    do(cid: string, id: string, val: MyTS.CommonObject = {}) {
        if (!cid) {
            // console.warn('cid is required');
            return;
        }
        if (/^c_.+/.test(id)) {
            this.pv(id, val); // 自动pv，基本不用主动调用
        } else if (/^b_.+_mc$/.test(id)) {
            this.click(cid, id, val); // cid省略，默认带上报pv的cid
        } else if (/^b_.+_mv$/.test(id)) {
            this.view(cid, id, val); // cid省略，默认带上报pv的cid
        }
    },
};
