import ComParams from '@/utils/comParam';

const comParam = ComParams.get();
/**
 * 自定义上报错误
 * @param name 错误标题
 * @param msg 错误信息
 * @param level 错误级别
 * @param tags 附加信息
 */
const owlReport = (name: string, msg: string, level: string = 'info', tags: MyTS.CommonObject = comParam) => {
    try {
        if (window.Owl) {
            window.Owl.addError(
                {
                    name,
                    msg,
                },
                {
                    level,
                    combo: false,
                    tags,
                },
            );
        }
    } catch (e) {
        console.log('OWL 自定义上报异常：', e);
    }
};

export default owlReport;
