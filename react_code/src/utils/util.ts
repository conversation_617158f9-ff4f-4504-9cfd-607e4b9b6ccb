import { Toast } from '@roo/roo';

export default {
    toast: (type: 'success' | 'fail' | 'error' | 'warn' | 'warning', msg: string, duration?: number, callback?: () => {}) => {
        if (!duration) {
            duration = 2000;
        }
        if (type === 'success') {
            duration = 1000;
        } else if (type === 'fail' || type === 'error') {
            duration = 3000;
            type = 'fail';
        } else if (type === 'warn' || type === 'warning') {
            duration = 3000;
            type = 'warning';
        }
        Toast.open({
            children: msg || '操作失败，系统异常',
            duration,
            status: type,
            position: 'topCenter',
            onClose: callback,
        });
    },
};
