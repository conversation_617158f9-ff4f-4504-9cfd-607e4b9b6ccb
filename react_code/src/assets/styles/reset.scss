@charset "UTF-8";

html {
    font-family: "PingFangSC-Regular", "Microsoft YaHei", FreeSans, Arimo, "Droid Sans","wenquanyi micro hei","Hiragino Sans GB", "Hiragino Sans GB W3", Arial, sans-serif;
    _font-family: '宋体';
    -ms-text-size-adjust: 100%;
    -webkit-text-size-adjust: 100%;
}
body {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  cursor:default;
}
body,h1,h2,h3,h4,h5,h6,p,blockquote,dl,dt,dd,ul,ol,li{
    margin:0;padding:0;
}
h1,h2,h3,h4,h5,h6{
    font-weight:500;
}
ul,ol,li{
    list-style:none;
}
table {
    border-collapse: collapse;border-spacing: 0;
}
td,th {
    padding: 0;
}
a {
    background-color: transparent;text-decoration: none;
}
a:active,a:hover {
    outline: 0;
}

img {
    border: 0;
}
button,input,optgroup,select,textarea {
    color: inherit;font: inherit;margin: 0;
}
button {
    overflow: visible;
}
button,select {
    text-transform: none;
}
button,
input[type="button"],
input[type="reset"],
input[type="submit"] {
    -webkit-appearance: button;cursor: pointer;
}
button::-moz-focus-inner,
input::-moz-focus-inner {
    border: 0;padding: 0;
}
input {
    line-height: normal;
}
input[type="checkbox"],
input[type="radio"] {
    box-sizing: border-box;padding: 0;
}

textarea {
    overflow: auto;
}

article,aside,details,figcaption,figure,footer,header,hgroup,main,menu,nav,section,summary {
    display: block;
}

svg:not(:root) {
    overflow: hidden;
}

html{-webkit-text-size-adjust:100%;-ms-text-size-adjust:100%}
html *{outline:0;-webkit-text-size-adjust:none;-webkit-tap-highlight-color:rgba(0,0,0,0)}
button[disabled],
input[disabled] {
    cursor: default;
}
input[type="number"]::-webkit-inner-spin-button,
input[type="number"]::-webkit-outer-spin-button {
    height: auto;
}
input[type="search"] {
    -webkit-appearance: textfield; box-sizing: content-box;
}
input[type="search"]::-webkit-search-cancel-button,
input[type="search"]::-webkit-search-decoration {
    -webkit-appearance: none;
}