@import "./reset.scss";
// @import "../iconfont/iconfont.css";
html,
body {
    width: 100%;
    height: 100%;
}

a,
button,
input,
textarea {
    -webkit-tap-highlight-color: transparent;
    -webkit-appearance: none;
}


/**去除某些浏览器type=number上线箭头**/
input[type=number] {
    -moz-appearance: textfield;
}

input[type=number]::-webkit-inner-spin-button,
input[type=number]::-webkit-outer-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

.left {
    float: left;
}

.right {
    float: right;
}

.clearfix:after {
    content: ".";
    display: block;
    height: 0;
    visibility: hidden;
    clear: both;
}

.clearfix {
    *zoom: 1;
}

.text--overflow {
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
}

.touch--scrolling {
    overflow: auto;
    overflow-scrolling: touch;
    -webkit-overflow-scrolling: touch;
    /*
    &::-webkit-scrollbar {
        background: rgba(0,0,0,0.0);
        height: 0;
        width: 0px;
    }
    */
}

.flex {
    display: flex;
}

.flex-vertical {
    flex-direction: column;
}

.flex-1 {
    flex: 1;
    display: block;
}