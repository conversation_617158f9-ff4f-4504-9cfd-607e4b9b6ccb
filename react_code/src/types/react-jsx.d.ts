declare namespace MyTS {
    interface Element {}
    interface IntrinsicElements {
        [elemName: string]: any;
    }

    interface IntrinsicAttributes {
        [key: string]: any;
    }

    interface CommonObject {
        [key: string]: any;
    }

    // dva 请求实体
    namespace dva {
        interface OptProps {
            initialState: any;
            models: Array<Object>;
            onError: Function;
            onAction?: Function;
        }
    }
}
