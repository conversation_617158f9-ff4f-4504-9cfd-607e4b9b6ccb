interface InitData {
    id: number,
    invoiceNumber: string,
    invoiceCode: string,
    checkCode: string,
    invoiceBillingDate: string,
    invoiceType: Array<string>,
    excludingTaxAmount: number | '',
    amount: number | '',
    invoiceStatus: string,
    checkMessage: string,
    invoiceS3Url:string,
    invoiceFileName:string,
    payBillId:number,
    partnerId:number,
    partnerType:number,
    bizLine:string,
    invoiceFileType:string
    /** 公司编码 */
    subject: string;
}
interface AddressDatas {
    id: number,
    addressee: string,
    phoneNumber: string,
    provinceName: string,
    cityName: string,
    districtName: string,
    address: string,
    provinceCode:string,
    districtCode:string,
    cityCode:string,
}