import React, { Suspense } from 'react';
import {
  BrowserRouter as Router,
  Switch,
} from 'react-router-dom';
import {
  Loading,
} from '@roo/roo';
import '@roo/roo/theme/default/index.css';
import FrontendAuth from '@components/FrontendAuth';
import EditInvoice from './pages/EditInvoice';


const NoMatch = () => (
  <div
    style={{
      textAlign: 'center',
      height: '300px',
      paddingTop: '100px',
    }}
  >
    <p>您访问的地址有误</p>
  </div>
);

const configRouteItems: Array<RouterConfigItem> = [
  {
    path: "/editInvoice",
    name: 'editInvoice',
    component: EditInvoice,
    auth: true,
  },
  
  {
    path: "/",
    name: 'noMatch',
    component: NoMatch,
    auth: false,
  }
]

const RouterConfig = () => (
  <Router basename='/invoicefe'>
    <Suspense
      fallback={(
        <Loading
          text="正在加载..."
        >
          <p style={{ height: '500px' }} />
        </Loading>
      )}
    >
      <Switch>
        <FrontendAuth
          routerConfig={configRouteItems}
          clientId={['dbdb9ee243', 'ec52eea2a6']}
          apiUrl="/finance/invoice/api/common/application/r/queryInvalidReasonCodes"
        />
      </Switch>
    </Suspense>
  </Router>
);
export default RouterConfig;