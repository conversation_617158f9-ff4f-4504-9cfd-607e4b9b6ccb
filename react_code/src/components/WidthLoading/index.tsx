import React from 'react';
import Empty from '@roo/roo-b/Empty';
import Loading from '../Loading';
import './index.scss';

interface IProps {
    /** 是否loading */
    loading?: boolean;
    /** 是否空状态 */
    empty?: boolean;
    /** 空状态文案 */
    emptyTip?: string;
    /** loading状态下是否显示元素，默认false */
    showWhenLoading?: boolean;
    /** 是否展示mask */
    showMask?: boolean;
    style?: React.CSSProperties;
    className?: string;
}
const WidthLoading: React.FC<IProps> = (props) => {
    const {
        loading,
        empty,
        emptyTip,
        showWhenLoading,
        showMask,
        children,
        style,
        className = '',
    } = props;
    return (
        <div
            className={`loading-data-wrapper ${className}`}
            style={style}
        >
            {
                !loading && empty && (
                    <Empty
                        text={emptyTip}
                        imgWidth={100}
                        imgHeight={100}
                        style={{
                            marginTop: 50,
                        }}
                    />
                )
            }
            {
                showWhenLoading ? (!empty && children) : (!loading && !empty && children)
            }
            {
                loading && <Loading showMask={showMask} />
            }
        </div>
    );
};
WidthLoading.defaultProps = {
    loading: false,
    empty: false,
    showWhenLoading: false,
    showMask: true,
};
export default WidthLoading;
