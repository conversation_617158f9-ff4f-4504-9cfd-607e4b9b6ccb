
import React, { Component } from "react";
import { Route, Redirect, with<PERSON><PERSON>er } from "react-router-dom";
import { RouteComponentProps } from 'react-router';
import JUMPSSO from '@/utils/jump_sso';
import { setCookie } from '@utiljs/cookie';

interface myProps {
  routerConfig: Array<RouterConfigItem>;
  redirectRoute?: string;
  clientId: Array<string>;
  apiUrl: string
}
type IProps = RouteComponentProps & myProps;
interface IState {
  isLogin: boolean;
}

class FrontendAuth extends Component<IProps, IState> {
  state = {
    isLogin: false,
  };

  render() {
    const { routerConfig, location, redirectRoute, clientId, apiUrl } = this.props;
    const { pathname, search } = location;
    const { isLogin } = this.state;
    // 统一种cookie参数
    if (search.length > 0) {
      const keyValue = search.slice(1);
      console.log('search', search);
      const keyArr = keyValue.split('&');
      const regex = /(\S*?)=(\S*)/;
      keyArr.forEach(ele => {
        const rr = regex.exec(ele);
        if (rr && rr.length > 2) {
          setCookie(rr[1], rr[2]);
        }
      });
    }
    const isLocal = window.location.href.indexOf('localhost') > -1;
    // 登陆状态判断
    // 因为dbdb9ee243_ssoid这个写入的时候是HttpOnly的，前端没办法取值，无法得到是否是登陆状态，因为通过前端手动种invoice_isAuth来进行判断的
    const islogin = isLogin || isLocal;
    // 目标路径获取
    const targetRouterConfig = routerConfig.find(
      (item) => {
        return item.path.replace(/\s*/g, "") === pathname
      }
    );
    // 没有匹配路由，且传了重定向路径
    if (!targetRouterConfig && redirectRoute) {
      return <Redirect to={redirectRoute} />;
    }
    // 这部分代码，是为了在非登陆状态下，访问不需要权限校验的路由
    if (targetRouterConfig && !targetRouterConfig.auth) {
      const { component } = targetRouterConfig;
      return <Route exact path={pathname} component={component} />
    }
    if (islogin) {
      // 如果路由合法，就跳转到相应的路由
      if (targetRouterConfig) {
        console.log('shide');
        return (<Route exact path={pathname} component={targetRouterConfig.component} />);
      } else {
        // 如果路由不合法，重定向到 404 页面
        return <Redirect to="/" />;
      }
    } else {
      // 非登陆状态下，当路由合法时且需要权限校验时，跳转到登陆页面，要求登陆
      // targetRouterConfig && targetRouterConfig.name == 'orderSearch' ? '' : 
      if (targetRouterConfig && targetRouterConfig.auth) {
        JUMPSSO.jumpToSSO(() => {
          console.log('跳转登录登录');
        }, clientId, apiUrl).then((res) => {
          if (res) {
            this.setState({ isLogin: res, });
          }
        }).catch((err) => {
          console.log('重定向登陆',err);
        });
        return <div></div>
      } else {
        // 非登陆状态下，路由不合法时，重定向至 404
        return <Redirect to="/" />;
      }
    }
  }
}
export default withRouter(FrontendAuth);