import React from 'react';
import ReactDOM from 'react-dom';
import {
    Loading as RooLoading,
} from '@roo/roo';
import ClassNames from 'classnames';
import './index.scss';


const domNode = document.createElement('div');
const createNode = () => {
    const node = (<LoadingCom />);
    return node;
};

const render = (visible: boolean) => {
    if (visible) {
        document.body.appendChild(domNode);
        const children = createNode();
        ReactDOM.render(children, domNode);
    }
    if (visible) {
        domNode.className = 'global-loading__wrapper';
    } else {
        domNode.className = 'global-loading__wrapper global-loading__wrapper--hide';
        ReactDOM.unmountComponentAtNode(domNode);
    }
};
const show = () => {
    render(true);
};

const hide = () => {
    render(false);
};

export type IProps = {
    /** 加载文案 */
    text?: string;
    /** 是否显示mask */
    showMask?: boolean;
    style?: React.CSSProperties;
    className?: string;
}

export default class LoadingCom extends React.PureComponent<IProps, any> {
    static show = show

    static hide = hide

    render() {
        const {
            text = '正在加载',
            showMask = true,
            style,
            className = '',
        } = this.props;
        return (
            <div
                className={ClassNames(className, 'pannel-loading__wrapper', !showMask ? 'pannel-loading__wrapper--nomask' : '')}
                style={style}
            >
                <RooLoading
                    visible
                    className="pannel-loading__inner"
                    text={text}
                />
            </div>
        );
    }
}
