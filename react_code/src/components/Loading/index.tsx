import React from 'react';
import ReactDOM from 'react-dom';
import {
    Loading as RooLoading,
} from '@roo/roo';
import ClassNames from 'classnames';
import './index.scss';

export type IProps = {
    /** 加载文案 */
    text?: string;
    /** 是否显示mask */
    showMask?: boolean;
    style?: React.CSSProperties;
    className?: string;
}

let globalLoadingDom: HTMLDivElement;
const renderGlobalLoading = (visible: boolean, props: IProps = {}) => {
    const {
        text,
        showMask,
    } = props;
    if (visible) {
        globalLoadingDom = document.createElement('div');
        document.body.appendChild(globalLoadingDom);
        ReactDOM.render((
            <LoadingCom
                text={text}
                showMask={showMask}
            />
        ), globalLoadingDom);
    }
    if (visible) {
        globalLoadingDom.className = 'global-loading__wrapper';
    } else {
        if (globalLoadingDom) {
            // globalLoadingDom.className = 'global-loading__wrapper global-loading__wrapper--hide';
            document.body.removeChild(globalLoadingDom);
        }
    }
};

export default class LoadingCom extends React.PureComponent<IProps, any> {
    static show = (props?: IProps) => renderGlobalLoading(true, props)

    static hide = () => renderGlobalLoading(false)

    render() {
        const {
            text = '正在加载',
            showMask = true,
            style,
            className = '',
        } = this.props;
        return (
            <div
                className={ClassNames(className, 'pannel-loading__wrapper', !showMask ? 'pannel-loading__wrapper--nomask' : '')}
                style={style}
            >
                <RooLoading
                    visible
                    className="pannel-loading__inner"
                    text={text}
                />
            </div>
        );
    }
}
