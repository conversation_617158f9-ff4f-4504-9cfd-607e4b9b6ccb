import { ErrorInfo } from 'react';
import withErrorBoundary from '@roo/roo-b/WithErrorBoundary';
import Empty from '@roo/roo-b/Empty';
import OwlReport from '@/utils/owlReport';

interface IFallbackProps {
    /** error.message 具体报错信息 */
    error: Error;
    /** ErrorInfo.componentStack 异常堆栈 */
    errorInfo: ErrorInfo;
    timestamp: number;
    className: string;
    /** 路由path, 或自定义path */
    path: string;
}

const FallbackComponent = (props: IFallbackProps) => {
    const { error, errorInfo, path } = props;
    // 采集页面ErrorBoundary异常日志
    if (error) {
        OwlReport(
            'ErrorBoundary上报异常',
            `页面信息：${JSON.stringify({ name: path })}
            错误信息：${error && error.message}；
            错误堆栈：${error && error.stack}；
            组件堆栈：${errorInfo && errorInfo.componentStack}。`,
        );
    }
    return (
        <Empty
            text="模块加载异常，请重试"
            imgHeight={100}
            style={{
                marginTop: 30,
            }}
        />
    );
};

const MyWithErrorBoundary = (component: any) => withErrorBoundary(FallbackComponent)(component);
export default MyWithErrorBoundary;
