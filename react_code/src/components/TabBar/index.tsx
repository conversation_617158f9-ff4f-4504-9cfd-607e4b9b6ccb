import React, { useEffect, useState } from 'react';
import { useHistory } from 'react-router-dom';
import ClassNames from 'classnames';
import './index.scss';
import request from '../../utils/request';

// 测试路有数据
// {
//   "sequence": 8,
//   "control": 1,
//   "dispatch": "receiptCheck",
//   "name": "发票审核",
//   "url": "/money/invoice-new-include-agent/#/receiptCheck",
//   "type": "hash"
// },
// {
//   "sequence": 9,
//   "control": 1,
//   "dispatch": "receiptList",
//   "name": "配送商发票核销",
//   "url": "/money/invoice-new-include-agent/#/receiptList",
//   "type": "hash"
// },
// {
//   "sequence": 10,
//   "control": 1,
//   "dispatch": "invoiceInfoQuery",
//   "name": "进项发票信息查询",
//   "url": "/money/invoice-new-input-tax/invoiceInfoQuery",
//   "type": "history"
// },
// {
//   "sequence": 11,
//   "control": 1,
//   "dispatch": "paymentInvoiceVerification",
//   "name": "付款单-进项发票核销",
//   "url": "/money/invoice-new-input-tax/paymentInvoiceVerification",
//   "type": "history"
// }


interface Iprops {
  tabName: string;
}
interface RouterObject {
  // 是否展示，权限控制
  control: number,
  // 非公共前缀路径名称
  dispatch: string,
  // tab名称
  name: string,
  sequence: number,
  // history还是hash
  type: string,
  // 对应tab地址
  url: string,
}
const currentProjectBasePath = 'invoice-new-input-tax';
const FC = (props: Iprops) => {
  const history = useHistory();
  // 保存路径信息
  const [tabList, changeTabList] = useState<Array<RouterObject>>([]);
  useEffect(() => {
    request.get<{}, Array<RouterObject>>('/money/outputTax/api/authority-new', {}).then((data) => {
      changeTabList(data);
    });
  }, []);

  return <div className='navtabs-wrapper'>
    <div className='navtabs-items'>
      {
        tabList.filter((ele) => ele.control === 1).map((ele: RouterObject) => {
          console.log('qhd', props.tabName === ele.dispatch);
          return <div 
          className={ClassNames('navtabs-wrapper__item', props.tabName === ele.dispatch ? 'navtabs-wrapper__item--active' : 'navtabs-wrapper__item--unactive')}
          onClick={() => {
            if (window.__POWERED_BY_QIANKUN__) {
                // 微前端
                window.pushSubNav(ele.url);
            } else {
              if (ele.url.indexOf(currentProjectBasePath) > -1) {
                // 当前工程
                history.push({pathname: `/${ele.dispatch}`});
              } else {
                // 别的工程
                window.location.href = ele.url;
              }
          }
          }}>
            {ele.name}
          </div>
        })
      }
    </div>
  </div>
}

export default FC;