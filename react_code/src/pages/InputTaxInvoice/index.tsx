import React, { useState, useEffect, useCallback, useMemo } from 'react'
import { RouteComponentProps } from 'react-router-dom';
import Form from '@roo/roo/Form';
import Grid from '@roo/roo/Grid';
import Button from '@roo/roo/Button';
import Select from '@roo/roo/Select';
import Table from '@roo/roo/Table';
import TabBar from '@components/TabBar';
import { InputTaxInvoiceStatusEnum,InvoiceAction,InputTaxInvoiceStatusList } from '../../utils/enum';
import request from '../../utils/request';
import './index.scss';

const { Row, Col } = Grid;
const Field = Form.field;
interface FormValues {
  partnerType: number | string,
  invoiceNumber: number | string,
  invoiceCode:number | string,
  invoiceStatus:number | string,
  sellerCompanyName:string
  buyerCompanyName:string
}
interface QueryRolePartnerType {
  partnerType:number,
  partnerTypeName:string
}
interface TspInputTaxKeplerInvoiceVoList {
  id:number,
  sellerCompanyName:string,
  buyerCompanyName:string,
  invoiceNumber:number,
  invoiceCode:number,
  amount:string,
  checkMessage:string,
  invoiceStatus:number,
  partnerType:number
}
interface QueryKeplerInvoiceInfoList {
  count:number,
  tspInputTaxKeplerInvoiceVoList:TspInputTaxKeplerInvoiceVoList[]
}
export default function InputTaxInvoice(props: RouteComponentProps) {
  const [dataSource, setDataSource] = useState<TspInputTaxKeplerInvoiceVoList[]>([]); // 获取查询发票列表数据
  const [pageCount, setPageCount] = useState<number>(0); // 获取列表总条数
  const [pageSize, setPageSize] = useState<number>(10); // 页码
  const [pageNo, setPageNo] = useState<number>(1); // 页数
  const [formValues, setFormValues] = useState<FormValues>({}); // 获取form表单数据
  const [queryRolePartnerType, setQueryRolePartnerType] = useState<QueryRolePartnerType[]>([]); //业务线


  const cols = {
    label: { span: 4 },
    field: { span: 6 }
  };

  // 获取发票文件url或影像url
  const preview = (invoiceNumber: number, invoiceCode: number, partnerType: number) => {
    request.get('/money/input/tax/invoice/queryKeplerInvoiceUrl', {
      invoiceNumber, invoiceCode, partnerType
    }).then((res) => {
      window.open(res)
    }).catch((error) => {
      console.log(error)
    })
  };

  // 查看详情
  const getIvoiceDetail = useCallback((invoiceNumber: number, invoiceCode: number, partnerType: number, invoiceStatus: number) => {
    window.open(`/money/invoice-new-input-tax/invoiceDetail?invoiceNumber=${invoiceNumber}&invoiceCode=${invoiceCode}&partnerType=${partnerType}&invoiceStatus=${invoiceStatus}`)
  },[]);
  const columns:any = useMemo(() =>[{
    label: '发票信息',
    align: 'center',
    children: [
      { prop: 'sellerCompanyName', label: '销方抬头', align: 'center', width: 120 },
      { prop: 'invoiceNumber', label: '发票号码', align: 'center', width: 120 },
      { prop: 'invoiceCode', label: '发票代码', align: 'center', width: 120, render: (text:number) => <p>{text || '-'}</p>  },
      { prop: 'amount', label: '价税合计金额', align: 'center', width: 120 },
      { prop: 'buyerCompanyName', label: '购方抬头', align: 'center', width: 120 },
      { prop: 'checkMessage', label: '系统审核结果', align: 'center', width: 120 },
      { prop: '', label: '发票图片', align: 'center', width: 120, render: (text:string, record:TspInputTaxKeplerInvoiceVoList) => <a onClick={() => preview(record.invoiceNumber, record.invoiceCode, record.partnerType)} style={{ cursor: "pointer" }}>预览</a> },
      { prop: 'invoiceStatus', label: '发票状态', align: 'center', width: 120, render: (text:number, record) => <p>{InputTaxInvoiceStatusEnum[text]}</p> },
    ]
  },
  {
    label: '操作', align: 'center', width: 120, render: (text:string, record:TspInputTaxKeplerInvoiceVoList) =>
      <div>
        <a onClick={() => getIvoiceDetail(record.invoiceNumber, record.invoiceCode, record.partnerType, record.invoiceStatus)} style={{ cursor: "pointer" }}>查看</a>
        <a onClick={() => auditInvoice(record.id, record.invoiceStatus, record.partnerType)} style={{ marginLeft: "10px", cursor: "pointer" }}>{InvoiceAction[record.invoiceStatus]}</a>
      </div>

  }
  ],[]);

  // 分页
  const handleChange = (page: number, pageSize: number) => {
    setPageSize(pageSize)
    setPageNo(page);
  };
  // 查询
  const selects = (value: FormValues) => {
    setFormValues(value)
    const {partnerType = '', invoiceNumber = '', invoiceCode = '',  invoiceStatus = '', sellerCompanyName = '', buyerCompanyName = ''} = value || {}
    request.get<{},QueryKeplerInvoiceInfoList>('/money/input/tax/invoice/queryKeplerInvoiceInfoList', {
      partnerType,
      invoiceNumber,
      invoiceCode,
      invoiceStatus,
      sellerCompanyName,
      buyerCompanyName,
      pageSize,
      pageNo
    }).then((res) => {
      setPageCount(res.count)
      setDataSource(res.tspInputTaxKeplerInvoiceVoList)
    }).catch((error) => {
      console.log(error)
    })
  };
  // 业务线类型
  useEffect(() => {
    request.get<{},Array<QueryRolePartnerType>>('/money/input/tax/invoice/queryRolePartnerType', {}).then((res) => {
      const newarr = res.map((item) => {
        return {
          value: item.partnerType,
          label: item.partnerTypeName
        }
      })
      setQueryRolePartnerType(newarr)
    })
  }, []);
  // 初始化列表 
  useEffect(() => {
    formValues.partnerType && selects(formValues)
  }, [pageCount, pageNo, pageSize]);
  // 通过/驳回
  const auditInvoice = (keplerInvoiceId: number, invoiceStatus: number, partnerType: number) => {
    request.post('/money/input/tax/invoice/auditInvoice', {
      keplerInvoiceId: keplerInvoiceId,
      toUpdateInvoiceStatus: InvoiceAction[invoiceStatus] === '驳回' ? 3 : 2,
      partnerType: partnerType
    }, {
      isJson: true,
    }).then((res) => {
      selects({
        partnerType,
        invoiceNumber: '',
        invoiceCode: '',
        invoiceStatus: '',
        sellerCompanyName: '',
        buyerCompanyName: ''
      })
    }).catch((error) => {
      console.log(error)
    })
  };
  return (
    <>
      <TabBar tabName='invoiceInfoQuery' />
      <div className='inputTax_wrap'>

        <div className='inputTax_select'>
          <Form
            cols={cols}
            onSubmit={(value, errors) => {
              console.log('submit callback: ', value, errors);
              selects(value)
            }}
          >
            {
              ({
                values, errors, submitForm
              }) => (
                <form>
                  <Row>
                    <Col span={3}>
                      <Field
                        label="业务线"
                        name="partnerType"
                        as={(
                          <Select
                            options={queryRolePartnerType}
                            placeholder="请选择业务线"
                          />
                        )}
                      />
                    </Col>
                    <Col span={3}>
                      <Field
                        label="发票号码"
                        name="invoiceNumber"
                        placeholder="请输入发票号码"
                      />
                    </Col>
                    <Col span={3}>
                      <Field
                        label="发票代码"
                        name="invoiceCode"
                        placeholder="请输入发票代码"
                      />
                    </Col>
                    <Col span={3}>
                      <Field
                        label="发票状态"
                        name="invoiceStatus"
                        as={(
                          <Select
                            options={InputTaxInvoiceStatusList}
                            placeholder="请选择发票状态"
                          />
                        )}
                      />
                    </Col>
                  </Row>
                  <Row style={{ marginTop: "16px" }}>
                    <Col span={3}>
                      <Field
                        label="销方抬头"
                        name="sellerCompanyName"
                        placeholder="请输入销方抬头"
                      />
                    </Col>
                    <Col span={3}>
                      <Field
                        label="购方抬头"
                        name="buyerCompanyName"
                        placeholder="请输入购方抬头"
                      />
                    </Col>
                    <Col span={3}>
                      <Field>
                        <Button className='select_button' onClick={submitForm}>
                          查询
                        </Button>
                      </Field>
                    </Col>
                  </Row>
                </form>
              )
            }
          </Form>
        </div>
        <div className='inputTax_list'>
          <Table
            border
            stripe
            rowKey="name"
            columns={columns}
            data={dataSource}
            pagination={{
              total: pageCount,
              pageSize: pageSize,
              showJumper: true,
              pageSizeOptions: [10, 20, 30, 50],
              onChange: handleChange,
              currentPage: pageNo,
              showTotal: (total) => `共 ${total} 条数据`
            }}
          />
        </div>
      </div>
    </>
  )
}
