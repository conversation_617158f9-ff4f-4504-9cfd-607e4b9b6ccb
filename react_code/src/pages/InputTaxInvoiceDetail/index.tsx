import React, { useState, useEffect, useCallback } from 'react';
import { RouteComponentProps } from 'react-router-dom';
import { Steps } from '@roo/roo';
import Table from '@roo/roo/Table';
import * as qs from 'query-string';
import request from '../../utils/request';
import { TspInputTaxInvoiceTypeEnum, InputTaxInvoiceStatusEnum } from '../../utils/enum';
import './index.scss';
import { Layout } from '@roo/roo/Grid';
import Panel from '@roo/roo/Panel';


interface InputTax {
  id: number,
  invoiceType: number,
  invoiceNumber: string,
  invoiceCode: string,
  invoiceBillingDate: string,
  buyerCompanyName: string,
  sellerCompanyName: string,
  excludingTaxAmount: string,
  tax: string,
  amount: string
}
interface InputTaxStatus {
  map(arg0: (item: {
    createTime: string;keplerInvoiceStatus:number
}, index: number) => { skey: string; title: any; desc: JSX.Element; }): any;
  keplerInvoiceStatus:number,
  createTime:string
}
interface DataSource {
  id:number,
  productionName:string,
  amount:string,
  taxRate	:number,
  taxAmount	:string
}

interface Info {
  tspInputTaxInvoiceProductionVoList:DataSource[],
  tspInputTaxKeplerInvoiceStatusLogVoList:InputTaxStatus,
   tspInputTaxKeplerInvoiceVo:InputTax
}
export default function InputTaxInvoiceDetail(props: RouteComponentProps) {
  const propsLocation = props.location.search && qs.parse(props.location.search);
  const [inputTaxStatus, setInputTaxStatus] = useState<InputTaxStatus[]>([]); // 发票状态详情数据
  const [inputTaxMessage, setInputTaxMessage] = useState<InputTax>({}); // 发票信息详情数据
  const [dataSource, setDataSource] = useState<DataSource[]>([]); // 获取详情表格数据

  useEffect(() => {
    const { invoiceNumber, invoiceCode, partnerType } = propsLocation;
    request.get<{},Info>('/money/input/tax/invoice/queryKeplerInvoiceInfo', {
      invoiceNumber, invoiceCode, partnerType
    }).then((res) => {
      const { tspInputTaxInvoiceProductionVoList, tspInputTaxKeplerInvoiceStatusLogVoList, tspInputTaxKeplerInvoiceVo } = res;
      const statusList = tspInputTaxKeplerInvoiceStatusLogVoList.map((item, index) => {
        return {
          skey: '✔️',
          // skey: ' ',
          title: InputTaxInvoiceStatusEnum[item.keplerInvoiceStatus],
          desc: <div className='step_message'>
            <p style={{ fontWeight: '500' }}>{item.createTime}</p>
          </div>,
        }
      })
      setInputTaxStatus(statusList)
      setInputTaxMessage(tspInputTaxKeplerInvoiceVo);
      setDataSource(tspInputTaxInvoiceProductionVoList)
    }).catch((error) => {
      console.log(error)
    })
  }, [])
  const columns: any = [
    { prop: 'productionName', label: '商品名称', width: 120 },
    { prop: 'amount', label: '金额', width: 120 },
    { prop: 'taxRate', label: '税率', width: 120 },
    { prop: 'taxAmount', label: '税额', width: 120 },
  ];
 
  // 查看影像
  const viewVideo = () => {
    const { invoiceNumber, invoiceCode, partnerType } = propsLocation
    request.get<{},string>('/money/input/tax/invoice/queryKeplerInvoiceUrl', {
      invoiceNumber, invoiceCode, partnerType
    }).then((res) => {
      window.open(res)
    }).catch((error) => {
      console.log(error)
    })
  }
  return (
    <div className='invoice_deatil'>
      <div className='invoice_detail_topTitle'>
        进项发票信息查询  &gt; 详情
      </div>
      <div className="invoice_deatil_main">
        <Panel
          title="发票状态"
          bordered
        >
          <div className='invoice_deatil_main_step'>
            <Steps
              steps={inputTaxStatus}
              active={1}
              direction="vertical"
            />
          </div>
        </Panel>
        <Panel
          title="发票信息"
          bordered
          style={{ marginTop: '24px' }}
        >
          <div className='invoice_detail_main_mesaage'>
            <Layout cols={4}>
              <span>发票类型：{TspInputTaxInvoiceTypeEnum[inputTaxMessage.invoiceType] || '-'}</span>
              <span>发票号码：{inputTaxMessage.invoiceNumber || '-'}</span>
              <span>发票代码：{inputTaxMessage.invoiceCode || '-'}</span>
              <span>开票日期：{inputTaxMessage.invoiceBillingDate || '-'}</span>
              <span style={{ marginTop: '24px', display: 'inline-block' }}>购买方：{inputTaxMessage.buyerCompanyName || '-'}</span>
              <span style={{ marginTop: '24px', display: 'inline-block' }}>销售方：{inputTaxMessage.sellerCompanyName || '-'}</span>
              <span style={{ marginTop: '24px', display: 'inline-block' }}>不含税总金额：{inputTaxMessage.excludingTaxAmount || '-'}</span>
              <span style={{ marginTop: '24px', display: 'inline-block' }}>总税额：{inputTaxMessage.tax || '-'}</span>
            </Layout>
            <div className='invoice_detail_mesaage_table'>
              <ul className='invoice_detail_mesaage_total'>
                <li>总价税合计：</li>
                <li style={{ color: "#00ABE4" }}>{inputTaxMessage.amount || '-'}元</li>
              </ul>
              <div style={{ marginTop: "24px" }}>
                <Table
                  rowKey="name"
                  hover
                  columns={columns}
                  data={dataSource}
                />
              </div>
            </div>
          </div>
        </Panel>
        <Panel
          title="发票文件"
          bordered
          style={{ marginTop: '24px' }}
        >
          <div className='invoice_deatil_main_file'>
            <p onClick={viewVideo}>查看影像</p>
          </div>
        </Panel>
      </div>
    </div>
  )
}
