import React, { useState, useEffect, useMemo } from 'react';
import { Table, Selector, DatePicker, Input, Upload, Button, Toast, Modal, Tooltip, Icon } from '@roo/roo'
import { FileModel } from '@roo/roo/Upload/interface';
import './index.scss';
import request from '../../utils/request';
import requestFile from '../../utils/uploadFile';
import {InvoiceStatusList,InvoiceTypeEnum} from '../../utils/enum'
import upload from '@assets/images/upload.png'
import moment from 'moment'
import { mulDecimals } from '@utiljs/math';


interface IProps {
    getInvoiceList: any;
    partnerId:number,
    partnerType:number,
    payBillId:number,
    bizLine: string,
    checkMessage: string,
    id: number,
}


interface InvoiceS3 {
    invoiceFileName:string,
    invoiceS3Url:string,
    invoiceFileType:string,
    tspInputTaxInvoiceOcrVoList: Array<InitData>,
    /** 发票总数量 */
    totalCount:number,
    /** 发票识别成功数量 */
    successCount:number,
}

interface Res{
    code:number,
    data:null,
    msg:string
}

interface CompanyInfo {
  /** 公司编码 */
  subject: string;
  /** 需开票价税合计金额，单位：分 */
  amount: number;
  /** 开票信息 */
  tspInputTaxBuyerInfoVo: {
    /** 抬头 */
    buyerCompanyName:string;
    /** 纳税人识别号 */
    buyerTaxNumber:string;
    /** 地址、电话 */
    buyerAddressAndPhone:string;
    /** 开户行及账号 */
    buyerBankNameAndAccount:string;
  },
  /** 支持的开票类型 */
  supportInvoiceType: Array<{
    invoiceTypeCode: number;
    type: string;
    invoiceTypeName: string;
  }>,
  /** 是否包含销方信息 */
  containSellerInfo?: boolean;
  /** 销方信息 - 合作商名称 */
  sellerName?: string;
  /** 销方税号 */
  sellerTaxNo?: string;
}

interface InitData {
    id: number;
    invoiceNumber: string;
    invoiceCode: string;
    checkCode: string;
    invoiceBillingDate: string | number;
    invoiceType: number | string;
    excludingTaxAmount: string | number;
    amount: string | number;
    invoiceStatus: string;
    checkMessage: string;
    invoiceS3Url: string;
    partnerId: number;
    payBillId: number;
    partnerType: number;
    bizLine: string;
    invoiceFileName: string;
    invoiceFileType: string;
    subject: string;
    /** 销方税号 */
    sellerTaxNo?: string;
}

const FC: React.FC<IProps> = (props:IProps) => {
    const [dataSource, setDataSource] = useState<Array<InitData>>([]); // 发票信息数据
    const [addId, setAddId] = useState<number>(1); // 添加发票信息id+ 1
    const [isEdit,setEdit] = useState<boolean>(false); // 是否修改发票信息
    const [companyList, setCompanyList] = useState<CompanyInfo[]>([]);
    const [loading, setLoading] = useState<boolean>(false); // 发票识别中
    const [successModal, setSuccessModal] = useState<boolean>(false); // 发票识别成功
    const [successCount,setSuccessCount] = useState<number>(0); // 发票识别成功数量
    const [totalCount, setTotalCount] = useState<number>(0); // 发票总数量
    const [subjectModel, setSubjectModel] = useState<boolean>(false); // 错误主体
    const [tspInputTaxInvoiceOcrVoList, setTspInputTaxInvoiceOcrVoList] = useState<InitData[]>([]); // 发票识别信息
    const [filteredLength, setFilteredLength] = useState<number>(0); // 过滤后的发票数量
    const [action, setAction] = useState<boolean>(false); //  操作按钮展示




    // 发票信息新增发票
    const addInvioceMessage = (subject: string, sellerTaxNo?: string) => {
        const dataObj: InitData = {
            id: addId + 1,
            invoiceNumber: "",
            invoiceCode: "",
            checkCode: '',
            invoiceBillingDate: '',
            invoiceType: '',
            excludingTaxAmount: '',
            amount: '',
            invoiceStatus: '',
            checkMessage: "",
            invoiceS3Url:"",
            partnerId:props.partnerId,
            payBillId:props.payBillId,
            partnerType:props.partnerType,
            bizLine:'waimai',
            invoiceFileName:"",
            invoiceFileType:'',
            subject,
            sellerTaxNo: sellerTaxNo || undefined,
        }
        setAddId(prevId => prevId + 1);
        setDataSource(prevDataSource => [...prevDataSource, dataObj]);
        setEdit(true)
        setAction(true);
    }
    // 发票信息删除操作
    const deleteInvoiceMessage = (record: InitData) => {
          setDataSource(dataSource.filter(item => item.id !== record.id));
    };
    // 发票信息表单
    const handleChange = (record: InitData, val: any, key: string) => {
        const _dataSource = dataSource.map(item => {
            if (item.id === record.id) {
                return {
                    ...item,
                    [key]: val.target.value,
                };
            }
            return item;
        });
        setDataSource(_dataSource);
    }

    // 发票代码校验
    const invoiceItemDisabled = (values: number, keys:string) => {
        if (keys === 'invoiceCode' && (Number(values) === InvoiceTypeEnum.EXHAUSTIVE_ELECTRONIC_ORDINARY_INVOICE || Number(values) === InvoiceTypeEnum.EXHAUSTIVE_ELECTRONIC_SPECIAL_INVOICE)) {
          return true
        }
        if (keys === 'checkCode' && (Number(values) === InvoiceTypeEnum.PAPER_SPECIAL_INVOICE || Number(values) === InvoiceTypeEnum.ELECTRONIC_SPECIAL_INVOICE || Number(values) === InvoiceTypeEnum.EXHAUSTIVE_ELECTRONIC_SPECIAL_INVOICE)) {
          return true
        }
    }
    // 发票类型
    const handleInvoiceChange = (record: InitData, val: any, key: string) => {
        const _dataSource = dataSource.map(item => {
            if (item.id === record.id) {
                if (Number(val) === InvoiceTypeEnum.PAPER_SPECIAL_INVOICE || Number(val) === InvoiceTypeEnum.PAPER_ORDINARY_INVOICE) {
                    item.invoiceS3Url = '' 
                    item.invoiceFileType = ''
                    item.invoiceFileName = ''
                }
                if (invoiceItemDisabled(val,'invoiceCode')) {
                    item.invoiceCode = ''
                }
                if (invoiceItemDisabled(val,'checkCode')) {
                    item.checkCode = ''
                }
                return {
                    ...item,
                    [key]: val,
                };
            }
            return item;
        });
        setDataSource(_dataSource);
    }

    // 金额换算
    const handleMoneyChange = (record: InitData, val: any, key: string) => {
        const _dataSource = dataSource.map(item => {
            if (item.id === record.id) {
                return {
                    ...item,
                    [key]: mulDecimals(Number(val.target.value), 100),
                };
            }
            return item;
        });
        setDataSource(_dataSource);
    }

    // 开票日期处理
    const handleDateChange = (raw: InitData, val: string | number | (string | number)[], key: string) => {
      const dates =new Date(Number(val)).setHours(0,0,0,0).toString()
        const _dataSource = dataSource.map(item => {
            if (item.id === raw.id) {
                return {
                    ...item,
                    [key]: Number(dates) / 1000,
                };
            }
            return item;
        });
        setDataSource(_dataSource);
    }

    // 表头样式必填样式
    const columsTh = (item: string) => {
        return (
            <div style={{ display: 'flex', marginLeft: '20%' }}>
                <span style={{ color: 'red' }}>*</span>
                <p style={{ marginLeft: "2px" }}>{item}</p>
            </div>
        )
    }

    // 获取默认返回发票信息
    useEffect(() => {
        request.get('/money/input/tax/invoice/queryInvoice',{
            payBillId:props.payBillId,
            partnerType:props.partnerType,
            partnerId:props.partnerId
            // payBillId:5020286134,
            // partnerType:104
        }).then((res: InitData[]) => {
            if (res.length) {
                setDataSource(res);
                props.getInvoiceList(res);
                setAction(true);
            } else if (dataSource.length > 0) {
              setAction(true);
            } else {
              setAction(false);
            }
        }).catch((error) => {
            if (error.message && error.message.indexOf('发票鉴权未通过') !== -1) {
                // 通知第三方外层页面鉴权失败
                window.parent.postMessage(JSON.stringify({commitType: 2}), '*')
            }
            Toast.open({
              title: error.message || '发生未知错误',
              status: 'fail',
              position: 'topCenter',
            });
         });
    },[])


    // 获取开票销方、金额列表
    useEffect(() => {
      request.get('/money/input/tax/invoice/queryIssueInvoiceInfo',{
        payBillId:props.payBillId,
        partnerType:props.partnerType,
        partnerId:props.partnerId
      }).then((res: CompanyInfo[]) => {
        setCompanyList(res)
      }).catch(error => {
        Toast.open({
          title: error.message || '发生未知错误',
          status: 'fail',
          position: 'topCenter',
        });
      })
    }, [])

    useEffect(() => {
      // 发票信息数据为空 不支持下载
      if (dataSource.length === 0) {
        props.getInvoiceList([]);
      }
    }, [dataSource])

    const getInvoiceTypeOptions = (subject: string) => {
        const company = companyList && companyList.filter(company => company.subject === subject)[0];

        if (company) {
          return company.supportInvoiceType.map(type => ({
            value: type.invoiceTypeCode,
            label: type.invoiceTypeName
          }))
        } else {
          return [];
        }
    }

  

    // 发票信息表
    const columns: any = [
        {
            prop: 'invoiceType',
            width: 200,
            renderTh: (() => (
                columsTh('发票类型')
            )),
            render: (text: string, record: InitData) => (
                 <Selector
                    options={getInvoiceTypeOptions(record.subject)}
                    value={text}
                    onChange={(val) => handleInvoiceChange(record, val, 'invoiceType')}
                    placeholder="请选择发票类型"
                    clearable={true}
                    disabled={!isEdit}
                    showArrow={isEdit}
                    // defaultValue='ofd'
                    className='selectDisabled'
                />
            )
        },
        {
            prop: 'invoiceCode',
            width: 160,
            renderTh: (() => (
                columsTh('发票代码')
            )),
            render: (text: string, record: InitData) => (
                <>
                    {
                        !isEdit ? <p style={{textAlign:'center'}}>{text || '--'}</p> : <Input value={text} onChange={val => handleChange(record, val, 'invoiceCode')} disabled={invoiceItemDisabled(record.invoiceType,'invoiceCode')} />
                    }
                </>
                
            )
        },
        {
            prop: 'invoiceNumber',
            width: 160,
            renderTh: (() => (
                columsTh('发票号码')
            )),
            render: (text: string, record: InitData) => (
                <>
                    {
                        !isEdit ? <p style={{textAlign:'center'}}>{text}</p> : <Input value={text} onChange={val => handleChange(record, val, 'invoiceNumber')} />
                    }
                </>
            )
        },
        {
            prop: 'invoiceBillingDate',
            align: 'center',
            width: 160,
            renderTh: (() => (
                columsTh('开票日期')
            )),
            render: (text: string, record: InitData) => (
                <>
                    {
                        !isEdit?<p style={{textAlign:'center'}}>{moment(record.invoiceBillingDate && (new Date(Number(text) * 1000)).toString()).format('YYYY-MM-DD')}</p>:<DatePicker
                        disabledDate={cur => cur.unix() > Date.now() / 1000}
                        renderExtraFooter={() => 'extra footer'}
                        format="YYYY-MM-DD"
                        popupContainer={() => document.querySelector('#scroll-wrap') || document.querySelector('#root')}
                        onChange={val => handleDateChange(record, val, 'invoiceBillingDate')}
                        defaultValue={record.invoiceBillingDate && (new Date(Number(text) * 1000)).toString()}
                    />
                    }
                </>

            )
        },
        {
            prop: 'excludingTaxAmount',
            width: 200,
            renderTh: (() => (
                columsTh('不含税金额')
            )),
            render: (text: string, record: InitData) => (
                <>
                    {
                         !isEdit ? <p style={{textAlign:'center'}}>{Number(text) / 100}</p> : <Input type="number" defaultValue={text === '' ? text : (Number(text) / 100).toString()} onChange={val => handleMoneyChange(record, val, 'excludingTaxAmount')} />
                    }
                </>
            )
        },
        {
            prop: 'amount',
            width: 200,
            renderTh: (() => (
                columsTh('价税合计金额')
            )),
            render: (text: string, record: InitData) => (
                <>
                {
                     !isEdit ? <p style={{textAlign:'center'}}>{Number(text) / 100}</p> :  <Input type="number" id='number' defaultValue={text === '' ? text : (Number(text) / 100).toString()} onChange={val => handleMoneyChange(record, val, 'amount')} />
                }
            </>
               
            )
        },
        {
            prop: 'checkCode',
            width: 200,
            label: '发票校验码（后六位）',
            align: 'center',
            render: (text: string, record: InitData) => (
                <>
                {
                     !isEdit ? <p style={{textAlign:'center'}}>{text || '--'}</p> :  <Input value={text} onChange={val => handleChange(record, val, 'checkCode')} disabled={invoiceItemDisabled(record.invoiceType,'checkCode')} />
                }
            </>
            )
        },
        {
            prop: 'invoiceS3Url',
            width: 100,
            label: '发票文件',
            align: 'center',
            render: (text: string, record: InitData, index: number) => {
                const defaultList: Array<FileModel> = [];
                const hasUrl = !!record.invoiceS3Url;
                if (hasUrl) {
                    defaultList.push({
                      url: record.invoiceS3Url,
                      name: record.invoiceFileName,
                      status: 'success',
                      uid: `${record.id}`
                  });
                }

                return (
                    <Upload
                        // multiple
                        showUploadList={hasUrl}
                        showType="list"
                        onRemove={() => onRemove(record)}
                        onSuccess={(res) => onSuccess(res, record)}
                        onPreview={onPreview}
                        // @ts-ignore
                        action="/money/input/tax/invoice/uploadFile"
                        accept='.pdf,.ofd,.xml'
                        disabled={!isEdit}
                        defaultFileList={defaultList}
                    >
                        {
                           !hasUrl && Number(record.invoiceType) != InvoiceTypeEnum.PAPER_SPECIAL_INVOICE && Number(record.invoiceType) != InvoiceTypeEnum.PAPER_ORDINARY_INVOICE  ? <div style={{ cursor: "pointer",width:'20px',height:"20px",marginLeft:'27px',marginTop:'10px' }} className='isShow'>
                            <img src={upload} alt="" style={{width:'100%',height:'100%'}} />
                        </div>:<></>
                        }
                    </Upload>
                )
            }
        },
        {
            prop: 'invoiceStatus',
            width: 180,
            renderTh: (() => (
                <div style={{ display: 'flex', marginLeft: '20%' }}>
                <p>发票状态</p>
            </div>
            )),
            render: (text: string, record: InitData) => (
                <Selector
                    options={InvoiceStatusList}
                    value={text}
                    onChange={val => handleChange(record, val, 'invoiceStatus')}
                    placeholder=""
                    clearable={true}
                    disabled
                    showArrow={false}
                    className='invoiceStatus'
                />
            )
        },
        {
            prop: 'checkMessage',
            width: 160,
            label: '验票结果',
            align: 'center',
            render: (text: string, record: InitData) => (
                <p style={{textAlign:'center'}}>{text || '--'}</p>
            )
        },
        {
            prop: 'actions',
            width: 100,
            label: '操作',
            align: 'center',
            fixed: 'right',
            render: (text: string, record: InitData) => (
                <>
                  {
                    isEdit ?  <a onClick={() => {
                        deleteInvoiceMessage(record)
                    }}>删除</a> : <></>
                  }
                </>
            )
        }
    ];

    //发票文件删除
    const onRemove = (record: InitData) => {
        const _dataSource = dataSource.map(item => {
            if (item.id === record.id) {
                  return {
                      ...item,
                      invoiceFileName: '',
                      invoiceFileType: '',
                      invoiceS3Url: '',
                  }
            }
            return item;
        });
        setDataSource(_dataSource);
        Toast.open({
            title: '删除成功',
            status: 'success',
            position: 'topCenter',
        });
    };

    // 发票文件change事件
    const onSuccess = (res: { code: number; msg: string; data: InvoiceS3; },record:InitData) => {
        if (res.code === 0) {
            Toast.open({
                title: res.msg,
                status: 'success',
                position: 'topCenter',
            });
            const {invoiceFileName,invoiceFileType,invoiceS3Url} = res.data
            const _dataSource = dataSource.map(item => {
                if (item.id === record.id) {
                    return {
                        ...item,
                        invoiceFileName: invoiceFileName,
                        invoiceFileType: invoiceFileType,
                        invoiceS3Url: invoiceS3Url,
                    }
                }
                return item;
            });
            setDataSource(_dataSource);
        } else {
            Toast.open({
                title: res.msg,
                status: 'warning',
                position: 'topCenter',
            });
        }       
    };

  

    const handleUpload = async (formData) => {
      setLoading(true);
      try {
        const response = await requestFile.uploadFile('/money/input/tax/invoice/uploadFileForOcr', formData);
        console.log(response,'response');
        if (response) {
          setSuccessModal(true);
          setEdit(true);
          const {tspInputTaxInvoiceOcrVoList, successCount, totalCount} = response;
          if (tspInputTaxInvoiceOcrVoList.length > 0) {
            setAction(true);
          } else {
            setAction(false);
          }
          setTspInputTaxInvoiceOcrVoList(tspInputTaxInvoiceOcrVoList);
          updateInvoiceList(tspInputTaxInvoiceOcrVoList);
          setSuccessCount(successCount);
          setTotalCount(totalCount);
        }
      } catch (error) {
        console.log(error,'error')
        Toast.open({
          title: error.message || '发生未知错误',
          status: 'fail',
          position: 'topCenter',
        });
      } finally {
        setLoading(false);
      }
    }


    const updateInvoiceList = (invoiceList: InitData[]) => {
      const currentMaxId = dataSource.reduce((maxId, item) => {
        return item.id > maxId ? item.id : maxId;
      }, 0);
      let newId = 0;
      invoiceList.forEach((item, index) => {
        newId = currentMaxId + index + 1;
        item.id = newId; // 递增 id
        item.payBillId = Number(props.payBillId) || 0;
        item.partnerType = Number(props.partnerType) || 0;
        item.bizLine = 'waimai';
        item.checkMessage = '';
        item.partnerId = Number(props.partnerId) || 0;

        // 根据销方税号匹配对应的合作商
        if (item.sellerTaxNo) {
          const matchedCompany = companyList.find(company =>
            company.containSellerInfo && company.sellerTaxNo === item.sellerTaxNo
          );
          if (matchedCompany) {
            item.subject = matchedCompany.subject;
          }
        }
      });
      setAddId(newId);
      setDataSource(prevDataSource => [...prevDataSource, ...invoiceList]);
    };

       
    // 自动识别上传
    const handleFileChange = (event) => {
      const selectedFiles = Array.from(event.target.files);
      console.log(selectedFiles,'selectedFiles')
      if (selectedFiles.length > 9) {
        Toast.open({
          title: '上传文件不能超过9个',
          status: 'fail',
          position: 'topCenter',
        });
      } else {
        const formData = new FormData();
        selectedFiles.forEach((file, index) => {
          formData.append(`file${index}`, file);
        });
        if (selectedFiles.length > 0) {
          handleUpload(formData);
        }
      }
    };

    const filtered = useMemo(() => companyList && companyList.filter(item => !tspInputTaxInvoiceOcrVoList.some(it => it.subject === item.subject)), [companyList, tspInputTaxInvoiceOcrVoList]);
    // 错误主体
    const onConfirm = () => {
      if (filtered.length > 0) {
        setFilteredLength(filtered.length);
        setSubjectModel(true);
      } else {
        setSuccessModal(false);
      }
    }

    const errorSubject = () => {
      setSubjectModel(false);
      setSuccessModal(false);
    }
    // 发票文件点击打开
    const onPreview = (file: FileModel) => {
        if (file.url) {
            window.open(file.url, '_blank');
            return;
        }

        if (file.previewUrl && file.previewUrl.startsWith('data:image/')) {
            const newWindow = window.open();
            if (newWindow) {
                // @ts-ignore
                newWindow.document.body.style = 'background: #000';
                newWindow.document.body.innerHTML = `<img src="${file.previewUrl}" style="position: absolute; right: 0;top: 0; bottom: 0; left: 0; height: 500px; margin:auto">`;
            }
            return;
        }

        // 其他情况下载到本地
        if (file._fileObj) {
            const newWindow = window.open();
            if (newWindow) {
                const aDom = document.createElement('a');
                aDom.style.display = 'none';
                aDom.href = URL.createObjectURL(file._fileObj);
                newWindow.document.body.appendChild(aDom);
                aDom.click();
                newWindow.document.body.removeChild(aDom);
            }
        }
    };

    // 保存非空校验
    const jiaoyan = (title:string) => {
        Toast.open({
            title: `${title}不可为空`,
            status: 'warning',
            position: 'topCenter',
        })
    }

    // 重复发票校验
    const checkDuplicateInvoices = (invoices: InitData[], companyList: CompanyInfo[]) => {
      let invoiceSet = new Set();
      for (let invoice of invoices) {
        for (let item of companyList) {
          // 对于包含销方信息的新数据结构，需要同时匹配 subject 和 sellerTaxNo
          const isMatched = item.containSellerInfo && item.sellerTaxNo 
            ? (item.subject === invoice.subject && item.sellerTaxNo === invoice.sellerTaxNo)
            : (item.subject === invoice.subject);
            
          if (isMatched) {
            // 使用 subject + sellerTaxNo + invoiceNumber 作为唯一标识
            const invoiceKey = item.containSellerInfo && item.sellerTaxNo 
              ? `${invoice.subject}-${invoice.sellerTaxNo}-${invoice.invoiceNumber}`
              : `${invoice.subject}-${invoice.invoiceNumber}`;
              
            if (invoiceSet.has(invoiceKey)) {
              Toast.open({
                title: '存在重复发票',
                status: 'warning',
                position: 'topCenter',
            });
              return false;
            } else {
              invoiceSet.add(invoiceKey);
            }
          }
        }
      }
      return true;
    };

    // 保存发票信息
    const save = () => {
        const isSave = dataSource.map((item) => {
            if (item.invoiceType.length === 0) {
                jiaoyan('发票类型')
                return
            }
            if (item.invoiceCode === '' && Number(item.invoiceType) != InvoiceTypeEnum.EXHAUSTIVE_ELECTRONIC_ORDINARY_INVOICE && Number(item.invoiceType) != InvoiceTypeEnum.EXHAUSTIVE_ELECTRONIC_SPECIAL_INVOICE) {
                jiaoyan('发票代码')
                return
            }
            if (item.invoiceNumber === '') {
                jiaoyan('发票号码')
                return
            }
            if (item.invoiceBillingDate === '') {
                jiaoyan('开票日期')
                return
            }
            if (item.excludingTaxAmount === '') {
                jiaoyan('不含税金额')
                return
            }
            if (item.amount === '') {
                jiaoyan('价税合计金额')
                return
            }
            if (item.checkCode === '' && Number(item.invoiceType) === InvoiceTypeEnum.ELECTRONIC_ORDINARY_INVOICE || item.checkCode === '' && Number(item.invoiceType) === InvoiceTypeEnum.PAPER_ORDINARY_INVOICE) {
                jiaoyan('发票校验码')
                return
            }

            if (item.excludingTaxAmount < 0 && item.excludingTaxAmount < item.amount) {
                Toast.open({
                    title: '不含税金额不能小于价税合计金额',
                    status: 'warning',
                    position: 'topCenter',
                })
                return
            }
            if (item.excludingTaxAmount >= 0 && item.excludingTaxAmount > item.amount) {
              Toast.open({
                title: '不含税金额不能大于价税合计金额',
                status: 'warning',
                position: 'topCenter',
            })
            return
            }
           return item
        })
      

        if (isSave.indexOf(undefined) === -1 && checkDuplicateInvoices(isSave, companyList)){
          const filtered = isSave.filter(item => companyList.some(it => it.subject === item.subject));
          request.post('/money/input/tax/invoice/bind',{
              partnerId:props.partnerId,
              payBillId:props.payBillId,
              partnerType:props.partnerType,
              bizLine:'waimai',
              tspInputTaxBindInvoiceVoList: filtered
          },{
              isJson: true
          }).then((res:Res) => {
              setEdit(false)
                  Toast.open({
                      title:"操作成功",
                      status:'success',
                      position: 'topCenter',
                  })
                  props.getInvoiceList(isSave)

                  // 通知第三方外层页面保存成功
                  window.parent.postMessage(JSON.stringify({commitType: 1}), '*')
          }).catch((error) => {
              Toast.open({
                title: error.message ||'发生未知错误',
                status: 'fail',
                position: 'topCenter',
              });
          });
        }
    };

    // 修改
    const edit = () => {
        setEdit(true)
    }
    const getAmount = (item: CompanyInfo[]) => {
      const totalAmount = item.reduce((total:number, currentItem: CompanyInfo) => {
        return total + currentItem.amount;
      }, 0);
      return (totalAmount / 100).toLocaleString('en-US', {minimumFractionDigits:2, maximumFractionDigits:2});
    };

    // 渲染表格头部信息
    const renderCompanyHeader = (company: CompanyInfo) => {
      return (
        <div className="table_header">
          <div className="company_info">
            {company.containSellerInfo ? (
              // 新数据结构 - 显示合作商名称
              <>
                <p>需开购方：{company.tspInputTaxBuyerInfoVo.buyerCompanyName} <span onClick={() => {
                  Modal.confirm({
                    title: null,
                    closable: true,
                    size: 'lg',
                    children: (
                      <div>
                          <p>抬头：{company.tspInputTaxBuyerInfoVo.buyerCompanyName}</p>
                          <p>纳税人识别号：{company.tspInputTaxBuyerInfoVo.buyerTaxNumber}</p>
                          <p>地址、电话：{company.tspInputTaxBuyerInfoVo.buyerAddressAndPhone}</p>
                          <p>开户行及账号：{company.tspInputTaxBuyerInfoVo.buyerBankNameAndAccount}</p>
                      </div>
                    ),
                    showCancelButton:false,
                    confirmText: '关闭'
                  })
                }}>更多信息</span></p>
                <p>合作商名称：{company.sellerName || '--'}</p>
              </>
            ) : (
              // 老数据结构 - 保持原样
              <p>需开购方：{company.tspInputTaxBuyerInfoVo.buyerCompanyName} <span onClick={() => {
                Modal.confirm({
                  title: null,
                  closable: true,
                  size: 'lg',
                  children: (
                    <div>
                        <p>抬头：{company.tspInputTaxBuyerInfoVo.buyerCompanyName}</p>
                        <p>纳税人识别号：{company.tspInputTaxBuyerInfoVo.buyerTaxNumber}</p>
                        <p>地址、电话：{company.tspInputTaxBuyerInfoVo.buyerAddressAndPhone}</p>
                        <p>开户行及账号：{company.tspInputTaxBuyerInfoVo.buyerBankNameAndAccount}</p>
                    </div>
                  ),
                  showCancelButton:false,
                  confirmText: '关闭'
                })
              }}>更多信息</span></p>
            )}
            <div style={{display: 'flex'}}>
              <p>需开票价税合计金额：{(company.amount / 100).toLocaleString('en-US', {minimumFractionDigits:2, maximumFractionDigits:2})}元</p>
              <p style={{marginLeft:'24px'}}>已上传发票{dataSource && dataSource.filter(obj => {
                // 对于包含销方信息的新数据结构，需要同时匹配 subject 和 sellerTaxNo
                if (company.containSellerInfo && company.sellerTaxNo) {
                  return obj.subject === company.subject && obj.sellerTaxNo === company.sellerTaxNo;
                }
                // 对于老数据结构，只匹配 subject
                return obj.subject === company.subject;
              }).length}张，累计价税合计金额{(() => {
                const filteredData = dataSource.filter(obj => {
                  // 对于包含销方信息的新数据结构，需要同时匹配 subject 和 sellerTaxNo
                  if (company.containSellerInfo && company.sellerTaxNo) {
                    return obj.subject === company.subject && obj.sellerTaxNo === company.sellerTaxNo;
                  }
                  // 对于老数据结构，只匹配 subject
                  return obj.subject === company.subject;
                });
                const totalAmount = filteredData.reduce((total: number, currentItem: InitData) => {
                  return total + Number(currentItem.amount || 0);
                }, 0);
                return (totalAmount / 100).toLocaleString('en-US', {minimumFractionDigits:2, maximumFractionDigits:2});
              })()}元</p>
            </div>
          </div>
          <div className='button'>
            <Button type='brand' className='add_invoice' onClick={() => {
              addInvioceMessage(company.subject, company.sellerTaxNo)
            }}>手动新增发票</Button>
            <Tooltip
              content="人工手动填写发票信息"
              effect="light"
            >
              <Icon className='infos' name="info-circle-o" />
            </Tooltip>
          </div>
        </div>
      );
    };

    return (
        <div>
            <div className='base_message'>
              <span className='base_span'></span>
              <p>填写发票信息</p>
              <p style={{ fontSize: '12px', color: "#ccc" }}>(请仔细录入确保数据准确)</p>
            </div>
            <div className='filesBtn'>
                <input 
                  id="file_headerpic"
                  type="file" 
                  multiple 
                  accept='.pdf,.ofd'
                  onChange={handleFileChange} />
                <label htmlFor="file_headerpic">
                    自动识别发票
                </label>
                <Tooltip
                  content="支持一次上传多个发票文件，并自动识别发票信息至对应开票主体；单次最多上传9个发票文件，每个发票文件仅可包含一张发票；电子发票文件类型仅支持PDF/OFD格式"
                  effect="light"
                >
                  <Icon className='info' name="info-circle-o" />
                </Tooltip>
            </div>
            {
                companyList && companyList.map(company => (
                  <div className='write_table' key={`${company.subject}-${company.sellerName || ''}`}>
                      {renderCompanyHeader(company)}
                      <Table
                          rowKey="id"
                          columns={columns}
                          data={dataSource && dataSource.filter(obj => {
                            // 对于包含销方信息的新数据结构，需要同时匹配 subject 和 sellerTaxNo
                            if (company.containSellerInfo && company.sellerTaxNo) {
                              return obj.subject === company.subject && obj.sellerTaxNo === company.sellerTaxNo;
                            }
                            // 对于老数据结构，只匹配 subject
                            return obj.subject === company.subject;
                          })}
                          scrollX={1200}
                      />
                  </div>
                ))
            }
            {
              action && <div className='write_table'>
                {
                    isEdit ? <div className='save'>
                      <Button type='brand-hollow' style={{marginRight:'12px'}} onClick={() => {
                         setEdit(false)
                      }}>取消</Button >
                      <Button type='brand' onClick={() => {
                          save()
                      }}>保存</Button >
                    </div>
                    : <Button type='brand' className='save' onClick={() => {
                        edit()
                    }}>修改</Button>
                }
            </div>}
            <Modal
              visible={loading}
              closable={false}
              showCancelButton={false}
              showConfirmButton={false}
              backdropStyle={{background:'rgba(0,0,0,0.5)'}}
            >
              发票识别中，请稍后...
            </Modal>
            <Modal
              visible={successModal}
              closable={false}
              showCancelButton={false}
              onConfirm={onConfirm}
              backdropStyle={{background:'rgba(0,0,0,0.5)'}}
            >
              您本次上传发票 {totalCount} 张，识别成功 {successCount} 张，识别失败 {totalCount-successCount} 张，若识别失败，您可尝试再次上传发票或手动添加发票信息，请仔细核对发票信息后提交。
            </Modal>
            <Modal
              visible={subjectModel}
              closable={false}
              showCancelButton={false}
              onConfirm={errorSubject}
              backdropStyle={{background:'rgba(0,0,0,0)'}}
              className='subject_modal'
            >
              {filteredLength}张发票主体错误
            </Modal>
        </div>
    )
}

export default FC;