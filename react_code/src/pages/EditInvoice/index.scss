.edit_invoice__wrapper {
    margin: 30px;
}
.invoice_message{
    width: 100%;
    height: 50px;
    background: #f9f9f9;
    line-height: 50px;
    padding-left: 24px;
    font-size: 20px;
    font-weight: 600;
    color: #515a6e;
}
.invoice_main{
    margin: 12px 24px;
}
.base_message{
    width: 100%;
    height: 40px;
    line-height: 40px;
    display: flex;
    align-items: center;
    position: relative;
    .base_span{
        width: 5px;
        height: 30px;
        background: #f89800;
    }
    p{
        color: #515a6e;
        font-size: 16px;
        font-weight: 600;
        margin-left: 10px;
    }
}

.messages{
    padding-left: 48px;
    line-height: 32px;
    font-size: 12px;
}
.write_table{
    margin: 12px 24px;
    position: relative;

    .table_header {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: flex-end;
      margin-bottom: 10px;
    }

    .addicon{
        position: absolute;
        right: -20px;
        top: 78px;
        cursor: pointer;
    }

    .company_info {
        line-height: 32px;

        span {
            color: #f89800;
            margin-left: 40px;
            cursor: pointer;
        }
    }
}
.save{
    position: absolute;
    right: 0;
    margin-top: 10px;
}
.form-group{
    margin-bottom: 0px;
}
.tuipiao_message{
    width: 100%;
    height: 40px;
    line-height: 40px;
    display: flex;
    align-items: center;
    position: relative;
    margin-top: 50px;
    .base_span{
        width: 5px;
        height: 30px;
        background: #f89800;
    }
    p{
        color: #515a6e;
        font-size: 16px;
        font-weight: 600;
        margin-left: 10px;
    }
}


.downBtn{
    margin: 12px;
    border:none;
    background: #fff;
    border-radius: 2px;
    border: 1px solid #ccc;
    font-size: 12px;
    font-weight: 600;
    cursor: pointer;
}
.selectDisabled {
    .roo-input:disabled, .roo-input.disabled, .form-control:disabled, .form-control.disabled{
        color: #222;
        border: none;
        background: #fff;
        text-align: center;
        // font-weight: 500;
    }
}
.invoiceStatus{
    .roo-input:disabled, .roo-input.disabled, .form-control:disabled, .form-control.disabled{
        color: #222;
        border: none;
        background: #fff;
        text-align: center;
        font-weight: 500;
    }
}
button[disabled]{
    background: rgb(237, 236, 236);
}
.info{
  font-size: 24px;
  color: #ccc;
  margin-left: 5px;
  margin-right: 5px;
}

.infos{
  font-size: 24px;
  color: #ccc;
  margin-left: 5px;
}
.button{
  display: flex;
  align-items: center;
}

.filesBtn{
  display: flex;
  align-items: center;
  justify-content: flex-end;
  padding-right: 18px;
  input[type=file]{
    display: none;
    opacity: 0;
  }
  label{
    color: #222222;
    background-color: #ffcc33;
    border-color: #ffcc33;
    cursor: pointer;
    user-select: none;
    border: 1px solid transparent;
    padding: 0 20px;
    font-size: 14px;
    line-height: 34px;
    border-radius: 2px;
    transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    background-image: linear-gradient(to right, #FFE14D, #FFC34D);
    border: none;
  }
}

.subject_modal{
  .roo-modal-body{
    height: 110px;
  }
}
