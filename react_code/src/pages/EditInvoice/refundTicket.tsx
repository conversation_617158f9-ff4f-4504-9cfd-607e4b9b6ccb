import React, { useState, useEffect } from 'react';
import { Table, Input, Button, Toast, Selector } from '@roo/roo'
import './index.scss';
import request from '../../utils/request';
import { SelectorOption, SelectorValue,} from '@roo/roo/Selector';

interface IProps {
    getRefunticket(arg0: any[]): unknown;
    partnerId:number,
    partnerType:number,
}


const addressDatas:Array<AddressDatas> = [{
    id: 0,
    addressee: "",
    phoneNumber: "",
    provinceName: '',
    cityName: "",
    districtName: '',
    address: "",
    provinceCode:'',
    districtCode:'',
    cityCode:'',
}];

interface CityData {
    code:number,
    name:string
}

const FC: React.FC<IProps> = (props:IProps) => {
    const [addressData, setAddressData] = useState<Array<AddressDatas>>(addressDatas); // 录入退货地址模版
    const [province, setProvince] = useState<CityData[]>([]) // 所在地区省信息
    const [cityData, setCityData] = useState<CityData[]>([]) // 所在地区市信息
    const [county, setCounty] = useState<CityData[]>([]) // 所在地区县信息    
    const [isEdit,setEdit] = useState<boolean>(true) // 是否修改

    // 获取省地址数据
    const getCity = () => {
        request.get('/money/input/tax/invoice/queryRegion',{
            level:1,
        }).then((res) => {
             setProvince(res);
        }).catch((error) => {
          Toast.open({
            title: error.message || '发生未知错误',
            status: 'fail',
            position: 'topCenter',
          });
         });
    }

    // 获取默认数据
    const getDefault = () => {
            request.get('/money/input/tax/invoice/queryAddress',{
            partnerId:props.partnerId,
            partnerType:props.partnerType
        }).then((res) => {
          if (res.id) {
            console.log(res,'res...')
            setAddressData([res])
            props.getRefunticket([res])
            if(res.provinceCode){
                request.get('/money/input/tax/invoice/queryRegion',{
                    level:2,
                    parentRegionId:res.provinceCode
                }).then((res) => {
                    setCityData(res)
                }).catch((error) => {
                  Toast.open({
                    title: error.message || '发生未知错误',
                    status: 'fail',
                    position: 'topCenter',
                  });
                 });
            }
            if(res.cityCode){
                request.get('/money/input/tax/invoice/queryRegion',{
                    level:3,
                    parentRegionId:res.cityCode
                }).then((res) => {
                    setCounty(res)
                }).catch((error) => {
                  Toast.open({
                    title: error.message || '发生未知错误',
                    status: 'fail',
                    position: 'topCenter',
                  });
                 });
            }
          }
        }).catch((error) => {
          Toast.open({
            title: error.message || '发生未知错误',
            status: 'fail',
            position: 'topCenter',
          });
         });
    }
    useEffect(() => {
        getCity()
        getDefault()
    },[])

    // 省数据格式处理
    const optionsPro = province && province.map((items:CityData) => {
        return {
            value: items.code,
            label: items.name,
        }
    })
    // 退票地址信息
    const handleChange = (record: AddressDatas,raw:SelectorOption, val: SelectorValue, key: string) => {
        // 获取市数据
        if(key==='provinceCode'){
            record.cityCode = ''
            record.districtCode = ''
            record.provinceName = raw.label
            setAddressData([record])
            request.get('/money/input/tax/invoice/queryRegion',{
                level:2,
                parentRegionId:val
            }).then((res:any) => {
                setCityData(res)
            }).catch((error) => {
              Toast.open({
                title: error.message || '发生未知错误',
                status: 'fail',
                position: 'topCenter',
              });
             });
        }
        // 获取区数据
        if(key==='cityCode'){
            record.cityName = raw.label
            setAddressData([record])
            request.get('/money/input/tax/invoice/queryRegion',{
                level:3,
                parentRegionId:val
            }).then((res:any) => {
                setCounty(res)
            }).catch((error) => {
              Toast.open({
                title: error.message || '发生未知错误',
                status: 'fail',
                position: 'topCenter',
              });
             });
        }
        if (key === 'districtCode') {
            record.districtName = raw.label
            setAddressData([record])
        }
        const _addressData = addressData.map((item:AddressDatas) => {
            if (item.id === record.id) {
                return {
                    ...item,
                    [key]:val,
                };
            }
            return item;
        });
        setAddressData(_addressData);
    }

    const handleInputChange = (record: AddressDatas,val: React.ChangeEvent<HTMLInputElement>, key: string) => {
        const _addressDatas = addressData.map((item:AddressDatas) => {
            if (item.id === record.id) {
                return {
                    ...item,
                    [key]: val.target.value,
                };
            }
            return item;
        });
        setAddressData(_addressDatas);
    }
    // 市数据格式处理
    const optionsCity = cityData && cityData.map((items:CityData) => {
        return {
            value: items.code,
            label: items.name,
        }
    })
    // 区数据格式处理
    const optionsCount = county && county.map((items:CityData) => {
        return {
            value: items.code,
            label: items.name,
        }
    })
    // 表头样式必填样式
    const columsTh = (item: string) => {
        return (
            <div style={{ display: 'flex', marginLeft:"35%" }}>
                <span style={{ color: 'red' }}>*</span>
                <p style={{ marginLeft: "2px"}}>{item}</p>
            </div>
        )
    }

    // 退票地址表
    const addressDataColumns: any = [
        {
            prop: 'addressee',
            width: 100,
            renderTh: (() => (
                columsTh('收件人')
            )),
            render: (text: string, record: AddressDatas) => (
                <>
                    {
                         isEdit ? <p style={{textAlign:"center"}}>{text}</p> :  <Input value={text}  onChange={(val) => handleInputChange(record, val, 'addressee')} />
                    }
                </>
            )
        },
        {
            prop: 'phoneNumber',
            width: 100,
            renderTh: (() => (
                columsTh('联系电话')
            )),
            render: (text: string, record: AddressDatas) => (
                <>
                   {
                       isEdit ? <p style={{textAlign:"center"}}>{text}</p> :   <Input value={text}  onChange={(val) => handleInputChange(record, val,'phoneNumber')} />
                   }
                </>
            )
        },
        {
            prop: 'provinceCode',
            width: 100,
            renderTh: (() => (
                columsTh('省')
            )),
            render: (text: string, record: AddressDatas) => (
                <div>
                     <Selector
                        value={text}
                        options={optionsPro}
                        onChange={(val,raw) => handleChange(record,raw, val, 'provinceCode')}
                        // onChange={handleChanges}
                        placeholder="请选择省"
                        clearable={true}
                        disabled={isEdit}
                        showArrow={isEdit?false:true}
                        className='selectDisabled'
                    />
                </div>
            )
        },
        {
            prop: 'cityCode',
            width: 100,
            renderTh: (() => (
                columsTh('市')
            )),
            render: (text: string, record: AddressDatas) => (
                <div>
                     <Selector
                        value={text}
                        options={optionsCity}
                        onChange={(val,raw) => handleChange(record,raw, val, 'cityCode')}
                        placeholder="请选择市"
                        clearable={true}
                        disabled={isEdit}
                        showArrow={isEdit?false:true}
                        className='selectDisabled'
                    />
                </div>
            )
        },
        {
            prop: 'districtCode',
            width: 100,
            renderTh: (() => (
                columsTh('区（县）')
            )),
            render: (text: string, record: AddressDatas) => (
                <div>
                     <Selector
                        value={text}
                        options={optionsCount}
                        onChange={(val,raw) => handleChange(record,raw, val, 'districtCode')}
                        placeholder="请选择区（县）"
                        clearable={true}
                        disabled={isEdit}
                        showArrow={isEdit?false:true}
                        className='selectDisabled'
                    />
                </div>
            )
        },
        {
            prop: 'address',
            width: 100,
            renderTh: (() => (
                columsTh('详细地址')
            )),
            render: (text: string, record: AddressDatas) => (
                <>
                    {
                        isEdit ? <p style={{textAlign:"center"}}>{text}</p> : <Input value={text} onChange={(val) => handleInputChange(record,val, 'address')} />
                    }
                </>
                    
            )
        },
    ];
    // 非空校验
    const jiaoyan = (title:string) => {
        Toast.open({
            title: `${title}不可为空`,
            status: 'fail',
            position: 'topCenter',
        })
    }

    // 退票地址保存
    const save = () => {
        addressData.forEach((item:AddressDatas) => {
            if (item.addressee === '') {
                jiaoyan('收件人')
                return
            }
            if (item.phoneNumber === '') {
                jiaoyan('联系电话')
                return
            }
            if (item.provinceCode === '') {
                jiaoyan('省')
                return
            }
            if (item.cityCode === '') {
                jiaoyan('市')
                return
            }
            if (item.districtCode === '') {
                jiaoyan('区（县）')
                return
            }
            if (item.address === '') {
                jiaoyan('详细地址')
                return
            }
            request.post('/money/input/tax/invoice/submitAddress',{
                    partnerId:props.partnerId,
                    partnerType:props.partnerType,
                    // ...item
                    addressee: item.addressee,
                    phoneNumber: item.phoneNumber,
                    provinceName: item.provinceName,
                    provinceCode: item.provinceCode,
                    cityName: item.cityName,
                    cityCode: item.cityCode,
                    districtName: item.districtName,
                    districtCode: item.districtCode,
                    address: item.address,
            },{
                isJson: true
            }).then((res) => {
                setEdit(true)
                if (res.code === 0) {
                    Toast.open({
                        title:"操作成功",
                        status:'success',
                        position: 'topCenter',

                    })
                    props.getRefunticket([item])

                    // 通知第三方外层页面保存成功
                    window.parent.postMessage(JSON.stringify({commitType: 1}), '*')
                }
            }).catch((error) => {
              Toast.open({
                title: error.message || '发生未知错误',
                status: 'fail',
                position: 'topCenter',
              });
             });
        })
    };

    return (
        <div>
            <div className='tuipiao_message'>
                <span className='base_span'></span>
                <p>退票地址信息</p>
                <p style={{ fontSize: '12px', color: "#ccc" }}>(请仔细填写退票地址)</p>
            </div>
            <div className='write_table'>

                <Table
                    rowKey="id"
                    columns={addressDataColumns}
                    data={addressData}
                    scrollX={1200}
                />
                {
                    !isEdit ? <div className='save'>
                      <Button  type='brand-hollow' style={{marginRight: '12px'}} onClick={() => {
                        setEdit(true)
                      }}>取消</Button>
                      <Button  type='brand' onClick={() => {
                        save()
                      }}>保存</Button> 
                    </div>
                    : <Button  type='brand' className='save' onClick={() => {
                      setEdit(false)
                    }}>修改</Button>
                }

            </div>
        </div>
    )
}

export default FC;