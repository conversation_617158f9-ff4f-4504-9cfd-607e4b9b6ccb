import React, { useEffect, useState,useCallback,useMemo } from 'react';
import { RouteComponentProps } from 'react-router-dom';
import request from '../../utils/request';
import Invoice from './invoice'
import RefundTicket from './refundTicket'
import axios from 'axios';
import './index.scss';
import * as qs from 'query-string';


interface PartnerInfo {
    partnerId: number,
    partnerType:Array<number>,
    partnerName	:string
    /** 合作商类型 */
    partnerTypeName: string;
}

const FC: React.FC<RouteComponentProps> = (props:RouteComponentProps) => {
    const [queryHint, setQueryHint] = useState<string>('')
    const [queryPartnerInfo, setQueryPartnerInfo] = useState<PartnerInfo>()
    const [invoiceList, changeInvoiceList] = useState<InitData[]>([])
    const [refunticket, changeRefunticket] = useState<AddressDatas[]>([])
    const [displayInvoiceHandover, setDisplayInvoiceHandover] = useState(false);
    const propsLocation = props.location.search && qs.parse(props.location.search)
    const partnerId = propsLocation.bizAcctId;
    const partnerType = propsLocation.source;
    const payBillId = propsLocation.payBillId;

    // usecallback
    const getInvoiceList = useCallback((res:InitData[]) => {
        changeInvoiceList(res)
    },[])

    const getRefunticket = useCallback((res: AddressDatas[]) => {
        changeRefunticket(res)
    },[])
    // 获取发票录票、寄送及结算流程
    const getHint = () => {
        request.get('/money/input/tax/invoice/queryHint',{
            partnerType:partnerType
        }).then((res) => {
            setQueryHint(res);
        }).catch((error) => {
           console.log(error)
        });
    }

    // 下载发票交接单
    const downLoad = () => {
        axios({
            url: '/money/input/tax/invoice/exportHandOverDocument', //调用的接口，该接口返回文件流
            method: 'get',
            params: {
                //接口的参数
                payBillId:payBillId,
                partnerType:partnerType,
                partnerId:partnerId
            },
            responseType: 'blob', 
        }).then((response) => {
            const url = window.URL.createObjectURL(new Blob([response.data],{
                type:
                "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8"
            }));
            const filename =response.headers['content-disposition'].split('=')[1]
            // console.log(decodeURIComponent(filename),'lll')
            const link = document.createElement('a');
            link.href = url;
            link.setAttribute('download', decodeURIComponent(filename)); //交接单.word
            document.body.appendChild(link);
            link.click();
        }).catch((error) => {
            console.log(error)
         });
    }

    // 获取供应商信息
    const getPartnerInfo = () => {
        request.get('/money/input/tax/invoice/queryPartnerInfo',{
            partnerId:partnerId,
            partnerType:partnerType
        }).then((res) => {
            setQueryPartnerInfo(res)
        }).catch((error) => {
            console.log(error)
         });
    }

    useEffect(() => {
        getHint();
        getPartnerInfo();
        
        axios({
          url: '/money/input/tax/invoice/displayInvoiceHandover', //调用的接口，该接口返回文件流
          method: 'get',
          params: {
              partnerType:partnerType,
          }
        }).then(response => {
          setDisplayInvoiceHandover(response.data.data)
        }).catch((error) => {
          console.log(error)
        });
    },[])

    return (
        <div className='edit_invoice__wrapper'>
            <div className='invoice_message'>开票信息</div>
            <div className='invoice_main'>
                <div className='base_message'>
                    <span className='base_span'></span>
                    <p>基础信息</p>
                </div>
                <ul className='messages'>
                    <li>{`${queryPartnerInfo?.partnerTypeName}商ID : ${partnerId}`}</li>
                    <li>{`${queryPartnerInfo?.partnerTypeName}商名称 : ${queryPartnerInfo?.partnerName}`}</li>
                </ul>
                <div className='tuipiao_message' style={{marginTop:"24px"}}>
                    <span className='base_span'></span>
                    <p>发票录票、寄送及结算流程</p>
                </div>
                <div style={{padding:'12px',marginBottom:"20px"}} dangerouslySetInnerHTML={{__html:queryHint}}></div>
                <Invoice partnerId={partnerId} partnerType={partnerType} payBillId={payBillId} getInvoiceList={getInvoiceList} />
                <RefundTicket partnerId={partnerId} partnerType={partnerType} getRefunticket={getRefunticket} />
                {   
                    displayInvoiceHandover ?
                      <>
                          <div className='tuipiao_message'>
                              <span className='base_span'></span>
                              <p>发票交接单</p>
                              <p style={{ fontSize: '12px', color: "#ccc" }}>(请下载打印此交接单并与纸质发票一同邮寄)</p>
                          </div>
                          {
                              <button disabled={invoiceList.length && refunticket.length ? false : true} className='downBtn' onClick={() => {
                                  downLoad()
                              }}>下载发票交接单</button>
                          }
                    </> : null
                }
            </div>

        </div>
    )
}

export default FC;