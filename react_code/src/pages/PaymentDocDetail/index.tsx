import React, { useEffect, useState, useCallback,useMemo } from 'react'
import { RouteComponentProps } from 'react-router-dom';
import Table from '@roo/roo/Table';
import * as qs from 'query-string';
import request from '../../utils/request';
import { TspInputTaxInvoiceTypeEnum, InvoiceStatusEnum, ApplyStatusEnums } from '../../utils/enum';
import Grid from '@roo/roo/Grid';
import Panel from '@roo/roo/Panel';
import './index.scss'

const { Row, Col } = Grid;
interface MerchantDetail {
  partnerName:string,
}
interface PaymentDeatil {
  startDate: string,
  endDate:string,
  amount:string
}
interface InvoiceDetailAmount {
  invoiceAmountSum: string
}
interface TspInputTaxInvoiceApplyVo {
  taxDiff: string,
  confirmAmount: string,
  confirmMisId: string,
  confirmTime: string
}

interface PaymentInvoiceDeatil {
  invoiceType:number,
  invoiceNumber:string,
  invoiceCode:string,
  invoiceBillingDate:string,
  excludingTaxAmount:string,
  amount:string,
  invoiceStatus:string,
  partnerType:number
}

interface QueryApplyInfo {
  tspInputTaxBindInvoiceVoList:PaymentInvoiceDeatil[],
  tspInputTaxPartnerInfoVo:MerchantDetail,
  tspInputTaxPayBillInfoVo:PaymentDeatil,
  tspInputTaxInvoiceApplyVo:TspInputTaxInvoiceApplyVo
}
export default function PaymentDocDetail(props: RouteComponentProps) {
  const propsLocation = props.location.search && qs.parse(props.location.search);
  const [paymentInvoiceDeatil, setPaymentInvoiceDeatil] = useState<PaymentInvoiceDeatil[]>([]); // 发票详情
  const [merchantDetail, setMerchantDetail] = useState<MerchantDetail>({}); // 商详情
  const [paymentDeatil, setPaymentDeatil] = useState<PaymentDeatil>({}); // 付款单详情
  const [tspInputTaxInvoiceApplyVo, setTspInputTaxInvoiceApplyVo] = useState<TspInputTaxInvoiceApplyVo>({}); // 核销详情
  const [invoiceDetailAmount, setInvoiceDetailAmount] = useState<InvoiceDetailAmount>({}); // 发票详情价税 税差金额展示
  useEffect(() => {
    const { partnerType, applyId } = propsLocation
    request.get<{},QueryApplyInfo>('/money/input/tax/invoice/queryApplyInfo', { partnerType, applyId }).then((res) => {
      setInvoiceDetailAmount(res);
      setPaymentInvoiceDeatil(res.tspInputTaxBindInvoiceVoList);
      setMerchantDetail(res.tspInputTaxPartnerInfoVo);
      setPaymentDeatil(res.tspInputTaxPayBillInfoVo);
      setTspInputTaxInvoiceApplyVo(res.tspInputTaxInvoiceApplyVo);
    }).catch((error) => {
      console.log(error)
    })
  }, [])

  // 查看发票详情
  const viewInvoice = useCallback((record: PaymentInvoiceDeatil) => {
    const { invoiceNumber, invoiceCode, partnerType, invoiceStatus } = record;
    window.open(`/money/invoice-new-input-tax/invoiceDetail?invoiceNumber=${invoiceNumber}&invoiceCode=${invoiceCode}&partnerType=${partnerType}&invoiceStatus=${invoiceStatus}`)
  },[])

  const columns = useMemo(() => [
    { prop: 'invoiceType', label: '发票类型', render: (text:number, record:PaymentInvoiceDeatil) => <p>{TspInputTaxInvoiceTypeEnum[text]}</p> },
    { prop: 'invoiceNumber', label: '发票号码' },
    { prop: 'invoiceCode', label: '发票代码' },
    { prop: 'invoiceBillingDate', label: '开票日期' },
    { prop: 'excludingTaxAmount', label: '不含税金额' },
    { prop: 'amount', label: '价税合计金额' },
    { prop: 'invoiceStatus', label: '发票状态', render: (text:number, record:PaymentInvoiceDeatil) => <p>{InvoiceStatusEnum[text]}</p> },
    {
      prop: 'action', label: '操作', render: (text:string, record:PaymentInvoiceDeatil) => <>
        <a style={{ cursor: 'pointer', color: "#00ABE4" }} onClick={() => {
          viewInvoice(record)
        }}>查看</a>
      </>
    },
  ],[]);
  return (
    <div className='paymentDateil_wrap'>
      <h4>核销历史查询  &gt; 详情</h4>
      <div className="paymentDateil_merchant">
        <Panel
          title="商详情"
          bordered
        >
          <Row className='row'>
            <Col
              size="sm"
              span={2}
            >
              商名称
            </Col>
            <Col
              size="sm"
              span={2}
            >
              {merchantDetail.partnerName || '-'}
            </Col>
          </Row>
        </Panel>
      </div>
      <div className="paymentDateil_merchant">
        <Panel
          title="付款单信息"
          bordered
        >
          <Row className='row'>
            <Col
              size="sm"
              span={2}
            >
              付款单账期
            </Col>
            <Col
              size="sm"
              span={2}
            >
              {paymentDeatil.startDate || '-'} ~ {paymentDeatil.endDate || '-'}
            </Col>
          </Row>
          <Row className='row'>
            <Col
              size="sm"
              span={2}
            >
              付款单金额
            </Col>
            <Col
              size="sm"
              span={2}
            >
              {paymentDeatil.amount || '-'}
            </Col>
          </Row>
        </Panel>

      </div>
      <div className="invoice_detail">
      <Panel
          title="发票详情"
          bordered
        >
        <div className="invoice_detail_table">
          <Table
            rowKey="name"
            hover
            columns={columns}
            data={paymentInvoiceDeatil}
          />
        </div>
        <div className='amount_message'>
          <ul style={{ marginRight: "24px"}}>
            <li>发票价税合计总金额</li>
            <li style={{ marginLeft: "50px", color: "#00ABE4" }}>{invoiceDetailAmount.invoiceAmountSum || '-'} 元</li>
          </ul>
          <ul style={{ marginTop: "8px", marginRight: "24px"}}>
            <li>发票税差扣款总金额</li>
            <li style={{ marginLeft: "50px", color: "#00ABE4" }}>{tspInputTaxInvoiceApplyVo.taxDiff || '-'} 元</li>
          </ul>
        </div>
        </Panel>

      </div>
      {
        propsLocation.applyStatus == ApplyStatusEnums.WRITTEN_OFF && <div className="paymentDateil_merchant" style={{ marginTop: "66px" }}>
          <Panel
          title="核销详情"
          bordered
        >
          <Row className='row'>
            <Col
              size="sm"
              span={2}
            >
              总核销金额
            </Col>
            <Col
              size="sm"
              span={2}
              style={{ color: "#00ABE4" }}
            >
              {tspInputTaxInvoiceApplyVo.confirmAmount || '-'} 元
            </Col>
          </Row>
          <Row className='row'>
            <Col
              size="sm"
              span={2}
            >
              操作人
            </Col>
            <Col
              size="sm"
              span={2}
            >
              {tspInputTaxInvoiceApplyVo.confirmMisId || '-'}
            </Col>
          </Row>
          <Row className='row'>
            <Col
              size="sm"
              span={2}
            >
              核销时间
            </Col>
            <Col
              size="sm"
              span={2}
            >
              {tspInputTaxInvoiceApplyVo.confirmTime || '-'}
            </Col>
          </Row>
          </Panel>
        </div>
      }
    </div>
  )
}
