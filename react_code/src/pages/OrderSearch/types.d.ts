declare namespace MyTypes {

  interface PromotionInfo {
    remark: string
  }
  interface OrderInfo {
    wmOrderViewId: number,
    wmOrderId: number,
    orderTime: string,
    status: string,
    wmPoiName: string,
    wmPoiId: string,
    wmPoiCity: string,
    totalPrice: number,
    shippingFee: number,
    userPayTotalAmount: number,
    totalPromotion: number,
    promotionInfo:PromotionInfo[],
  }
  interface LogisticsFeeInfo {
    mainFeeMode: string | number,
    mainFeeModeName: string,
    mainLogisticsCode: string,
    mainLogisticsCodeName: string,
    techFee: string,
    amount: string,
    field_6: string,
    perfGoodsPriceFee: PerfDistanceFeeData | any,
    divideRate: string,
    amount: string,
    discountAmount: string,
    discountDivideRate: string,
    perfCapRate: string,
    discountTechRate: string,
    specialTechRate: string,
    discountPerfRate: string,
    specialPerfRate: string,
    perfPrice: string,
    perfDistanceDetail: string,
    perfTimePlanDetail: string,
    perfWeightPlanDetail: string,
    perfAdjustPlanDetail: string,
    perfCategoryPlanDetail: string,
    discountTechFee: string,
    perfDistanceFee: string,
    perfGoodsPriceFee: string,
    perfTimeFee: string
    discountEndtime: number,
    platformFee: string,
  }
  interface StepPriceDetailList {
    title: string,
    comment:string
  }
  interface CommissionDataList {
    title: string,
    info: string,
    amount: string,
    startPrice: string | number,
    startPriceComment: string | number,
    stepPrice: string | number,
    stepPriceComment: string | number,
    stepPriceDetailList: StepPriceDetailList[],
    count: number,
    isShowDetail: number,
    techAllowanceDetailList: string | number,
    agreeAllowanceDetailList: string | number,
    originalAmount: string | number
  }
  interface SubDataList {
    totalAmount: string
    name: string,
    count: number,
    tags: string | number,
    info: string,
    originalInfo: string,
    remark: string,
    originalRemark: string,
    skuDetail: string | number,
    commissionDataList: CommissionDataList[],
    weight: string | number,
    isShowDetail: number
  }
  interface DataList {
    title: string,
    icon: string,
    totalAmount: string,
    subDataList: SubDataList[],
    originTotalAmount: string | number,
    remark: string,
    version: number
  }
  interface BillChargeInfo {
    chargeTypeCode: number,
    settleState: number,
    isPhf: boolean,
    wmOrderViewId: string,
    wmOrderViewId: string,
    wmOrderDaySeq: number,
    wmOrderSubmittedDate: string,
    wmOrderCompletedDate: string,
    wmOrderRefundedDate: string | number,
    wmOrderCanceledDate: string | number,
    wmOrderCompensatedDate: string | number,
    wmOrderShippingType: string,
    wmOrderPayType: string,
    amount: number,
    estimateAmount: number,
    dailyBillDate: string,
    dataList: DataList[],
    other: string,
    ctime: string,
    chargeAlias: string | number,
    orderSeqComment: string | number,
    chargeModel: number,
    nestDataList: Array[],
    associateOrderSeq: string | number,
    settleNegativeType: number,
    settleNegativeTypeName: string | number
  }
  interface InitFee {
    beginDistance: number,
    endDistance: number,
    fee: number,
    beginPrice: number,
    endPrice: number,
  }
  interface PerfDistanceFeeData {
    initFee: InitFee,
    beginDistance: number,
    endDistance: number,
    step: number,
    stepFee: number,
    beginPrice: number,
    endPrice: number,
    maxFee: number,
    fee: number,
    beginTime: string,
    endTime:string,
    stepPrice: number,
    price:number,
    beginWeight: number,
    endWeight: number,
    beginDate: number,
    endDate: number
  }

  interface GetRelus {
    title: string,
    comment: string
  }

  interface IsShowDetail{
    name: string,
    isShowDetail:number,
    couponDetail:{couponName: string,couponDesc: string},
    remark: string
  }

  interface Data {
    billChargeInfo: BillChargeInfo,
    orderInfo: OrderInfo,
    logisticsFeeInfo: LogisticsFeeInfo,
    msg: string
  }

  interface PpromotionInfos {
    totalPrice: number;
    shippingFee: number;
    fee: string;
    userPayTotalAmount: number;
  }
}