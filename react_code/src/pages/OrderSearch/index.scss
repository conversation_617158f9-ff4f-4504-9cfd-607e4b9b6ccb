.orderWrap {
  // width: 100%;
  height: 100%;
  overflow: hidden;
  padding: 24px;
}

.orderTop {
  width: 100%;
  padding: 24px;
  margin-top: 24px;
  background: #fff;
}

.searchRow {
  display: flex;
  align-items: center;
}

.orderMain {
  width: 100%;
  padding: 24px;
  margin-top: 24px;
  background: #fff;
}

.orderMessageTitle {
  width: 100%;
  display: flex;
  align-items: center;

  i {
    width: 4px;
    height: 17px;
    background: #FFCC33;
    display: inline-block;
  }

  p {
    margin-left: 8px;
    font-weight: 500;
    font-family: PingFangSC-Medium;
    font-size: 16px;
    color: #222222;
  }
}

.orderMessageDetail {
  margin-top: 12px;
  margin-left: 8px;
  margin-bottom: 48px;
}

.orderMessagLable {
  font-weight: 400;
  font-family: PingFangSC-Regular;
  font-size: 14px;
  color: #666666;
}

.orderMessagValue {
  font-weight: 400;
  font-family: PingFangSC-Regular;
  font-size: 14px;
  color: #222222;
}

.baseMessage {
  margin-top: 24px;
  font-weight: 500;
  font-family: PingFangSC-Medium;
  font-size: 16px;
  color: #222222;
  margin-left: 8px;
}

.detailCart_titles {
  font-weight: 500;
  font-family: PingFangSC-Medium;
  font-size: 16px;
  color: #222222;
}

.serviceFee {
  width: 100%;
  display: flex;
  margin-top: 24px;
  align-items: center;
}

.serviceFeeTitle {
  font-weight: 500;
  font-family: PingFangSC-Medium;
  font-size: 16px;
  color: #222222;
}

.serviceFeeMessage {
  font-weight: 400;
  font-family: PingFangSC-Regular;
  font-size: 14px;
  color: #666666;
  margin-left: 8px;
}

.distanceCharge {
  font-weight: 400;
  font-family: PingFangSC-Regular;
  font-size: 14px;
  color: #222222;
  margin-top: 16px;
}

.orderDetailChargeTable {
  margin-top: 10px;
}

.charDetailTable {
  margin-top: 10px;
  margin-left: 8px;
}


.noData {
  width: 100%;
  height: 180px;
  background: #fff;
  margin-top: 24px;
  text-align: center;
  line-height: 180px;

  p {
    font-weight: 500;
    font-family: PingFangSC-Medium;
    font-size: 14px;
    color: #222222;
    letter-spacing: 0;
  }
}

.detailCart {
  width: 30%;
  padding: 24px;
  background: #F5F6FA;
  border-radius: 20px;
  margin-top: 6px;
  position: relative;
  margin-top: 15px;
}

.detailCart_title {
  display: flex;
  align-items: center;
}

.detailCart_amount {
  position: absolute;
  right: 24px;
  margin-top: 10px;
  font-weight: 500;
  font-family: PingFangSC-Medium;
  font-size: 16px;
  color: #222222;
}

.detailCart_info {
  display: flex;
  margin-top: 16px;
  position: relative;
  align-items: center;
}

.detailCart_info_name {
  font-weight: 500;
  font-family: PingFangSC-Medium;
  font-size: 16px;
  color: #222222;
  align-items: center;
}

.detailCart_info_count {
  position: absolute;
  left: 50%;
}

.detailCart_info_amount {
  position: absolute;
  right: 0px;
  margin-top: 10px;
  font-weight: 500;
  font-family: PingFangSC-Medium;
  font-size: 16px;
  color: #222222;
}
.detailCart_info_box{
  position: absolute;
  right: 0px;
  top: -22px;
  margin-top: 10px;
  font-weight: 500;
  font-family: PingFangSC-Medium;
  font-size: 16px;
  color: #222222;
}
.detailCart_info_remark {
  color: #666;
  margin-top: 6px;
}

.distribution_name {
  font-weight: 400;
  font-family: PingFangSC-Regular;
  font-size: 14px;
  color: #222222;
  margin-top: 12px;
  margin-left: 8px;
}

.distribution_table {
  margin-top: 10px;
  margin-left: 8px;
  margin-bottom: 24px;
  table {
    width: 100%;
    border: 1px solid #eee;
    font-weight: 400;
    font-family: PingFangSC-Regular;
    font-size: 14px;
    letter-spacing: 0;
  }
  table thead{
    background: #F5F5F5;
    height: 49px;
    color: #666;
  }
  table th,table td{
    // height: 49px;
    border: 1px solid #eee;
    // margin:2px;
    padding: 12px;
    text-align: left;
}
}
.roo-table thead{
  background-color: #F5F5F5;
}
.commission{
  display: flex;
  text-align: center;
  font-family: PingFangSC-Regular;
  margin-top: 12px;
  // align-items: center;
}

.size_box{
  font-weight: 500;
  font-family: PingFangSC-Medium;
  font-size: 16px;
  color: #222222;
  margin-top: -12px;
}

.active_remark{
  margin-top: 6px;
  color: #666666;
  font-size: 14px;
}
.active_name{
  font-weight: 500;
  font-family: PingFangSC-Medium;
  font-size: 14px;
  color: #222222;
}
.commission_info{
  margin-left: 12px;
  color: #666666;
}
.commission_icon{
  margin-left: 3px;
  margin-right: 3px;
  color: #666;
  cursor: pointer;
}

.prices{
  display: flex;
  align-items: center;
  position: relative;
  font-weight: 500;
  font-family: PingFangSC-Medium;
  font-size: 14px;
  color: #222222;
}
.price_num{
  position: absolute;
  right: 24px;
}
.step_wrap{
  width: 100%;
  padding: 12px;
  background: #F5F6FA;
  border-radius: 10px;
  margin-top: 12px;
}

.fee{
  display: flex;
  align-items: center;
  margin-top: 8px;
  flex-wrap: wrap;
}
.text {
  width: 100%;
  font-size: 14px;
   color: #666;
   margin-top: 8px; 
}