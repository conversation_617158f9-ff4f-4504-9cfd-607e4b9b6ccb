import React, { useState, useEffect, useCallback, useRef } from 'react'
import './index.scss';
import Form from '@roo/roo/Form';
import Grid from '@roo/roo/Grid';
import Button from '@roo/roo/Button';
import Table from '@roo/roo/Table';
import Loading from '@roo/roo/Loading';
import Icon from '@roo/roo/Icon';
import Modal from '@roo/roo/Modal';
import Toast from '@roo/roo/Toast';
import { Alert } from '@roo/roo';
import queryString from 'query-string';
import request from '../../utils/request';
import { formatAmountToString } from '../../utils'
import { columns, perColumns, timeColumns, varietyColumns, amountColumns, activeColumns, perfDistanceDetailcolumns, perfTimePlanDetailcolumns, perfWeightPlanDetailcolumns, perfAdjustPlanDetailcolumns, perfCategoryPlanDetailcolumns } from './columns'
import moment from 'moment';

const { Row, Col } = Grid;
const Field = Form.field;
const { confirm } = Modal;
export default function index() {
  const [orderInfo, setOrderInfo] = useState<MyTypes.OrderInfo>({}); // 订单信息
  const [logisticsFeeInfo, setLogisticsFeeInfo] = useState<MyTypes.LogisticsFeeInfo>({}); // 配送相关信息
  const [isLoading, setIsLoading] = useState<boolean>(false); // 查询loading
  const [billChargeInfo, setBillChargeInfo] = useState<MyTypes.BillChargeInfo>({}); // 原订单卡片信息
  const [perfDistanceFeeData, setPerfDistanceFeeData] = useState<MyTypes.PerfDistanceFeeData[]>([]); // 距离收费表格数据
  const [perfGoodsPriceFeeData, setPerfGoodsPriceFeeData] = useState<MyTypes.PerfDistanceFeeData[]>([]); // 价格收费表格数据
  const [perfTimeFeeData, setPerfTimeFeeData] = useState<MyTypes.PerfDistanceFeeData[]>([]); // 时间收费表格数据
  const [perfDistanceDetailData, setPerfDistanceDetailData] = useState<MyTypes.PerfDistanceFeeData[]>([]); // 企客-距离收费
  const [perfTimePlanDetailData, setPerfTimePlanDetailData] = useState<MyTypes.PerfDistanceFeeData[]>([]); // 企客-时段收费
  const [perfWeightPlanDetailData, setPerfWeightPlanDetailData] = useState<MyTypes.PerfDistanceFeeData[]>([]); // 企客-重量收费
  const [perfAdjustPlanDetailData, setPerfAdjustPlanDetailData] = useState<MyTypes.PerfDistanceFeeData[]>([]); // 企客-节假日/大促收费
  const [perfCategoryPlanDetailData, setPerfCategoryPlanDetailData] = useState<MyTypes.PerfDistanceFeeData[]>([]); // 企客-品类收费
  const [promotionInfo, setPromotionInfo] = useState<MyTypes.PpromotionInfos>({}); // 用户支付金额
  const formRef = useRef<Form>();
  const cols = {
    label: { span: 4 },
    field: { span: 8 }
};
  // 订单号校验
  const rules = {
    wmOrderViewId: {
      required: true,
      message: '订单号不可为空',
      trigger: 'onBlur',
    },
  };
  // form 表单初始值
  const formValue = {
    wmOrderViewId: ''
  }

  useEffect(() => {
    const parsed = queryString.parse(window.location.search);
    if (parsed.wmOrderViewId) {
        if(formRef?.current){
            formRef?.current?.setFieldValue('wmOrderViewId', parsed.wmOrderViewId);
        }
        handleSelect({ wmOrderViewId: parsed.wmOrderViewId });
    }
  }, []);

  // 查询
  const handleSelect = useCallback((value: any) => {
    if (!value.wmOrderViewId) {
      return
    }
    setIsLoading(true);
    request.get<{}, MyTypes.Data>('/xianfuBill/poiBillCharge/billChargeDetailForPR', (value)).then((res) => {
      setIsLoading(false);
      if (res.msg) {
        Toast.open({
          title: `${res.msg}`,
          status: 'fail',
          position: 'topCenter',
        });
        return
      }
      setOrderInfo(res.orderInfo);
      setLogisticsFeeInfo(res.logisticsFeeInfo);
      setBillChargeInfo(res.billChargeInfo);
      // 距离收费表格数据
      const perfDistanceFeeData = res.logisticsFeeInfo.perfDistanceFee && JSON.parse(res.logisticsFeeInfo.perfDistanceFee);
      setPerfDistanceFeeData(perfDistanceFeeData && [perfDistanceFeeData.initFee].concat(perfDistanceFeeData.phases));
      // 价格收费表格数据
      const perfGoodsPriceFeeData = res.logisticsFeeInfo.perfGoodsPriceFee && JSON.parse(res.logisticsFeeInfo.perfGoodsPriceFee);
      setPerfGoodsPriceFeeData(perfGoodsPriceFeeData && [perfGoodsPriceFeeData.initFee].concat(perfGoodsPriceFeeData.phases));
      // 时间收费表格数据
      const perfTimeFeeData = res.logisticsFeeInfo.perfTimeFee && JSON.parse(res.logisticsFeeInfo.perfTimeFee);
      setPerfTimeFeeData(perfTimeFeeData && perfTimeFeeData.phases);

      // 企客 - 距离收费
      const perfDistanceDetailData = res.logisticsFeeInfo.perfDistanceDetail && JSON.parse(res.logisticsFeeInfo.perfDistanceDetail);
      setPerfDistanceDetailData(perfDistanceDetailData);
      // 企客 - 时段收费
      const perfTimePlanDetailData = res.logisticsFeeInfo.perfTimePlanDetail && JSON.parse(res.logisticsFeeInfo.perfTimePlanDetail);
      setPerfTimePlanDetailData(perfTimePlanDetailData);
      // 企客 - 重量收费
      const perfWeightPlanDetailData = res.logisticsFeeInfo.perfWeightPlanDetail && JSON.parse(res.logisticsFeeInfo.perfWeightPlanDetail);
      setPerfWeightPlanDetailData(perfWeightPlanDetailData);
      // 企客 - 节假日/大促收费
      const perfAdjustPlanDetailData = res.logisticsFeeInfo.perfAdjustPlanDetail && JSON.parse(res.logisticsFeeInfo.perfAdjustPlanDetail);
      setPerfAdjustPlanDetailData(perfAdjustPlanDetailData);
      // 企客 - 品类收费
      const perfCategoryPlanDetailData = res.logisticsFeeInfo.perfCategoryPlanDetail && JSON.parse(res.logisticsFeeInfo.perfCategoryPlanDetail);
      setPerfCategoryPlanDetailData(perfCategoryPlanDetailData);


      // 用户支付金额
      const promotionInfo = {
        totalPrice: res.orderInfo.totalPrice,
        shippingFee: res.orderInfo.shippingFee,
        fee: `${res.orderInfo.totalPromotion}   ${res.orderInfo.promotionInfo ? res.orderInfo.promotionInfo.map((item, index) => `[${index + 1}]   ${item.remark}`) : ''}`,
        userPayTotalAmount: res.orderInfo.userPayTotalAmount
      }
      setPromotionInfo(promotionInfo)
    }).catch((error) => {
      console.log(error)
    }).finally(() => {
      setIsLoading(false);
    });
  }, [orderInfo, logisticsFeeInfo, isLoading, billChargeInfo, perfDistanceFeeData, perfGoodsPriceFeeData, perfTimeFeeData, perfDistanceDetailData, perfTimePlanDetailData, perfWeightPlanDetailData, perfAdjustPlanDetailData, perfCategoryPlanDetailData, promotionInfo]);
  // 获取 ？规则
  const getRelus = (its: MyTypes.CommissionDataList) => {
    confirm({
      title: `${its.title}计价规则`,
      children: <div>
        <div className='prices'>
          <p>起步价</p>
          <p className='price_num'>{its.startPrice}</p>
        </div>
        <div className='prices'>
          <p>距离阶梯加价</p>
          <p className='price_num'>{its.stepPrice}</p>
        </div>
        {
          its.stepPriceDetailList && its.stepPriceDetailList.map((el: MyTypes.GetRelus, i: number) => {
            return <div key={i} className='step_wrap'>
              <p className='step_wrap-title'>{el.title}</p>
              <p className='step_wrap_comment'>{el.comment}</p>
            </div>
          })
        }
      </div>,
      status: 'success',
      confirmText: "我知道了",
      onConfirm: () => { }
    });
  }
  // 价格收费
  const maxAmount = logisticsFeeInfo.perfGoodsPriceFee && JSON.parse(logisticsFeeInfo.perfGoodsPriceFee + '');
  // 判断返回数据是否有值
  const isData = Object.keys(orderInfo).length && Object.keys(logisticsFeeInfo).length && Object.keys(billChargeInfo).length
  // 不同费率模式 (外卖2.0 企客1.0 企客2.0)
  const feeModal = logisticsFeeInfo.mainFeeMode === 4 || logisticsFeeInfo.mainFeeMode === 5 || logisticsFeeInfo.mainFeeMode === 9;
  return (
    <div className='orderWrap'>
      <Alert
          title="查询范围：仅外卖直营订单，包括新费率2.0、TR模式、企客、企客新收费模式。暂不支持费率1.0、代理、拼好饭订单"
          type="warning"
          icon="roo-icon-info-circle"
      />
      <div className="orderTop">
        <Form
          value={formValue}
          colon
          cols={cols}
          rules={rules}
          onSubmit={(value, errors) => {
            handleSelect(value)
          }}
          ref={formRef}
        >
          {
            ({
              values, errors, submitForm, resetForm
            }) => (
              <form>
                <Row className='searchRow'>
                  <Col span={6} style={{marginLeft:"-7%"}}>
                    <Field 
                      label="订单展示ID"
                      name="wmOrderViewId"
                      placeholder='请输入订单展示id查询/订单id查询'
                    />
                  </Col>
                  <Col span={6}>
                    <Button style={{  marginTop: '-13px' }} onClick={submitForm}>
                      查询
                    </Button>
                    <Button type='hollow' style={{ marginLeft: '8px', marginTop: '-13px' }} onClick={() => {
                      resetForm()
                      setOrderInfo({})
                      setBillChargeInfo({})
                      setLogisticsFeeInfo({})
                    }}>
                      重置
                    </Button>
                  </Col>
                </Row>
              </form>
            )
          }
        </Form>
        <Loading
          fullscreen
          visible={isLoading}
        />
      </div>
      {
        isData ? <div className="orderMain">
          <div className="orderMessage">
            <div className="orderMessageTitle">
              <i></i>
              <p>订单信息</p>
            </div>
            <div className="orderMessageDetail">
              <Row>
                <Col
                  size="sm"
                  span={1}
                  className='orderMessagLable'
                >
                  订单号
                </Col>
                <Col
                  size="sm"
                  span={3}
                  className='orderMessagValue'
                >
                  {orderInfo.wmOrderId || '-'}
                </Col>
                <Col
                  size="sm"
                  span={1}
                  className='orderMessagLable'
                >
                  下单时间
                </Col>
                <Col
                  size="sm"
                  span={3}
                  className='orderMessagValue'
                >
                  {orderInfo.orderTime || '-'}
                </Col>
                <Col
                  size="sm"
                  span={1}
                  className='orderMessagLable'
                >
                  订单展示号
                </Col>
                <Col
                  size="sm"
                  span={3}
                  className='orderMessagValue'
                >
                  {orderInfo.wmOrderViewId || '-'}
                </Col>
              </Row>
              <Row style={{ marginTop: "16px" }}>
                <Col
                  size="sm"
                  span={1}
                  className='orderMessagLable'
                >
                  订单状态
                </Col>
                <Col
                  size="sm"
                  span={3}
                  className='orderMessagValue'
                >
                  {orderInfo.status || '-'}
                </Col>
                <Col
                  size="sm"
                  span={1}
                  className='orderMessagLable'
                >
                  配送方式
                </Col>
                <Col
                  size="sm"
                  span={3}
                  className='orderMessagValue'
                >
                  {billChargeInfo.wmOrderShippingType || '-'}
                </Col>
              </Row>
            </div>
            <div className="orderMessageTitle">
              <i></i>
              <p>商家信息</p>
            </div>
            <p className='baseMessage'>基础信息</p>
            <div className="orderMessageDetail">
              <Row>
                <Col
                  size="sm"
                  span={1}
                  className='orderMessagLable'
                >
                  商家ID
                </Col>
                <Col
                  size="sm"
                  span={3}
                  className='orderMessagValue'
                >
                  {orderInfo.wmPoiId || '-'}
                </Col>
                <Col
                  size="sm"
                  span={1}
                  className='orderMessagLable'
                >
                  商家名称
                </Col>
                <Col
                  size="sm"
                  span={3}
                  className='orderMessagValue'
                >
                  {orderInfo.wmPoiName || '-'}
                </Col>
                <Col
                  size="sm"
                  span={1}
                  className='orderMessagLable'
                >
                  主配费率模式
                </Col>
                <Col
                  size="sm"
                  span={3}
                  className='orderMessagValue'
                >
                  <p>{logisticsFeeInfo.mainFeeModeName || '-'}</p>
                  {
                    // TR :平台服务费  分成（divideRate）、保底（amount）  配送方式后端做
                    logisticsFeeInfo.mainFeeMode === 1 && (logisticsFeeInfo.platformFee || logisticsFeeInfo.divideRate || logisticsFeeInfo.amount) && <div className='fee'>
                      ({logisticsFeeInfo.platformFee && <p>平台服务费：{logisticsFeeInfo.platformFee}</p>}&nbsp;&nbsp;{logisticsFeeInfo.divideRate && <p>分成：{logisticsFeeInfo.divideRate}</p>}&nbsp;&nbsp;{logisticsFeeInfo.amount && <p>保底：{logisticsFeeInfo.amount}</p>})
                    </div>
                  }
                  {
                    // 外卖费率2.0 : 保底、技术服务费（佣金）、履约服务费收费封顶比例(先不加)	、 
                    // 先不加：技术服务费优惠折扣、履约服务费优惠折扣、技术服务费特批折扣、履约服务费特批折扣
                    // 展示： 佣金（techFee）、保底（amount）、佣金优惠discountTechFee + 到期时间discountEndtime
                    logisticsFeeInfo.mainFeeMode === 4 && (logisticsFeeInfo.techFee || logisticsFeeInfo.amount || logisticsFeeInfo.discountTechRate) && <div className='fee'>
                      ({logisticsFeeInfo.techFee && <p>佣金：{logisticsFeeInfo.techFee}</p>}&nbsp;&nbsp;{logisticsFeeInfo.amount && <p>保底：{logisticsFeeInfo.amount}</p>}&nbsp;&nbsp;{logisticsFeeInfo.discountTechRate && <p>佣金优惠折扣系数：{logisticsFeeInfo.discountTechRate}</p>} {logisticsFeeInfo.discountEndtime && <p>(到期时间：{moment(logisticsFeeInfo.discountEndtime * 1000).format('yyyy-MM-DD')})</p>})
                    </div>
                  }
                  {
                    // 企客: 技术服务费（佣金）、履约服务费基础配送费（先不加）、
                    // 优惠技术服务费 = 佣金优惠discountTechFee discountEndtim、（todo: 不知道该字段名 先不加）特批技术服务费
                    // 展示： 技术服务费（佣金）、优惠技术服务费 = 佣金优惠discountTechFee + 到期时间discountEndtim
                    logisticsFeeInfo.mainFeeMode === 5 && (logisticsFeeInfo.techFee || logisticsFeeInfo.discountTechFee) && <div className='fee'>
                      ({logisticsFeeInfo.techFee && <p>佣金：{logisticsFeeInfo.techFee}</p>}&nbsp;&nbsp;{logisticsFeeInfo.discountTechFee && <p>优惠佣金：{logisticsFeeInfo.discountTechFee}</p>}&nbsp;&nbsp;{logisticsFeeInfo.discountEndtime && <p>(到期时间：{moment(logisticsFeeInfo.discountEndtime * 1000).format('yyyy-MM-DD')})</p>})
                    </div>
                  }
                  {
                    // 企客标准化: 保底、技术服务费（佣金）、履约服务费基础配送费（先不加）、
                    // 技术服务费优惠折扣（先不加）、技术服务费特批折扣（先不加）
                    // 展示：保底、技术服务费（佣金）、
                    logisticsFeeInfo.mainFeeMode === 9 && (logisticsFeeInfo.amount || logisticsFeeInfo.techFee) && <div className='fee'>
                      ({logisticsFeeInfo.amount && <p>保底：{logisticsFeeInfo.amount}</p>}&nbsp;&nbsp;{logisticsFeeInfo.techFee && <p>佣金：{logisticsFeeInfo.techFee}</p>})
                    </div>
                  }
                </Col>
              </Row>
              <Row style={{ marginTop: "16px" }}>
                <Col
                  size="sm"
                  span={1}
                  className='orderMessagLable'
                >
                  配送方式
                </Col>
                <Col
                  size="sm"
                  span={3}
                  className='orderMessagValue'
                >
                  {logisticsFeeInfo.mainLogisticsCodeName || '-'}
                </Col>
                <Col
                  size="sm"
                  span={1}
                  className='orderMessagLable'
                >
                  城市
                </Col>
                <Col
                  size="sm"
                  span={3}
                  className='orderMessagValue'
                >
                  {orderInfo.wmPoiCity || '-'}
                </Col>
              </Row>

              {feeModal && <><div className='serviceFee'>
                <p className='serviceFeeTitle'>配送服务费</p>
              </div>
                {logisticsFeeInfo.perfPrice && <p className='text'>基础配送费{logisticsFeeInfo.perfPrice}元/单</p>}
              </>}
              {
                // 外卖费率 2.0
                logisticsFeeInfo.mainFeeMode === 4 && <div>
                  {
                    perfDistanceFeeData && <div>
                      <p className='distanceCharge'>距离收费</p>
                      <div className="orderDetailChargeTable">
                        <Table
                          border
                          rowKey="beginDistance"
                          columns={columns}
                          data={perfDistanceFeeData}
                        />
                      </div>
                    </div>
                  }

                  {
                    perfGoodsPriceFeeData && <div>
                      <p className='distanceCharge'>价格收费（封顶金额：{maxAmount ? maxAmount.maxFee : '-'}元）</p>
                      <div className="orderDetailChargeTable">
                        <Table
                          border
                          rowKey="beginDistance"
                          columns={perColumns}
                          data={perfGoodsPriceFeeData}
                        />
                      </div>
                    </div>
                  }

                  {
                    perfTimeFeeData && <div>
                      <p className='distanceCharge'>时段收费</p>
                      <div className="orderDetailChargeTable">
                        <Table
                          border
                          rowKey="beginTime"
                          columns={timeColumns}
                          data={perfTimeFeeData}
                        />
                      </div>
                    </div>
                  }
                </div>
              }
              {
                // 企客模式
                (logisticsFeeInfo.mainFeeMode === 5 || logisticsFeeInfo.mainFeeMode === 9) && <div>

                  {perfDistanceDetailData && <div>
                    <p className='distanceCharge'>距离收费</p>
                    <div className="orderDetailChargeTable">
                      <Table
                        border
                        rowKey="beginDistance"
                        columns={perfDistanceDetailcolumns}
                        data={perfDistanceDetailData}
                      />
                    </div>
                  </div>}
                  {
                    perfTimePlanDetailData && <div>
                      <p className='distanceCharge'>时段收费</p>
                      <div className="orderDetailChargeTable">
                        <Table
                          border
                          rowKey="beginTime"
                          columns={perfTimePlanDetailcolumns}
                          data={perfTimePlanDetailData}
                        />
                      </div>
                    </div>
                  }
                  {
                    perfWeightPlanDetailData && <div>
                      <p className='distanceCharge'>重量收费</p>
                      <div className="orderDetailChargeTable">
                        <Table
                          border
                          rowKey="beginWeight"
                          columns={perfWeightPlanDetailcolumns}
                          data={perfWeightPlanDetailData}
                        />
                      </div>
                    </div>
                  }
                  {
                    perfAdjustPlanDetailData && <div>
                      <p className='distanceCharge'>节假日/大促收费</p>
                      <div className="orderDetailChargeTable">
                        <Table
                          border
                          rowKey="beginDate"
                          columns={perfAdjustPlanDetailcolumns}
                          data={perfAdjustPlanDetailData}
                        />
                      </div>
                    </div>
                  }
                  {
                    perfCategoryPlanDetailData && <div>
                      <p className='distanceCharge'>品类收费</p>
                      <div className="orderDetailChargeTable">
                        <Table
                          border
                          rowKey="categoryName"
                          columns={perfCategoryPlanDetailcolumns}
                          data={perfCategoryPlanDetailData}
                        />
                      </div>
                    </div>
                  }
                </div>
              }
              {logisticsFeeInfo.discountPerfRate && feeModal && <p className='text'>配送服务费优惠折扣系数{logisticsFeeInfo.discountPerfRate}</p>}
              {logisticsFeeInfo.perfCapRate && feeModal && <div className='text'>配送服务费收费封顶比例{logisticsFeeInfo.perfCapRate}%</div>}

            </div>
            <div className="orderMessageTitle" style={{ marginTop: '-24px' }}>
              <i></i>
              <p>计费详情</p>
            </div>
            {
              billChargeInfo.dataList && billChargeInfo.dataList.map((item, index) => {
                item.subDataList && item.subDataList.forEach((items) => { items.totalAmount = item.totalAmount })
                return <div key={index}>
                  {
                    (item.title.indexOf('商品总价', 0) > -1) && item.subDataList && <div>
                      <p className='baseMessage'>{item.title}</p>
                      <div className="charDetailTable">
                        <Table
                          border
                          rowKey="name"
                          columns={varietyColumns}
                          data={item.subDataList}
                        />
                      </div>
                    </div>
                  }
                  {
                    (item.title.indexOf('活动支出', 0) > -1 || item.title.indexOf('商家对顾客的活动补贴', 0) > -1) && item.subDataList && <div>
                      <p className='baseMessage'>{item.title}</p>
                      <div className="charDetailTable">
                        <Table
                          border
                          rowKey="name"
                          columns={activeColumns}
                          data={item.subDataList}
                        />
                      </div>
                    </div>
                  }
                  {
                    (item.title.indexOf('平台', 0) > -1 || item.title.indexOf('配送', 0) > -1 || item.title.indexOf('佣金', 0) > -1) && item.subDataList  && <div>
                      {item.version === 0 && <p className='baseMessage'>{item.title}</p>}
                      {
                        item.subDataList && item.subDataList.map((it, ind) => {
                          return <div key={ind}>
                            <p className={item.version === 1 ? 'baseMessage' : 'distribution_name'}>{it.name}</p>
                            <div className="distribution_table">
                              <table>
                                <thead>
                                  <tr>
                                    <th>计算公式</th>
                                    <th>{it.name}</th>
                                  </tr>
                                </thead>
                                <tbody>
                                  {(it.remark || it.info) && <tr>
                                    <th>
                                    <p className='active_name'>{it.remark}</p>
                                      {
                                        !(it.originalRemark || it.originalRemark) && it.commissionDataList && it.commissionDataList.map((its, inds) => {
                                          return <div key={inds} className='commission'>
                                            <p className='commission_title'>{its.title}</p>
                                            {its.isShowDetail != 0 && <span className='commission_icon' onClick={() => { getRelus(its) }}><Icon name="question-o" /></span>}
                                            <p className='commission_amount'>({its.amount})</p>
                                            <p className='commission_info'>{its.info}</p>
                                          </div>
                                        })
                                      }
                                    </th>
                                    <th>{it.originalInfo ?? it.info}</th>
                                  </tr>}
                                  {(it.originalRemark || it.originalRemark) && <tr>
                                    <th>
                                      <p className='active_name'>{it.originalRemark}</p>
                                      {
                                        it.commissionDataList && it.commissionDataList.map((its, inds) => {
                                          return <div key={inds} className='commission'>
                                            <p className='commission_title'>{its.title}</p>
                                            {its.isShowDetail != 0 && <span className='commission_icon' onClick={() => { getRelus(its) }}><Icon name="question-o" /></span>}
                                            <p className='commission_amount'>({its.amount})</p>
                                            <p className='commission_info'>{its.info}</p>
                                          </div>
                                        })
                                      }
                                    </th>
                                    <th>{it.info}（折后）</th>
                                  </tr>}
                                </tbody>
                              </table>
                            </div>
                          </div>
                        })
                      }
                    </div>
                  }
                </div>
              })
            }
            <p className='baseMessage'>用户支付金额</p>
            <div className="charDetailTable">
              <Table
                border
                rowKey="totalPrice"
                columns={amountColumns}
                data={[promotionInfo]}
              />
            </div>

            <p className='baseMessage'>商家结算款</p>
            <p style={{ color: "#666", marginLeft: "8px", marginTop: "10px", fontSize: '14px' }}>({billChargeInfo.other})</p>
            <div className="distribution_table">
              <table>
                <thead>
                  <tr>
                    {
                      billChargeInfo.dataList && billChargeInfo.dataList.map((item, index) => {
                        return <th key={index}>{item.title}</th>
                      })
                    }
                    <th>合计（应付商家款）</th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    {
                      billChargeInfo.dataList && billChargeInfo.dataList.map((item, index) => {
                        return <th key={index}>{item.totalAmount}</th>
                      })
                    }
                    <th>{formatAmountToString(billChargeInfo.amount)}</th>
                  </tr>
                </tbody>
              </table>
            </div>
            <p className='baseMessage'>骑手配送收入</p>
            <div className="distribution_table">
              <table>
                <thead>
                  <tr>
                    <th>合计</th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <th><a href="https://peisong.meituan.com/billingcenter/pricingplayback#/" target="_blank">跳转至配送平台查询</a></th>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div> : <div className='noData'>
          <p>暂无数据</p>
        </div>
      }
    </div>
  )
}
