import React from "react";
import moment from "moment";
import Icon from '@roo/roo/Icon';
import Modal from '@roo/roo/Modal';
import './index.scss'

const { confirm } = Modal;
export const columns: any = [
  {
    prop: 'beginDistance', label: '距离（公里）', width: 167, render: (text: number, record: MyTypes.PerfDistanceFeeData) => {
      return (
        <>
          {
            record.endDistance === 999999999 ? <p>{record.beginDistance}(不含)以上</p> : <p>{record.beginDistance}(含) - {record.endDistance}(含)</p>
          }
        </>
      )
    }
  },
  { prop: 'fee', label: '收费规则', width: 167,  render: (text: number, record: MyTypes.PerfDistanceFeeData) => {
    return (
      <>
        {
          record.step ? <p>每多 {record.step}公里多收{record.stepFee}元</p> : <p>起步价 {record.fee}元</p>
        }
      </>
    )
  }},
  { prop: 'name', label: '说明', width: 167, render: (text: number, record: MyTypes.PerfDistanceFeeData) => {
    return (
      <>
        {
          record.step && <p>不足{record.step}公里按{record.step}公里收费</p>
        }
      </>
    )
  } },
];
export const perColumns: any = [
  {
    prop: 'beginDistance', label: '客单价（元）', width: 167, render: (text: number, record: MyTypes.PerfDistanceFeeData) => {
      return (
        <>
          {
            record.endPrice === 999999999 ? <p>{record.beginPrice}(不含)以上</p> : <p>{record.beginPrice}(含) - {record.endPrice}(含)</p>
          }
        </>
      )
    }
  },
  { prop: 'fee', label: '收费规则', width: 167,  render: (text: number, record: MyTypes.PerfDistanceFeeData) => {
    return (
      <>
        {
          record.step ? <p>每多 {record.step}元多收{record.stepFee}元</p> : <p>每单收 {record.fee}元</p>
        }
      </>
    )
  }},
  { prop: 'name', label: '说明', width: 167, render: (text: number, record: MyTypes.PerfDistanceFeeData) => {
    return (
      <>
        {
          record.step && <p>不足{record.step}元按{record.step}元收费</p>
        }
      </>
    )
  } },
];
export const timeColumns: any = [
  { prop: 'beginTime', label: '时间区间', width: 167, render: (text: number, record: MyTypes.PerfDistanceFeeData) => {
    return (
      <>
        {
          <p>{record.beginTime}-{record.endTime}</p>
        }
      </>
    )
  }},
  { prop: 'fee', label: '收费规则', width: 167,render: (text: number, record: MyTypes.PerfDistanceFeeData) => {
    return (
      <>
        {
          <p>每单{record.fee || '-'}元</p>
        }
      </>
    )
  }},
];
export const perfDistanceDetailcolumns: any = [
  {
    prop: 'beginDistance', label: '距离（公里）', width: 167, render: (text: number, record: MyTypes.PerfDistanceFeeData) => {
      return (
        <>
          {
            record.endDistance === 999999999 ? <p>{`(${record.beginDistance / 1000},~KM]`}</p> : <p>{`(${record.beginDistance / 1000},${record.endDistance / 1000}KM]`}</p>
          }
        </>
      )
    }
  },
  { prop: 'stepPrice', label: '收费规则', width: 167,  render: (text: number, record: MyTypes.PerfDistanceFeeData) => {
    return (
      <>
        {
          record.beginDistance === 0 ? <p>不加价</p> : <p>加价 {record.stepPrice}元/KM</p>
        }
      </>
    )
  }},
  { prop: 'name', label: '计费规则', width: 167, fullRender: ({ index, value }) => {
    if (index === 0) {
        return <td rowSpan={index}>以美团系统导航距离为准(单位KM)，按照阶梯进行距离加价。 距离加价=该区间起始金额+（导航距离-起始距离）[向上取整]*该区间距离加价标准</td>;
    }
    if (index) {
        return null;
    }

    return <td>{ value }</td>;
}},
];
export const perfTimePlanDetailcolumns: any = [
  { prop: 'beginTime', label: '时间区间', width: 167, render: (text: number, record: MyTypes.PerfDistanceFeeData) => {
    return (
      <>
        {
          <p>{record.beginTime}-{record.endTime}</p>
        }
      </>
    )
  }},
  { prop: 'price', label: '收费规则', width: 167,render: (text: number, record: MyTypes.PerfDistanceFeeData) => {
    return (
      <>
        {
          <p>每单{record.price || '-'}元</p>
        }
      </>
    )
  }},
  { prop: 'name', label: '计费规则', width: 167, fullRender: ({ index, value }) => {
    if (index === 0) {
        return <td rowSpan={index}>商家在不同时段使用企客配送服务时，需要按照上述时段的收费规则向美团支付额外费用。即时单按系统接单时间，预约单按预计送达时间。</td>;
    }
    if (index) {
        return null;
    }

    return <td>{ value }</td>;
}},
]
export const perfWeightPlanDetailcolumns: any = [
  { prop: 'beginWeight', label: '重量', width: 167, render: (text: number, record: MyTypes.PerfDistanceFeeData) => {
    return (
      <>
         {
            record.endWeight === 999999999 ? <p>{`(${record.beginWeight},~KG]`}</p> : <p>{`(${record.beginWeight},${record.endWeight}KG]`}</p>
          }
      </>
    )
  }},
  { prop: 'stepPrice', label: '收费规则', width: 167,render: (text: number, record: MyTypes.PerfDistanceFeeData) => {
    return (
      <>
        {
          <p>加价{record.stepPrice || '-'}元/KG</p>
        }
      </>
    )
  }},
  { prop: 'name', label: '计费规则', width: 167, fullRender: ({ index, value }) => {
    if (index === 0) {
        return <td rowSpan={index}>以美团系统中订单重量为准(单位KG)，按照阶梯进行重量加价。 重量加价=该区间起始金额+（订单重量-起始重量）[向上取整]*该区间重量加价标准</td>;
    }
    if (index) {
        return null;
    }

    return <td>{ value }</td>;
}},
];
export const perfAdjustPlanDetailcolumns:any = [
  { prop: 'beginDate', label: '节假日/大促', width: 167, render: (text: number, record: MyTypes.PerfDistanceFeeData) => {
    return (
      <>
        {
          <p>{moment(record.beginDate * 1000).format('yyyy-MM-DD')} - {moment(record.endDate * 1000).format('yyyy-MM-DD')}</p>
        }
      </>
    )
  }},
  { prop: 'price', label: '收费规则', width: 167,render: (text: number, record: MyTypes.PerfDistanceFeeData) => {
    return (
      <>
        {
          <p>加价{record.price || '-'}元</p>
        }
      </>
    )
  }},
];
export const perfCategoryPlanDetailcolumns:any = [
  { prop: 'categoryName', label: '品类', width: 167 },
  { prop: 'price', label: '收费规则', width: 167,render: (text: number, record: MyTypes.PerfDistanceFeeData) => {
    return (
      <>
        {
          <p>加价{record.price || '-'}元</p>
        }
      </>
    )
  }},
];
export const varietyColumns: any = [
  { prop: 'name', label: '菜品信息', width: 445 },
  { prop: 'info', label: '单价', width: 89 },
  { prop: 'count', label: '数量', width: 89 },
  { prop: 'totalAmount', label: '合计', width: 89,fullRender: ({ index, value }) => {
    if (index === 0) {
        return <td rowSpan={index}>{value}</td>;
    }
    if (index) {
        return null;
    }

    return <td>{ value }</td>;
}},
  // { prop: 'age', label: '餐盒单价', width: 89 },
  // { prop: 'age', label: '餐盒数量', width: 89 },
  // { prop: 'age', label: '单菜品总价', width: 227 },
];
export const activeColumns: any = [
  { prop: 'name', label: '商家对顾客的活动补贴', width: 163,render: (text: string, record: MyTypes.IsShowDetail) => {
    return (
      <>
        {
          <div className="active_info">
            <div style={{display:'flex'}}>
              <p className="active_name">{record.name}</p>
              {record.isShowDetail != 0 && <span className='commission_icon' onClick={() => {
                 confirm({
                  title: `${record.couponDetail.couponName}`,
                  children: <p>{record.couponDetail.couponDesc}</p>,
                  status: 'success',
                  confirmText: "我知道了",
                  onConfirm: () => {}
              });
              }}><Icon name="question-o" /></span>}
            </div>
              <p className="active_remark">{record.remark}</p>
          </div>
        }
      </>
    )
  }},
  { prop: 'info', label: '补贴金额', width: 163 },
  { prop: 'totalAmount', label: '合计', width: 89,fullRender: ({ index, value }) => {
    if (index === 0) {
        return <td rowSpan={index}>{value}</td>;
    }
    if (index) {
        return null;
    }

    return <td>{ value }</td>;
}},
]
export const amountColumns: any = [
  { prop: 'totalPrice', label: '菜品总价', width: 254 },
  { prop: 'shippingFee', label: '用户支付的配送费', width: 254 },
  { prop: 'fee', label: '优惠', width: 330 },
  { prop: 'userPayTotalAmount', label: '合计', width: 188 },
];
export const commissionColumns: any = [
  { prop: 'name', label: '抽金基数', width: 163 },
  { prop: 'age', label: '佣金比例', width: 163 },
  { prop: 'age', label: '佣金', width: 163 },
];
export const distributionColumns: any = [
  { prop: 'name', label: '距离收费', width: 124 },
  { prop: 'age', label: '价格区间', width: 124 },
  { prop: 'age', label: '时段收费', width: 124 },
  { prop: 'age', label: '配送服务费', width: 124 },
];
export const popleDistributionColumns: any = [
  { prop: 'name', label: '用户支付配送', width: 124 },
  { prop: 'age', label: '商家补贴', width: 124 },
  { prop: 'age', label: '时段收费', width: 124 },
  { prop: 'age', label: '用户配送费', width: 124 },
];
export const settlementAmountColumns: any = [
  { prop: 'name', label: '订单原价', width: 164 },
  { prop: 'age', label: 'C配', width: 164 },
  { prop: 'age', label: '商补', width: 164 },
  { prop: 'age', label: '佣金', width: 164 },
  { prop: 'age', label: '商配', width: 164 },
  { prop: 'age', label: '合计（应付商家款）', width: 164 },
];
