import React, { useState, useEffect, useCallback, useMemo } from 'react'
import { RouteComponentProps } from 'react-router-dom';
import Table from '@roo/roo/Table';
import Form from '@roo/roo/Form';
import Select, { SelectOptions } from '@roo/roo/Select';
import Grid from '@roo/roo/Grid';
import Button from '@roo/roo/Button';
import { ApplyStatusEnum , ApplyStatusEnums, InvoiceStatusEnum, InvoiceStatusEnums,PartnerTypeEnum} from '../../utils/enum';
import CheckBox from '@roo/roo/CheckBox';
import TabBar from '@components/TabBar';
import request from '../../utils/request';
import WriteOffModel from './components/writeOff_model'
import './index.scss'

interface QueryRolePartnerType {
  partnerType:number,
  partnerTypeName:string
  partnerTypeDisplayName: string;
}
interface FormValues {
  partnerType: number,
  partnerId:number,
  payBillId:number,
  applyStatus:number
}
interface WriteList {
  payBillId: number;
  partnerType: number;
}
interface TspInputTaxPartnerInfoVo {
  partnerId:number,
  partnerType:number,
  partnerName:string
}
interface tspInputTaxInvoiceApplyVo {
  payBillId:number,
  payBillAmount:string,
  applyStatus:number,
  taxDiff:string,
  confirmAmount:string,
  id	:number
}
interface TspInputTaxBindInvoiceVoList {
  invoiceNumber:string,
  invoiceStatus:number,
  invoiceCode:string,
  amount:string
}
interface TspInputTaxPayBillInfoVo {
  id:number,
  amount:string,
  startDate: string,
  endDate:string
}
interface TspInputTaxInvoiceApplyDetailVoList {
  tspInputTaxPartnerInfoVo:TspInputTaxPartnerInfoVo,
  tspInputTaxInvoiceApplyVo:tspInputTaxInvoiceApplyVo,
  tspInputTaxBindInvoiceVoList:TspInputTaxBindInvoiceVoList[],
  tspInputTaxPayBillInfoVo:TspInputTaxPayBillInfoVo
}
interface Info {
  tspInputTaxInvoiceApplyDetailVoList:TspInputTaxInvoiceApplyDetailVoList[],
  count: number
}
const { Row, Col } = Grid;
const Field = Form.field;
export default function PaymentDoc(props:RouteComponentProps) {
  const [tspInputTaxInvoiceApplyDetailVoList,setTspInputTaxInvoiceApplyDetailVoList] = useState([]);
  const [currentObj,setCurrentObj] = useState({}); // 当前表格数据合并的展示
  const [isBatchWrite,setIsBatchWrite] = useState<boolean>(false); // 批量核销弹窗展示
  const [writeList, setWriteList] = useState<WriteList[]>([]); // 批量核销列表
  const [pageCount,setPageCount] = useState<number>(0); // 获取列表总条数
  const [pageSize,setPageSize] = useState<number>(10); // 页码
  const [pageNo,setPageNo] = useState<number>(1); // 页数
  const [formValues,setFormValues] = useState<FormValues>({}); // 获取form表单数据
  const [queryRolePartnerType,setQueryRolePartnerType] = useState<SelectOptions[]>([]); //业务线

  const getInitData = (value:{partnerType:number, partnerId:number, payBillId :number, applyStatus:number}) => {
    setFormValues(value)
    const {partnerType = '', partnerId = '', payBillId = '',  applyStatus = ''} = value || {}
    request.get<{},Info>('/money/input/tax/invoice/queryApplyInfoList',{
      partnerType,
      partnerId,
      payBillId,
      applyStatus,
      pageSize,
      pageNo
    }).then((res) => {
      // 数据平铺处理
      const arr = [];
      // const listCountArr = [];
      const obj: {[key: number] : number }= {};
      let sum: number = 0;
      res.tspInputTaxInvoiceApplyDetailVoList && res.tspInputTaxInvoiceApplyDetailVoList.forEach((j) => {
        if (j.tspInputTaxBindInvoiceVoList && j.tspInputTaxBindInvoiceVoList.length) {
          const len = j.tspInputTaxBindInvoiceVoList.length;
          // listCountArr.push(len);
          obj[sum] = len;
          sum += len;
          j.tspInputTaxBindInvoiceVoList.forEach((k) => {
            arr.push({ ...k, ...j,...j.tspInputTaxPartnerInfoVo,...j.tspInputTaxInvoiceApplyVo,...j.tspInputTaxPayBillInfoVo,totalPriceamounts:k.amount});
          });
        } else {
          arr.push({ ...j });
        }
      });
      setCurrentObj(obj)
      setTspInputTaxInvoiceApplyDetailVoList(arr)
      setPageCount(res.count)
    }).catch((error) => {
      console.log(error)
    })
  };
 // 业务线类型
 useEffect(() => {
  request.get<{},QueryRolePartnerType[]>('/money/input/tax/invoice/queryRolePartnerType',{}).then((res) => {
      const newarr = res.map((item:{partnerType:number,partnerTypeName:string, partnerTypeDisplayName: string}) => {
          return {
            value: item.partnerType,
            label: item.partnerTypeName,
            displayName: item.partnerTypeDisplayName,
          }
      })
      setQueryRolePartnerType(newarr)
  })
},[]);
  useEffect(() => {
    formValues.partnerType && getInitData(formValues)
  },[pageCount,pageSize,pageNo])

  const cols = {
    label: { span: 4 },
    field: { span: 6 }
  };

  // 获取 checkbox 当条数据
  const handleChange = (e) => {
      if (e.target.checked) {
        setWriteList([...writeList,tspInputTaxInvoiceApplyDetailVoList[e.target.value]]);
      }  else {
        setWriteList(writeList.filter(item => item !== tspInputTaxInvoiceApplyDetailVoList[e.target.value]));
      }
  }
  // 查看数据详情
  const lookDetail = useCallback((index) => {
    window.open(`/money/invoice-new-input-tax/paymentDetail?applyId=${tspInputTaxInvoiceApplyDetailVoList[index].applyId}&partnerType=${tspInputTaxInvoiceApplyDetailVoList[index].partnerType}&applyStatus=${tspInputTaxInvoiceApplyDetailVoList[index].applyStatus}`)
  },[tspInputTaxInvoiceApplyDetailVoList])
  // 核销
  const write = (index:number) => {
    setIsBatchWrite(true)
    setWriteList([...writeList,tspInputTaxInvoiceApplyDetailVoList[index]]);
  }

// 付款单状态
const getApplyCode = (code:number) => {
  for (var i = 0; i < ApplyStatusEnum.length; i++) {
    if (ApplyStatusEnum[i].value == code) return ApplyStatusEnum[i].label;
  }
}
// 根据业务线获取xx商名称
const getPartnerType = (values:FormValues,keys:string) => {
  if (values && values.partnerType) {
    const option  = queryRolePartnerType.filter(t => t.value === values.partnerType)[0];
    if (option) {
       return keys === 'partnerId' ? `${option.displayName}ID` : `${option.displayName}名称`;
    }
  }

  return keys === 'partnerId' ? '商ID' : '商名称'
};  
console.log(writeList,isBatchWrite,'ryu')
// 表格合并统一处理
  const colums_fullRender = (index:number, columnValue:string | number,keys:string) => {
    for(let key in currentObj){
    if (index === Number(key)) {
      if (keys === 'checkbox') {
        return <td rowSpan={currentObj[key]} style={{textAlign:"center"}}>
                 <CheckBox value={index} checked={writeList.some(item => item === tspInputTaxInvoiceApplyDetailVoList[index]) ? true : false} onChange={handleChange} />
               </td>;
      } else if (keys === 'action') {
          return <td rowSpan={currentObj[key]}>
            <a style={{cursor:"pointer"}} onClick={() => {
              lookDetail(index)
            }}>查看详情</a>
            {
              tspInputTaxInvoiceApplyDetailVoList[index].applyStatus === ApplyStatusEnums.UNWRITTEN_OFF && tspInputTaxInvoiceApplyDetailVoList[index].tspInputTaxBindInvoiceVoList.every((item) => item.invoiceStatus === InvoiceStatusEnums.TOBE_WRITTEN_OFF) && <a style={{marginLeft:"12px",cursor:"pointer"}} onClick={() => {
                write(index)
              }}>核销</a>
            }
          </td>
      } else if (keys === 'applyStatus') {
        return <td rowSpan={currentObj[key]} style={{textAlign:"center"}}>{getApplyCode(columnValue)}</td>;
      } else if (keys === 'startDate'){
        return <td rowSpan={currentObj[key]} style={{textAlign:"center"}}>{tspInputTaxInvoiceApplyDetailVoList[index].startDate}~{tspInputTaxInvoiceApplyDetailVoList[index].endDate}</td>;
      } else {
        return <td rowSpan={currentObj[key]} style={{textAlign:"center"}}>{columnValue}</td>;
      }
     
    }}
    if (index) {
      return null;
    }
    return <td>{columnValue}</td>;
  }
  const columns: any = [
    {
      prop: 'checkbox', label: '选择', width: 100,align:"center", fullRender: ({ index, columnValue }) => colums_fullRender(index, columnValue ,'checkbox')
    },
    {
      label: '付款信息',
      align: 'center',
      children: [
        {
          prop: 'partnerId', label: getPartnerType(formValues,'partnerId'), width: 160,align:"center", fullRender: ({ index, columnValue }) => colums_fullRender(index, columnValue,'')
        },
        {
          prop: 'partnerName', label: getPartnerType(formValues,'partnerName'), width: 160,align:"center", fullRender: ({ index, columnValue }) => colums_fullRender(index, columnValue, '')
        },
        {
          prop: 'startDate', label: '付款单账期', width: 200,align:"center", fullRender: ({ index, columnValue }) => colums_fullRender(index, columnValue, 'startDate')
        },
        {
          prop: 'payBillId', label: '付款单号', width: 120,align:"center", fullRender: ({ index, columnValue }) => colums_fullRender(index, columnValue, '')
        },
        {
          prop: 'payBillAmount', label: '付款单金额', width: 120,align:"center", fullRender: ({ index, columnValue }) => colums_fullRender(index, columnValue, '')
        },
        {
          prop: 'applyStatus', label: '付款单状态', width: 120,align:"center", fullRender: ({ index, columnValue }) => colums_fullRender(index, columnValue, 'applyStatus')
        },
      ]
    },
    {
      label: '发票信息',
      align: 'center',
      children: [
        { prop: 'invoiceNumber', label: '发票号码', width: 120,align:"center", },
        { prop: 'invoiceCode', label: '发票代码', width: 120,align:"center",render: (text:number) => <p>{text || '-'}</p> },
        { prop: 'totalPriceamounts', label: '价税合计金额', width: 120,align:"center", },
        { prop: 'invoiceStatus', label: '发票状态', width: 120 ,align:"center",render: (text:number,record) => <p>{InvoiceStatusEnum[text]}</p> },
      ]
    },
    {
      prop: 'taxDiff', label: '总差额扣款金额', width: 140,align:"center", fullRender: ({ index, columnValue }) => colums_fullRender(index, columnValue, '')
    },
    {
      prop: 'confirmAmount', label: '总核销金额', width: 120,align:"center", fullRender: ({ index, columnValue }) => colums_fullRender(index, columnValue, '')
    },
    {
      prop: 'action', label: '操作', width: 140,align:"center", fullRender: ({ index, columnValue }) => colums_fullRender(index, columnValue, 'action')

    },
  ];

  // 批量核销
  const batchWrite = () => {
    setIsBatchWrite(true)
  }

  // 分页
  const handlePageChange = (page:number,pageSize:number) => {
    setPageSize(pageSize)
    setPageNo(page);
}
// 批量核销按钮权限
const isWriteButton = writeList.every((item) => item.applyStatus === ApplyStatusEnums.UNWRITTEN_OFF && item.tspInputTaxBindInvoiceVoList.every((it) => it.invoiceStatus === InvoiceStatusEnums.TOBE_WRITTEN_OFF))
  return (
    <>
    <TabBar tabName='paymentInvoiceVerification'/>
    <div className='payment_wrap'>
      <div className="payment_select">
        <Form
          cols={cols}
          onSubmit={(value, errors) => {
            console.log('submit callback: ', value, errors);
            getInitData(value)
          }}
        >
          {
            ({
              values, errors, submitForm
            }) => (
              <form>
                <Row>
                  <Col span={3}>
                    <Field
                      label="业务线"
                      name="partnerType"
                      as={(
                        <Select
                          options={queryRolePartnerType}
                          placeholder="请选择业务线"
                        />
                      )}
                    />
                  </Col>
                  <Col span={3}>
                    <Field
                      label={getPartnerType(values,'partnerId')}
                      name="partnerId"
                      placeholder={`请输入${getPartnerType(values,'partnerId')}`}
                    />
                  </Col>
                  <Col span={3}>
                    <Field
                      label="付款单状态"
                      name="applyStatus"
                      as={(
                        <Select
                          options={ApplyStatusEnum}
                          placeholder="请选择付款单状态"
                        />
                      )}
                    />
                  </Col>
                  <Col span={3}>
                    <Field
                      label="付款单号"
                      name="payBillId"
                      placeholder="请输入付款单号"
                    />
                  </Col>
                </Row>
                <div style={{ paddingLeft:"83%",marginBottom:"16px" }}>
                <div style={{display:'flex'}}>
                      <Button className='select_button' onClick={submitForm}>
                        查询
                      </Button>
                      <Button style={{marginLeft:"24px"}} onClick={batchWrite} disabled={isWriteButton && writeList.length > 0 ? false : true}>
                        批量核销
                      </Button>
                      </div>
                </div>
              </form>
            )
          }
        </Form>
      </div>
      <div className='payment_list'>
        <Table
          border
          stripe
          rowKey="name"
          columns={columns}
          data={tspInputTaxInvoiceApplyDetailVoList}
          scrollX={1200}
          pagination={{
            total: pageCount,
            pageSize: pageSize,
            showJumper: true,
            pageSizeOptions: [10, 20, 30, 50],
            onChange:handlePageChange,
            currentPage:pageNo,
            showTotal: (total) => `共 ${total} 条数据`
        }}
        />
      </div>
      {
          isBatchWrite && <WriteOffModel closeWriteOff={() => {
            setIsBatchWrite(false)
            setWriteList([])
          }} writeList = {writeList} getInitData={getInitData} formValues={formValues}  />
      }
    </div>
    </>
  )
}
