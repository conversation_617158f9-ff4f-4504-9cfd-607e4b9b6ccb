import React, { useState,useEffect } from 'react';
import Table from '@roo/roo/Table';
import Button from '@roo/roo/Button';
import request from '../../../utils/request';
import './index.scss'

interface WriteoffList {
  payBillId: number;
  partnerType: number;
  partnerId:number,
  partnerName:string,
  startDate:string,
  amount:string,
  estimatedWriteAmount:string
}
export default function writeOff_model({
  closeWriteOff,
  writeList,
  getInitData,
  formValues
}) {
  const [writeoffList,setWriteoffList] = useState<WriteoffList[]>([]); // 批量核销列表数据
  useEffect(() => {
        let arr = [];
    writeList.map((item) => {
      request.get('/money/input/tax/invoice/queryActualPayAmount',{
        partnerType : item.partnerType,
        payBillId	: item.payBillId,
      },{
        showError:false
      }).then((res) => {
        arr.push({...item,estimatedWriteAmount:res})
        setWriteoffList(arr)
      }).catch((error) => {
        arr.push({...item,estimatedWriteAmount:'-'})
        setWriteoffList(arr)
      })
    })
  },[])
  const columns: any = [
    { prop: 'partnerId', label: '商ID',width:120 },
    { prop: 'partnerName', label: '商名称', width:120 },
    { prop: 'startDate', label: '付款单月份',width:120 },
    { prop: 'amount', label: '付款单金额',width:120 },
    { prop: 'estimatedWriteAmount', label: '预计核销金额',width:120 },
  ];

  const confirmWrite = () => {
    const tspInputTaxInvoiceConfirmRequestList = writeList.map((item:WriteoffList) => {
      return {
        partnerType : item.partnerType,
        payBillId: item.payBillId
      }
    })
    request.post('/money/input/tax/invoice/confirmPayBill',tspInputTaxInvoiceConfirmRequestList,{
      isJson: true,
      isArray: true
  }).then((res) => {
      getInitData(formValues);
      closeWriteOff();
    })
  }

  return (
    <div className='model_wrapper'>
      <div className='model_wrap'>
        <h4>当前选中{writeList.length}条付款单</h4>
        <div className="model_table">
          <Table
            rowKey="name"
            hover
            columns={columns}
            data={writeoffList}
            scrollY={100}
          />
        </div>
        <h4>请确认是否核销</h4>
        <div className="model_button">
          <Button onClick={confirmWrite}>确认核销</Button>
          <Button onClick={closeWriteOff}>取消</Button>
        </div>
      </div>
    </div>
  )
}
