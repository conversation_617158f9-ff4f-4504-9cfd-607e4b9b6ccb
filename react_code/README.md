## 结算业务：进项税发票系统

### 1.项目简介

提供各合作方（外卖渠道商）在 PC 端上录入发票，填写退票地址，下载发票交接单等功能

### 2.系统地址

- `prod` [线上地址](https://invoice.waimai.meituan.com/invoicefe/editInvoice?bizAcctId=2&source=104&payBillId=5018787357&token=null)
- `stage` [st 地址](https://invoice.waimai.st.sankuai.com?bizAcctId=2&source=104&payBillId=5018787357&token=null)
- `test` [test 地址](http://invoice.waimai.test.sankuai.com/invoicefe/editInvoice?bizAcctId=2&source=104&payBillId=5018787357&token=null)

### 3.项目结构

```
.
├── README.md
├── build
│   ├── env
│   └── pages
├── manifest.yaml
├── mock
├── nine.js
├── nine.json
├── package-lock.json
├── package.json
├── pre-deploy
├── public
│   └── index.ejs
├── src
│   ├── assets
│   ├── components
│   ├── dva.ts
│   ├── index.tsx
│   ├── pages
│   ├── router.tsx
│   ├── services
│   ├── types
│   └── utils
├── tsconfig.base.json
├── tsconfig.json

```

### 4.开发步骤

Node 版本要求： Node >= v19.5.0

cd react_code

#### 安装依赖

```bash
mnpm install
```

#### 启动项目

```bash
nine dev
```

#### 在浏览器访问链接

```
### 发票信息录入页面
    http://localhost:3030/invoicefe/editInvoice

```

#### 修改mock数据地址

https://yapi.sankuai.com/project/27929
### 5.相关文档

[商家对账 pc 端所有页面](https://km.sankuai.com/page/206900724)

[结算业务介绍](https://km.sankuai.com/page/109009803)

### 6.鉴权相关说明

正常线上鉴权流程:

```
跳转页面带参数
   ↓
发票录入页面     取 bizAcctId（账号id） 、source（业务来源） 、 token（sso登陆态下非必填项）、payBillId（账单id） 等参数鉴权，鉴权通过后转发到对应后端服务
   ↓
moneyeapi     结算后端服务
```

### 7.构建 & 分析说明

```bash
# 仅构建，结果在 build 目录
npm run build
```
