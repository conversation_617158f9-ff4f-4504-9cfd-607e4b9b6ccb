// nine.js
const webpack = require('webpack');
const path = require('path');
const parser = require('argv-parser');
const BundleAnalyzerPlugin = require('webpack-bundle-analyzer');
const config = require('./nine.json');

const NODE_ENV = process.argv[2] === 'dev' ? 'dev' : (process.env.AWP_DEPLOY_ENV || process.env.NODE_ENV || 'production');

function getPublicPath() {
  console.log('process.env.AWP_DEPLOY_ENV=======', process.env.AWP_DEPLOY_ENV);
  if (process.env.AWP_DEPLOY_ENV) {
    return `${process.env.PUBLIC_PATH}/${process.env.AWP_DEPLOY_ENV}`
  }
  return '/'
}

const rules = {
  analyze: {
    type: Boolean,
    short: 'a',
  },
};

const parsedArgv = parser.parse(process.argv, { rules }).parsed;
const plugins = [
  // 由于nine内置了 NODE_ENV，并且在qa环境设置了production，为避免冲突，重新定义一个
  new webpack.DefinePlugin({
    'process.env.NODE_ENV': JSON.stringify(process.env.AWP_DEPLOY_ENV),
    'process.env.DEPLOY_ENV': JSON.stringify(process.env.DEPLOY_ENV),
  }),
];
if (parsedArgv.analyze) {
  plugins.push(new BundleAnalyzerPlugin.BundleAnalyzerPlugin());
}

config.appConfig = {
  ...config.appConfig,
  webpackCustom: {
    output: {
      library: 'inputTax',
      jsonpFunction: 'webpackJsonp_inputTax',
    },
    devServer: { // dev环境proxy设置
      port: 3030,
      host: 'localhost',
      historyApiFallback: {
        rewrites: [{
          from: /\/invoicefe.*/,
          to: '/index.html'
        }, {
          from: /\/money.*/,
          to: '/main.html'
        }],
      },
      openPage: '',
      proxy: {
        '/money/input': {
          target: 'https://yapi.sankuai.com/mock/27929',
          // target: 'https://proxy.waimai.st.sankuai.com',
          changeOrigin: true,
          secure: false,
        },
        '/xianfuBill/poiBillCharge': {
          target: 'https://yapi.sankuai.com/mock/7109',
          // target: 'https://proxy.waimai.st.sankuai.com',
          changeOrigin: true,
          secure: false,
        },
      },
    },
    resolve: {
      alias: {
        '@': path.resolve(__dirname, 'src'),
        '@components': path.resolve(__dirname, 'src/components'),
        '@/utils': path.resolve(__dirname, 'src/utils'),
        '@assets': path.resolve(__dirname, 'src/assets'),
      },
    },
    plugins,
  },
  outputPath: path.join('..', __dirname, 'dist')
};

config.babelConfig = {
  usePolyfill: true,
  plugins: [
    'const-enum',
    [
      'module:@roo/babel-roo-import-plugin',
      {
        style: 'css', // 样式引入方式,目前不支持其他选项
        libraryDirectory: 'es', // 使用的库类型。es、lib可选，默认lib
        libraryName: '@roo/roo',
        camel2DashComponentName: false,
      },
    ],
  ],
};

module.exports = config;
