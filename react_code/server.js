const express = require('express');
const path = require('path');
const app = express();
const port = 3000;

const routers = [{
  path: '/money/invoice-new-input-tax*', // 发票管理后台
  file: 'main'
},{
  path: '/invoicefe*', // 发票录入系统
  file: 'index'
}];

app.use(express.static('build/env'));

routers.forEach(item => {
    const filePath = `${path.dirname(__dirname)}/react_code/build/pages/${item.file}`;
    console.log(filePath);
    app.get(item.path, (req, res) => {
      res.sendFile(`${filePath}.html`);
    });
});
app.listen(port, () => console.log(`Example app listening on port ${port}!`));