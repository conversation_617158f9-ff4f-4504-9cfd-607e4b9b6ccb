{"generatorType": "@nine/nine-generator-react-b", "builderType": "@nine/nine-preset-wm-b", "publishType": "@nine/nine-preset-wm-b", "preset": "@nine/nine-preset-wm-b", "localBuilderVersion": true, "appConfig": {"bucketName": "test-3ff7930f", "team": "b", "multipleCDN": false, "useTestS3": false, "iGateAppKey": "", "lxConfig": {"category": "waimai_e", "appnm": "test"}, "entry": [{"src": "./src/index.tsx", "name": "index", "title": "发票信息管理", "template": "./public/index.ejs"}, {"src": "./src/main.tsx", "name": "main", "title": "结算后台管理", "template": "./public/main.ejs"}]}}