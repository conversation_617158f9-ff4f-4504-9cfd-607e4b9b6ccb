//https://eslint.org/docs/user-guide/configuring
module.exports = {
    root: true,
    parserOptions: {
        extraFileExtensions: [".scss"],
    },
    extends: [
      // 继承外卖的统一配置
      "@wmfe/eslint-config-mt/eslintrc.react.js",
      "@wmfe/eslint-config-mt/eslintrc.typescript-react.js",
      'plugin:@wmfe/b/tsrecommended',
    ],
    parser:"@typescript-eslint/parser",
    // plugins: [],  // 添加自定义插件
    rules: {
      "react/jsx-max-props-per-line": ['error', { "maximum": 3, "when": "always" }], // 即 JSX 中属性最多支持每行3个
      "react/state-in-constructor": "off", // state 不用强制写在 constructor 中，更简洁
      "operator-linebreak": "off", // 强制操作符使用一致的换行符风格，默认配置["after", { "overrides": { "?": "before", ":": "before" } }]
      "no-new": "off", // 开启后，禁止使用 new 关键字调用构造函数但却不将结果赋值给一个变量来保持一致性和约定
      "no-plusplus": "off", // 开启后，禁止使用一元操作符 ++ 和 --
      "no-script-url": "off", // 开启后，在链接地址中禁止使用 javascript: ，例如 location.href = "javascript:void(0)";
      "import/extensions": [
        "error",
        "ignorePackages",
        { "jsx": "never", "js": "never", "ts": "never", "tsx": "never" }
      ],
      "@typescript-eslint/explicit-function-return-type": "off",
      "import/no-unresolved": ["error", { ignore: ['^@roo/roo-mobile/*'] }],
      "@wmfe/b/type-annotation": ["error", ['any', 'Function']],
    },
    globals: {
        registered: true,
    },
    settings: {
      // "import/extensions": [".js", ".jsx", ".ts", ".tsx", ".json"], // 待验证 ESLint 生效的文件，与下面的extensions是否重复？
      "import/resolver": { // 若webpack配置了，可直接使用webpack的配置，避免写两份
        alias: {
          // 文件路径别名配置
            map: [
                ['@', './src/questionnaire/'],
                ['@commonlib', './src/commonlib/'],
            ],
            extensions: [".js", ".jsx", ".ts", ".tsx", ".json"],
        },
      },
    },
};