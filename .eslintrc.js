module.exports = {
    extends: [
        '@wmfe/eslint-config-mt/eslintrc.vue.js',
        '@wmfe/eslint-config-mt/eslintrc.prettier.js',
    ],
    rules: {
        'no-unused-vars':["error", {
            "vars": "all",
            "args": "none",
            "caughtErrors": "all",
            "ignoreRestSiblings": false,
            "reportUsedIgnorePattern": false
        }],
        'no-plusplus': 0,
        'vue/html-indent': [
            'error',
            4,
            {
                attribute: 1,
                closeBracket: 0,
                alignAttributesVertically: true,
                ignores: ['template'],
            },
        ],
        'vue/order-in-components': 4,
        'vue/max-attributes-per-line': [
            2,
            {
                singleline: 200,
                multiline: {
                    max: 1,
                    allowFirstLine: false,
                },
            },
        ],
        'import/extensions': 0
    },
    globals: {
        lib: true,
        KNB: true,
        KNBP: true,
        Transform: true,
        AlloyFinger: true,
        FastClick: true,
    },
};
